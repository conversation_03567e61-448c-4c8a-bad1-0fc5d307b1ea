# Azure DevOps CI/CD Pipelines for Oracul Application

This document provides comprehensive instructions for setting up and configuring Azure DevOps CI/CD pipelines for the Oracul application, which consists of a .NET 9.0 backend API and an Angular 15 frontend.

## 📁 Pipeline Files

- **`azure-pipelines-backend.yml`** - Backend API pipeline (.NET 9.0 + Entity Framework Core)
- **`azure-pipelines-frontend.yml`** - Frontend pipeline (Angular 15 + Material Design)

## 🏗️ Architecture Overview

### Backend Pipeline
- **Build**: .NET 9.0 ASP.NET Core Web API
- **Data Layer**: Entity Framework Core with SQL Server
- **Testing**: xUnit integration tests
- **Deployment**: Azure App Service with deployment slots
- **Database**: Automated EF Core migrations

### Frontend Pipeline
- **Build**: Angular 15 with Material Design
- **Testing**: Jasmine/Karma unit tests + ESLint
- **Deployment**: Azure App Service or Azure Static Web Apps
- **Optimization**: Production builds with tree-shaking

## 🚀 Prerequisites

### Azure Resources Required

1. **Azure App Services**
   - Backend API: `oracul-backend-api`
   - Frontend: `oracul-frontend`
   - Deployment slots: `staging` (optional but recommended)

2. **Azure SQL Database**
   - SQL Server: `oracul-sql-server`
   - Databases: `OraculDB` (production), `OraculDB-staging`

3. **Azure Storage Account**
   - Blob storage for file uploads
   - Connection string for BlobStorage configuration

4. **Azure DevOps Service Connections**
   - Azure Resource Manager service connection
   - Name: `Azure-Service-Connection` (update in pipeline variables)

### Required Variable Groups

Create the following variable groups in Azure DevOps Library:

#### Backend Variables
```yaml
# Connection Strings
StagingConnectionString: "Server=your-server.database.windows.net;Database=OraculDB-staging;User Id=your-user;Password=your-password;TrustServerCertificate=True;"
ProductionConnectionString: "Server=your-server.database.windows.net;Database=OraculDB;User Id=your-user;Password=your-password;TrustServerCertificate=True;"

# JWT Configuration
JwtSecretKey: "YourSuperSecretKeyThatIsAtLeast32CharactersLongForSecurity123456789"

# Azure Storage
AzureBlobConnectionString: "DefaultEndpointsProtocol=https;AccountName=your-storage;AccountKey=your-key;EndpointSuffix=core.windows.net"

# Client URLs
StagingClientUrl: "https://oracul-frontend-staging.azurewebsites.net"
ProductionClientUrl: "https://oracul-frontend.azurewebsites.net"

# SQL Admin (for migrations)
SqlAdminUsername: "your-sql-admin"
SqlAdminPassword: "your-sql-password"
```

#### Frontend Variables (if using Static Web Apps)
```yaml
StaticWebAppsDeploymentToken: "your-static-web-apps-deployment-token"
```

## 📋 Setup Instructions

### 1. Create Azure DevOps Project
1. Create a new project in Azure DevOps
2. Import your repository or connect to existing Git repo

### 2. Set Up Service Connections
1. Go to **Project Settings** → **Service connections**
2. Create **Azure Resource Manager** connection
3. Name it `Azure-Service-Connection`
4. Grant access to all pipelines

### 3. Create Variable Groups
1. Go to **Pipelines** → **Library**
2. Create variable groups with the variables listed above
3. Mark sensitive variables (passwords, keys) as secret

### 4. Create Pipeline Files
1. Add the provided YAML files to your repository root
2. Update the variable values in the files to match your environment

### 5. Create Pipelines
1. Go to **Pipelines** → **Pipelines**
2. Click **New pipeline**
3. Select **Azure Repos Git** (or your source)
4. Select your repository
5. Choose **Existing Azure Pipelines YAML file**
6. Select `azure-pipelines-backend.yml` for backend
7. Repeat for frontend with `azure-pipelines-frontend.yml`

### 6. Configure Environments
1. Go to **Pipelines** → **Environments**
2. Create environments: `staging` and `production`
3. Add approval gates for production environment (recommended)

## 🔧 Configuration Customization

### Backend Pipeline Customization

Update these variables in `azure-pipelines-backend.yml`:

```yaml
variables:
  azureSubscription: 'Your-Service-Connection-Name'
  appServiceName: 'your-backend-app-service'
  resourceGroupName: 'your-resource-group'
  sqlServerName: 'your-sql-server'
  databaseName: 'YourDatabaseName'
```

### Frontend Pipeline Customization

Update these variables in `azure-pipelines-frontend.yml`:

```yaml
variables:
  azureSubscription: 'Your-Service-Connection-Name'
  appServiceName: 'your-frontend-app-service'
  resourceGroupName: 'your-resource-group'
  staticWebAppName: 'your-static-web-app' # If using Static Web Apps
```

### Deployment Options

#### Option 1: Azure App Service (Default)
- Suitable for full-stack applications
- Supports custom domains and SSL certificates
- Integrated with Application Insights

#### Option 2: Azure Static Web Apps (Frontend Only)
- Cost-effective for static content
- Built-in CDN and global distribution
- Automatic HTTPS and custom domains

To switch to Static Web Apps:
1. Uncomment the Static Web Apps deployment tasks
2. Comment out the App Service deployment tasks
3. Configure the `StaticWebAppsDeploymentToken` variable

## 🧪 Testing Configuration

### Backend Tests
- **Framework**: xUnit
- **Coverage**: Cobertura format
- **Integration Tests**: ASP.NET Core TestServer

### Frontend Tests
- **Unit Tests**: Jasmine/Karma
- **Linting**: ESLint
- **Coverage**: Istanbul/NYC

## 🔄 Branching Strategy

The pipelines are configured for:
- **`main` branch**: Deploys to production
- **`develop` branch**: Deploys to staging
- **Pull Requests**: Build and test only

## 🚨 Troubleshooting

### Common Issues

1. **EF Core Migration Failures**
   - Ensure connection strings are correct
   - Check SQL Server firewall rules
   - Verify database permissions

2. **Angular Build Failures**
   - Check Node.js version compatibility
   - Clear npm cache: `npm cache clean --force`
   - Verify package.json dependencies

3. **Deployment Failures**
   - Verify service connection permissions
   - Check App Service configuration
   - Review deployment logs in Azure portal

### Debugging Tips

1. **Enable verbose logging** in pipeline YAML:
   ```yaml
   - script: dotnet build --verbosity detailed
   ```

2. **Check Azure App Service logs**:
   - Go to App Service → Monitoring → Log stream
   - Review Application Insights for runtime errors

3. **Test locally** before pipeline deployment:
   ```bash
   # Backend
   dotnet build
   dotnet test
   dotnet publish

   # Frontend
   npm install
   npm run test
   npm run build
   ```

## 📚 Additional Resources

- [Azure DevOps Documentation](https://docs.microsoft.com/en-us/azure/devops/)
- [Azure App Service Documentation](https://docs.microsoft.com/en-us/azure/app-service/)
- [Entity Framework Core Migrations](https://docs.microsoft.com/en-us/ef/core/managing-schemas/migrations/)
- [Angular Deployment Guide](https://angular.io/guide/deployment)

## 🔐 Security Best Practices

1. **Use Azure Key Vault** for sensitive configuration
2. **Enable managed identity** for Azure resources
3. **Implement approval gates** for production deployments
4. **Regular security scanning** with Azure Security Center
5. **Monitor with Application Insights** for runtime security
