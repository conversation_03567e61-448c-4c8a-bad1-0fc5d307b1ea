import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Quill Editor
import { QuillModule } from 'ngx-quill';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';

// Components
import { ArticleViewComponent } from './article-view/article-view.component';
import { ArticleEditorComponent } from './article-editor/article-editor.component';
import { ArticleManagementComponent, ConfirmDeleteDialogComponent } from './article-management/article-management.component';
import { ImageUploadComponent } from './components/image-upload/image-upload.component';
import { ArticlePreviewDialogComponent } from './components/article-preview-dialog/article-preview-dialog.component';

// Services
import { ArticleService } from '../shared/services/article.service';

const routes = [
  {
    path: 'manage',
    component: ArticleManagementComponent
  },
  {
    path: 'editor',
    component: ArticleEditorComponent
  },
  {
    path: 'editor/:id',
    component: ArticleEditorComponent
  },
  {
    path: ':slug',
    component: ArticleViewComponent
  }
];

@NgModule({
  declarations: [
    ArticleViewComponent,
    ArticleEditorComponent,
    ArticleManagementComponent,
    ConfirmDeleteDialogComponent,
    ImageUploadComponent,
    ArticlePreviewDialogComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    FormsModule,
    QuillModule.forRoot(),
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTabsModule,
    MatMenuModule,
    MatTooltipModule,
    MatDividerModule
  ],
  providers: [
    ArticleService
  ]
})
export class ArticlesModule { }
