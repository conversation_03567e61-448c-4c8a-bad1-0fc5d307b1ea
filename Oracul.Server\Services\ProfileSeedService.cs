using Oracul.Data.Interfaces;
using Oracul.Data.Models;

namespace Oracul.Server.Services
{
    /// <summary>
    /// Service for seeding astrology-focused profile data
    /// </summary>
    public class ProfileSeedService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProfileSeedService> _logger;

        public ProfileSeedService(IUnitOfWork unitOfWork, ILogger<ProfileSeedService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Seed the database with 5 diverse oracle/astrology profiles
        /// </summary>
        public async Task SeedProfileDataAsync()
        {
            try
            {
                // Check if profiles already exist
                var existingProfiles = await _unitOfWork.Profiles.GetAllAsync();
                if (existingProfiles.Count() >= 5)
                {
                    _logger.LogInformation("Профилните данни вече са заредени в базата данни");
                    return;
                }

                // Clear existing profiles if less than 5
                if (existingProfiles.Any())
                {
                    foreach (var existingProfile in existingProfiles)
                    {
                        _unitOfWork.Profiles.SoftDelete(existingProfile);
                    }
                    await _unitOfWork.SaveChangesAsync();
                }

                // Create 5 diverse oracle profiles
                await CreateOracleProfilesAsync();

                _logger.LogInformation("5 различни оракулски профила са заредени успешно в базата данни");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при зареждане на профилните данни");
                throw;
            }
        }

        /// <summary>
        /// Create 5 diverse oracle profiles with different specialties
        /// </summary>
        private async Task CreateOracleProfilesAsync()
        {
            // Skip Luna Starweaver since she already exists, create only the new oracle profiles
            var oracleProfiles = new[]
            {
                new
                {
                    Email = "<EMAIL>",
                    FirstName = "Sage",
                    LastName = "Moonchild",
                    Username = "sage-moonchild",
                    Slug = "sage-moonchild",
                    Title = "Crystal Healer & Energy Worker",
                    Headline = "Healing through the power of crystals and energy work. Transforming lives with ancient crystal wisdom and modern energy techniques.",
                    Summary = "Master crystal healer and energy worker with 15 years of experience in vibrational healing. Specializing in chakra balancing, crystal therapy, Reiki healing, and energy clearing. I help clients release blockages and align with their highest potential through the sacred power of crystals.",
                    Location = new { City = "Santa Fe", State = "NM", Country = "USA", Display = "Santa Fe, NM" },
                    ProfilePhoto = "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
                    CoverPhoto = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=300&fit=crop",
                    Website = "https://crystalmoonhealing.com",
                    Phone = "+1 (505) 555-MOON",
                    Address = "888 Sacred Mountain Way",
                    PostalCode = "87501",
                    Skills = new[] { "Crystal Healing", "Energy Work", "Chakra Balancing", "Reiki Healing", "Meditation", "Aura Cleansing", "Sacred Geometry", "Sound Healing" },
                    Specialty = "Crystal Healing"
                },
                new
                {
                    Email = "<EMAIL>",
                    FirstName = "River",
                    LastName = "Palmistry",
                    Username = "river-palmistry",
                    Slug = "river-palmistry",
                    Title = "Third-Generation Palm Reader & Life Path Guide",
                    Headline = "Reading the stories written in your hands. Revealing your life's journey through the ancient art of palmistry passed down through generations.",
                    Summary = "Third-generation palm reader with an inherited gift for seeing life's path through the lines of your hands. With 18 years of practice, I provide insights into personality, relationships, career potential, and future possibilities through detailed palm analysis.",
                    Location = new { City = "New Orleans", State = "LA", Country = "USA", Display = "New Orleans, LA" },
                    ProfilePhoto = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
                    CoverPhoto = "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=300&fit=crop",
                    Website = "https://riverpalmistry.com",
                    Phone = "+1 (504) 555-PALM",
                    Address = "123 French Quarter Lane",
                    PostalCode = "70116",
                    Skills = new[] { "Palm Reading", "Life Path Analysis", "Personality Reading", "Relationship Compatibility", "Career Guidance", "Hand Analysis", "Chiromancy", "Fortune Telling" },
                    Specialty = "Palmistry"
                },
                new
                {
                    Email = "<EMAIL>",
                    FirstName = "Aurora",
                    LastName = "Wisdom",
                    Username = "aurora-wisdom",
                    Slug = "aurora-wisdom",
                    Title = "Spiritual Counselor & Intuitive Life Coach",
                    Headline = "Guiding souls toward their highest potential. Combining spiritual wisdom with practical life coaching to help you manifest your dreams.",
                    Summary = "Intuitive spiritual counselor and life coach with 14 years of experience helping people find their purpose and overcome challenges. I offer compassionate guidance for all of life's journeys, combining spiritual insights with practical coaching techniques.",
                    Location = new { City = "Portland", State = "OR", Country = "USA", Display = "Portland, OR" },
                    ProfilePhoto = "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
                    CoverPhoto = "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=800&h=300&fit=crop",
                    Website = "https://aurorawisdom.com",
                    Phone = "+1 (503) 555-WISE",
                    Address = "456 Spiritual Heights Blvd",
                    PostalCode = "97205",
                    Skills = new[] { "Spiritual Counseling", "Life Coaching", "Purpose Discovery", "Intuitive Reading", "Manifestation", "Mindfulness", "Meditation Guidance", "Soul Healing" },
                    Specialty = "Spiritual Counseling"
                },
                new
                {
                    Email = "<EMAIL>",
                    FirstName = "Cosmic",
                    LastName = "Dawn",
                    Username = "cosmic-dawn",
                    Slug = "cosmic-dawn",
                    Title = "Numerologist & Sacred Geometry Expert",
                    Headline = "Discovering your life's blueprint through numbers. Revealing the hidden patterns and meanings in your life through numerology and sacred geometry.",
                    Summary = "Master numerologist and sacred geometry expert with 16 years of experience revealing the hidden patterns and meanings in your life through the power of numbers. I help clients understand their life path, soul purpose, and divine timing through detailed numerological analysis.",
                    Location = new { City = "Boulder", State = "CO", Country = "USA", Display = "Boulder, CO" },
                    ProfilePhoto = "https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=150&h=150&fit=crop&crop=face",
                    CoverPhoto = "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=300&fit=crop",
                    Website = "https://cosmicdawnnumerology.com",
                    Phone = "+1 (303) 555-NUMS",
                    Address = "789 Sacred Numbers Ave",
                    PostalCode = "80302",
                    Skills = new[] { "Numerology", "Sacred Geometry", "Life Path Numbers", "Name Analysis", "Birth Date Reading", "Compatibility Numbers", "Business Numerology", "Pythagorean System" },
                    Specialty = "Numerology"
                }
            };

            foreach (var oracleData in oracleProfiles)
            {
                await CreateSingleOracleProfileAsync(oracleData);
            }
        }

        /// <summary>
        /// Create a single oracle profile with all related data
        /// </summary>
        private async Task CreateSingleOracleProfileAsync(dynamic oracleData)
        {
            // Check if user already exists
            var existingUser = await _unitOfWork.Users.GetByEmailAsync(oracleData.Email);
            User user;

            if (existingUser == null)
            {
                // Create new user
                user = new User
                {
                    FirstName = oracleData.FirstName,
                    LastName = oracleData.LastName,
                    Email = oracleData.Email,
                    PasswordHash = "$2a$11$example.hash.for.testing.purposes.only",
                    IsActive = true,
                    EmailConfirmed = true,
                    CreatedAt = DateTime.UtcNow
                };
                _unitOfWork.Users.Add(user);
                await _unitOfWork.SaveChangesAsync();
            }
            else
            {
                user = existingUser;
            }

            // Check if profile already exists for this user
            var existingProfile = await _unitOfWork.Profiles.GetByUserIdAsync(user.Id);
            if (existingProfile != null)
            {
                _logger.LogInformation($"Профилът за {oracleData.FirstName} {oracleData.LastName} вече съществува");
                return;
            }

            // Create profile
            var profile = new UserProfile
            {
                UserId = user.Id,
                Username = oracleData.Username,
                Slug = oracleData.Slug,
                IsPublic = true,
                ProfileCompletionPercentage = 90,
                ProfilePhotoUrl = oracleData.ProfilePhoto,
                CoverPhotoUrl = oracleData.CoverPhoto,
                FirstName = oracleData.FirstName,
                LastName = oracleData.LastName,
                ProfessionalTitle = oracleData.Title,
                Headline = oracleData.Headline,
                Summary = oracleData.Summary,
                ProfileViews = Random.Shared.Next(500, 4000),
                LastViewedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 30)),
                CreatedAt = DateTime.UtcNow.AddYears(-Random.Shared.Next(1, 3))
            };
            _unitOfWork.Profiles.Add(profile);
            await _unitOfWork.SaveChangesAsync();

            // Add location
            var location = new ProfileLocation
            {
                UserProfileId = profile.Id,
                City = oracleData.Location.City,
                State = oracleData.Location.State,
                Country = oracleData.Location.Country,
                DisplayLocation = oracleData.Location.Display
            };
            _unitOfWork.Profiles.GetDbContext().Set<ProfileLocation>().Add(location);

            // Add contact information
            var contactInfo = new ContactInformation
            {
                UserProfileId = profile.Id,
                Email = oracleData.Email,
                IsEmailPublic = true,
                Website = oracleData.Website,
                PortfolioUrl = oracleData.Website + "/portfolio"
            };
            _unitOfWork.Profiles.GetDbContext().Set<ContactInformation>().Add(contactInfo);
            await _unitOfWork.SaveChangesAsync();

            // Add business address
            var businessAddress = new BusinessAddress
            {
                ContactInformationId = contactInfo.Id,
                Street = oracleData.Address,
                City = oracleData.Location.City,
                State = oracleData.Location.State,
                PostalCode = oracleData.PostalCode,
                Country = oracleData.Location.Country,
                IsPublic = true
            };
            _unitOfWork.Profiles.GetDbContext().Set<BusinessAddress>().Add(businessAddress);

            // Add phone number
            var phoneNumber = new PhoneNumber
            {
                ContactInformationId = contactInfo.Id,
                Number = oracleData.Phone,
                Type = "business",
                IsPublic = true,
                IsPrimary = true
            };
            _unitOfWork.Profiles.GetDbContext().Set<PhoneNumber>().Add(phoneNumber);

            // Add skills
            foreach (var skillName in oracleData.Skills)
            {
                var skill = new ProfileSkill
                {
                    UserProfileId = profile.Id,
                    Name = skillName,
                    Category = oracleData.Specialty,
                    Endorsements = Random.Shared.Next(20, 100),
                    ProficiencyLevel = Random.Shared.Next(1, 4) switch
                    {
                        1 => "beginner",
                        2 => "intermediate",
                        3 => "advanced",
                        _ => "expert"
                    }
                };
                _unitOfWork.Profiles.GetDbContext().Set<ProfileSkill>().Add(skill);
            }

            // Add a blog post
            var blogPost = new BlogPost
            {
                UserProfileId = profile.Id,
                Title = GetBlogTitleForSpecialty(oracleData.Specialty),
                Excerpt = GetBlogExcerptForSpecialty(oracleData.Specialty),
                Content = GetBlogContentForSpecialty(oracleData.Specialty),
                PublishedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 90)),
                ReadCount = Random.Shared.Next(100, 3000),
                Slug = GetBlogSlugForSpecialty(oracleData.Specialty),
                FeaturedImageUrl = GetBlogImageForSpecialty(oracleData.Specialty)
            };
            _unitOfWork.Profiles.GetDbContext().Set<BlogPost>().Add(blogPost);
            await _unitOfWork.SaveChangesAsync();

            // Add blog post tags
            var tags = GetBlogTagsForSpecialty(oracleData.Specialty);
            foreach (var tag in tags)
            {
                var blogPostTag = new BlogPostTag
                {
                    BlogPostId = blogPost.Id,
                    Tag = tag
                };
                _unitOfWork.Profiles.GetDbContext().Set<BlogPostTag>().Add(blogPostTag);
            }
            await _unitOfWork.SaveChangesAsync();

            // Create additional featured articles for this profile
            await CreateAdditionalArticlesForProfile(profile.Id, oracleData.Specialty);

            // Add certifications
            var certifications = GetCertificationsForSpecialty(oracleData.Specialty, profile.Id);
            foreach (var certification in certifications)
            {
                _unitOfWork.Profiles.GetDbContext().Set<Certification>().Add(certification);
            }

            // Add work experience
            var experience = new WorkExperience
            {
                UserProfileId = profile.Id,
                Company = GetCompanyNameForSpecialty(oracleData.Specialty, oracleData.FirstName),
                Position = "Founder & Lead " + oracleData.Specialty + " Practitioner",
                StartDate = DateTime.UtcNow.AddYears(-Random.Shared.Next(5, 15)),
                IsCurrent = true,
                Description = GetExperienceDescriptionForSpecialty(oracleData.Specialty),
                Location = oracleData.Location.Display,
                CompanyLogoUrl = "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=48&h=48&fit=crop"
            };
            _unitOfWork.Profiles.GetDbContext().Set<WorkExperience>().Add(experience);
            await _unitOfWork.SaveChangesAsync();

            // Add social links
            var socialLinks = new[]
            {
                new SocialLink { UserProfileId = profile.Id, Platform = "instagram", Url = $"https://instagram.com/{oracleData.Username.Replace("-", "_")}", DisplayName = $"@{oracleData.Username.Replace("-", "_")}", IsPublic = true },
                new SocialLink { UserProfileId = profile.Id, Platform = "youtube", Url = $"https://youtube.com/c/{oracleData.FirstName}{oracleData.Specialty}", DisplayName = $"{oracleData.FirstName} {oracleData.Specialty}", IsPublic = true },
                new SocialLink { UserProfileId = profile.Id, Platform = "facebook", Url = $"https://facebook.com/{oracleData.FirstName}{oracleData.Specialty}Studio", DisplayName = $"{oracleData.FirstName} {oracleData.Specialty} Studio", IsPublic = true }
            };

            foreach (var socialLink in socialLinks)
            {
                _unitOfWork.Profiles.GetDbContext().Set<SocialLink>().Add(socialLink);
            }

            await _unitOfWork.SaveChangesAsync();

            // Update profile completion percentage
            await _unitOfWork.Profiles.UpdateProfileCompletionPercentageAsync(profile.Id);
        }

        #region Helper Methods for Specialty-Specific Content

        private string GetBlogTitleForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "Mercury Retrograde: Navigating Communication Challenges",
                "Crystal Healing" => "The Power of Amethyst: Healing Your Third Eye Chakra",
                "Palmistry" => "Reading Life Lines: Understanding Your Destiny Through Your Hands",
                "Spiritual Counseling" => "Finding Your Soul Purpose: A Guide to Spiritual Awakening",
                "Numerology" => "Your Life Path Number: Unlocking Your Divine Blueprint",
                _ => "Spiritual Wisdom for Modern Times"
            };
        }

        private string GetBlogExcerptForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "Discover how to harness the transformative energy of Mercury retrograde periods and turn apparent setbacks into opportunities for growth.",
                "Crystal Healing" => "Learn how amethyst can enhance your intuition and open your third eye chakra for deeper spiritual insights.",
                "Palmistry" => "Explore the ancient art of palm reading and discover what your life lines reveal about your destiny and life path.",
                "Spiritual Counseling" => "A comprehensive guide to discovering your soul's purpose and aligning with your highest spiritual potential.",
                "Numerology" => "Understand the significance of your life path number and how it influences your personality, relationships, and life journey.",
                _ => "Ancient wisdom for navigating modern spiritual challenges."
            };
        }

        private string GetBlogContentForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => GetAstrologyArticleContent(),
                "Crystal Healing" => GetCrystalHealingArticleContent(),
                "Palmistry" => GetPalmistryArticleContent(),
                "Spiritual Counseling" => GetSpiritualCounselingArticleContent(),
                "Numerology" => GetNumerologyArticleContent(),
                _ => GetGeneralSpiritualArticleContent()
            };
        }

        private string GetBlogSlugForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "mercury-retrograde-navigation-guide",
                "Crystal Healing" => "amethyst-third-eye-chakra-healing",
                "Palmistry" => "reading-life-lines-destiny-guide",
                "Spiritual Counseling" => "finding-soul-purpose-spiritual-awakening",
                "Numerology" => "life-path-number-divine-blueprint",
                _ => "spiritual-wisdom-modern-times"
            };
        }

        private string GetBlogImageForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=200&fit=crop",
                "Crystal Healing" => "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop",
                "Palmistry" => "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop",
                "Spiritual Counseling" => "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=200&fit=crop",
                "Numerology" => "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=200&fit=crop",
                _ => "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop"
            };
        }

        private Certification[] GetCertificationsForSpecialty(string specialty, int profileId)
        {
            return specialty switch
            {
                "Astrology" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Certified Professional Astrologer (CPA)", IssuingOrganization = "International Society for Astrological Research", IssueDate = DateTime.UtcNow.AddYears(-5), ExpirationDate = DateTime.UtcNow.AddYears(1), CredentialId = "ISAR-CPA-2019-7734" },
                    new Certification { UserProfileId = profileId, Name = "Advanced Tarot Certification", IssuingOrganization = "American Tarot Association", IssueDate = DateTime.UtcNow.AddYears(-4), CredentialId = "ATA-ADV-2020-9821" }
                },
                "Crystal Healing" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Master Crystal Healer", IssuingOrganization = "Crystal Healing Institute", IssueDate = DateTime.UtcNow.AddYears(-6), ExpirationDate = DateTime.UtcNow.AddYears(2), CredentialId = "CHI-MASTER-2018-3344" },
                    new Certification { UserProfileId = profileId, Name = "Reiki Master Teacher", IssuingOrganization = "International Reiki Association", IssueDate = DateTime.UtcNow.AddYears(-5), CredentialId = "IRA-MASTER-2019-5566" }
                },
                "Palmistry" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Certified Palmistry Expert", IssuingOrganization = "International Palmistry Association", IssueDate = DateTime.UtcNow.AddYears(-7), CredentialId = "IPA-EXPERT-2017-8899" },
                    new Certification { UserProfileId = profileId, Name = "Hand Analysis Specialist", IssuingOrganization = "American Hand Analysis Society", IssueDate = DateTime.UtcNow.AddYears(-6), CredentialId = "AHAS-SPEC-2018-1122" }
                },
                "Spiritual Counseling" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Certified Spiritual Counselor", IssuingOrganization = "International Spiritual Counseling Association", IssueDate = DateTime.UtcNow.AddYears(-8), ExpirationDate = DateTime.UtcNow.AddYears(1), CredentialId = "ISCA-CERT-2016-4455" },
                    new Certification { UserProfileId = profileId, Name = "Life Coach Certification", IssuingOrganization = "International Coach Federation", IssueDate = DateTime.UtcNow.AddYears(-5), CredentialId = "ICF-COACH-2019-7788" }
                },
                "Numerology" => new[]
                {
                    new Certification { UserProfileId = profileId, Name = "Master Numerologist", IssuingOrganization = "International Numerology Association", IssueDate = DateTime.UtcNow.AddYears(-9), CredentialId = "INA-MASTER-2015-9900" },
                    new Certification { UserProfileId = profileId, Name = "Sacred Geometry Specialist", IssuingOrganization = "Sacred Geometry Institute", IssueDate = DateTime.UtcNow.AddYears(-4), CredentialId = "SGI-SPEC-2020-2233" }
                },
                _ => new Certification[0]
            };
        }

        private string GetCompanyNameForSpecialty(string specialty, string firstName)
        {
            return specialty switch
            {
                "Astrology" => $"{firstName} Astrology Studio",
                "Crystal Healing" => $"{firstName} Crystal Healing Center",
                "Palmistry" => $"{firstName} Palm Reading Studio",
                "Spiritual Counseling" => $"{firstName} Spiritual Wisdom Center",
                "Numerology" => $"{firstName} Numerology Institute",
                _ => $"{firstName} Spiritual Services"
            };
        }

        private string GetExperienceDescriptionForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => "Founded and operate a thriving astrology practice offering personalized readings, workshops, and spiritual guidance. Specialize in natal chart interpretation, relationship compatibility, and life transition guidance.",
                "Crystal Healing" => "Established a comprehensive crystal healing practice providing energy work, chakra balancing, and vibrational healing services. Help clients release blockages and align with their highest potential.",
                "Palmistry" => "Built a successful palmistry practice offering detailed hand analysis, life path readings, and personality insights. Provide guidance on relationships, career potential, and future possibilities.",
                "Spiritual Counseling" => "Created a holistic spiritual counseling practice combining traditional counseling with spiritual guidance. Help clients discover their purpose and navigate life's challenges with wisdom and compassion.",
                "Numerology" => "Developed a specialized numerology practice offering life path analysis, name readings, and timing guidance. Help clients understand their divine blueprint and make aligned life decisions.",
                _ => "Established a comprehensive spiritual practice offering various forms of guidance and healing services."
            };
        }

        private string GetAstrologyArticleContent()
        {
            return @"Mercury retrograde periods are often misunderstood and feared, but they offer powerful opportunities for growth and introspection. During these times, we're invited to slow down, review, and realign with our true path.

## Разбиране на Меркурий ретроград

Меркурий ретроград се случва 3-4 пъти годишно, когато планетата изглежда да се движи назад в небето от гледна точка на Земята. Този астрономически феномен има дълбоко духовно значение в астрологията.

### Какво означава това за вас?

По време на Меркурий ретроград, комуникацията, технологиите и пътуванията могат да бъдат засегнати. Но вместо да се страхуваме, можем да използваме това време за:

- **Преосмисляне** на важни решения
- **Преразглеждане** на стари проекти
- **Възстановяване** на връзки
- **Рефлексия** върху личностния растеж

### Практически съвети за навигиране

1. **Забавете темпото** - Не бързайте с важни решения
2. **Проверете детайлите** - Прегледайте договори и документи внимателно
3. **Резервирайте време** - Планирайте допълнително време за пътувания
4. **Практикувайте търпение** - Технологичните проблеми са временни

### Духовните възможности

Меркурий ретроград ни предлага уникална възможност да се свържем с нашата интуиция и да преосмислим нашия жизнен път. Това е време за медитация, журналиране и дълбока саморефлексия.

## Заключение

Вместо да се страхуваме от Меркурий ретроград, можем да го приемем като космически подарък за личностно развитие и духовен растеж. Използвайте това време мъдро и ще откриете скрити благословии в това, което първоначално изглежда като предизвикателство.";
        }

        private string GetCrystalHealingArticleContent()
        {
            return @"Amethyst, known as the stone of spiritual wisdom, has been revered for centuries for its ability to enhance intuition and promote spiritual growth. This beautiful purple crystal works directly with the third eye chakra.

## Силата на аметиста за третото око

Аметистът е един от най-мощните кристали за духовно развитие и интуитивно прозрение. Неговата лилава енергия резонира директно с третото око чакра, помагайки ни да отворим вратите към по-висшето съзнание.

### Свойства на аметиста

- **Цвят**: От светло лилаво до дълбоко виолетово
- **Чакра**: Третото око (Аджна) и коронната чакра
- **Планета**: Юпитер и Нептун
- **Елемент**: Въздух и вода

### Как работи аметистът с третото око?

Третото око чакра, разположена между веждите, е центърът на нашата интуиция и духовно виждане. Когато тази чакра е блокирана, можем да изпитваме:

- Липса на яснота в мисленето
- Затруднения в медитацията
- Слаба интуиция
- Духовна дезориентация

### Техники за работа с аметист

#### 1. Медитация с аметист
Поставете аметист на третото си око по време на медитация. Визуализирайте лилава светлина, която се разширява от кристала и изпълва цялото ви съзнание.

#### 2. Носене като бижу
Носете аметистови обеци или колие близо до главата, за да поддържате постоянна връзка с енергията на кристала.

#### 3. Поставяне в спалнята
Поставете аметист до леглото си за по-ясни сънища и по-дълбок сън.

### Почистване и зареждане

За да поддържате аметиста си в оптимално състояние:

- **Почистване**: Промийте с течаща вода веднъж седмично
- **Зареждане**: Поставете под лунна светлина или върху селенит
- **Програмиране**: Задайте ясна интенция за духовен растеж

## Заключение

Аметистът е мощен съюзник в нашето духовно пътешествие. Чрез работа с този кристал можем да отворим третото си око и да достигнем до по-дълбоки нива на мъдрост и разбиране.";
        }

        private string GetPalmistryArticleContent()
        {
            return @"The lines on your palms tell a story that spans your entire lifetime. Each line, mount, and marking reveals insights about your personality, potential, and life path.

## Четенето на длани: Прозорец към съдбата

Хиромантията, или четенето на длани, е древно изкуство, което ни позволява да разчетем картата на нашия живот, написана в линиите на ръцете ни. Всяка длан е уникална и носи информация за нашето минало, настояще и бъдеще.

### Основните линии на дланта

#### 1. Линията на живота
- **Местоположение**: Обгражда палеца
- **Значение**: Жизненост, здраве, основни жизнени промени
- **Четене**: По-дълга линия означава по-дълъг живот, но качеството е по-важно от дължината

#### 2. Линията на сърцето
- **Местоположение**: Най-горната хоризонтална линия
- **Значение**: Емоции, любов, връзки
- **Четене**: Дълбока линия показва силни емоции, прекъсната - емоционални травми

#### 3. Линията на главата
- **Местоположение**: Средната хоризонтална линия
- **Значение**: Интелект, мислене, комуникация
- **Четене**: Права линия - логично мислене, извита - креативност

#### 4. Линията на съдбата
- **Местоположение**: Вертикална линия в центъра
- **Значение**: Кариера, жизнен път, предназначение
- **Четене**: Ясна линия - определен път, отсъстваща - свобода на избора

### Планините на дланта

Планините са издутините области на дланта, всяка свързана с различна планета:

- **Планината на Венера**: Любов и страст
- **Планината на Юпитер**: Лидерство и амбиция
- **Планината на Сатурн**: Мъдрост и дисциплина
- **Планината на Аполон**: Творчество и слава
- **Планината на Меркурий**: Комуникация и бизнес

### Как да четете собствената си длан

1. **Изберете доминиращата ръка** - тя показва настоящето и бъдещето
2. **Започнете с основните линии** - живот, сърце, глава
3. **Обърнете внимание на качеството** - дълбочина, яснота, прекъсвания
4. **Разгледайте планините** - тяхната развитост показва силни области
5. **Търсете специални знаци** - звезди, кръстове, острови

### Етика в хиромантията

Важно е да помним, че хиромантията е инструмент за самопознание, не предопределение. Линиите могат да се променят с времето, отразявайки нашите избори и растеж.

## Заключение

Четенето на длани ни предлага уникален поглед върху нашия жизнен път. Чрез разбиране на символите в ръцете си можем да вземаме по-осъзнати решения и да живеем в хармония с нашето истинско предназначение.";
        }

        private string GetSpiritualCounselingArticleContent()
        {
            return @"Finding your soul purpose is one of the most profound journeys you can embark upon. It requires deep introspection, spiritual practice, and often guidance from experienced counselors.

## Намиране на душевното предназначение

Всеки от нас е дошъл на тази земя с уникална мисия и предназначение. Откриването на това предназначение е ключът към истинското щастие и изпълнение в живота.

### Какво е душевно предназначение?

Душевното предназначение е дълбокото призвание на душата ви - това, за което сте родени да правите в този живот. То не винаги съвпада с професията ви, но винаги е свързано с начина, по който можете да служите на света.

### Признаци, че сте открили предназначението си

- **Усещане за поток**: Времето спира, когато правите това
- **Естествена лекота**: Дейността ви се получава без усилие
- **Дълбоко удовлетворение**: Чувствате се изпълнени и смислени
- **Положително въздействие**: Работата ви помага на другите
- **Синхроничности**: Вселената ви подкрепя с знаци

### Стъпки към откриване на предназначението

#### 1. Саморефлексия
- Какво ви носеше радост като дете?
- Кои дейности ви зареждат с енергия?
- За какво хората често ви молят помощ?
- Какви проблеми в света ви вълнуват най-много?

#### 2. Медитация и молитва
Редовната духовна практика ви свързва с вътрешната мъдрост и интуиция. Задавайте въпроси и слушайте отговорите, които идват от сърцето.

#### 3. Изследване на таланти
- Направете списък на естествените си способности
- Помислете как можете да ги използвате за служене
- Експериментирайте с различни начини за изразяване

#### 4. Търсене на наставничество
Духовен консултант може да ви помогне да:
- Разпознаете блокировките
- Разберете кармичните уроци
- Получите яснота за следващите стъпки

### Преодоляване на препятствията

#### Страх от неуспех
Помнете, че неуспехът е част от ученето. Всяка грешка ви приближава до истината.

#### Социални очаквания
Не позволявайте на мненията на другите да определят вашия път. Вашето предназначение е уникално за вас.

#### Финансови притеснения
Започнете малко. Можете да следвате предназначението си постепенно, докато изграждате стабилност.

### Живот в съответствие с предназначението

Когато живеете според душевното си предназначение:
- Привличате правилните хора и възможности
- Изпитвате дълбоко удовлетворение
- Вашата енергия се увеличава
- Оказвате положително влияние на света

## Заключение

Откриването на душевното предназначение е процес, не еднократно събитие. Бъдете търпеливи със себе си и доверете се на процеса. Вселената ви подкрепя в това свещено пътешествие към автентичност и служене.";
        }

        private string GetNumerologyArticleContent()
        {
            return @"Your life path number is calculated from your birth date and reveals the core lessons and experiences you're meant to encounter in this lifetime.

## Числото на жизнения път: Вашият божествен план

Нумерологията ни учи, че числата носят вибрационна енергия, която влияе на нашия живот. Числото на жизнения път е най-важното число в нумерологичната ви карта и разкрива основната ви мисия в този живот.

### Как да изчислите числото на жизнения път

Използвайте пълната си дата на раждане и съберете всички цифри до получаване на едноцифрено число (или главно число 11, 22, 33).

**Пример**: 15.03.1985
1+5+0+3+1+9+8+5 = 32
3+2 = 5

Числото на жизнения път е 5.

### Значения на числата на жизнения път

#### Число 1 - Лидерът
- **Качества**: Независимост, иновация, лидерство
- **Предизвикателства**: Егоизъм, нетърпимост
- **Мисия**: Да прокладвате нови пътища и вдъхновявате другите

#### Число 2 - Дипломатът
- **Качества**: Сътрудничество, чувствителност, миротворчество
- **Предизвикателства**: Нерешителност, зависимост
- **Мисия**: Да създавате хармония и баланс

#### Число 3 - Творецът
- **Качества**: Креативност, комуникация, оптимизъм
- **Предизвикателства**: Разпръснатост, повърхностност
- **Мисия**: Да вдъхновявате и забавлявате другите

#### Число 4 - Строителят
- **Качества**: Стабилност, организация, практичност
- **Предизвикателства**: Твърдоглавие, ограниченост
- **Мисия**: Да създавате солидни основи за бъдещето

#### Число 5 - Авантюристът
- **Качества**: Свобода, любопитство, адаптивност
- **Предизвикателства**: Непостоянство, импулсивност
- **Мисия**: Да изследвате и споделяте нови преживявания

#### Число 6 - Грижовникът
- **Качества**: Отговорност, състрадание, семейност
- **Предизвикателства**: Прекалена грижа, контрол
- **Мисия**: Да лекувате и подкрепяте другите

#### Число 7 - Търсачът
- **Качества**: Духовност, анализ, интуиция
- **Предизвикателства**: Изолация, скептицизъм
- **Мисия**: Да търсите истината и споделяте мъдрост

#### Число 8 - Постигачът
- **Качества**: Амбиция, материален успех, власт
- **Предизвикателства**: Материализъм, безмилостност
- **Мисия**: Да постигате успех и помагате на другите

#### Число 9 - Хуманистът
- **Качества**: Състрадание, универсална любов, служене
- **Предизвикателства**: Мартирство, разочарование
- **Мисия**: Да служите на човечеството

### Главни числа

#### Число 11 - Интуитивният учител
Висша вибрация на число 2, носи духовно прозрение и интуиция.

#### Число 22 - Главният строител
Висша вибрация на число 4, способен да материализира големи визии.

#### Число 33 - Главният учител
Висша вибрация на число 6, носи безусловна любов и лечение.

### Как да използвате знанието

Разбирането на числото на жизнения път ви помага да:
- Разпознаете естествените си таланти
- Разберете предизвикателствата си
- Вземате решения в съответствие с предназначението си
- Развиете по-дълбоко самопознание

## Заключение

Числото на жизнения път е като космически GPS, който ви насочва към вашето истинско предназначение. Използвайте тази мъдрост, за да живеете по-осъзнат и изпълнен живот.";
        }

        private string GetGeneralSpiritualArticleContent()
        {
            return @"Spiritual wisdom has been passed down through generations, offering guidance for those seeking deeper meaning and purpose in their lives.

## Духовна мъдрост за съвременния свят

В нашия забързан свят, духовната мъдрост предлага оазис на спокойствие и разбиране. Тези вечни истини могат да ни помогнат да навигираме предизвикателствата на съвременния живот.

### Основни принципи на духовната мъдрост

#### 1. Всичко е свързано
Вселената е единна мрежа от енергия и съзнание. Това, което правим на едно място, влияе на цялото.

#### 2. Любовта е най-висшата сила
Любовта е основната енергия на творението. Тя лекува, трансформира и обединява.

#### 3. Настоящият момент е всичко
Истинската сила и мир се намират в настоящия момент. Миналото е история, бъдещето е мистерия.

#### 4. Всичко се случва с причина
Няма случайности във Вселената. Всяко преживяване носи урок за растеж.

### Практики за духовно развитие

#### Медитация
Редовната медитация ни свързва с вътрешния мир и мъдрост.

#### Благодарност
Практикуването на благодарност трансформира нашата перспектива и привлича изобилие.

#### Служене
Служенето на другите е път към духовно изпълнение и растеж.

#### Прошка
Прошката освобождава енергията и отваря сърцето за любов.

## Заключение

Духовната мъдрост е наше право по рождение. Чрез практикуване на тези принципи можем да живеем по-осъзнат и изпълнен живот.";
        }

        private string[] GetBlogTagsForSpecialty(string specialty)
        {
            return specialty switch
            {
                "Astrology" => new[] { "Астрология", "Меркурий ретроград", "Планетарни транзити", "Духовност" },
                "Crystal Healing" => new[] { "Кристали", "Лечение", "Аметист", "Чакри", "Енергия" },
                "Palmistry" => new[] { "Хиромантия", "Четене на длани", "Съдба", "Линии", "Предсказания" },
                "Spiritual Counseling" => new[] { "Духовно консултиране", "Предназначение", "Саморазвитие", "Мъдрост" },
                "Numerology" => new[] { "Нумерология", "Жизнен път", "Числа", "Божествен план", "Вибрации" },
                _ => new[] { "Духовност", "Мъдрост", "Развитие", "Съзнание" }
            };
        }

        private async Task CreateAdditionalArticlesForProfile(int profileId, string specialty)
        {
            var additionalArticles = GetAdditionalArticlesForSpecialty(specialty, profileId);

            foreach (var article in additionalArticles)
            {
                var blogPost = new BlogPost
                {
                    UserProfileId = profileId,
                    Title = article.Title,
                    Excerpt = article.Excerpt,
                    Content = article.Content,
                    PublishedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 180)),
                    ReadCount = Random.Shared.Next(50, 2500),
                    Slug = article.Slug,
                    FeaturedImageUrl = article.ImageUrl
                };

                _unitOfWork.Profiles.GetDbContext().Set<BlogPost>().Add(blogPost);
                await _unitOfWork.SaveChangesAsync();

                // Add tags
                foreach (var tag in article.Tags)
                {
                    var blogPostTag = new BlogPostTag
                    {
                        BlogPostId = blogPost.Id,
                        Tag = tag
                    };
                    _unitOfWork.Profiles.GetDbContext().Set<BlogPostTag>().Add(blogPostTag);
                }
                await _unitOfWork.SaveChangesAsync();
            }
        }

        private List<AdditionalArticle> GetAdditionalArticlesForSpecialty(string specialty, int profileId)
        {
            return specialty switch
            {
                "Astrology" => new List<AdditionalArticle>
                {
                    new AdditionalArticle
                    {
                        Title = "Разбиране на вашата натална карта: Ръководство за начинаещи",
                        Excerpt = "Открийте тайните, скрити във вашата натална карта и научете как планетарните позиции при раждането ви влияят на личността и жизнения ви път.",
                        Content = GetNatalChartArticleContent(),
                        Slug = "understanding-birth-chart-beginners-guide",
                        ImageUrl = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop",
                        Tags = new[] { "Натална карта", "Астрология", "Начинаещи", "Планети" }
                    }
                },
                "Crystal Healing" => new List<AdditionalArticle>
                {
                    new AdditionalArticle
                    {
                        Title = "Кристално лечение: Избор на правилните камъни",
                        Excerpt = "Изследвайте метафизичните свойства на кристалите и научете как да избирате перфектните камъни за вашето духовно пътешествие.",
                        Content = GetCrystalSelectionArticleContent(),
                        Slug = "crystal-healing-choosing-right-stones",
                        ImageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=400&fit=crop",
                        Tags = new[] { "Кристали", "Лечение", "Избор", "Енергия" }
                    }
                },
                "Spiritual Counseling" => new List<AdditionalArticle>
                {
                    new AdditionalArticle
                    {
                        Title = "Лунните фази и тяхното влияние върху емоциите",
                        Excerpt = "Изследвайте как различните лунни фази влияят на вашите емоции и как да използвате тази енергия за личностен растеж.",
                        Content = GetLunarPhasesArticleContent(),
                        Slug = "lunar-phases-emotional-influence",
                        ImageUrl = "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=800&h=400&fit=crop",
                        Tags = new[] { "Луна", "Емоции", "Фази", "Енергия" }
                    }
                },
                _ => new List<AdditionalArticle>()
            };
        }

        private class AdditionalArticle
        {
            public string Title { get; set; } = string.Empty;
            public string Excerpt { get; set; } = string.Empty;
            public string Content { get; set; } = string.Empty;
            public string Slug { get; set; } = string.Empty;
            public string ImageUrl { get; set; } = string.Empty;
            public string[] Tags { get; set; } = Array.Empty<string>();
        }

        private string GetNatalChartArticleContent()
        {
            return @"Натална карта е като космическа снимка на небето в момента на вашето раждане. Тя разкрива уникалната ви личност и жизнен път.

## Какво е натална карта?

Натална карта, известна също като хороскоп на раждането, е астрологична карта, която показва точните позиции на планетите в момента на вашето раждане. Тя е вашият личен космически отпечатък.

### Основни елементи на натална карта

#### 1. Слънцето (Слънчев знак)
Представлява вашата основна личност и его. Това е знакът, който повечето хора знаят.

#### 2. Луната (Лунен знак)
Показва вашите емоции, интуиция и подсъзнателни нужди.

#### 3. Възходящият знак (Асцендент)
Как се представяте пред света и първото впечатление, което правите.

### Как да четете натална карта

1. **Започнете с голямата тройка** - Слънце, Луна, Възходящ
2. **Разгледайте планетите в знаците** - Какви енергии изразявате
3. **Проучете къщите** - В кои области на живота се проявяват енергиите
4. **Анализирайте аспектите** - Как планетите взаимодействат помежду си

## Заключение

Натална карта е мощен инструмент за самопознание. Тя ви помага да разберете силните си страни и да работите с предизвикателствата си.";
        }

        private string GetCrystalSelectionArticleContent()
        {
            return @"Избирането на правилните кристали е изкуство, което изисква интуиция, знание и връзка с енергията на камъните.

## Как да изберете правилните кристали

Всеки кристал носи уникална вибрация и може да ви помогне в различни области на живота.

### Кристали за начинаещи

#### 1. Кварц
- **Свойства**: Усилване на енергията, яснота
- **Употреба**: Медитация, лечение

#### 2. Аметист
- **Свойства**: Духовност, интуиция
- **Употреба**: Третото око, сън

#### 3. Розов кварц
- **Свойства**: Любов, лечение на сърцето
- **Употреба**: Връзки, самолюбов

### Как да избирате

1. **Доверете се на интуицията** - Кой кристал ви привлича?
2. **Определете целта** - За какво искате помощ?
3. **Почувствайте енергията** - Как се чувствате, когато го докоснете?

## Заключение

Правилният кристал ще резонира с вашата енергия и ще ви подкрепи в духовното ви пътешествие.";
        }

        private string GetLunarPhasesArticleContent()
        {
            return @"Луната влияе на нашите емоции и енергия чрез различните си фази. Разбирането на този цикъл може да трансформира живота ви.

## Четирите основни лунни фази

### 1. Новолуние
- **Енергия**: Начала, намерения
- **Емоции**: Интроспекция, планиране
- **Действия**: Поставяне на цели, медитация

### 2. Растяща луна
- **Енергия**: Растеж, действие
- **Емоции**: Оптимизъм, мотивация
- **Действия**: Работа по проекти, изграждане

### 3. Пълнолуние
- **Енергия**: Кулминация, манифестация
- **Емоции**: Интензивност, яснота
- **Действия**: Завършване, освобождаване

### 4. Намаляваща луна
- **Енергия**: Освобождаване, почистване
- **Емоции**: Рефлексия, прошка
- **Действия**: Отпускане, детоксикация

## Как да работите с лунните фази

1. **Следете лунния календар**
2. **Адаптирайте дейностите си**
3. **Практикувайте лунни ритуали**
4. **Слушайте тялото и емоциите си**

## Заключение

Синхронизирането с лунните фази ви помага да живеете в хармония с естествените ритми на Вселената.";
        }

        #endregion
    }
}
