"use strict";(self.webpackChunkoracul_client=self.webpackChunkoracul_client||[]).push([[50],{9050:(li,j,l)=>{l.r(j),l.d(j,{ArticlesModule:()=>ui});var h=l(6895),y=l(1390),C=l(4006);function G(n,r,e,i,o,a,s){try{var c=n[a](s),d=c.value}catch(_){return void e(_)}c.done?r(d):Promise.resolve(d).then(i,o)}function K(n){return function(){var r=this,e=arguments;return new Promise(function(i,o){var a=n.apply(r,e);function s(d){G(a,i,o,s,c,"next",d)}function c(d){G(a,i,o,s,c,"throw",d)}s(void 0)})}}var t=l(4650);const z={toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["clean"],["link","image","video"]]},$=new t.OlP("config",{providedIn:"root",factory:()=>({modules:z})});var I=l(1481),mt=l(9770),gt=l(5191),pt=l(6805),ht=l(930);function ft(n,r){const e="object"==typeof r;return new Promise((i,o)=>{const a=new ht.Hp({next:s=>{i(s),a.unsubscribe()},error:o,complete:()=>{e?i(r.defaultValue):o(new pt.K)}});n.subscribe(a)})}var _t=l(727),H=l(4968),k=l(7579),vt=l(6063);class Ct extends k.x{constructor(r=1/0,e=1/0,i=vt.l){super(),this._bufferSize=r,this._windowTime=e,this._timestampProvider=i,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=e===1/0,this._bufferSize=Math.max(1,r),this._windowTime=Math.max(1,e)}next(r){const{isStopped:e,_buffer:i,_infiniteTimeWindow:o,_timestampProvider:a,_windowTime:s}=this;e||(i.push(r),!o&&i.push(a.now()+s)),this._trimBuffer(),super.next(r)}_subscribe(r){this._throwIfClosed(),this._trimBuffer();const e=this._innerSubscribe(r),{_infiniteTimeWindow:i,_buffer:o}=this,a=o.slice();for(let s=0;s<a.length&&!r.closed;s+=i?1:2)r.next(a[s]);return this._checkFinalizedStatuses(r),e}_trimBuffer(){const{_bufferSize:r,_timestampProvider:e,_buffer:i,_infiniteTimeWindow:o}=this,a=(o?1:2)*r;if(r<1/0&&a<i.length&&i.splice(0,i.length-a),!o){const s=e.now();let c=0;for(let d=1;d<i.length&&i[d]<=s;d+=2)c=d;c&&i.splice(0,c+1)}}}var xt=l(3099),W=l(5577),X=l(8372);function At(n,r){1&n&&t._UZ(0,"div",2)}function Mt(n,r){1&n&&t._UZ(0,"pre",2)}function wt(n,r){if(1&n&&(t.ynx(0),t.YNc(1,At,1,0,"div",1),t.YNc(2,Mt,1,0,"pre",1),t.BQk()),2&n){const e=t.oxw();t.xp6(1),t.Q6J("ngIf",!e.preserve),t.xp6(1),t.Q6J("ngIf",e.preserve)}}function Tt(n,r){1&n&&t._UZ(0,"div",2)}function Ot(n,r){1&n&&t._UZ(0,"pre",2)}function Zt(n,r){if(1&n&&(t.ynx(0),t.YNc(1,Tt,1,0,"div",1),t.YNc(2,Ot,1,0,"pre",1),t.BQk()),2&n){const e=t.oxw();t.xp6(1),t.Q6J("ngIf",!e.preserve),t.xp6(1),t.Q6J("ngIf",e.preserve)}}const Pt=[[["","quill-editor-toolbar",""]]],yt=["[quill-editor-toolbar]"];function Et(n,r){1&n&&t._UZ(0,"div",1)}function St(n,r){1&n&&t._UZ(0,"pre",1)}const q=(n,r)=>n||r||"html";let F=(()=>{class n{constructor(e,i){var o=this;this.config=i,this.quill$=(0,mt.P)(K(function*(){if(!o.Quill){const a=o.document.addEventListener;o.document.addEventListener=o.document.__zone_symbol__addEventListener||o.document.addEventListener;const s=yield l.e(971).then(l.t.bind(l,9971,19));o.document.addEventListener=a,o.Quill=s.default?s.default:s}return o.config.customOptions?.forEach(a=>{const s=o.Quill.import(a.import);s.whitelist=a.whitelist,o.Quill.register(s,!0,o.config.suppressGlobalRegisterWarning)}),yield o.registerCustomModules(o.Quill,o.config.customModules,o.config.suppressGlobalRegisterWarning)})).pipe(function bt(n,r,e){let i,o=!1;return n&&"object"==typeof n?({bufferSize:i=1/0,windowTime:r=1/0,refCount:o=!1,scheduler:e}=n):i=n??1/0,(0,xt.B)({connector:()=>new Ct(i,r,e),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}({bufferSize:1,refCount:!0})),this.document=e.get(h.K0),this.config||(this.config={modules:z})}getQuill(){return this.quill$}registerCustomModules(e,i,o){return K(function*(){if(Array.isArray(i))for(let{implementation:a,path:s}of i)(0,gt.b)(a)&&(a=yield ft(a)),e.register(s,a,o);return e})()}}return n.\u0275fac=function(e){return new(e||n)(t.LFG(t.zs3),t.LFG($,8))},n.\u0275prov=t.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),qt=(()=>{class n{constructor(e,i,o,a,s,c,d,_){this.elementRef=i,this.cd=o,this.domSanitizer=a,this.platformId=s,this.renderer=c,this.zone=d,this.service=_,this.required=!1,this.customToolbarPosition="top",this.styles=null,this.strict=!0,this.customOptions=[],this.customModules=[],this.preserveWhitespace=!1,this.trimOnValidation=!1,this.compareValues=!1,this.filterNull=!1,this.defaultEmptyValue=null,this.onEditorCreated=new t.vpe,this.onEditorChanged=new t.vpe,this.onContentChanged=new t.vpe,this.onSelectionChanged=new t.vpe,this.onFocus=new t.vpe,this.onBlur=new t.vpe,this.disabled=!1,this.preserve=!1,this.toolbarPosition="top",this.subscription=null,this.quillSubscription=null,this.valueGetter=(p,m)=>{let g=m.querySelector(".ql-editor").innerHTML;("<p><br></p>"===g||"<div><br></div>"===g)&&(g=this.defaultEmptyValue);let f=g;const O=q(this.format,this.service.config.format);if("text"===O)f=p.getText();else if("object"===O)f=p.getContents();else if("json"===O)try{f=JSON.stringify(p.getContents())}catch{f=p.getText()}return f},this.valueSetter=(p,m)=>{const g=q(this.format,this.service.config.format);if("html"===g)return([!0,!1].includes(this.sanitize)?this.sanitize:this.service.config.sanitize||!1)&&(m=this.domSanitizer.sanitize(t.q3G.HTML,m)),p.clipboard.convert(m);if("json"===g)try{return JSON.parse(m)}catch{return[{insert:m}]}return m},this.selectionChangeHandler=(p,m,g)=>{const f=!p&&!!this.onModelTouched;!this.onBlur.observed&&!this.onFocus.observed&&!this.onSelectionChanged.observed&&!f||this.zone.run(()=>{null===p?this.onBlur.emit({editor:this.quillEditor,source:g}):null===m&&this.onFocus.emit({editor:this.quillEditor,source:g}),this.onSelectionChanged.emit({editor:this.quillEditor,oldRange:m,range:p,source:g}),f&&this.onModelTouched(),this.cd.markForCheck()})},this.textChangeHandler=(p,m,g)=>{const f=this.quillEditor.getText(),O=this.quillEditor.getContents();let Z=this.editorElem.querySelector(".ql-editor").innerHTML;("<p><br></p>"===Z||"<div><br></div>"===Z)&&(Z=this.defaultEmptyValue);const P=this.trackChanges||this.service.config.trackChanges,dt=("user"===g||P&&"all"===P)&&!!this.onModelChange;!this.onContentChanged.observed&&!dt||this.zone.run(()=>{dt&&this.onModelChange(this.valueGetter(this.quillEditor,this.editorElem)),this.onContentChanged.emit({content:O,delta:p,editor:this.quillEditor,html:Z,oldDelta:m,source:g,text:f}),this.cd.markForCheck()})},this.editorChangeHandler=(p,m,g,f)=>{if(this.onEditorChanged.observed)if("text-change"===p){const O=this.quillEditor.getText(),Z=this.quillEditor.getContents();let P=this.editorElem.querySelector(".ql-editor").innerHTML;("<p><br></p>"===P||"<div><br></div>"===P)&&(P=this.defaultEmptyValue),this.zone.run(()=>{this.onEditorChanged.emit({content:Z,delta:m,editor:this.quillEditor,event:p,html:P,oldDelta:g,source:f,text:O}),this.cd.markForCheck()})}else this.zone.run(()=>{this.onEditorChanged.emit({editor:this.quillEditor,event:p,oldRange:g,range:m,source:f}),this.cd.markForCheck()})},this.document=e.get(h.K0)}static normalizeClassNames(e){return e.trim().split(" ").reduce((o,a)=>{const s=a.trim();return s&&o.push(s),o},[])}ngOnInit(){this.preserve=this.preserveWhitespace,this.toolbarPosition=this.customToolbarPosition}ngAfterViewInit(){(0,h.PM)(this.platformId)||(this.quillSubscription=this.service.getQuill().pipe((0,W.z)(e=>{const i=[this.service.registerCustomModules(e,this.customModules)],o=this.beforeRender??this.service.config.beforeRender;return o&&i.push(o()),Promise.all(i).then(()=>e)})).subscribe(e=>{this.editorElem=this.elementRef.nativeElement.querySelector("[quill-editor-element]");const i=this.elementRef.nativeElement.querySelector("[quill-editor-toolbar]"),o=Object.assign({},this.modules||this.service.config.modules);i?o.toolbar=i:void 0===o.toolbar&&(o.toolbar=z.toolbar);let a=void 0!==this.placeholder?this.placeholder:this.service.config.placeholder;void 0===a&&(a="Insert text here ..."),this.styles&&Object.keys(this.styles).forEach(g=>{this.renderer.setStyle(this.editorElem,g,this.styles[g])}),this.classes&&this.addClasses(this.classes),this.customOptions.forEach(g=>{const f=e.import(g.import);f.whitelist=g.whitelist,e.register(f,!0)});let s=this.bounds&&"self"===this.bounds?this.editorElem:this.bounds;s||(s=this.service.config.bounds?this.service.config.bounds:this.document.body);let c=this.debug;!c&&!1!==c&&this.service.config.debug&&(c=this.service.config.debug);let d=this.readOnly;!d&&!1!==this.readOnly&&(d=void 0!==this.service.config.readOnly&&this.service.config.readOnly);let _=this.defaultEmptyValue;this.service.config.hasOwnProperty("defaultEmptyValue")&&(_=this.service.config.defaultEmptyValue);let p=this.scrollingContainer;!p&&null!==this.scrollingContainer&&(p=null===this.service.config.scrollingContainer||this.service.config.scrollingContainer?this.service.config.scrollingContainer:null);let m=this.formats;if(!m&&void 0===m&&(m=this.service.config.formats?[...this.service.config.formats]:null===this.service.config.formats?null:void 0),this.zone.runOutsideAngular(()=>{if(this.quillEditor=new e(this.editorElem,{bounds:s,debug:c,formats:m,modules:o,placeholder:a,readOnly:d,defaultEmptyValue:_,scrollingContainer:p,strict:this.strict,theme:this.theme||(this.service.config.theme?this.service.config.theme:"snow")}),this.linkPlaceholder){const f=this.quillEditor?.theme?.tooltip?.root?.querySelector("input[data-link]");f?.dataset&&(f.dataset.link=this.linkPlaceholder)}}),this.content){if("text"===q(this.format,this.service.config.format))this.quillEditor.setText(this.content,"silent");else{const f=this.valueSetter(this.quillEditor,this.content);this.quillEditor.setContents(f,"silent")}this.quillEditor.getModule("history").clear()}this.setDisabledState(),this.addQuillEventListeners(),(this.onEditorCreated.observed||this.onValidatorChanged)&&requestAnimationFrame(()=>{this.onValidatorChanged&&this.onValidatorChanged(),this.onEditorCreated.emit(this.quillEditor),this.onEditorCreated.complete()})}))}ngOnDestroy(){this.dispose(),this.quillSubscription?.unsubscribe(),this.quillSubscription=null}ngOnChanges(e){if(this.quillEditor){if(e.readOnly&&this.quillEditor.enable(!e.readOnly.currentValue),e.placeholder&&(this.quillEditor.root.dataset.placeholder=e.placeholder.currentValue),e.defaultEmptyValue&&(this.quillEditor.root.dataset.defaultEmptyValue=e.defaultEmptyValue.currentValue),e.styles){const i=e.styles.currentValue,o=e.styles.previousValue;o&&Object.keys(o).forEach(a=>{this.renderer.removeStyle(this.editorElem,a)}),i&&Object.keys(i).forEach(a=>{this.renderer.setStyle(this.editorElem,a,this.styles[a])})}if(e.classes){const i=e.classes.currentValue,o=e.classes.previousValue;o&&this.removeClasses(o),i&&this.addClasses(i)}e.debounceTime&&this.addQuillEventListeners()}}addClasses(e){n.normalizeClassNames(e).forEach(i=>{this.renderer.addClass(this.editorElem,i)})}removeClasses(e){n.normalizeClassNames(e).forEach(i=>{this.renderer.removeClass(this.editorElem,i)})}writeValue(e){if(this.filterNull&&null===e||(this.content=e,!this.quillEditor))return;const i=q(this.format,this.service.config.format),o=this.valueSetter(this.quillEditor,e);if(this.compareValues){const a=this.quillEditor.getContents();if(JSON.stringify(a)===JSON.stringify(o))return}e?"text"===i?this.quillEditor.setText(e):this.quillEditor.setContents(o):this.quillEditor.setText("")}setDisabledState(e=this.disabled){this.disabled=e,this.quillEditor&&(e?(this.quillEditor.disable(),this.renderer.setAttribute(this.elementRef.nativeElement,"disabled","disabled")):(this.readOnly||this.quillEditor.enable(),this.renderer.removeAttribute(this.elementRef.nativeElement,"disabled")))}registerOnChange(e){this.onModelChange=e}registerOnTouched(e){this.onModelTouched=e}registerOnValidatorChange(e){this.onValidatorChanged=e}validate(){if(!this.quillEditor)return null;const e={};let i=!0;const o=this.quillEditor.getText(),a=this.trimOnValidation?o.trim().length:1===o.length&&0===o.trim().length?0:o.length-1,s=this.quillEditor.getContents().ops,c=s&&1===s.length&&["\n",""].includes(s[0].insert);return this.minLength&&a&&a<this.minLength&&(e.minLengthError={given:a,minLength:this.minLength},i=!1),this.maxLength&&a>this.maxLength&&(e.maxLengthError={given:a,maxLength:this.maxLength},i=!1),this.required&&!a&&c&&(e.requiredError={empty:!0},i=!1),i?null:e}addQuillEventListeners(){this.dispose(),this.zone.runOutsideAngular(()=>{this.subscription=new _t.w0,this.subscription.add((0,H.R)(this.quillEditor,"selection-change").subscribe(([o,a,s])=>{this.selectionChangeHandler(o,a,s)}));let e=(0,H.R)(this.quillEditor,"text-change"),i=(0,H.R)(this.quillEditor,"editor-change");"number"==typeof this.debounceTime&&(e=e.pipe((0,X.b)(this.debounceTime)),i=i.pipe((0,X.b)(this.debounceTime))),this.subscription.add(e.subscribe(([o,a,s])=>{this.textChangeHandler(o,a,s)})),this.subscription.add(i.subscribe(([o,a,s,c])=>{this.editorChangeHandler(o,a,s,c)}))})}dispose(){null!==this.subscription&&(this.subscription.unsubscribe(),this.subscription=null)}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(t.zs3),t.Y36(t.SBq),t.Y36(t.sBO),t.Y36(I.H7),t.Y36(t.Lbi),t.Y36(t.Qsj),t.Y36(t.R0b),t.Y36(F))},n.\u0275dir=t.lG2({type:n,inputs:{format:"format",theme:"theme",modules:"modules",debug:"debug",readOnly:"readOnly",placeholder:"placeholder",maxLength:"maxLength",minLength:"minLength",required:"required",formats:"formats",customToolbarPosition:"customToolbarPosition",sanitize:"sanitize",beforeRender:"beforeRender",styles:"styles",strict:"strict",scrollingContainer:"scrollingContainer",bounds:"bounds",customOptions:"customOptions",customModules:"customModules",trackChanges:"trackChanges",preserveWhitespace:"preserveWhitespace",classes:"classes",trimOnValidation:"trimOnValidation",linkPlaceholder:"linkPlaceholder",compareValues:"compareValues",filterNull:"filterNull",debounceTime:"debounceTime",defaultEmptyValue:"defaultEmptyValue",valueGetter:"valueGetter",valueSetter:"valueSetter"},outputs:{onEditorCreated:"onEditorCreated",onEditorChanged:"onEditorChanged",onContentChanged:"onContentChanged",onSelectionChanged:"onSelectionChanged",onFocus:"onFocus",onBlur:"onBlur"},features:[t.TTD]}),n})(),tt=(()=>{class n extends qt{constructor(e,i,o,a,s,c,d,_){super(e,i,o,a,s,c,d,_)}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(t.zs3),t.Y36(t.SBq),t.Y36(t.sBO),t.Y36(I.H7),t.Y36(t.Lbi),t.Y36(t.Qsj),t.Y36(t.R0b),t.Y36(F))},n.\u0275cmp=t.Xpm({type:n,selectors:[["quill-editor"]],standalone:!0,features:[t._Bn([{multi:!0,provide:C.JU,useExisting:(0,t.Gpc)(()=>n)},{multi:!0,provide:C.Cf,useExisting:(0,t.Gpc)(()=>n)}]),t.qOj,t.jDz],ngContentSelectors:yt,decls:3,vars:2,consts:[[4,"ngIf"],["quill-editor-element","",4,"ngIf"],["quill-editor-element",""]],template:function(e,i){1&e&&(t.F$t(Pt),t.YNc(0,wt,3,2,"ng-container",0),t.Hsn(1),t.YNc(2,Zt,3,2,"ng-container",0)),2&e&&(t.Q6J("ngIf","top"!==i.toolbarPosition),t.xp6(2),t.Q6J("ngIf","top"===i.toolbarPosition))},dependencies:[h.ez,h.O5],styles:["[_nghost-%COMP%]{display:inline-block}"]}),n})(),Dt=(()=>{class n{constructor(e,i){this.sanitizer=e,this.service=i,this.content="",this.innerHTML="",this.themeClass="ql-snow"}ngOnChanges(e){if(e.theme?this.themeClass=`ql-${e.theme.currentValue||(this.service.config.theme?this.service.config.theme:"snow")} ngx-quill-view-html`:this.theme||(this.themeClass=`ql-${this.service.config.theme?this.service.config.theme:"snow"} ngx-quill-view-html`),e.content){const i=e.content.currentValue,o=[!0,!1].includes(this.sanitize)?this.sanitize:this.service.config.sanitize||!1;this.innerHTML=o?i:this.sanitizer.bypassSecurityTrustHtml(i)}}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(I.H7),t.Y36(F))},n.\u0275cmp=t.Xpm({type:n,selectors:[["quill-view-html"]],inputs:{content:"content",theme:"theme",sanitize:"sanitize"},standalone:!0,features:[t.TTD,t.jDz],decls:2,vars:2,consts:[[1,"ql-container",3,"ngClass"],[1,"ql-editor",3,"innerHTML"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t._UZ(1,"div",1),t.qZA()),2&e&&(t.Q6J("ngClass",i.themeClass),t.xp6(1),t.Q6J("innerHTML",i.innerHTML,t.oJD))},dependencies:[h.ez,h.mk],styles:[".ql-container.ngx-quill-view-html{border:0}\n"],encapsulation:2}),n})(),Ut=(()=>{class n{constructor(e,i,o,a,s,c){this.elementRef=e,this.renderer=i,this.zone=o,this.service=a,this.domSanitizer=s,this.platformId=c,this.strict=!0,this.customModules=[],this.customOptions=[],this.preserveWhitespace=!1,this.onEditorCreated=new t.vpe,this.preserve=!1,this.quillSubscription=null,this.valueSetter=(d,_)=>{const p=q(this.format,this.service.config.format);let m=_;if("text"===p)d.setText(m);else{if("html"===p)([!0,!1].includes(this.sanitize)?this.sanitize:this.service.config.sanitize||!1)&&(_=this.domSanitizer.sanitize(t.q3G.HTML,_)),m=d.clipboard.convert(_);else if("json"===p)try{m=JSON.parse(_)}catch{m=[{insert:_}]}d.setContents(m)}}}ngOnInit(){this.preserve=this.preserveWhitespace}ngOnChanges(e){this.quillEditor&&e.content&&this.valueSetter(this.quillEditor,e.content.currentValue)}ngAfterViewInit(){(0,h.PM)(this.platformId)||(this.quillSubscription=this.service.getQuill().pipe((0,W.z)(e=>{const i=[this.service.registerCustomModules(e,this.customModules)],o=this.beforeRender??this.service.config.beforeRender;return o&&i.push(o()),Promise.all(i).then(()=>e)})).subscribe(e=>{const i=Object.assign({},this.modules||this.service.config.modules);i.toolbar=!1,this.customOptions.forEach(c=>{const d=e.import(c.import);d.whitelist=c.whitelist,e.register(d,!0)});let o=this.debug;!o&&!1!==o&&this.service.config.debug&&(o=this.service.config.debug);let a=this.formats;!a&&void 0===a&&(a=this.service.config.formats?Object.assign({},this.service.config.formats):null===this.service.config.formats?null:void 0);const s=this.theme||(this.service.config.theme?this.service.config.theme:"snow");this.editorElem=this.elementRef.nativeElement.querySelector("[quill-view-element]"),this.zone.runOutsideAngular(()=>{this.quillEditor=new e(this.editorElem,{debug:o,formats:a,modules:i,readOnly:!0,strict:this.strict,theme:s})}),this.renderer.addClass(this.editorElem,"ngx-quill-view"),this.content&&this.valueSetter(this.quillEditor,this.content),this.onEditorCreated.observers.length&&requestAnimationFrame(()=>{this.onEditorCreated.emit(this.quillEditor),this.onEditorCreated.complete()})}))}ngOnDestroy(){this.quillSubscription?.unsubscribe(),this.quillSubscription=null}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(t.SBq),t.Y36(t.Qsj),t.Y36(t.R0b),t.Y36(F),t.Y36(I.H7),t.Y36(t.Lbi))},n.\u0275cmp=t.Xpm({type:n,selectors:[["quill-view"]],inputs:{format:"format",theme:"theme",modules:"modules",debug:"debug",formats:"formats",sanitize:"sanitize",beforeRender:"beforeRender",strict:"strict",content:"content",customModules:"customModules",customOptions:"customOptions",preserveWhitespace:"preserveWhitespace"},outputs:{onEditorCreated:"onEditorCreated"},standalone:!0,features:[t.TTD,t.jDz],decls:2,vars:2,consts:[["quill-view-element","",4,"ngIf"],["quill-view-element",""]],template:function(e,i){1&e&&(t.YNc(0,Et,1,0,"div",0),t.YNc(1,St,1,0,"pre",0)),2&e&&(t.Q6J("ngIf",!i.preserve),t.xp6(1),t.Q6J("ngIf",i.preserve))},dependencies:[h.ez,h.O5],styles:[".ql-container.ngx-quill-view{border:0}\n"],encapsulation:2}),n})(),It=(()=>{class n{static forRoot(e){return{ngModule:n,providers:[{provide:$,useValue:e}]}}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({imports:[tt,Ut,Dt]}),n})();var v=l(3546),A=l(4859),E=l(7392),B=l(7331),D=l(1572),et=l(3162),T=l(9549),it=l(4144),J=l(4385),nt=l(6709),b=l(671),R=l(8739),ot=l(2687),rt=l(1281),at=l(9521),M=l(3238),kt=l(6451),u=l(7340);const Ft=["mat-sort-header",""];function Bt(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"div",3),t.NdJ("@arrowPosition.start",function(){t.CHM(e);const o=t.oxw();return t.KtG(o._disableViewStateAnimation=!0)})("@arrowPosition.done",function(){t.CHM(e);const o=t.oxw();return t.KtG(o._disableViewStateAnimation=!1)}),t._UZ(1,"div",4),t.TgZ(2,"div",5),t._UZ(3,"div",6)(4,"div",7)(5,"div",8),t.qZA()()}if(2&n){const e=t.oxw();t.Q6J("@arrowOpacity",e._getArrowViewState())("@arrowPosition",e._getArrowViewState())("@allowChildren",e._getArrowDirectionState()),t.xp6(2),t.Q6J("@indicator",e._getArrowDirectionState()),t.xp6(1),t.Q6J("@leftPointer",e._getArrowDirectionState()),t.xp6(1),t.Q6J("@rightPointer",e._getArrowDirectionState())}}const Nt=["*"],st=new t.OlP("MAT_SORT_DEFAULT_OPTIONS"),Yt=(0,M.dB)((0,M.Id)(class{}));let L=(()=>{class n extends Yt{get direction(){return this._direction}set direction(e){this._direction=e}get disableClear(){return this._disableClear}set disableClear(e){this._disableClear=(0,rt.Ig)(e)}constructor(e){super(),this._defaultOptions=e,this.sortables=new Map,this._stateChanges=new k.x,this.start="asc",this._direction="",this.sortChange=new t.vpe}register(e){this.sortables.set(e.id,e)}deregister(e){this.sortables.delete(e.id)}sort(e){this.active!=e.id?(this.active=e.id,this.direction=e.start?e.start:this.start):this.direction=this.getNextSortDirection(e),this.sortChange.emit({active:this.active,direction:this.direction})}getNextSortDirection(e){if(!e)return"";let o=function Qt(n,r){let e=["asc","desc"];return"desc"==n&&e.reverse(),r||e.push(""),e}(e.start||this.start,e?.disableClear??this.disableClear??!!this._defaultOptions?.disableClear),a=o.indexOf(this.direction)+1;return a>=o.length&&(a=0),o[a]}ngOnInit(){this._markInitialized()}ngOnChanges(){this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete()}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(st,8))},n.\u0275dir=t.lG2({type:n,selectors:[["","matSort",""]],hostAttrs:[1,"mat-sort"],inputs:{disabled:["matSortDisabled","disabled"],active:["matSortActive","active"],start:["matSortStart","start"],direction:["matSortDirection","direction"],disableClear:["matSortDisableClear","disableClear"]},outputs:{sortChange:"matSortChange"},exportAs:["matSort"],features:[t.qOj,t.TTD]}),n})();const w=M.mZ.ENTERING+" "+M.yN.STANDARD_CURVE,S={indicator:(0,u.X$)("indicator",[(0,u.SB)("active-asc, asc",(0,u.oB)({transform:"translateY(0px)"})),(0,u.SB)("active-desc, desc",(0,u.oB)({transform:"translateY(10px)"})),(0,u.eR)("active-asc <=> active-desc",(0,u.jt)(w))]),leftPointer:(0,u.X$)("leftPointer",[(0,u.SB)("active-asc, asc",(0,u.oB)({transform:"rotate(-45deg)"})),(0,u.SB)("active-desc, desc",(0,u.oB)({transform:"rotate(45deg)"})),(0,u.eR)("active-asc <=> active-desc",(0,u.jt)(w))]),rightPointer:(0,u.X$)("rightPointer",[(0,u.SB)("active-asc, asc",(0,u.oB)({transform:"rotate(45deg)"})),(0,u.SB)("active-desc, desc",(0,u.oB)({transform:"rotate(-45deg)"})),(0,u.eR)("active-asc <=> active-desc",(0,u.jt)(w))]),arrowOpacity:(0,u.X$)("arrowOpacity",[(0,u.SB)("desc-to-active, asc-to-active, active",(0,u.oB)({opacity:1})),(0,u.SB)("desc-to-hint, asc-to-hint, hint",(0,u.oB)({opacity:.54})),(0,u.SB)("hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void",(0,u.oB)({opacity:0})),(0,u.eR)("* => asc, * => desc, * => active, * => hint, * => void",(0,u.jt)("0ms")),(0,u.eR)("* <=> *",(0,u.jt)(w))]),arrowPosition:(0,u.X$)("arrowPosition",[(0,u.eR)("* => desc-to-hint, * => desc-to-active",(0,u.jt)(w,(0,u.F4)([(0,u.oB)({transform:"translateY(-25%)"}),(0,u.oB)({transform:"translateY(0)"})]))),(0,u.eR)("* => hint-to-desc, * => active-to-desc",(0,u.jt)(w,(0,u.F4)([(0,u.oB)({transform:"translateY(0)"}),(0,u.oB)({transform:"translateY(25%)"})]))),(0,u.eR)("* => asc-to-hint, * => asc-to-active",(0,u.jt)(w,(0,u.F4)([(0,u.oB)({transform:"translateY(25%)"}),(0,u.oB)({transform:"translateY(0)"})]))),(0,u.eR)("* => hint-to-asc, * => active-to-asc",(0,u.jt)(w,(0,u.F4)([(0,u.oB)({transform:"translateY(0)"}),(0,u.oB)({transform:"translateY(-25%)"})]))),(0,u.SB)("desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active",(0,u.oB)({transform:"translateY(0)"})),(0,u.SB)("hint-to-desc, active-to-desc, desc",(0,u.oB)({transform:"translateY(-25%)"})),(0,u.SB)("hint-to-asc, active-to-asc, asc",(0,u.oB)({transform:"translateY(25%)"}))]),allowChildren:(0,u.X$)("allowChildren",[(0,u.eR)("* <=> *",[(0,u.IO)("@*",(0,u.pV)(),{optional:!0})])])};let N=(()=>{class n{constructor(){this.changes=new k.x}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275prov=t.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();const Ht={provide:N,deps:[[new t.FiY,new t.tp0,N]],useFactory:function zt(n){return n||new N}},Jt=(0,M.Id)(class{});let Rt=(()=>{class n extends Jt{get sortActionDescription(){return this._sortActionDescription}set sortActionDescription(e){this._updateSortActionDescription(e)}get disableClear(){return this._disableClear}set disableClear(e){this._disableClear=(0,rt.Ig)(e)}constructor(e,i,o,a,s,c,d,_){super(),this._intl=e,this._changeDetectorRef=i,this._sort=o,this._columnDef=a,this._focusMonitor=s,this._elementRef=c,this._ariaDescriber=d,this._showIndicatorHint=!1,this._viewState={},this._arrowDirection="",this._disableViewStateAnimation=!1,this.arrowPosition="after",this._sortActionDescription="Sort",_?.arrowPosition&&(this.arrowPosition=_?.arrowPosition),this._handleStateChanges()}ngOnInit(){!this.id&&this._columnDef&&(this.id=this._columnDef.name),this._updateArrowDirection(),this._setAnimationTransitionState({toState:this._isSorted()?"active":this._arrowDirection}),this._sort.register(this),this._sortButton=this._elementRef.nativeElement.querySelector(".mat-sort-header-container"),this._updateSortActionDescription(this._sortActionDescription)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(e=>{const i=!!e;i!==this._showIndicatorHint&&(this._setIndicatorHintVisible(i),this._changeDetectorRef.markForCheck())})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._sort.deregister(this),this._rerenderSubscription.unsubscribe()}_setIndicatorHintVisible(e){this._isDisabled()&&e||(this._showIndicatorHint=e,this._isSorted()||(this._updateArrowDirection(),this._setAnimationTransitionState(this._showIndicatorHint?{fromState:this._arrowDirection,toState:"hint"}:{fromState:"hint",toState:this._arrowDirection})))}_setAnimationTransitionState(e){this._viewState=e||{},this._disableViewStateAnimation&&(this._viewState={toState:e.toState})}_toggleOnInteraction(){this._sort.sort(this),("hint"===this._viewState.toState||"active"===this._viewState.toState)&&(this._disableViewStateAnimation=!0)}_handleClick(){this._isDisabled()||this._sort.sort(this)}_handleKeydown(e){!this._isDisabled()&&(e.keyCode===at.L_||e.keyCode===at.K5)&&(e.preventDefault(),this._toggleOnInteraction())}_isSorted(){return this._sort.active==this.id&&("asc"===this._sort.direction||"desc"===this._sort.direction)}_getArrowDirectionState(){return`${this._isSorted()?"active-":""}${this._arrowDirection}`}_getArrowViewState(){const e=this._viewState.fromState;return(e?`${e}-to-`:"")+this._viewState.toState}_updateArrowDirection(){this._arrowDirection=this._isSorted()?this._sort.direction:this.start||this._sort.start}_isDisabled(){return this._sort.disabled||this.disabled}_getAriaSortAttribute(){return this._isSorted()?"asc"==this._sort.direction?"ascending":"descending":"none"}_renderArrow(){return!this._isDisabled()||this._isSorted()}_updateSortActionDescription(e){this._sortButton&&(this._ariaDescriber?.removeDescription(this._sortButton,this._sortActionDescription),this._ariaDescriber?.describe(this._sortButton,e)),this._sortActionDescription=e}_handleStateChanges(){this._rerenderSubscription=(0,kt.T)(this._sort.sortChange,this._sort._stateChanges,this._intl.changes).subscribe(()=>{this._isSorted()&&(this._updateArrowDirection(),("hint"===this._viewState.toState||"active"===this._viewState.toState)&&(this._disableViewStateAnimation=!0),this._setAnimationTransitionState({fromState:this._arrowDirection,toState:"active"}),this._showIndicatorHint=!1),!this._isSorted()&&this._viewState&&"active"===this._viewState.toState&&(this._disableViewStateAnimation=!1,this._setAnimationTransitionState({fromState:"active",toState:this._arrowDirection})),this._changeDetectorRef.markForCheck()})}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(N),t.Y36(t.sBO),t.Y36(L,8),t.Y36("MAT_SORT_HEADER_COLUMN_DEF",8),t.Y36(ot.tE),t.Y36(t.SBq),t.Y36(ot.$s,8),t.Y36(st,8))},n.\u0275cmp=t.Xpm({type:n,selectors:[["","mat-sort-header",""]],hostAttrs:[1,"mat-sort-header"],hostVars:3,hostBindings:function(e,i){1&e&&t.NdJ("click",function(){return i._handleClick()})("keydown",function(a){return i._handleKeydown(a)})("mouseenter",function(){return i._setIndicatorHintVisible(!0)})("mouseleave",function(){return i._setIndicatorHintVisible(!1)}),2&e&&(t.uIk("aria-sort",i._getAriaSortAttribute()),t.ekj("mat-sort-header-disabled",i._isDisabled()))},inputs:{disabled:"disabled",id:["mat-sort-header","id"],arrowPosition:"arrowPosition",start:"start",sortActionDescription:"sortActionDescription",disableClear:"disableClear"},exportAs:["matSortHeader"],features:[t.qOj],attrs:Ft,ngContentSelectors:Nt,decls:4,vars:7,consts:[[1,"mat-sort-header-container","mat-focus-indicator"],[1,"mat-sort-header-content"],["class","mat-sort-header-arrow",4,"ngIf"],[1,"mat-sort-header-arrow"],[1,"mat-sort-header-stem"],[1,"mat-sort-header-indicator"],[1,"mat-sort-header-pointer-left"],[1,"mat-sort-header-pointer-right"],[1,"mat-sort-header-pointer-middle"]],template:function(e,i){1&e&&(t.F$t(),t.TgZ(0,"div",0)(1,"div",1),t.Hsn(2),t.qZA(),t.YNc(3,Bt,6,6,"div",2),t.qZA()),2&e&&(t.ekj("mat-sort-header-sorted",i._isSorted())("mat-sort-header-position-before","before"===i.arrowPosition),t.uIk("tabindex",i._isDisabled()?null:0)("role",i._isDisabled()?null:"button"),t.xp6(3),t.Q6J("ngIf",i._renderArrow()))},dependencies:[h.O5],styles:[".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}"],encapsulation:2,data:{animation:[S.indicator,S.leftPointer,S.rightPointer,S.arrowOpacity,S.arrowPosition,S.allowChildren]},changeDetection:0}),n})(),Lt=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({providers:[Ht],imports:[h.ez,M.BQ]}),n})();var x=l(5412),Y=l(7009),Vt=l(3848),U=l(1561),jt=l(266),ut=l(4850),Gt=l(9841),Kt=l(2722),Q=l(7251),$t=l(6518),Wt=l(8890);function Xt(n,r){if(1&n&&(t.TgZ(0,"div",3),t._UZ(1,"mat-spinner"),t.TgZ(2,"p"),t._uU(3),t.qZA()()),2&n){const e=t.oxw();t.xp6(3),t.hij("",e.t.common.loading,"...")}}function te(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"div",4)(1,"mat-card",5)(2,"mat-card-content")(3,"div",6)(4,"mat-icon",7),t._uU(5,"error"),t.qZA(),t.TgZ(6,"h2"),t._uU(7),t.qZA(),t.TgZ(8,"p"),t._uU(9),t.qZA(),t.TgZ(10,"button",8),t.NdJ("click",function(){t.CHM(e);const o=t.oxw();return t.KtG(o.goBack())}),t.TgZ(11,"mat-icon"),t._uU(12,"arrow_back"),t.qZA(),t._uU(13),t.qZA()()()()()}if(2&n){const e=t.oxw();t.xp6(7),t.Oqu(e.t.errors.general),t.xp6(2),t.Oqu(e.error),t.xp6(4),t.hij(" ",e.t.common.back," ")}}function ee(n,r){if(1&n&&(t.TgZ(0,"div",30),t._UZ(1,"img",31),t.qZA()),2&n){const e=t.oxw(2);t.xp6(1),t.Q6J("src",e.article.featuredImageUrl||e.article.imageUrl,t.LSH)("alt",e.article.title)}}function ie(n,r){if(1&n&&(t.TgZ(0,"mat-chip",34),t._uU(1),t.qZA()),2&n){const e=r.$implicit;t.xp6(1),t.Oqu(e)}}function ne(n,r){if(1&n&&(t.TgZ(0,"div",32),t.YNc(1,ie,2,1,"mat-chip",33),t.qZA()),2&n){const e=t.oxw(2);t.xp6(1),t.Q6J("ngForOf",e.article.tags)}}function oe(n,r){1&n&&(t.TgZ(0,"div",35),t._UZ(1,"div",36),t.qZA())}function re(n,r){if(1&n&&(t.TgZ(0,"div",46)(1,"div",47)(2,"span"),t._uU(3),t.qZA(),t.TgZ(4,"mat-icon"),t._uU(5,"trending_up"),t.qZA()(),t._UZ(6,"mat-progress-bar",48),t.qZA()),2&n){const e=t.oxw(3);t.xp6(3),t.hij("\u041f\u0440\u043e\u0447\u0435\u0442\u043e\u0445\u0442\u0435 ",e.getContentCompletionPercentage(),"% \u043e\u0442 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430"),t.xp6(3),t.Q6J("value",e.getContentCompletionPercentage())}}function ae(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"div",37)(1,"mat-card",38)(2,"mat-card-header")(3,"mat-card-title")(4,"mat-icon"),t._uU(5,"lock"),t.qZA(),t._uU(6),t.qZA(),t.TgZ(7,"mat-card-subtitle"),t._uU(8),t.qZA()(),t.TgZ(9,"mat-card-content"),t.YNc(10,re,7,2,"div",39),t.TgZ(11,"p"),t._uU(12),t.qZA(),t.TgZ(13,"div",40)(14,"div",41)(15,"mat-icon"),t._uU(16,"auto_stories"),t.qZA(),t.TgZ(17,"span"),t._uU(18),t.qZA()(),t.TgZ(19,"div",41)(20,"mat-icon"),t._uU(21,"psychology"),t.qZA(),t.TgZ(22,"span"),t._uU(23),t.qZA()(),t.TgZ(24,"div",41)(25,"mat-icon"),t._uU(26,"bookmark_added"),t.qZA(),t.TgZ(27,"span"),t._uU(28),t.qZA()(),t.TgZ(29,"div",41)(30,"mat-icon"),t._uU(31,"notifications"),t.qZA(),t.TgZ(32,"span"),t._uU(33),t.qZA()()(),t.TgZ(34,"div",42)(35,"mat-icon"),t._uU(36,"group"),t.qZA(),t.TgZ(37,"span"),t._uU(38),t.qZA()()(),t.TgZ(39,"mat-card-actions",43)(40,"button",8),t.NdJ("click",function(){t.CHM(e);const o=t.oxw(2);return t.KtG(o.onLoginClick())}),t.TgZ(41,"mat-icon"),t._uU(42,"login"),t.qZA(),t._uU(43),t.qZA(),t.TgZ(44,"button",44),t.NdJ("click",function(){t.CHM(e);const o=t.oxw(2);return t.KtG(o.onRegisterClick())}),t.TgZ(45,"mat-icon"),t._uU(46,"person_add"),t.qZA(),t._uU(47),t.qZA(),t.TgZ(48,"button",45),t.NdJ("click",function(){t.CHM(e);const o=t.oxw(2);return t.KtG(o.onCloseAuthPrompt())}),t.TgZ(49,"mat-icon"),t._uU(50,"close"),t.qZA(),t._uU(51),t.qZA()()()()}if(2&n){const e=t.oxw(2);t.xp6(6),t.hij(" ",e.t.articles.preview.signInToRead," "),t.xp6(2),t.hij(" ",e.t.articles.preview.joinCommunity," "),t.xp6(2),t.Q6J("ngIf",!e.isAuthenticated),t.xp6(2),t.Oqu(e.t.articles.preview.valuableContent),t.xp6(6),t.Oqu(e.t.articles.preview.unlimitedAccess),t.xp6(5),t.Oqu(e.t.articles.preview.connectWithAstrologers),t.xp6(5),t.Oqu(e.t.articles.preview.saveArticles),t.xp6(5),t.Oqu(e.t.articles.preview.notifications),t.xp6(5),t.Oqu(e.t.articles.preview.socialProof),t.xp6(5),t.hij(" ",e.t.auth.signIn," "),t.xp6(4),t.hij(" ",e.t.auth.createAccount," "),t.xp6(4),t.hij(" ",e.t.common.close," ")}}function se(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"div",9)(1,"div",10)(2,"button",11),t.NdJ("click",function(){t.CHM(e);const o=t.oxw();return t.KtG(o.goBack())}),t.TgZ(3,"mat-icon"),t._uU(4,"arrow_back"),t.qZA()(),t.TgZ(5,"div",12)(6,"button",13),t.NdJ("click",function(){t.CHM(e);const o=t.oxw();return t.KtG(o.shareArticle())}),t.TgZ(7,"mat-icon"),t._uU(8,"share"),t.qZA()()()(),t.YNc(9,ee,2,2,"div",14),t.TgZ(10,"div",15)(11,"mat-chip",16),t._uU(12),t.qZA(),t.TgZ(13,"div",17)(14,"span",18),t._uU(15),t.qZA(),t.TgZ(16,"span",19),t._uU(17,"\u2022"),t.qZA(),t.TgZ(18,"span",20),t._uU(19),t.qZA(),t.TgZ(20,"span",19),t._uU(21,"\u2022"),t.qZA(),t.TgZ(22,"span",21),t._uU(23),t.qZA(),t.TgZ(24,"span",19),t._uU(25,"\u2022"),t.qZA(),t.TgZ(26,"span",22),t._uU(27),t.qZA()()(),t.TgZ(28,"h1",23),t._uU(29),t.qZA(),t.TgZ(30,"p",24),t._uU(31),t.qZA(),t.YNc(32,ne,2,1,"div",25),t.TgZ(33,"div",26),t._UZ(34,"div",27),t.YNc(35,oe,2,0,"div",28),t.qZA(),t.YNc(36,ae,52,12,"div",29),t.qZA()}if(2&n){const e=t.oxw();t.xp6(2),t.uIk("aria-label",e.t.common.back),t.xp6(4),t.uIk("aria-label",e.t.articles.share),t.xp6(3),t.Q6J("ngIf",e.article.featuredImageUrl||e.article.imageUrl),t.xp6(3),t.Oqu(e.article.category),t.xp6(3),t.AsE("",e.t.articles.author,": ",e.article.author,""),t.xp6(4),t.Oqu(e.formatDate(e.article.publishedAt)),t.xp6(4),t.AsE("",e.article.readTime," ",e.t.home.minRead,""),t.xp6(4),t.AsE("",e.article.readCount," ",e.t.home.views,""),t.xp6(2),t.Oqu(e.article.title),t.xp6(2),t.Oqu(e.article.excerpt),t.xp6(1),t.Q6J("ngIf",e.article.tags&&e.article.tags.length>0),t.xp6(1),t.ekj("blurred",e.shouldShowBlur()),t.xp6(1),t.Q6J("innerHTML",e.getContentPreview(),t.oJD),t.xp6(1),t.Q6J("ngIf",e.shouldShowBlur()),t.xp6(1),t.Q6J("ngIf",e.shouldShowAuthPrompt())}}let ue=(()=>{const r=class{constructor(i,o,a,s,c){this.route=i,this.router=o,this.articleService=a,this.authService=s,this.t=c,this.article=null,this.isLoading=!0,this.isAuthenticated=!1,this.showAuthPrompt=!1,this.error=null,this.destroy$=new k.x}ngOnInit(){(0,Gt.a)([this.authService.isAuthenticated$,this.route.params]).pipe((0,Kt.R)(this.destroy$)).subscribe(([i,o])=>{this.isAuthenticated=i;const a=o.slug;a&&this.loadArticle(a)})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}loadArticle(i){this.isLoading=!0,this.error=null,this.articleService.getArticleBySlug(i).subscribe({next:o=>{this.article=o,this.isLoading=!1,this.articleService.recordArticleView(o.id).subscribe(),!this.isAuthenticated&&(!o.content||o.content.length<500)&&(this.showAuthPrompt=!0)},error:o=>{this.error=o.message,this.isLoading=!1,console.error("Error loading article:",o)}})}onLoginClick(){this.router.navigate(["/login"],{queryParams:{returnUrl:this.router.url}})}onRegisterClick(){this.router.navigate(["/register"],{queryParams:{returnUrl:this.router.url}})}onCloseAuthPrompt(){this.showAuthPrompt=!1}goBack(){this.router.navigate(["/home"])}shareArticle(){navigator.share&&this.article?navigator.share({title:this.article.title,text:this.article.excerpt,url:window.location.href}).catch(console.error):this.article&&navigator.clipboard.writeText(window.location.href).then(()=>{console.log("URL copied to clipboard")}).catch(console.error)}formatDate(i){return new Date(i).toLocaleDateString("bg-BG",{year:"numeric",month:"long",day:"numeric"})}getContentPreview(){if(!this.article?.content)return"";if(this.isAuthenticated)return this.article.content;const i=this.article.content,o=i.split(" "),s=i.split("\n\n").slice(0,3).join("\n\n"),c=o.slice(0,150).join(" ");return s.length<c.length?s:c}shouldShowBlur(){return!this.isAuthenticated&&!!this.article?.content&&this.article.content.length>500}shouldShowAuthPrompt(){return!this.isAuthenticated&&!!this.article?.content&&this.article.content.length>300}getContentCompletionPercentage(){if(!this.article?.content||this.isAuthenticated)return 100;const i=this.getContentPreview().length;return Math.round(i/this.article.content.length*100)}};let n=r;return r.\u0275fac=function(o){return new(o||r)(t.Y36(y.gz),t.Y36(y.F0),t.Y36(Q.n),t.Y36($t.e),t.Y36(Wt.D))},r.\u0275cmp=t.Xpm({type:r,selectors:[["app-article-view"]],decls:3,vars:3,consts:[["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","article-container",4,"ngIf"],[1,"loading-container"],[1,"error-container"],[1,"error-card"],[1,"error-content"],[1,"error-icon"],["mat-raised-button","","color","primary",3,"click"],[1,"article-container"],[1,"article-header"],["mat-icon-button","",1,"back-button",3,"click"],[1,"article-actions"],["mat-icon-button","",3,"click"],["class","featured-image",4,"ngIf"],[1,"article-meta"],[1,"category-chip"],[1,"meta-info"],[1,"author"],[1,"separator"],[1,"date"],[1,"read-time"],[1,"read-count"],[1,"article-title"],[1,"article-excerpt"],["class","article-tags",4,"ngIf"],[1,"article-content-wrapper"],[1,"article-content",3,"innerHTML"],["class","blur-overlay",4,"ngIf"],["class","auth-prompt-overlay",4,"ngIf"],[1,"featured-image"],[1,"article-featured-image",3,"src","alt"],[1,"article-tags"],["class","tag-chip",4,"ngFor","ngForOf"],[1,"tag-chip"],[1,"blur-overlay"],[1,"fade-gradient"],[1,"auth-prompt-overlay"],[1,"auth-prompt-card"],["class","reading-progress",4,"ngIf"],[1,"auth-benefits"],[1,"benefit-item"],[1,"social-proof"],[1,"auth-actions"],["mat-raised-button","","color","accent",3,"click"],["mat-button","",3,"click"],[1,"reading-progress"],[1,"progress-info"],["mode","determinate","color","accent",3,"value"]],template:function(o,a){1&o&&(t.YNc(0,Xt,4,1,"div",0),t.YNc(1,te,14,3,"div",1),t.YNc(2,se,37,19,"div",2)),2&o&&(t.Q6J("ngIf",a.isLoading),t.xp6(1),t.Q6J("ngIf",a.error&&!a.isLoading),t.xp6(1),t.Q6J("ngIf",a.article&&!a.isLoading&&!a.error))},dependencies:[h.sg,h.O5,v.a8,v.hq,v.dn,v.dk,v.$j,v.n5,A.lW,A.RK,E.Hw,B.HS,D.Ou,et.pW],styles:['.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;padding:2rem}.error-card[_ngcontent-%COMP%]{max-width:500px;margin:2rem auto}.error-content[_ngcontent-%COMP%]{text-align:center}.error-icon[_ngcontent-%COMP%]{font-size:3rem;color:var(--theme-error);margin-bottom:1rem}.article-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:1rem;background:linear-gradient(135deg,var(--theme-background) 0%,var(--theme-accent-light) 100%);min-height:100vh;position:relative}.article-container[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;right:0;height:200px;background:linear-gradient(135deg,rgba(210,166,208,.1) 0%,transparent 100%);pointer-events:none;z-index:0}.article-container[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{position:relative;z-index:1}.article-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem;padding:.5rem 0}.back-button[_ngcontent-%COMP%]{color:var(--theme-primary)}.article-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem}.featured-image[_ngcontent-%COMP%]{width:100%;margin-bottom:2rem;border-radius:16px;overflow:hidden;box-shadow:0 8px 32px #67455c26,0 4px 16px #d2a6d01a;position:relative}.featured-image[_ngcontent-%COMP%]:after{content:"";position:absolute;inset:0;background:linear-gradient(135deg,rgba(210,166,208,.1) 0%,transparent 50%,rgba(103,69,92,.1) 100%);pointer-events:none}.article-featured-image[_ngcontent-%COMP%]{width:100%;height:300px;object-fit:cover;display:block;transition:transform .3s ease}.featured-image[_ngcontent-%COMP%]:hover   .article-featured-image[_ngcontent-%COMP%]{transform:scale(1.02)}.article-meta[_ngcontent-%COMP%]{margin-bottom:1.5rem}.category-chip[_ngcontent-%COMP%]{background:var(--theme-accent);color:var(--theme-text-primary);margin-bottom:1rem}.meta-info[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;align-items:center;gap:.5rem;color:var(--theme-text-secondary);font-size:.9rem}.separator[_ngcontent-%COMP%]{color:var(--theme-text-disabled)}.article-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;line-height:1.2;color:var(--theme-text-primary);margin-bottom:1rem}.article-excerpt[_ngcontent-%COMP%]{font-size:1.2rem;line-height:1.6;color:var(--theme-text-secondary);margin-bottom:2rem;font-style:italic;padding:1rem;background:var(--theme-accent-light);border-radius:8px;border-left:4px solid var(--theme-accent)}.article-tags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;margin-bottom:2rem}.tag-chip[_ngcontent-%COMP%]{background:var(--theme-primary-light);color:#fff;font-size:.8rem}.article-content-wrapper[_ngcontent-%COMP%]{position:relative;margin-bottom:2rem;overflow:hidden;border-radius:12px}.article-content[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.8;color:var(--theme-text-primary);padding:1.5rem;background:var(--theme-surface);border-radius:12px;box-shadow:0 2px 8px #0000000d}.article-content-wrapper.blurred[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]{filter:blur(2px);pointer-events:none;-webkit-user-select:none;user-select:none;transition:filter .3s ease}.blur-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;pointer-events:none;z-index:1}.fade-gradient[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;height:250px;background:linear-gradient(to bottom,transparent 0%,rgba(230,219,236,.3) 20%,rgba(230,219,236,.6) 40%,rgba(230,219,236,.8) 60%,rgba(230,219,236,.95) 80%,var(--theme-accent-light) 100%);border-radius:0 0 12px 12px}.blur-overlay[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background:radial-gradient(ellipse at center bottom,rgba(210,166,208,.1) 0%,transparent 70%);pointer-events:none}.auth-prompt-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(0,0,0,.6);display:flex;align-items:center;justify-content:center;z-index:1000;padding:1rem}.auth-prompt-card[_ngcontent-%COMP%]{max-width:500px;width:100%;background:var(--theme-surface);border-radius:16px;box-shadow:0 8px 32px #0003}.auth-prompt-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:var(--theme-primary);font-size:1.5rem}.auth-prompt-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--theme-accent)}.auth-benefits[_ngcontent-%COMP%]{margin:1.5rem 0}.benefit-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:1rem;color:var(--theme-text-primary)}.benefit-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--theme-accent);font-size:1.2rem}.auth-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap;justify-content:center;padding:1rem}.auth-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.reading-progress[_ngcontent-%COMP%]{margin-bottom:1.5rem;padding:1rem;background:var(--theme-accent-light);border-radius:8px;border:1px solid var(--theme-accent)}.progress-info[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin-bottom:.5rem;font-weight:500;color:var(--theme-primary)}.progress-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--theme-accent)}.social-proof[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-top:1.5rem;padding:1rem;background:linear-gradient(135deg,var(--theme-accent-light) 0%,var(--theme-background) 100%);border-radius:8px;font-style:italic;color:var(--theme-text-secondary)}.social-proof[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--theme-accent)}@media (max-width: 768px){.article-container[_ngcontent-%COMP%]{padding:.5rem}.article-title[_ngcontent-%COMP%]{font-size:2rem}.article-excerpt[_ngcontent-%COMP%]{font-size:1.1rem;padding:.75rem}.article-content[_ngcontent-%COMP%]{font-size:1rem}.meta-info[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.25rem}.separator[_ngcontent-%COMP%]{display:none}.auth-actions[_ngcontent-%COMP%]{flex-direction:column}.auth-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}.article-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--theme-primary);margin:2rem 0 1rem;font-size:1.5rem}.article-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--theme-primary-light);margin:1.5rem 0 .75rem;font-size:1.3rem}.article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:1.5rem}.article-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]{border-left:4px solid var(--theme-accent);padding-left:1rem;margin:1.5rem 0;font-style:italic;color:var(--theme-text-secondary);background:var(--theme-accent-light);padding:1rem;border-radius:0 8px 8px 0}.article-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .article-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin-bottom:1.5rem;padding-left:2rem}.article-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:.5rem}']}),n})();var V=l(2340);function le(n,r){if(1&n&&(t.TgZ(0,"div",21),t._UZ(1,"img",22),t.qZA()),2&n){const e=t.oxw();t.xp6(1),t.Q6J("src",e.getImageUrl(),t.LSH)("alt",e.data.title)}}function ce(n,r){if(1&n&&(t.TgZ(0,"span",23),t._uU(1),t.qZA()),2&n){const e=t.oxw();t.xp6(1),t.Oqu(e.data.category)}}function de(n,r){if(1&n&&(t.TgZ(0,"div",24),t._UZ(1,"div",25),t.qZA()),2&n){const e=t.oxw();t.xp6(1),t.Q6J("innerHTML",e.data.content,t.oJD)}}function me(n,r){if(1&n&&(t.TgZ(0,"mat-chip",29),t._uU(1),t.qZA()),2&n){const e=r.$implicit;t.xp6(1),t.hij(" ",e," ")}}function ge(n,r){if(1&n&&(t.TgZ(0,"div",26)(1,"h4"),t._uU(2,"\u0422\u0430\u0433\u043e\u0432\u0435:"),t.qZA(),t.TgZ(3,"div",27),t.YNc(4,me,2,1,"mat-chip",28),t.qZA()()),2&n){const e=t.oxw();t.xp6(4),t.Q6J("ngForOf",e.data.tags)}}let pe=(()=>{const r=class{constructor(i,o){this.dialogRef=i,this.data=o}onClose(){this.dialogRef.close()}calculateReadTime(i){if(!i)return 0;const a=i.replace(/<[^>]*>/g,"").trim().split(/\s+/).length,c=Math.ceil(a/225);return Math.max(1,c)}getReadTime(){return this.data.estimatedReadTime||this.calculateReadTime(this.data.content)}getImageUrl(){return this.data.featuredImageUrl?this.data.featuredImageUrl.startsWith("http")?this.data.featuredImageUrl:`${V.N.apiUrl.replace("/api","")}${this.data.featuredImageUrl}`:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop"}};let n=r;return r.\u0275fac=function(o){return new(o||r)(t.Y36(x.so),t.Y36(x.WI))},r.\u0275cmp=t.Xpm({type:r,selectors:[["app-article-preview-dialog"]],decls:36,vars:9,consts:[[1,"preview-dialog"],[1,"dialog-header"],["mat-dialog-title",""],["mat-icon-button","",1,"close-button",3,"click"],["mat-dialog-content","",1,"dialog-content"],[1,"article-preview"],["class","article-image",4,"ngIf"],[1,"article-header"],[1,"article-meta"],["class","category",4,"ngIf"],[1,"read-time"],[1,"article-title"],[1,"article-info"],[1,"author"],[1,"publish-date"],[1,"article-excerpt"],["class","article-content",4,"ngIf"],["class","article-tags",4,"ngIf"],["mat-dialog-actions","",1,"dialog-actions"],["mat-button","",3,"click"],["mat-raised-button","","color","primary",3,"click"],[1,"article-image"],[1,"featured-image",3,"src","alt"],[1,"category"],[1,"article-content"],[1,"content-html",3,"innerHTML"],[1,"article-tags"],[1,"tags-container"],["class","tag-chip",4,"ngFor","ngForOf"],[1,"tag-chip"]],template:function(o,a){1&o&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h2",2),t._uU(3,"\u041f\u0440\u0435\u0433\u043b\u0435\u0434 \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430"),t.qZA(),t.TgZ(4,"button",3),t.NdJ("click",function(){return a.onClose()}),t.TgZ(5,"mat-icon"),t._uU(6,"close"),t.qZA()()(),t.TgZ(7,"div",4)(8,"article",5),t.YNc(9,le,2,2,"div",6),t.TgZ(10,"header",7)(11,"div",8),t.YNc(12,ce,2,1,"span",9),t.TgZ(13,"span",10)(14,"mat-icon"),t._uU(15,"schedule"),t.qZA(),t._uU(16),t.qZA()(),t.TgZ(17,"h1",11),t._uU(18),t.qZA(),t.TgZ(19,"div",12)(20,"span",13),t._uU(21),t.qZA(),t.TgZ(22,"span",14),t._uU(23),t.qZA()()(),t.TgZ(24,"div",15)(25,"p"),t._uU(26),t.qZA()(),t.YNc(27,de,2,1,"div",16),t.YNc(28,ge,5,1,"div",17),t.qZA()(),t.TgZ(29,"div",18)(30,"button",19),t.NdJ("click",function(){return a.onClose()}),t._uU(31,"\u0417\u0430\u0442\u0432\u043e\u0440\u0438"),t.qZA(),t.TgZ(32,"button",20),t.NdJ("click",function(){return a.onClose()}),t.TgZ(33,"mat-icon"),t._uU(34,"edit"),t.qZA(),t._uU(35," \u041f\u0440\u043e\u0434\u044a\u043b\u0436\u0438 \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435\u0442\u043e "),t.qZA()()()),2&o&&(t.xp6(9),t.Q6J("ngIf",a.data.featuredImageUrl),t.xp6(3),t.Q6J("ngIf",a.data.category),t.xp6(4),t.hij(" ",a.getReadTime()," \u043c\u0438\u043d \u0447\u0435\u0442\u0435\u043d\u0435 "),t.xp6(2),t.Oqu(a.data.title),t.xp6(3),t.hij("\u043e\u0442 ",a.data.author,""),t.xp6(2),t.Oqu("\u0414\u043d\u0435\u0441"),t.xp6(3),t.Oqu(a.data.excerpt),t.xp6(1),t.Q6J("ngIf",a.data.content),t.xp6(1),t.Q6J("ngIf",a.data.tags&&a.data.tags.length>0))},dependencies:[h.sg,h.O5,A.lW,A.RK,E.Hw,B.HS,x.uh,x.xY,x.H8],styles:[".preview-dialog[_ngcontent-%COMP%]{max-width:800px;width:100%}.dialog-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 24px;border-bottom:1px solid #e0e0e0}.dialog-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;color:#3f2f4e}.close-button[_ngcontent-%COMP%]{color:#666}.dialog-content[_ngcontent-%COMP%]{padding:0;max-height:70vh;overflow-y:auto}.article-preview[_ngcontent-%COMP%]{padding:24px}.article-image[_ngcontent-%COMP%]{margin-bottom:24px;border-radius:8px;overflow:hidden}.featured-image[_ngcontent-%COMP%]{width:100%;height:300px;object-fit:cover;display:block}.article-header[_ngcontent-%COMP%]{margin-bottom:24px}.article-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;margin-bottom:12px}.category[_ngcontent-%COMP%]{background-color:#e6dbec;color:#3f2f4e;padding:4px 12px;border-radius:16px;font-size:12px;font-weight:500;text-transform:uppercase}.read-time[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;color:#666;font-size:14px}.read-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.article-title[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#3f2f4e;line-height:1.2;margin:0 0 16px}.article-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;color:#666;font-size:14px}.author[_ngcontent-%COMP%]{font-weight:500}.article-excerpt[_ngcontent-%COMP%]{margin-bottom:24px;padding:16px;background-color:#f8f9fa;border-left:4px solid #d2a6d0;border-radius:4px}.article-excerpt[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:16px;line-height:1.6;color:#555;font-style:italic}.article-content[_ngcontent-%COMP%]{margin-bottom:32px}.content-html[_ngcontent-%COMP%]{font-size:16px;line-height:1.7;color:#333}.content-html[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#3f2f4e;margin-top:24px;margin-bottom:12px}.content-html[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:28px}.content-html[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px}.content-html[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px}.content-html[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:16px}.content-html[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin-bottom:16px;padding-left:24px}.content-html[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px}.content-html[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]{border-left:4px solid #d2a6d0;padding-left:16px;margin:16px 0;font-style:italic;color:#666}.content-html[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;border-radius:8px;margin:16px 0}.content-html[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:2px 6px;border-radius:4px;font-family:Courier New,monospace;font-size:14px}.content-html[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:8px;overflow-x:auto;margin:16px 0}.article-tags[_ngcontent-%COMP%]{border-top:1px solid #e0e0e0;padding-top:24px}.article-tags[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#3f2f4e;font-size:16px}.tags-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px}.tag-chip[_ngcontent-%COMP%]{background-color:#e6dbec;color:#3f2f4e}.dialog-actions[_ngcontent-%COMP%]{padding:16px 24px;border-top:1px solid #e0e0e0;gap:8px}@media (max-width: 768px){.preview-dialog[_ngcontent-%COMP%]{max-width:100vw;height:100vh}.dialog-content[_ngcontent-%COMP%]{max-height:calc(100vh - 120px)}.article-preview[_ngcontent-%COMP%]{padding:16px}.article-title[_ngcontent-%COMP%]{font-size:24px}.featured-image[_ngcontent-%COMP%]{height:200px}}"]}),n})();var he=l(2843),lt=l(262),fe=l(529);let _e=(()=>{const r=class{constructor(i){this.http=i,this.API_URL=`${V.N.apiUrl}/fileupload`}uploadImage(i){const o=new FormData;return o.append("file",i),this.http.post(`${this.API_URL}/image`,o).pipe((0,lt.K)(this.handleError))}deleteImage(i){return this.http.delete(`${this.API_URL}/image?fileName=${encodeURIComponent(i)}`).pipe((0,lt.K)(this.handleError))}validateImageFile(i){return["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(i.type)?i.size>5242880?{valid:!1,error:"\u0424\u0430\u0439\u043b\u044a\u0442 \u0435 \u0442\u0432\u044a\u0440\u0434\u0435 \u0433\u043e\u043b\u044f\u043c. \u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u043d\u0438\u044f\u0442 \u0440\u0430\u0437\u043c\u0435\u0440 \u0435 5MB"}:{valid:!0}:{valid:!1,error:"\u041d\u0435\u043f\u043e\u0434\u0434\u044a\u0440\u0436\u0430\u043d \u0444\u043e\u0440\u043c\u0430\u0442 \u043d\u0430 \u0444\u0430\u0439\u043b\u0430. \u0420\u0430\u0437\u0440\u0435\u0448\u0435\u043d\u0438 \u0441\u0430: JPG, PNG, GIF, WebP"}}getImageUrl(i){return i.startsWith("http")?i:`${V.N.apiUrl.replace("/api","")}${i}`}handleError(i){let o="\u0412\u044a\u0437\u043d\u0438\u043a\u043d\u0430 \u043d\u0435\u043e\u0447\u0430\u043a\u0432\u0430\u043d\u0430 \u0433\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043a\u0430\u0447\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0444\u0430\u0439\u043b\u0430";return i.error&&i.error.message?o=i.error.message:0===i.status?o="\u041d\u044f\u043c\u0430 \u0432\u0440\u044a\u0437\u043a\u0430 \u0441\u044a\u0441 \u0441\u044a\u0440\u0432\u044a\u0440\u0430":401===i.status?o="\u041d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u0430 \u0435 \u0430\u0432\u0442\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0446\u0438\u044f \u0437\u0430 \u043a\u0430\u0447\u0432\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435":403===i.status?o="\u041d\u044f\u043c\u0430\u0442\u0435 \u043f\u0440\u0430\u0432\u0430 \u0437\u0430 \u043a\u0430\u0447\u0432\u0430\u043d\u0435 \u043d\u0430 \u0444\u0430\u0439\u043b\u043e\u0432\u0435":413===i.status?o="\u0424\u0430\u0439\u043b\u044a\u0442 \u0435 \u0442\u0432\u044a\u0440\u0434\u0435 \u0433\u043e\u043b\u044f\u043c":i.status>=500&&(o="\u0421\u044a\u0440\u0432\u044a\u0440\u043d\u0430 \u0433\u0440\u0435\u0448\u043a\u0430. \u041c\u043e\u043b\u044f, \u043e\u043f\u0438\u0442\u0430\u0439\u0442\u0435 \u043e\u0442\u043d\u043e\u0432\u043e \u043f\u043e-\u043a\u044a\u0441\u043d\u043e"),console.error("File upload service error:",i),(0,he._)(()=>new Error(o))}};let n=r;return r.\u0275fac=function(o){return new(o||r)(t.LFG(fe.eN))},r.\u0275prov=t.Yz7({token:r,factory:r.\u0275fac,providedIn:"root"}),n})();function ve(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"div",3),t._UZ(1,"img",4),t.TgZ(2,"div",5)(3,"button",6),t.NdJ("click",function(){t.CHM(e);const o=t.oxw();return t.KtG(o.removeImage())}),t.TgZ(4,"mat-icon"),t._uU(5,"delete"),t.qZA()()()()}if(2&n){const e=t.oxw();t.xp6(1),t.Q6J("src",e.getImageUrl(),t.LSH)("alt",e.placeholder)}}function Ce(n,r){1&n&&(t.TgZ(0,"div",10),t._UZ(1,"mat-progress-spinner",11),t.TgZ(2,"p"),t._uU(3,"\u041a\u0430\u0447\u0432\u0430\u043d\u0435 \u043d\u0430 \u0441\u043d\u0438\u043c\u043a\u0430\u0442\u0430..."),t.qZA()())}function xe(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"div",12)(1,"mat-icon",13),t._uU(2,"cloud_upload"),t.qZA(),t.TgZ(3,"p",14),t._uU(4),t.qZA(),t.TgZ(5,"p",15),t._uU(6,"\u041f\u043b\u044a\u0437\u043d\u0435\u0442\u0435 \u0438 \u043f\u0443\u0441\u043d\u0435\u0442\u0435 \u0444\u0430\u0439\u043b \u0442\u0443\u043a \u0438\u043b\u0438"),t.qZA(),t.TgZ(7,"input",16,17),t.NdJ("change",function(o){t.CHM(e);const a=t.oxw(2);return t.KtG(a.onFileSelected(o))}),t.qZA(),t.TgZ(9,"button",18),t.NdJ("click",function(){t.CHM(e);const o=t.MAs(8);return t.KtG(o.click())}),t.TgZ(10,"mat-icon"),t._uU(11,"add_photo_alternate"),t.qZA(),t._uU(12," \u0418\u0437\u0431\u0435\u0440\u0435\u0442\u0435 \u0444\u0430\u0439\u043b "),t.qZA(),t.TgZ(13,"p",19),t._uU(14," \u041f\u043e\u0434\u0434\u044a\u0440\u0436\u0430\u043d\u0438 \u0444\u043e\u0440\u043c\u0430\u0442\u0438: JPG, PNG, GIF, WebP"),t._UZ(15,"br"),t._uU(16," \u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u0435\u043d \u0440\u0430\u0437\u043c\u0435\u0440: 5MB "),t.qZA()()}if(2&n){const e=t.oxw(2);t.xp6(4),t.Oqu(e.placeholder),t.xp6(3),t.Q6J("accept",e.accept)}}function be(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"div",7),t.NdJ("dragover",function(o){t.CHM(e);const a=t.oxw();return t.KtG(a.onDragOver(o))})("dragleave",function(o){t.CHM(e);const a=t.oxw();return t.KtG(a.onDragLeave(o))})("drop",function(o){t.CHM(e);const a=t.oxw();return t.KtG(a.onDrop(o))}),t.YNc(1,Ce,4,0,"div",8),t.YNc(2,xe,17,2,"div",9),t.qZA()}if(2&n){const e=t.oxw();t.ekj("drag-over",e.dragOver)("uploading",e.isUploading),t.xp6(1),t.Q6J("ngIf",e.isUploading),t.xp6(1),t.Q6J("ngIf",!e.isUploading)}}let Ae=(()=>{const r=class{constructor(i,o){this.fileUploadService=i,this.snackBar=o,this.placeholder="\u041a\u0430\u0447\u0435\u0442\u0435 \u0441\u043d\u0438\u043c\u043a\u0430",this.accept="image/*",this.imageUploaded=new t.vpe,this.imageRemoved=new t.vpe,this.isUploading=!1,this.dragOver=!1}onFileSelected(i){const o=i.target;o.files&&o.files.length>0&&this.uploadFile(o.files[0])}onDragOver(i){i.preventDefault(),this.dragOver=!0}onDragLeave(i){i.preventDefault(),this.dragOver=!1}onDrop(i){i.preventDefault(),this.dragOver=!1,i.dataTransfer?.files&&i.dataTransfer.files.length>0&&this.uploadFile(i.dataTransfer.files[0])}uploadFile(i){const o=this.fileUploadService.validateImageFile(i);o.valid?(this.isUploading=!0,this.fileUploadService.uploadImage(i).subscribe({next:a=>{const s=this.fileUploadService.getImageUrl(a.url);this.imageUploaded.emit(s),this.snackBar.open("\u0421\u043d\u0438\u043c\u043a\u0430\u0442\u0430 \u0435 \u043a\u0430\u0447\u0435\u043d\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.isUploading=!1},error:a=>{console.error("Error uploading image:",a),this.snackBar.open(a.message||"\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043a\u0430\u0447\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u043d\u0438\u043c\u043a\u0430\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.isUploading=!1}})):this.snackBar.open(o.error,"\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3})}removeImage(){this.imageRemoved.emit()}getImageUrl(){return this.currentImageUrl?this.fileUploadService.getImageUrl(this.currentImageUrl):""}};let n=r;return r.\u0275fac=function(o){return new(o||r)(t.Y36(_e),t.Y36(Y.ux))},r.\u0275cmp=t.Xpm({type:r,selectors:[["app-image-upload"]],inputs:{currentImageUrl:"currentImageUrl",placeholder:"placeholder",accept:"accept"},outputs:{imageUploaded:"imageUploaded",imageRemoved:"imageRemoved"},decls:3,vars:2,consts:[[1,"image-upload-container"],["class","current-image",4,"ngIf"],["class","upload-area",3,"drag-over","uploading","dragover","dragleave","drop",4,"ngIf"],[1,"current-image"],[1,"uploaded-image",3,"src","alt"],[1,"image-overlay"],["mat-icon-button","","color","warn",1,"remove-button",3,"click"],[1,"upload-area",3,"dragover","dragleave","drop"],["class","upload-loading",4,"ngIf"],["class","upload-prompt",4,"ngIf"],[1,"upload-loading"],["mode","indeterminate","diameter","40"],[1,"upload-prompt"],[1,"upload-icon"],[1,"upload-text"],[1,"upload-hint"],["type","file",1,"file-input",3,"accept","change"],["fileInput",""],["mat-raised-button","","color","primary",3,"click"],[1,"file-requirements"]],template:function(o,a){1&o&&(t.TgZ(0,"div",0),t.YNc(1,ve,6,2,"div",1),t.YNc(2,be,3,6,"div",2),t.qZA()),2&o&&(t.xp6(1),t.Q6J("ngIf",a.currentImageUrl&&!a.isUploading),t.xp6(1),t.Q6J("ngIf",!a.currentImageUrl||a.isUploading))},dependencies:[h.O5,A.lW,A.RK,E.Hw,D.Ou],styles:[".image-upload-container[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.current-image[_ngcontent-%COMP%]{position:relative;display:inline-block;border-radius:8px;overflow:hidden;box-shadow:0 2px 8px #0000001a}.uploaded-image[_ngcontent-%COMP%]{max-width:100%;max-height:300px;width:auto;height:auto;display:block}.image-overlay[_ngcontent-%COMP%]{position:absolute;top:0;right:0;background:rgba(0,0,0,.5);border-radius:0 0 0 8px}.remove-button[_ngcontent-%COMP%]{color:#fff}.upload-area[_ngcontent-%COMP%]{border:2px dashed #d2a6d0;border-radius:8px;padding:40px 20px;text-align:center;background-color:#fafafa;transition:all .3s ease;cursor:pointer}.upload-area[_ngcontent-%COMP%]:hover{border-color:#67455c;background-color:#f5f5f5}.upload-area.drag-over[_ngcontent-%COMP%]{border-color:#67455c;background-color:#e6dbec;transform:scale(1.02)}.upload-area.uploading[_ngcontent-%COMP%]{border-color:#d2a6d0;background-color:#f9f9f9;cursor:not-allowed}.upload-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px}.upload-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:14px}.upload-prompt[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.upload-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;color:#d2a6d0}.upload-text[_ngcontent-%COMP%]{font-size:18px;font-weight:500;color:#3f2f4e;margin:0}.upload-hint[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.file-input[_ngcontent-%COMP%]{display:none}.file-requirements[_ngcontent-%COMP%]{font-size:12px;color:#999;margin:8px 0 0;line-height:1.4}@media (max-width: 768px){.upload-area[_ngcontent-%COMP%]{padding:30px 15px}.upload-icon[_ngcontent-%COMP%]{font-size:36px;width:36px;height:36px}.upload-text[_ngcontent-%COMP%]{font-size:16px}.uploaded-image[_ngcontent-%COMP%]{max-height:200px}}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.upload-area.drag-over[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1s infinite}"]}),n})();function Me(n,r){1&n&&(t.TgZ(0,"div",8),t._UZ(1,"mat-progress-spinner",9),t.TgZ(2,"p"),t._uU(3,"\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430..."),t.qZA()())}function we(n,r){1&n&&(t.TgZ(0,"mat-error"),t._uU(1," \u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435\u0442\u043e \u0435 \u0437\u0430\u0434\u044a\u043b\u0436\u0438\u0442\u0435\u043b\u043d\u043e "),t.qZA())}function Te(n,r){1&n&&(t.TgZ(0,"mat-error"),t._uU(1," \u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435\u0442\u043e \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0431\u044a\u0434\u0435 \u043f\u043e-\u0434\u044a\u043b\u0433\u043e \u043e\u0442 200 \u0441\u0438\u043c\u0432\u043e\u043b\u0430 "),t.qZA())}function Oe(n,r){1&n&&(t.TgZ(0,"mat-error"),t._uU(1," \u041a\u0440\u0430\u0442\u043a\u043e\u0442\u043e \u043e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0435 \u0437\u0430\u0434\u044a\u043b\u0436\u0438\u0442\u0435\u043b\u043d\u043e "),t.qZA())}function Ze(n,r){1&n&&(t.TgZ(0,"mat-error"),t._uU(1," \u041a\u0440\u0430\u0442\u043a\u043e\u0442\u043e \u043e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0431\u044a\u0434\u0435 \u043f\u043e-\u0434\u044a\u043b\u0433\u043e \u043e\u0442 500 \u0441\u0438\u043c\u0432\u043e\u043b\u0430 "),t.qZA())}function Pe(n,r){if(1&n&&(t.TgZ(0,"mat-option",30),t._uU(1),t.qZA()),2&n){const e=r.$implicit;t.Q6J("value",e),t.xp6(1),t.hij(" ",e," ")}}function ye(n,r){1&n&&(t.TgZ(0,"mat-error"),t._uU(1," Meta \u043e\u043f\u0438\u0441\u0430\u043d\u0438\u0435\u0442\u043e \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0431\u044a\u0434\u0435 \u043f\u043e-\u0434\u044a\u043b\u0433\u043e \u043e\u0442 160 \u0441\u0438\u043c\u0432\u043e\u043b\u0430 "),t.qZA())}function Ee(n,r){1&n&&(t.TgZ(0,"mat-error"),t._uU(1," \u041a\u043b\u044e\u0447\u043e\u0432\u0438\u0442\u0435 \u0434\u0443\u043c\u0438 \u043d\u0435 \u043c\u043e\u0433\u0430\u0442 \u0434\u0430 \u0431\u044a\u0434\u0430\u0442 \u043f\u043e-\u0434\u044a\u043b\u0433\u0438 \u043e\u0442 500 \u0441\u0438\u043c\u0432\u043e\u043b\u0430 "),t.qZA())}function Se(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"form",10)(1,"mat-form-field",11)(2,"mat-label"),t._uU(3,"\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435 *"),t.qZA(),t._UZ(4,"input",12),t.YNc(5,we,2,0,"mat-error",13),t.YNc(6,Te,2,0,"mat-error",13),t.qZA(),t.TgZ(7,"mat-form-field",11)(8,"mat-label"),t._uU(9,"\u041a\u0440\u0430\u0442\u043a\u043e \u043e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 *"),t.qZA(),t._UZ(10,"textarea",14),t.YNc(11,Oe,2,0,"mat-error",13),t.YNc(12,Ze,2,0,"mat-error",13),t.qZA(),t.TgZ(13,"mat-form-field",11)(14,"mat-label"),t._uU(15,"\u041a\u0430\u0442\u0435\u0433\u043e\u0440\u0438\u044f"),t.qZA(),t.TgZ(16,"mat-select",15)(17,"mat-option",16),t._uU(18,"\u0411\u0435\u0437 \u043a\u0430\u0442\u0435\u0433\u043e\u0440\u0438\u044f"),t.qZA(),t.YNc(19,Pe,2,2,"mat-option",17),t.qZA()(),t.TgZ(20,"mat-form-field",11)(21,"mat-label"),t._uU(22,"\u0422\u0430\u0433\u043e\u0432\u0435"),t.qZA(),t._UZ(23,"input",18),t.TgZ(24,"mat-hint"),t._uU(25,"\u0420\u0430\u0437\u0434\u0435\u043b\u0435\u0442\u0435 \u0442\u0430\u0433\u043e\u0432\u0435\u0442\u0435 \u0441\u044a\u0441 \u0437\u0430\u043f\u0435\u0442\u0430\u044f (\u043d\u0430\u043f\u0440. \u0430\u0441\u0442\u0440\u043e\u043b\u043e\u0433\u0438\u044f, \u0442\u0430\u0440\u043e, \u0445\u043e\u0440\u043e\u0441\u043a\u043e\u043f)"),t.qZA()(),t.TgZ(26,"div",19)(27,"label",20),t._uU(28,"\u0421\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435"),t.qZA(),t._UZ(29,"quill-editor",21),t.qZA(),t.TgZ(30,"div",22)(31,"label",20),t._uU(32,"\u041e\u0441\u043d\u043e\u0432\u043d\u0430 \u0441\u043d\u0438\u043c\u043a\u0430"),t.qZA(),t.TgZ(33,"app-image-upload",23),t.NdJ("imageUploaded",function(o){t.CHM(e);const a=t.oxw();return t.KtG(a.onFeaturedImageUploaded(o))})("imageRemoved",function(){t.CHM(e);const o=t.oxw();return t.KtG(o.onFeaturedImageRemoved())}),t.qZA()(),t.TgZ(34,"mat-card",24)(35,"mat-card-subtitle"),t._uU(36,"SEO \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438"),t.qZA(),t.TgZ(37,"mat-form-field",11)(38,"mat-label"),t._uU(39,"Meta \u043e\u043f\u0438\u0441\u0430\u043d\u0438\u0435"),t.qZA(),t._UZ(40,"textarea",25),t.YNc(41,ye,2,0,"mat-error",13),t.qZA(),t.TgZ(42,"mat-form-field",11)(43,"mat-label"),t._uU(44,"\u041a\u043b\u044e\u0447\u043e\u0432\u0438 \u0434\u0443\u043c\u0438"),t.qZA(),t._UZ(45,"input",26),t.YNc(46,Ee,2,0,"mat-error",13),t.qZA()(),t.TgZ(47,"div",27)(48,"mat-checkbox",28),t._uU(49," \u0420\u0430\u0437\u0440\u0435\u0448\u0438 \u043a\u043e\u043c\u0435\u043d\u0442\u0430\u0440\u0438 "),t.qZA(),t.TgZ(50,"mat-checkbox",29),t._uU(51," \u041f\u0440\u0435\u043f\u043e\u0440\u044a\u0447\u0430\u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f "),t.qZA()()()}if(2&n){const e=t.oxw();let i,o,a,s,c,d,_;t.Q6J("formGroup",e.articleForm),t.xp6(5),t.Q6J("ngIf",null==(i=e.articleForm.get("title"))?null:i.hasError("required")),t.xp6(1),t.Q6J("ngIf",null==(o=e.articleForm.get("title"))?null:o.hasError("maxlength")),t.xp6(5),t.Q6J("ngIf",null==(a=e.articleForm.get("excerpt"))?null:a.hasError("required")),t.xp6(1),t.Q6J("ngIf",null==(s=e.articleForm.get("excerpt"))?null:s.hasError("maxlength")),t.xp6(7),t.Q6J("ngForOf",e.categories),t.xp6(10),t.Q6J("modules",e.quillConfig),t.xp6(4),t.Q6J("currentImageUrl",null==(c=e.articleForm.get("featuredImageUrl"))?null:c.value),t.xp6(8),t.Q6J("ngIf",null==(d=e.articleForm.get("metaDescription"))?null:d.hasError("maxlength")),t.xp6(5),t.Q6J("ngIf",null==(_=e.articleForm.get("metaKeywords"))?null:_.hasError("maxlength"))}}let ct=(()=>{const r=class{constructor(i,o,a,s,c,d){this.fb=i,this.route=o,this.router=a,this.snackBar=s,this.dialog=c,this.articleService=d,this.isEditMode=!1,this.isLoading=!1,this.isSaving=!1,this.quillConfig={toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["clean"],["link","image","video"]]},this.categories=["\u0410\u0441\u0442\u0440\u043e\u043b\u043e\u0433\u0438\u044f","\u0422\u0430\u0440\u043e","\u041d\u0443\u043c\u0435\u0440\u043e\u043b\u043e\u0433\u0438\u044f","\u041c\u0435\u0434\u0438\u0442\u0430\u0446\u0438\u044f","\u041a\u0440\u0438\u0441\u0442\u0430\u043b\u0438","\u0414\u0443\u0445\u043e\u0432\u043d\u043e\u0441\u0442","\u0425\u043e\u0440\u043e\u0441\u043a\u043e\u043f\u0438","\u0421\u044a\u043d\u0438\u0449\u0430","\u0415\u043d\u0435\u0440\u0433\u0438\u044f","\u0427\u0430\u043a\u0440\u0438"],this.articleForm=this.createForm()}ngOnInit(){this.route.params.subscribe(i=>{i.id&&(this.isEditMode=!0,this.articleId=+i.id,this.loadArticle())})}createForm(){return this.fb.group({title:["",[C.kI.required,C.kI.maxLength(200)]],excerpt:["",[C.kI.required,C.kI.maxLength(500)]],content:[""],category:[""],tags:[""],featuredImageUrl:[""],metaDescription:["",C.kI.maxLength(160)],metaKeywords:["",C.kI.maxLength(500)],allowComments:[!0],isFeatured:[!1]})}loadArticle(){this.articleId&&(this.isLoading=!0,this.articleService.getArticleForEdit(this.articleId).subscribe({next:i=>{this.populateForm(i),this.isLoading=!1},error:i=>{console.error("Error loading article:",i),this.snackBar.open("\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.isLoading=!1}}))}populateForm(i){this.articleForm.patchValue({title:i.title,excerpt:i.excerpt,content:i.content,category:i.category,tags:i.tags.join(", "),featuredImageUrl:i.featuredImageUrl,metaDescription:i.metaDescription,metaKeywords:i.metaKeywords,allowComments:i.allowComments,isFeatured:i.isFeatured})}onSaveDraft(){this.saveArticle(!0)}onPublish(){this.saveArticle(!1)}saveArticle(i){if(this.articleForm.invalid)return void this.markFormGroupTouched();this.isSaving=!0;const o=this.articleForm.value,a=this.parseTags(o.tags);this.isEditMode&&this.articleId?this.articleService.updateArticle(this.articleId,{title:o.title,excerpt:o.excerpt,content:o.content,category:o.category,tags:a,featuredImageUrl:o.featuredImageUrl,metaDescription:o.metaDescription,metaKeywords:o.metaKeywords,allowComments:o.allowComments,isFeatured:o.isFeatured}).subscribe({next:c=>{this.snackBar.open("\u0421\u0442\u0430\u0442\u0438\u044f\u0442\u0430 \u0435 \u043e\u0431\u043d\u043e\u0432\u0435\u043d\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),i?this.router.navigate(["/articles/manage"]):this.publishArticle(c.id),this.isSaving=!1},error:c=>{console.error("Error updating article:",c),this.snackBar.open("\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.isSaving=!1}}):this.articleService.createArticle({title:o.title,excerpt:o.excerpt,content:o.content,category:o.category,tags:a,featuredImageUrl:o.featuredImageUrl,metaDescription:o.metaDescription,metaKeywords:o.metaKeywords,allowComments:o.allowComments,isFeatured:o.isFeatured,saveAsDraft:i}).subscribe({next:c=>{this.snackBar.open("\u0421\u0442\u0430\u0442\u0438\u044f\u0442\u0430 \u0435 \u0441\u044a\u0437\u0434\u0430\u0434\u0435\u043d\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.router.navigate(["/articles/manage"]),this.isSaving=!1},error:c=>{console.error("Error creating article:",c),this.snackBar.open("\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.isSaving=!1}})}publishArticle(i){this.articleService.publishArticle(i).subscribe({next:()=>{this.snackBar.open("\u0421\u0442\u0430\u0442\u0438\u044f\u0442\u0430 \u0435 \u043f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.router.navigate(["/articles/manage"])},error:o=>{console.error("Error publishing article:",o),this.snackBar.open("\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3})}})}parseTags(i){return i?i.split(",").map(o=>o.trim()).filter(o=>o.length>0):[]}markFormGroupTouched(){Object.keys(this.articleForm.controls).forEach(i=>{this.articleForm.get(i)?.markAsTouched()})}onCancel(){this.router.navigate(["/articles/manage"])}onPreview(){if(this.articleForm.invalid)return this.markFormGroupTouched(),void this.snackBar.open("\u041c\u043e\u043b\u044f, \u043f\u043e\u043f\u044a\u043b\u043d\u0435\u0442\u0435 \u0437\u0430\u0434\u044a\u043b\u0436\u0438\u0442\u0435\u043b\u043d\u0438\u0442\u0435 \u043f\u043e\u043b\u0435\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3});const i=this.articleForm.value,o=this.parseTags(i.tags),a={title:i.title,excerpt:i.excerpt,content:i.content,category:i.category,tags:o,featuredImageUrl:i.featuredImageUrl,author:"\u0412\u0438\u0435",estimatedReadTime:this.calculateReadTime(i.content)};this.dialog.open(pe,{width:"90vw",maxWidth:"800px",maxHeight:"90vh",data:a})}calculateReadTime(i){if(!i)return 1;const a=i.replace(/<[^>]*>/g,"").trim().split(/\s+/).length,c=Math.ceil(a/225);return Math.max(1,c)}onFeaturedImageUploaded(i){this.articleForm.patchValue({featuredImageUrl:i})}onFeaturedImageRemoved(){this.articleForm.patchValue({featuredImageUrl:""})}};let n=r;return r.\u0275fac=function(o){return new(o||r)(t.Y36(C.qu),t.Y36(y.gz),t.Y36(y.F0),t.Y36(Y.ux),t.Y36(x.uw),t.Y36(Q.n))},r.\u0275cmp=t.Xpm({type:r,selectors:[["app-article-editor"]],decls:23,vars:9,consts:[[1,"article-editor-container"],[1,"editor-card"],["class","loading-container",4,"ngIf"],[3,"formGroup",4,"ngIf"],["align","end"],["mat-button","",3,"disabled","click"],["mat-raised-button","","color","accent",3,"disabled","click"],["mat-raised-button","","color","primary",3,"disabled","click"],[1,"loading-container"],["mode","indeterminate"],[3,"formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","title","placeholder","\u0412\u044a\u0432\u0435\u0434\u0435\u0442\u0435 \u0437\u0430\u0433\u043b\u0430\u0432\u0438\u0435 \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430"],[4,"ngIf"],["matInput","","formControlName","excerpt","rows","3","placeholder","\u0412\u044a\u0432\u0435\u0434\u0435\u0442\u0435 \u043a\u0440\u0430\u0442\u043a\u043e \u043e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430"],["formControlName","category"],["value",""],[3,"value",4,"ngFor","ngForOf"],["matInput","","formControlName","tags","placeholder","\u0412\u044a\u0432\u0435\u0434\u0435\u0442\u0435 \u0442\u0430\u0433\u043e\u0432\u0435, \u0440\u0430\u0437\u0434\u0435\u043b\u0435\u043d\u0438 \u0441\u044a\u0441 \u0437\u0430\u043f\u0435\u0442\u0430\u044f"],[1,"content-editor"],[1,"editor-label"],["formControlName","content","placeholder","\u041d\u0430\u043f\u0438\u0448\u0435\u0442\u0435 \u0441\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430 \u0442\u0443\u043a...",1,"full-width",3,"modules"],[1,"featured-image-section"],["placeholder","\u041a\u0430\u0447\u0435\u0442\u0435 \u043e\u0441\u043d\u043e\u0432\u043d\u0430 \u0441\u043d\u0438\u043c\u043a\u0430 \u0437\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430",3,"currentImageUrl","imageUploaded","imageRemoved"],[1,"seo-section"],["matInput","","formControlName","metaDescription","rows","2","placeholder","\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435 \u0437\u0430 \u0442\u044a\u0440\u0441\u0430\u0447\u043a\u0438\u0442\u0435 (\u0434\u043e 160 \u0441\u0438\u043c\u0432\u043e\u043b\u0430)"],["matInput","","formControlName","metaKeywords","placeholder","\u043a\u043b\u044e\u0447\u043e\u0432\u0430 \u0434\u0443\u043c\u0430 1, \u043a\u043b\u044e\u0447\u043e\u0432\u0430 \u0434\u0443\u043c\u0430 2, \u043a\u043b\u044e\u0447\u043e\u0432\u0430 \u0434\u0443\u043c\u0430 3"],[1,"options-section"],["formControlName","allowComments"],["formControlName","isFeatured"],[3,"value"]],template:function(o,a){1&o&&(t.TgZ(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),t._uU(4),t.qZA()(),t.TgZ(5,"mat-card-content"),t.YNc(6,Me,4,0,"div",2),t.YNc(7,Se,52,10,"form",3),t.qZA(),t.TgZ(8,"mat-card-actions",4)(9,"button",5),t.NdJ("click",function(){return a.onCancel()}),t._uU(10," \u041e\u0442\u043a\u0430\u0437 "),t.qZA(),t.TgZ(11,"button",5),t.NdJ("click",function(){return a.onPreview()}),t.TgZ(12,"mat-icon"),t._uU(13,"visibility"),t.qZA(),t._uU(14," \u041f\u0440\u0435\u0433\u043b\u0435\u0434 "),t.qZA(),t.TgZ(15,"button",6),t.NdJ("click",function(){return a.onSaveDraft()}),t.TgZ(16,"mat-icon"),t._uU(17,"save"),t.qZA(),t._uU(18),t.qZA(),t.TgZ(19,"button",7),t.NdJ("click",function(){return a.onPublish()}),t.TgZ(20,"mat-icon"),t._uU(21,"publish"),t.qZA(),t._uU(22),t.qZA()()()()),2&o&&(t.xp6(4),t.hij(" ",a.isEditMode?"\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f":"\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u043e\u0432\u0430 \u0441\u0442\u0430\u0442\u0438\u044f"," "),t.xp6(2),t.Q6J("ngIf",a.isLoading),t.xp6(1),t.Q6J("ngIf",!a.isLoading),t.xp6(2),t.Q6J("disabled",a.isSaving),t.xp6(2),t.Q6J("disabled",a.isSaving||a.articleForm.invalid),t.xp6(4),t.Q6J("disabled",a.isSaving||a.articleForm.invalid),t.xp6(3),t.hij(" ",a.isSaving?"\u0417\u0430\u043f\u0438\u0441\u0432\u0430\u043d\u0435...":"\u0417\u0430\u043f\u0430\u0437\u0438 \u043a\u0430\u0442\u043e \u0447\u0435\u0440\u043d\u043e\u0432\u0430"," "),t.xp6(1),t.Q6J("disabled",a.isSaving||a.articleForm.invalid),t.xp6(3),t.hij(" ",a.isSaving?"\u041f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0435...":"\u041f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u0439"," "))},dependencies:[h.sg,h.O5,C._Y,C.Fj,C.JJ,C.JL,C.sg,C.u,tt,v.a8,v.hq,v.dn,v.dk,v.$j,v.n5,A.lW,E.Hw,D.Ou,T.KE,T.hX,T.bx,T.TO,it.Nt,J.gD,M.ey,nt.oG,Ae],styles:[".article-editor-container[_ngcontent-%COMP%]{max-width:1000px;margin:0 auto;padding:20px}.editor-card[_ngcontent-%COMP%]{margin-bottom:20px}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#666}.content-editor[_ngcontent-%COMP%]{margin-bottom:16px}.editor-label[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:500;color:#3f2f4e;margin-bottom:8px}.featured-image-section[_ngcontent-%COMP%]{margin-bottom:16px}.seo-section[_ngcontent-%COMP%]{margin:20px 0;padding:16px;background-color:#f8f9fa}.options-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin:20px 0}.options-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{margin-bottom:8px}  .ql-editor{min-height:300px;font-family:Roboto,sans-serif;font-size:14px;line-height:1.6}  .ql-toolbar{border-top:1px solid #ccc;border-left:1px solid #ccc;border-right:1px solid #ccc;background-color:#f8f9fa}  .ql-container{border-bottom:1px solid #ccc;border-left:1px solid #ccc;border-right:1px solid #ccc}  .ql-editor.ql-blank:before{color:#999;font-style:italic}  .ql-toolbar .ql-stroke{stroke:#3f2f4e}  .ql-toolbar .ql-fill{fill:#3f2f4e}  .ql-toolbar button:hover .ql-stroke{stroke:#d2a6d0}  .ql-toolbar button:hover .ql-fill{fill:#d2a6d0}  .ql-toolbar button.ql-active .ql-stroke{stroke:#67455c}  .ql-toolbar button.ql-active .ql-fill{fill:#67455c}@media (max-width: 768px){.article-editor-container[_ngcontent-%COMP%]{padding:10px}.editor-card[_ngcontent-%COMP%]{margin:0}  .ql-toolbar{padding:8px}  .ql-editor{min-height:200px}}mat-card-actions[_ngcontent-%COMP%]{padding:16px 24px;gap:8px}mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:8px}mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child{margin-left:0}.mat-form-field.ng-invalid[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%], .mat-form-field.ng-invalid[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{color:#f44336}  .ql-editor::-webkit-scrollbar{width:8px}  .ql-editor::-webkit-scrollbar-track{background:#f1f1f1}  .ql-editor::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:4px}  .ql-editor::-webkit-scrollbar-thumb:hover{background:#a8a8a8}"]}),n})();function qe(n,r){if(1&n&&(t.TgZ(0,"mat-option",11),t._uU(1),t.qZA()),2&n){const e=r.$implicit;t.Q6J("value",e.value),t.xp6(1),t.hij(" ",e.label," ")}}function De(n,r){1&n&&(t.TgZ(0,"div",12),t._UZ(1,"mat-progress-spinner",13),t.TgZ(2,"p"),t._uU(3,"\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u0438\u0442\u0435..."),t.qZA()())}function Ue(n,r){1&n&&(t.TgZ(0,"th",30),t._uU(1,"\u0417\u0430\u0433\u043b\u0430\u0432\u0438\u0435"),t.qZA())}function Ie(n,r){if(1&n&&(t.TgZ(0,"span",35),t._uU(1),t.qZA()),2&n){const e=t.oxw().$implicit;t.xp6(1),t.Oqu(e.category)}}function ke(n,r){if(1&n&&(t.TgZ(0,"td",31)(1,"div",32)(2,"span",33),t._uU(3),t.qZA(),t.YNc(4,Ie,2,1,"span",34),t.qZA()()),2&n){const e=r.$implicit;t.xp6(3),t.Oqu(e.title),t.xp6(1),t.Q6J("ngIf",e.category)}}function Fe(n,r){1&n&&(t.TgZ(0,"th",36),t._uU(1,"\u0421\u0442\u0430\u0442\u0443\u0441"),t.qZA())}function Be(n,r){if(1&n&&(t.TgZ(0,"td",37)(1,"mat-chip",38),t._uU(2),t.qZA()()),2&n){const e=r.$implicit,i=t.oxw(2);t.xp6(1),t.Q6J("color",i.getStatusColor(e.status)),t.xp6(1),t.hij(" ",e.status," ")}}function Ne(n,r){1&n&&(t.TgZ(0,"th",36),t._uU(1,"\u041a\u0430\u0442\u0435\u0433\u043e\u0440\u0438\u044f"),t.qZA())}function Ye(n,r){if(1&n&&(t.TgZ(0,"td",37),t._uU(1),t.qZA()),2&n){const e=r.$implicit;t.xp6(1),t.hij(" ",e.category||"\u0411\u0435\u0437 \u043a\u0430\u0442\u0435\u0433\u043e\u0440\u0438\u044f"," ")}}function Qe(n,r){1&n&&(t.TgZ(0,"th",30),t._uU(1,"\u0414\u0430\u0442\u0430 \u043d\u0430 \u043f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0435"),t.qZA())}function ze(n,r){if(1&n&&(t.TgZ(0,"div"),t._uU(1),t.ALo(2,"date"),t.qZA()),2&n){const e=t.oxw().$implicit;t.xp6(1),t.hij(" ",t.xi3(2,1,e.publishedAt,"dd.MM.yyyy HH:mm")," ")}}function He(n,r){1&n&&(t.TgZ(0,"span",41),t._uU(1,"\u041d\u0435 \u0435 \u043f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0430"),t.qZA())}function Je(n,r){if(1&n&&(t.TgZ(0,"td",37),t.YNc(1,ze,3,4,"div",39),t.YNc(2,He,2,0,"ng-template",null,40,t.W1O),t.qZA()),2&n){const e=r.$implicit,i=t.MAs(3);t.xp6(1),t.Q6J("ngIf",e.publishedAt)("ngIfElse",i)}}function Re(n,r){1&n&&(t.TgZ(0,"th",30),t._uU(1,"\u041f\u0440\u0435\u0433\u043b\u0435\u0434\u0438"),t.qZA())}function Le(n,r){if(1&n&&(t.TgZ(0,"td",37)(1,"div",42)(2,"mat-icon",43),t._uU(3,"visibility"),t.qZA(),t.TgZ(4,"span"),t._uU(5),t.qZA()()()),2&n){const e=r.$implicit;t.xp6(5),t.Oqu(e.readCount)}}function Ve(n,r){1&n&&(t.TgZ(0,"th",36),t._uU(1,"\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044f"),t.qZA())}function je(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"button",47),t.NdJ("click",function(){t.CHM(e);const o=t.oxw().article,a=t.oxw(3);return t.KtG(a.publishArticle(o.id))}),t.TgZ(1,"mat-icon"),t._uU(2,"publish"),t.qZA(),t.TgZ(3,"span"),t._uU(4,"\u041f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u0439"),t.qZA()()}}function Ge(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"button",47),t.NdJ("click",function(){t.CHM(e);const o=t.oxw().article,a=t.oxw(3);return t.KtG(a.unpublishArticle(o.id))}),t.TgZ(1,"mat-icon"),t._uU(2,"unpublished"),t.qZA(),t.TgZ(3,"span"),t._uU(4,"\u0421\u043a\u0440\u0438\u0439"),t.qZA()()}}function Ke(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"button",47),t.NdJ("click",function(){const a=t.CHM(e).article,s=t.oxw(3);return t.KtG(s.viewArticle(a))}),t.TgZ(1,"mat-icon"),t._uU(2,"visibility"),t.qZA(),t.TgZ(3,"span"),t._uU(4,"\u041f\u0440\u0435\u0433\u043b\u0435\u0434"),t.qZA()(),t.TgZ(5,"button",47),t.NdJ("click",function(){const a=t.CHM(e).article,s=t.oxw(3);return t.KtG(s.editArticle(a.id))}),t.TgZ(6,"mat-icon"),t._uU(7,"edit"),t.qZA(),t.TgZ(8,"span"),t._uU(9,"\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435"),t.qZA()(),t.YNc(10,je,5,0,"button",48),t.YNc(11,Ge,5,0,"button",48),t._UZ(12,"mat-divider"),t.TgZ(13,"button",49),t.NdJ("click",function(){const a=t.CHM(e).article,s=t.oxw(3);return t.KtG(s.deleteArticle(a.id,a.title))}),t.TgZ(14,"mat-icon"),t._uU(15,"delete"),t.qZA(),t.TgZ(16,"span"),t._uU(17,"\u0418\u0437\u0442\u0440\u0438\u0439"),t.qZA()()}if(2&n){const e=r.article,i=t.oxw(3);t.xp6(10),t.Q6J("ngIf",i.canPublish(e.status)),t.xp6(1),t.Q6J("ngIf",i.canUnpublish(e.status))}}const $e=function(n){return{article:n}};function We(n,r){if(1&n&&(t.TgZ(0,"td",37)(1,"button",44)(2,"mat-icon"),t._uU(3,"more_vert"),t.qZA()(),t.TgZ(4,"mat-menu",null,45),t.YNc(6,Ke,18,2,"ng-template",46),t.qZA()()),2&n){const e=r.$implicit,i=t.MAs(5);t.xp6(1),t.Q6J("matMenuTriggerFor",i)("matMenuTriggerData",t.VKq(2,$e,e))}}function Xe(n,r){1&n&&t._UZ(0,"tr",50)}function ti(n,r){1&n&&t._UZ(0,"tr",51)}function ei(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"div",52)(1,"mat-icon",53),t._uU(2,"article"),t.qZA(),t.TgZ(3,"h3"),t._uU(4,"\u041d\u044f\u043c\u0430 \u043d\u0430\u043c\u0435\u0440\u0435\u043d\u0438 \u0441\u0442\u0430\u0442\u0438\u0438"),t.qZA(),t.TgZ(5,"p"),t._uU(6,'\u0417\u0430\u043f\u043e\u0447\u043d\u0435\u0442\u0435 \u0434\u0430 \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u0442\u0435 \u0441\u044a\u0434\u044a\u0440\u0436\u0430\u043d\u0438\u0435, \u043a\u0430\u0442\u043e \u043d\u0430\u0442\u0438\u0441\u043d\u0435\u0442\u0435 \u0431\u0443\u0442\u043e\u043d\u0430 "\u041d\u043e\u0432\u0430 \u0441\u0442\u0430\u0442\u0438\u044f".'),t.qZA(),t.TgZ(7,"button",8),t.NdJ("click",function(){t.CHM(e);const o=t.oxw(2);return t.KtG(o.createNewArticle())}),t.TgZ(8,"mat-icon"),t._uU(9,"add"),t.qZA(),t._uU(10," \u0421\u044a\u0437\u0434\u0430\u0439 \u043f\u044a\u0440\u0432\u0430\u0442\u0430 \u0441\u0438 \u0441\u0442\u0430\u0442\u0438\u044f "),t.qZA()()}}const ii=function(){return[5,10,25,50]};function ni(n,r){if(1&n){const e=t.EpF();t.TgZ(0,"mat-paginator",54),t.NdJ("page",function(o){t.CHM(e);const a=t.oxw(2);return t.KtG(a.onPageChange(o))}),t.qZA()}if(2&n){const e=t.oxw(2);t.Q6J("length",e.totalCount)("pageSize",e.pageSize)("pageSizeOptions",t.DdM(3,ii))}}function oi(n,r){if(1&n&&(t.TgZ(0,"div",14)(1,"table",15),t.ynx(2,16),t.YNc(3,Ue,2,0,"th",17),t.YNc(4,ke,5,2,"td",18),t.BQk(),t.ynx(5,19),t.YNc(6,Fe,2,0,"th",20),t.YNc(7,Be,3,2,"td",21),t.BQk(),t.ynx(8,22),t.YNc(9,Ne,2,0,"th",20),t.YNc(10,Ye,2,1,"td",21),t.BQk(),t.ynx(11,23),t.YNc(12,Qe,2,0,"th",17),t.YNc(13,Je,4,2,"td",21),t.BQk(),t.ynx(14,24),t.YNc(15,Re,2,0,"th",17),t.YNc(16,Le,6,1,"td",21),t.BQk(),t.ynx(17,25),t.YNc(18,Ve,2,0,"th",20),t.YNc(19,We,7,4,"td",21),t.BQk(),t.YNc(20,Xe,1,0,"tr",26),t.YNc(21,ti,1,0,"tr",27),t.qZA(),t.YNc(22,ei,11,0,"div",28),t.YNc(23,ni,1,4,"mat-paginator",29),t.qZA()),2&n){const e=t.oxw();t.xp6(1),t.Q6J("dataSource",e.dataSource),t.xp6(19),t.Q6J("matHeaderRowDef",e.displayedColumns),t.xp6(1),t.Q6J("matRowDefColumns",e.displayedColumns),t.xp6(1),t.Q6J("ngIf",0===e.dataSource.data.length),t.xp6(1),t.Q6J("ngIf",e.dataSource.data.length>0)}}let ri=(()=>{const r=class{constructor(i,o,a,s){this.articleService=i,this.router=o,this.dialog=a,this.snackBar=s,this.displayedColumns=["title","status","category","publishedAt","readCount","actions"],this.dataSource=new b.by,this.isLoading=!1,this.totalCount=0,this.pageSize=10,this.currentPage=0,this.selectedStatus="",this.statusOptions=[{value:"",label:"\u0412\u0441\u0438\u0447\u043a\u0438 \u0441\u0442\u0430\u0442\u0438\u0438"},{value:"Draft",label:"\u0427\u0435\u0440\u043d\u043e\u0432\u0438"},{value:"Published",label:"\u041f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0438"},{value:"Scheduled",label:"\u041f\u043b\u0430\u043d\u0438\u0440\u0430\u043d\u0438"},{value:"Archived",label:"\u0410\u0440\u0445\u0438\u0432\u0438\u0440\u0430\u043d\u0438"}]}ngOnInit(){this.loadArticles()}ngAfterViewInit(){this.dataSource.paginator=this.paginator,this.dataSource.sort=this.sort}loadArticles(){this.isLoading=!0,this.articleService.getMyArticles(this.currentPage+1,this.pageSize,this.selectedStatus).subscribe({next:i=>{this.dataSource.data=i.articles.map(o=>({id:o.id,title:o.title,status:this.getStatusLabel(o.status||"Draft"),category:o.category,publishedAt:o.publishedAt?new Date(o.publishedAt):void 0,lastSavedAt:o.lastSavedAt?new Date(o.lastSavedAt):void 0,readCount:o.readCount||0,estimatedReadTime:o.estimatedReadTime||0})),this.totalCount=i.totalCount,this.isLoading=!1},error:i=>{console.error("Error loading articles:",i),this.snackBar.open("\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u0438\u0442\u0435","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.isLoading=!1}})}onStatusFilterChange(){this.currentPage=0,this.loadArticles()}onPageChange(i){this.currentPage=i.pageIndex,this.pageSize=i.pageSize,this.loadArticles()}createNewArticle(){this.router.navigate(["/articles/editor"])}editArticle(i){this.router.navigate(["/articles/editor",i])}viewArticle(i){this.snackBar.open("\u0424\u0443\u043d\u043a\u0446\u0438\u044f\u0442\u0430 \u0437\u0430 \u043f\u0440\u0435\u0433\u043b\u0435\u0434 \u0449\u0435 \u0431\u044a\u0434\u0435 \u0434\u043e\u0431\u0430\u0432\u0435\u043d\u0430 \u0441\u043a\u043e\u0440\u043e","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3})}publishArticle(i){this.articleService.publishArticle(i).subscribe({next:()=>{this.snackBar.open("\u0421\u0442\u0430\u0442\u0438\u044f\u0442\u0430 \u0435 \u043f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.loadArticles()},error:o=>{console.error("Error publishing article:",o),this.snackBar.open("\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3})}})}unpublishArticle(i){this.articleService.unpublishArticle(i).subscribe({next:()=>{this.snackBar.open("\u0421\u0442\u0430\u0442\u0438\u044f\u0442\u0430 \u0435 \u0441\u043a\u0440\u0438\u0442\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.loadArticles()},error:o=>{console.error("Error unpublishing article:",o),this.snackBar.open("\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0441\u043a\u0440\u0438\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3})}})}deleteArticle(i,o){this.dialog.open(ai,{width:"400px",data:{title:o}}).afterClosed().subscribe(s=>{s&&this.articleService.deleteArticle(i).subscribe({next:()=>{this.snackBar.open("\u0421\u0442\u0430\u0442\u0438\u044f\u0442\u0430 \u0435 \u0438\u0437\u0442\u0440\u0438\u0442\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3}),this.loadArticles()},error:c=>{console.error("Error deleting article:",c),this.snackBar.open("\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0438\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430","\u0417\u0430\u0442\u0432\u043e\u0440\u0438",{duration:3e3})}})})}getStatusLabel(i){return{Draft:"\u0427\u0435\u0440\u043d\u043e\u0432\u0430",Published:"\u041f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0430",Scheduled:"\u041f\u043b\u0430\u043d\u0438\u0440\u0430\u043d\u0430",Archived:"\u0410\u0440\u0445\u0438\u0432\u0438\u0440\u0430\u043d\u0430"}[i]||i}getStatusColor(i){return{\u0427\u0435\u0440\u043d\u043e\u0432\u0430:"accent",\u041f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0430:"primary",\u041f\u043b\u0430\u043d\u0438\u0440\u0430\u043d\u0430:"warn",\u0410\u0440\u0445\u0438\u0432\u0438\u0440\u0430\u043d\u0430:""}[i]||""}canPublish(i){return"\u0427\u0435\u0440\u043d\u043e\u0432\u0430"===i||"\u0410\u0440\u0445\u0438\u0432\u0438\u0440\u0430\u043d\u0430"===i}canUnpublish(i){return"\u041f\u0443\u0431\u043b\u0438\u043a\u0443\u0432\u0430\u043d\u0430"===i}};let n=r;return r.\u0275fac=function(o){return new(o||r)(t.Y36(Q.n),t.Y36(y.F0),t.Y36(x.uw),t.Y36(Y.ux))},r.\u0275cmp=t.Xpm({type:r,selectors:[["app-article-management"]],viewQuery:function(o,a){if(1&o&&(t.Gf(R.NW,5),t.Gf(L,5)),2&o){let s;t.iGM(s=t.CRH())&&(a.paginator=s.first),t.iGM(s=t.CRH())&&(a.sort=s.first)}},decls:22,vars:4,consts:[[1,"article-management-container"],[1,"management-card"],[1,"toolbar"],[1,"toolbar-left"],["appearance","outline",1,"status-filter"],[3,"value","valueChange","selectionChange"],[3,"value",4,"ngFor","ngForOf"],[1,"toolbar-right"],["mat-raised-button","","color","primary",3,"click"],["class","loading-container",4,"ngIf"],["class","table-container",4,"ngIf"],[3,"value"],[1,"loading-container"],["mode","indeterminate"],[1,"table-container"],["mat-table","","matSort","",1,"articles-table",3,"dataSource"],["matColumnDef","title"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","","class","title-cell",4,"matCellDef"],["matColumnDef","status"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","category"],["matColumnDef","publishedAt"],["matColumnDef","readCount"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["class","no-data",4,"ngIf"],["showFirstLastButtons","",3,"length","pageSize","pageSizeOptions","page",4,"ngIf"],["mat-header-cell","","mat-sort-header",""],["mat-cell","",1,"title-cell"],[1,"title-content"],[1,"article-title"],["class","article-category",4,"ngIf"],[1,"article-category"],["mat-header-cell",""],["mat-cell",""],["selected","",3,"color"],[4,"ngIf","ngIfElse"],["notPublished",""],[1,"not-published"],[1,"read-stats"],[1,"stats-icon"],["mat-icon-button","",3,"matMenuTriggerFor","matMenuTriggerData"],["actionMenu","matMenu"],["matMenuContent",""],["mat-menu-item","",3,"click"],["mat-menu-item","",3,"click",4,"ngIf"],["mat-menu-item","",1,"delete-action",3,"click"],["mat-header-row",""],["mat-row",""],[1,"no-data"],[1,"no-data-icon"],["showFirstLastButtons","",3,"length","pageSize","pageSizeOptions","page"]],template:function(o,a){1&o&&(t.TgZ(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),t._uU(4,"\u0423\u043f\u0440\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u043d\u0430 \u0441\u0442\u0430\u0442\u0438\u0438"),t.qZA(),t.TgZ(5,"mat-card-subtitle"),t._uU(6,"\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u0439\u0442\u0435, \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u0439\u0442\u0435 \u0438 \u0443\u043f\u0440\u0430\u0432\u043b\u044f\u0432\u0430\u0439\u0442\u0435 \u0432\u0430\u0448\u0438\u0442\u0435 \u0441\u0442\u0430\u0442\u0438\u0438"),t.qZA()(),t.TgZ(7,"mat-card-content")(8,"div",2)(9,"div",3)(10,"mat-form-field",4)(11,"mat-label"),t._uU(12,"\u0424\u0438\u043b\u0442\u044a\u0440 \u043f\u043e \u0441\u0442\u0430\u0442\u0443\u0441"),t.qZA(),t.TgZ(13,"mat-select",5),t.NdJ("valueChange",function(c){return a.selectedStatus=c})("selectionChange",function(){return a.onStatusFilterChange()}),t.YNc(14,qe,2,2,"mat-option",6),t.qZA()()(),t.TgZ(15,"div",7)(16,"button",8),t.NdJ("click",function(){return a.createNewArticle()}),t.TgZ(17,"mat-icon"),t._uU(18,"add"),t.qZA(),t._uU(19," \u041d\u043e\u0432\u0430 \u0441\u0442\u0430\u0442\u0438\u044f "),t.qZA()()(),t.YNc(20,De,4,0,"div",9),t.YNc(21,oi,24,5,"div",10),t.qZA()()()),2&o&&(t.xp6(13),t.Q6J("value",a.selectedStatus),t.xp6(1),t.Q6J("ngForOf",a.statusOptions),t.xp6(6),t.Q6J("ngIf",a.isLoading),t.xp6(1),t.Q6J("ngIf",!a.isLoading))},dependencies:[h.sg,h.O5,v.a8,v.dn,v.dk,v.$j,v.n5,A.lW,A.RK,E.Hw,B.HS,D.Ou,T.KE,T.hX,J.gD,M.ey,b.BZ,b.fO,b.as,b.w1,b.Dz,b.nj,b.ge,b.ev,b.XQ,b.Gk,R.NW,L,Rt,U.VK,U.OP,U.KA,U.p6,ut.d,h.uU],styles:[".article-management-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.management-card[_ngcontent-%COMP%]{margin-bottom:20px}.toolbar[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px;flex-wrap:wrap;gap:16px}.toolbar-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.toolbar-right[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.status-filter[_ngcontent-%COMP%]{min-width:200px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#666}.table-container[_ngcontent-%COMP%]{width:100%;overflow-x:auto}.articles-table[_ngcontent-%COMP%]{width:100%;min-width:800px}.title-cell[_ngcontent-%COMP%]{max-width:300px}.title-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.article-title[_ngcontent-%COMP%]{font-weight:500;color:#3f2f4e;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.article-category[_ngcontent-%COMP%]{font-size:12px;color:#666;background-color:#e6dbec;padding:2px 8px;border-radius:12px;display:inline-block;max-width:-moz-fit-content;max-width:fit-content}.not-published[_ngcontent-%COMP%]{color:#999;font-style:italic}.read-stats[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.stats-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#666}.delete-action[_ngcontent-%COMP%]{color:#f44336}.delete-action[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#f44336}.no-data[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;color:#666}.no-data-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;color:#ccc;margin-bottom:16px}.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:16px 0 8px;color:#3f2f4e}.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:24px;max-width:400px;margin-left:auto;margin-right:auto}  .mat-chip.mat-primary{background-color:#67455c;color:#fff}  .mat-chip.mat-accent{background-color:#d2a6d0;color:#3f2f4e}  .mat-chip.mat-warn{background-color:#ff9800;color:#fff}.mat-table[_ngcontent-%COMP%]{background:transparent}.mat-header-cell[_ngcontent-%COMP%]{color:#3f2f4e;font-weight:600;border-bottom:2px solid #e6dbec}.mat-cell[_ngcontent-%COMP%]{border-bottom:1px solid #f0f0f0}.mat-row[_ngcontent-%COMP%]:hover{background-color:#fafafa}@media (max-width: 768px){.article-management-container[_ngcontent-%COMP%]{padding:10px}.toolbar[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.toolbar-left[_ngcontent-%COMP%], .toolbar-right[_ngcontent-%COMP%]{justify-content:center}.status-filter[_ngcontent-%COMP%]{min-width:auto;width:100%}.table-container[_ngcontent-%COMP%]{margin:0 -10px}.articles-table[_ngcontent-%COMP%]{min-width:600px}.title-cell[_ngcontent-%COMP%]{max-width:200px}}@media (max-width: 480px){.articles-table[_ngcontent-%COMP%]{min-width:500px}.title-cell[_ngcontent-%COMP%]{max-width:150px}.article-title[_ngcontent-%COMP%]{font-size:14px}.article-category[_ngcontent-%COMP%]{font-size:10px;padding:1px 6px}}.mat-menu-panel[_ngcontent-%COMP%]{min-width:180px}.mat-menu-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.mat-menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:0}.mat-paginator[_ngcontent-%COMP%]{background:transparent;border-top:1px solid #e0e0e0;margin-top:16px}.mat-card-header[_ngcontent-%COMP%]{margin-bottom:16px}.mat-card-title[_ngcontent-%COMP%]{color:#3f2f4e;font-size:24px;font-weight:500}.mat-card-subtitle[_ngcontent-%COMP%]{color:#666;margin-top:4px}"]}),n})(),ai=(()=>{const r=class{constructor(i,o){this.dialogRef=i,this.data=o}onCancel(){this.dialogRef.close(!1)}onConfirm(){this.dialogRef.close(!0)}};let n=r;return r.\u0275fac=function(o){return new(o||r)(t.Y36(x.so),t.Y36(x.WI))},r.\u0275cmp=t.Xpm({type:r,selectors:[["app-confirm-delete-dialog"]],decls:15,vars:1,consts:[["mat-dialog-title",""],["align","end"],["mat-button","",3,"click"],["mat-raised-button","","color","warn",3,"click"]],template:function(o,a){1&o&&(t.TgZ(0,"h2",0),t._uU(1,"\u041f\u043e\u0442\u0432\u044a\u0440\u0436\u0434\u0435\u043d\u0438\u0435 \u0437\u0430 \u0438\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435"),t.qZA(),t.TgZ(2,"mat-dialog-content")(3,"p"),t._uU(4,'\u0421\u0438\u0433\u0443\u0440\u043d\u0438 \u043b\u0438 \u0441\u0442\u0435, \u0447\u0435 \u0438\u0441\u043a\u0430\u0442\u0435 \u0434\u0430 \u0438\u0437\u0442\u0440\u0438\u0435\u0442\u0435 \u0441\u0442\u0430\u0442\u0438\u044f\u0442\u0430 "'),t.TgZ(5,"strong"),t._uU(6),t.qZA(),t._uU(7,'"?'),t.qZA(),t.TgZ(8,"p"),t._uU(9,"\u0422\u043e\u0432\u0430 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0431\u044a\u0434\u0435 \u043e\u0442\u043c\u0435\u043d\u0435\u043d\u043e."),t.qZA()(),t.TgZ(10,"mat-dialog-actions",1)(11,"button",2),t.NdJ("click",function(){return a.onCancel()}),t._uU(12,"\u041e\u0442\u043a\u0430\u0437"),t.qZA(),t.TgZ(13,"button",3),t.NdJ("click",function(){return a.onConfirm()}),t._uU(14,"\u0418\u0437\u0442\u0440\u0438\u0439"),t.qZA()()),2&o&&(t.xp6(6),t.Oqu(a.data.title))},dependencies:[A.lW,x.uh,x.xY,x.H8],encapsulation:2}),n})();const si=[{path:"manage",component:ri},{path:"editor",component:ct},{path:"editor/:id",component:ct},{path:":slug",component:ue}];let ui=(()=>{const r=class{};let n=r;return r.\u0275fac=function(o){return new(o||r)},r.\u0275mod=t.oAB({type:r}),r.\u0275inj=t.cJS({providers:[Q.n],imports:[h.ez,y.Bz.forChild(si),C.UX,C.u5,It.forRoot(),v.QW,A.ot,E.Ps,B.Hi,D.Cq,et.Cv,T.lN,it.c,J.LD,nt.p9,b.p0,R.TU,Lt,x.Is,Y.ZX,Vt.Nh,U.Tx,jt.AV,ut.t]}),n})()}}]);