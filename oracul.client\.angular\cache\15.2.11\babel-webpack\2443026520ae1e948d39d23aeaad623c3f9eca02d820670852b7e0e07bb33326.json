{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { AppComponent } from './app.component';\nimport { ThemeService } from './core/theme/theme.service';\ndescribe('AppComponent', () => {\n  let component;\n  let mockThemeService;\n  beforeEach(() => {\n    const themeServiceSpy = jasmine.createSpyObj('ThemeService', ['getCurrentTheme']);\n    TestBed.configureTestingModule({\n      providers: [{\n        provide: ThemeService,\n        useValue: themeServiceSpy\n      }]\n    });\n    mockThemeService = TestBed.inject(ThemeService);\n    // Create component manually without rendering template\n    component = new AppComponent(mockThemeService);\n  });\n  it('should create the app', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize theme on init', () => {\n    spyOn(component, 'ngOnInit').and.callThrough();\n    component.ngOnInit();\n    expect(component.ngOnInit).toHaveBeenCalled();\n  });\n});", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,4BAA4B;AAEzDC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5B,IAAIC,SAAuB;EAC3B,IAAIC,gBAA8C;EAElDC,UAAU,CAAC,MAAK;IACd,MAAMC,eAAe,GAAGC,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,CAAC,CAAC;IAEjFT,OAAO,CAACU,sBAAsB,CAAC;MAC7BC,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEV,YAAY;QAAEW,QAAQ,EAAEN;MAAe,CAAE;KAEvD,CAAC;IAEFF,gBAAgB,GAAGL,OAAO,CAACc,MAAM,CAACZ,YAAY,CAAiC;IAE/E;IACAE,SAAS,GAAG,IAAIH,YAAY,CAACI,gBAAgB,CAAC;EAChD,CAAC,CAAC;EAEFU,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/BC,MAAM,CAACZ,SAAS,CAAC,CAACa,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzCG,KAAK,CAACd,SAAS,EAAE,UAAU,CAAC,CAACe,GAAG,CAACC,WAAW,EAAE;IAE9ChB,SAAS,CAACiB,QAAQ,EAAE;IAEpBL,MAAM,CAACZ,SAAS,CAACiB,QAAQ,CAAC,CAACC,gBAAgB,EAAE;EAC/C,CAAC,CAAC;AACJ,CAAC,CAAC", "names": ["TestBed", "AppComponent", "ThemeService", "describe", "component", "mockThemeService", "beforeEach", "themeServiceSpy", "jasmine", "createSpyObj", "configureTestingModule", "providers", "provide", "useValue", "inject", "it", "expect", "toBeTruthy", "spyOn", "and", "callThrough", "ngOnInit", "toHaveBeenCalled"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\app.component.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\r\nimport { AppComponent } from './app.component';\r\nimport { ThemeService } from './core/theme/theme.service';\r\n\r\ndescribe('AppComponent', () => {\r\n  let component: AppComponent;\r\n  let mockThemeService: jasmine.SpyObj<ThemeService>;\r\n\r\n  beforeEach(() => {\r\n    const themeServiceSpy = jasmine.createSpyObj('ThemeService', ['getCurrentTheme']);\r\n\r\n    TestBed.configureTestingModule({\r\n      providers: [\r\n        { provide: ThemeService, useValue: themeServiceSpy }\r\n      ]\r\n    });\r\n\r\n    mockThemeService = TestBed.inject(ThemeService) as jasmine.SpyObj<ThemeService>;\r\n\r\n    // Create component manually without rendering template\r\n    component = new AppComponent(mockThemeService);\r\n  });\r\n\r\n  it('should create the app', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n\r\n  it('should initialize theme on init', () => {\r\n    spyOn(component, 'ngOnInit').and.callThrough();\r\n\r\n    component.ngOnInit();\r\n\r\n    expect(component.ngOnInit).toHaveBeenCalled();\r\n  });\r\n});\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}