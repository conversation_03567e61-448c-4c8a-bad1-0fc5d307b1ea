{"ast": null, "code": "import { Subject, takeUntil, combineLatest } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../shared/services/article.service\";\nimport * as i3 from \"../../auth/services/auth.service\";\nimport * as i4 from \"../../core/i18n/translation.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/progress-bar\";\nfunction ArticleViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.t.common.loading, \"...\");\n  }\n}\nfunction ArticleViewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"mat-card\", 5)(2, \"mat-card-content\")(3, \"div\", 6)(4, \"mat-icon\", 7);\n    i0.ɵɵtext(5, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_1_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.goBack());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.t.errors.general);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.t.common.back, \" \");\n  }\n}\nfunction ArticleViewComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"img\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r5.article.featuredImageUrl || ctx_r5.article.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r5.article.title);\n  }\n}\nfunction ArticleViewComponent_div_2_div_32_mat_chip_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(tag_r10);\n  }\n}\nfunction ArticleViewComponent_div_2_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, ArticleViewComponent_div_2_div_32_mat_chip_1_Template, 2, 1, \"mat-chip\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.article.tags);\n  }\n}\nfunction ArticleViewComponent_div_2_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleViewComponent_div_2_div_36_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"trending_up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"mat-progress-bar\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u041F\\u0440\\u043E\\u0447\\u0435\\u0442\\u043E\\u0445\\u0442\\u0435 \", ctx_r11.getContentCompletionPercentage(), \"% \\u043E\\u0442 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r11.getContentCompletionPercentage());\n  }\n}\nfunction ArticleViewComponent_div_2_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"mat-card\", 38)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\");\n    i0.ɵɵtemplate(10, ArticleViewComponent_div_2_div_36_div_10_Template, 7, 2, \"div\", 39);\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 40)(14, \"div\", 41)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"auto_stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 41)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 41)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"bookmark_added\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 41)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 42)(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"mat-card-actions\", 43)(40, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_div_36_Template_button_click_40_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.onLoginClick());\n    });\n    i0.ɵɵelementStart(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_div_36_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onRegisterClick());\n    });\n    i0.ɵɵelementStart(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_div_36_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onCloseAuthPrompt());\n    });\n    i0.ɵɵelementStart(49, \"mat-icon\");\n    i0.ɵɵtext(50, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.articles.preview.signInToRead, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.articles.preview.joinCommunity, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.isAuthenticated);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.valuableContent);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.unlimitedAccess);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.connectWithAstrologers);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.saveArticles);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.notifications);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.socialProof);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.auth.signIn, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.auth.createAccount, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.common.close, \" \");\n  }\n}\nfunction ArticleViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.goBack());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"arrow_back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.shareArticle());\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"share\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(9, ArticleViewComponent_div_2_div_9_Template, 2, 2, \"div\", 14);\n    i0.ɵɵelementStart(10, \"div\", 15)(11, \"mat-chip\", 16);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 17)(14, \"span\", 18);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 19);\n    i0.ɵɵtext(17, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 20);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 19);\n    i0.ɵɵtext(21, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 21);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 19);\n    i0.ɵɵtext(25, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 22);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"h1\", 23);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 24);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, ArticleViewComponent_div_2_div_32_Template, 2, 1, \"div\", 25);\n    i0.ɵɵelementStart(33, \"div\", 26);\n    i0.ɵɵelement(34, \"div\", 27);\n    i0.ɵɵtemplate(35, ArticleViewComponent_div_2_div_35_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, ArticleViewComponent_div_2_div_36_Template, 52, 12, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.t.common.back);\n    i0.ɵɵadvance(4);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.t.articles.share);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.article.featuredImageUrl || ctx_r2.article.imageUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.article.category);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.t.articles.author, \": \", ctx_r2.article.author, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(ctx_r2.article.publishedAt));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.article.readTime, \" \", ctx_r2.t.home.minRead, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.article.readCount, \" \", ctx_r2.t.home.views, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.article.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.article.excerpt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.article.tags && ctx_r2.article.tags.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"blurred\", ctx_r2.shouldShowBlur());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getContentPreview(), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shouldShowBlur());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shouldShowAuthPrompt());\n  }\n}\nexport class ArticleViewComponent {\n  constructor(route, router, articleService, authService, t) {\n    this.route = route;\n    this.router = router;\n    this.articleService = articleService;\n    this.authService = authService;\n    this.t = t;\n    this.article = null;\n    this.isLoading = true;\n    this.isAuthenticated = false;\n    this.showAuthPrompt = false;\n    this.error = null;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    // Get authentication state and article slug\n    combineLatest([this.authService.isAuthenticated$, this.route.params]).pipe(takeUntil(this.destroy$)).subscribe(([isAuthenticated, params]) => {\n      this.isAuthenticated = isAuthenticated;\n      const slug = params['slug'];\n      if (slug) {\n        this.loadArticle(slug);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadArticle(slug) {\n    this.isLoading = true;\n    this.error = null;\n    this.articleService.getArticleBySlug(slug).subscribe({\n      next: article => {\n        this.article = article;\n        this.isLoading = false;\n        // Record article view for analytics\n        this.articleService.recordArticleView(article.id).subscribe();\n        // Show auth prompt for anonymous users if content is limited\n        if (!this.isAuthenticated && (!article.content || article.content.length < 500)) {\n          this.showAuthPrompt = true;\n        }\n      },\n      error: error => {\n        this.error = error.message;\n        this.isLoading = false;\n        console.error('Error loading article:', error);\n      }\n    });\n  }\n  onLoginClick() {\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: this.router.url\n      }\n    });\n  }\n  onRegisterClick() {\n    this.router.navigate(['/register'], {\n      queryParams: {\n        returnUrl: this.router.url\n      }\n    });\n  }\n  onCloseAuthPrompt() {\n    this.showAuthPrompt = false;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  shareArticle() {\n    if (navigator.share && this.article) {\n      navigator.share({\n        title: this.article.title,\n        text: this.article.excerpt,\n        url: window.location.href\n      }).catch(console.error);\n    } else if (this.article) {\n      // Fallback: copy URL to clipboard\n      navigator.clipboard.writeText(window.location.href).then(() => {\n        // Could show a toast notification here\n        console.log('URL copied to clipboard');\n      }).catch(console.error);\n    }\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString('bg-BG', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  getContentPreview() {\n    if (!this.article?.content) return '';\n    // For authenticated users, show full content\n    if (this.isAuthenticated) {\n      return this.article.content;\n    }\n    // For anonymous users, show limited content\n    const content = this.article.content;\n    const words = content.split(' ');\n    // Show first 150 words or first 3 paragraphs, whichever is shorter\n    const paragraphs = content.split('\\n\\n');\n    const firstThreeParagraphs = paragraphs.slice(0, 3).join('\\n\\n');\n    const first150Words = words.slice(0, 150).join(' ');\n    return firstThreeParagraphs.length < first150Words.length ? firstThreeParagraphs : first150Words;\n  }\n  shouldShowBlur() {\n    return !this.isAuthenticated && !!this.article?.content && this.article.content.length > 500;\n  }\n  shouldShowAuthPrompt() {\n    return !this.isAuthenticated && !!this.article?.content && this.article.content.length > 300;\n  }\n  getContentCompletionPercentage() {\n    if (!this.article?.content || this.isAuthenticated) return 100;\n    const previewLength = this.getContentPreview().length;\n    const totalLength = this.article.content.length;\n    return Math.round(previewLength / totalLength * 100);\n  }\n  static {\n    this.ɵfac = function ArticleViewComponent_Factory(t) {\n      return new (t || ArticleViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ArticleService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.TranslationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ArticleViewComponent,\n      selectors: [[\"app-article-view\"]],\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"article-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [1, \"error-card\"], [1, \"error-content\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"article-container\"], [1, \"article-header\"], [\"mat-icon-button\", \"\", 1, \"back-button\", 3, \"click\"], [1, \"article-actions\"], [\"mat-icon-button\", \"\", 3, \"click\"], [\"class\", \"featured-image\", 4, \"ngIf\"], [1, \"article-meta\"], [1, \"category-chip\"], [1, \"meta-info\"], [1, \"author\"], [1, \"separator\"], [1, \"date\"], [1, \"read-time\"], [1, \"read-count\"], [1, \"article-title\"], [1, \"article-excerpt\"], [\"class\", \"article-tags\", 4, \"ngIf\"], [1, \"article-content-wrapper\"], [1, \"article-content\", 3, \"innerHTML\"], [\"class\", \"blur-overlay\", 4, \"ngIf\"], [\"class\", \"auth-prompt-overlay\", 4, \"ngIf\"], [1, \"featured-image\"], [1, \"article-featured-image\", 3, \"src\", \"alt\"], [1, \"article-tags\"], [\"class\", \"tag-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"tag-chip\"], [1, \"blur-overlay\"], [1, \"fade-gradient\"], [1, \"auth-prompt-overlay\"], [1, \"auth-prompt-card\"], [\"class\", \"reading-progress\", 4, \"ngIf\"], [1, \"auth-benefits\"], [1, \"benefit-item\"], [1, \"social-proof\"], [1, \"auth-actions\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"reading-progress\"], [1, \"progress-info\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"]],\n      template: function ArticleViewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ArticleViewComponent_div_0_Template, 4, 1, \"div\", 0);\n          i0.ɵɵtemplate(1, ArticleViewComponent_div_1_Template, 14, 3, \"div\", 1);\n          i0.ɵɵtemplate(2, ArticleViewComponent_div_2_Template, 37, 19, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.article && !ctx.isLoading && !ctx.error);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.MatChip, i10.MatProgressSpinner, i11.MatProgressBar],\n      styles: [\".loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  min-height: 400px;\\r\\n  padding: 2rem;\\r\\n}\\r\\n\\r\\n.error-card[_ngcontent-%COMP%] {\\r\\n  max-width: 500px;\\r\\n  margin: 2rem auto;\\r\\n}\\r\\n\\r\\n.error-content[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.error-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 3rem;\\r\\n  color: var(--theme-error);\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\n\\r\\n.article-container[_ngcontent-%COMP%] {\\r\\n  max-width: 800px;\\r\\n  margin: 0 auto;\\r\\n  padding: 1rem;\\r\\n  background: linear-gradient(\\r\\n    135deg,\\r\\n    var(--theme-background) 0%,\\r\\n    var(--theme-accent-light) 100%\\r\\n  );\\r\\n  min-height: 100vh;\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.article-container[_ngcontent-%COMP%]::before {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  height: 200px;\\r\\n  background: linear-gradient(\\r\\n    135deg,\\r\\n    rgba(210, 166, 208, 0.1) 0%,\\r\\n    transparent 100%\\r\\n  );\\r\\n  pointer-events: none;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.article-container[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  z-index: 1;\\r\\n}\\r\\n\\r\\n\\r\\n.article-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  margin-bottom: 1rem;\\r\\n  padding: 0.5rem 0;\\r\\n}\\r\\n\\r\\n.back-button[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.article-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 0.5rem;\\r\\n}\\r\\n\\r\\n\\r\\n.featured-image[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  margin-bottom: 2rem;\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n  box-shadow:\\r\\n    0 8px 32px rgba(103, 69, 92, 0.15),\\r\\n    0 4px 16px rgba(210, 166, 208, 0.1);\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.featured-image[_ngcontent-%COMP%]::after {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: linear-gradient(\\r\\n    135deg,\\r\\n    rgba(210, 166, 208, 0.1) 0%,\\r\\n    transparent 50%,\\r\\n    rgba(103, 69, 92, 0.1) 100%\\r\\n  );\\r\\n  pointer-events: none;\\r\\n}\\r\\n\\r\\n.article-featured-image[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 300px;\\r\\n  object-fit: cover;\\r\\n  display: block;\\r\\n  transition: transform 0.3s ease;\\r\\n}\\r\\n\\r\\n.featured-image[_ngcontent-%COMP%]:hover   .article-featured-image[_ngcontent-%COMP%] {\\r\\n  transform: scale(1.02);\\r\\n}\\r\\n\\r\\n\\r\\n.article-meta[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 1.5rem;\\r\\n}\\r\\n\\r\\n.category-chip[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-accent);\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\n.meta-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  align-items: center;\\r\\n  gap: 0.5rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  font-size: 0.9rem;\\r\\n}\\r\\n\\r\\n.separator[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-text-disabled);\\r\\n}\\r\\n\\r\\n\\r\\n.article-title[_ngcontent-%COMP%] {\\r\\n  font-size: 2.5rem;\\r\\n  font-weight: 700;\\r\\n  line-height: 1.2;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\n\\r\\n.article-excerpt[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  line-height: 1.6;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin-bottom: 2rem;\\r\\n  font-style: italic;\\r\\n  padding: 1rem;\\r\\n  background: var(--theme-accent-light);\\r\\n  border-radius: 8px;\\r\\n  border-left: 4px solid var(--theme-accent);\\r\\n}\\r\\n\\r\\n\\r\\n.article-tags[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 0.5rem;\\r\\n  margin-bottom: 2rem;\\r\\n}\\r\\n\\r\\n.tag-chip[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-primary-light);\\r\\n  color: white;\\r\\n  font-size: 0.8rem;\\r\\n}\\r\\n\\r\\n\\r\\n.article-content-wrapper[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  margin-bottom: 2rem;\\r\\n  overflow: hidden;\\r\\n  border-radius: 12px;\\r\\n}\\r\\n\\r\\n.article-content[_ngcontent-%COMP%] {\\r\\n  font-size: 1.1rem;\\r\\n  line-height: 1.8;\\r\\n  color: var(--theme-text-primary);\\r\\n  padding: 1.5rem;\\r\\n  background: var(--theme-surface);\\r\\n  border-radius: 12px;\\r\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n.article-content-wrapper.blurred[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%] {\\r\\n  filter: blur(2px);\\r\\n  pointer-events: none;\\r\\n  -webkit-user-select: none;\\r\\n          user-select: none;\\r\\n  transition: filter 0.3s ease;\\r\\n}\\r\\n\\r\\n\\r\\n.blur-overlay[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  pointer-events: none;\\r\\n  z-index: 1;\\r\\n}\\r\\n\\r\\n.fade-gradient[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  bottom: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  height: 250px;\\r\\n  background: linear-gradient(\\r\\n    to bottom,\\r\\n    transparent 0%,\\r\\n    rgba(230, 219, 236, 0.3) 20%,\\r\\n    rgba(230, 219, 236, 0.6) 40%,\\r\\n    rgba(230, 219, 236, 0.8) 60%,\\r\\n    rgba(230, 219, 236, 0.95) 80%,\\r\\n    var(--theme-accent-light) 100%\\r\\n  );\\r\\n  border-radius: 0 0 12px 12px;\\r\\n}\\r\\n\\r\\n\\r\\n.blur-overlay[_ngcontent-%COMP%]::before {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: radial-gradient(\\r\\n    ellipse at center bottom,\\r\\n    rgba(210, 166, 208, 0.1) 0%,\\r\\n    transparent 70%\\r\\n  );\\r\\n  pointer-events: none;\\r\\n}\\r\\n\\r\\n\\r\\n.auth-prompt-overlay[_ngcontent-%COMP%] {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.6);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  z-index: 1000;\\r\\n  padding: 1rem;\\r\\n}\\r\\n\\r\\n.auth-prompt-card[_ngcontent-%COMP%] {\\r\\n  max-width: 500px;\\r\\n  width: 100%;\\r\\n  background: var(--theme-surface);\\r\\n  border-radius: 16px;\\r\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n.auth-prompt-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 0.5rem;\\r\\n  color: var(--theme-primary);\\r\\n  font-size: 1.5rem;\\r\\n}\\r\\n\\r\\n.auth-prompt-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-accent);\\r\\n}\\r\\n\\r\\n.auth-benefits[_ngcontent-%COMP%] {\\r\\n  margin: 1.5rem 0;\\r\\n}\\r\\n\\r\\n.benefit-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 0.75rem;\\r\\n  margin-bottom: 1rem;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.benefit-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-accent);\\r\\n  font-size: 1.2rem;\\r\\n}\\r\\n\\r\\n.auth-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 1rem;\\r\\n  flex-wrap: wrap;\\r\\n  justify-content: center;\\r\\n  padding: 1rem;\\r\\n}\\r\\n\\r\\n.auth-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\r\\n  min-width: 120px;\\r\\n}\\r\\n\\r\\n\\r\\n.reading-progress[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 1.5rem;\\r\\n  padding: 1rem;\\r\\n  background: var(--theme-accent-light);\\r\\n  border-radius: 8px;\\r\\n  border: 1px solid var(--theme-accent);\\r\\n}\\r\\n\\r\\n.progress-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: space-between;\\r\\n  margin-bottom: 0.5rem;\\r\\n  font-weight: 500;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.progress-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-accent);\\r\\n}\\r\\n\\r\\n\\r\\n.social-proof[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 0.5rem;\\r\\n  margin-top: 1.5rem;\\r\\n  padding: 1rem;\\r\\n  background: linear-gradient(135deg, var(--theme-accent-light) 0%, var(--theme-background) 100%);\\r\\n  border-radius: 8px;\\r\\n  font-style: italic;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.social-proof[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-accent);\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .article-container[_ngcontent-%COMP%] {\\r\\n    padding: 0.5rem;\\r\\n  }\\r\\n\\r\\n  .article-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n\\r\\n  .article-excerpt[_ngcontent-%COMP%] {\\r\\n    font-size: 1.1rem;\\r\\n    padding: 0.75rem;\\r\\n  }\\r\\n\\r\\n  .article-content[_ngcontent-%COMP%] {\\r\\n    font-size: 1rem;\\r\\n  }\\r\\n\\r\\n  .meta-info[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: flex-start;\\r\\n    gap: 0.25rem;\\r\\n  }\\r\\n\\r\\n  .separator[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n\\r\\n  .auth-actions[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n  }\\r\\n\\r\\n  .auth-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.article-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary);\\r\\n  margin: 2rem 0 1rem 0;\\r\\n  font-size: 1.5rem;\\r\\n}\\r\\n\\r\\n.article-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-primary-light);\\r\\n  margin: 1.5rem 0 0.75rem 0;\\r\\n  font-size: 1.3rem;\\r\\n}\\r\\n\\r\\n.article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 1.5rem;\\r\\n}\\r\\n\\r\\n.article-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\r\\n  border-left: 4px solid var(--theme-accent);\\r\\n  padding-left: 1rem;\\r\\n  margin: 1.5rem 0;\\r\\n  font-style: italic;\\r\\n  color: var(--theme-text-secondary);\\r\\n  background: var(--theme-accent-light);\\r\\n  padding: 1rem;\\r\\n  border-radius: 0 8px 8px 0;\\r\\n}\\r\\n\\r\\n.article-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .article-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 1.5rem;\\r\\n  padding-left: 2rem;\\r\\n}\\r\\n\\r\\n.article-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 0.5rem;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,EAAEC,aAAa,QAAQ,MAAM;;;;;;;;;;;;;;;ICDxDC,8BAAiD;IAC/CA,8BAA2B;IAC3BA,yBAAG;IAAAA,YAAyB;IAAAA,iBAAI;;;;IAA7BA,eAAyB;IAAzBA,yDAAyB;;;;;;IAI9BA,8BAAyD;IAIpBA,qBAAK;IAAAA,iBAAW;IAC7CA,0BAAI;IAAAA,YAAsB;IAAAA,iBAAK;IAC/BA,yBAAG;IAAAA,YAAW;IAAAA,iBAAI;IAClBA,kCAA6D;IAAnBA;MAAAA;MAAA;MAAA,OAASA,8BAAQ;IAAA,EAAC;IAC1DA,iCAAU;IAAAA,2BAAU;IAAAA,iBAAW;IAC/BA,aACF;IAAAA,iBAAS;;;;IALLA,eAAsB;IAAtBA,6CAAsB;IACvBA,eAAW;IAAXA,kCAAW;IAGZA,eACF;IADEA,qDACF;;;;;IAsBNA,+BAAiF;IAC/EA,0BAEoC;IACtCA,iBAAM;;;;IAHCA,eAAoD;IAApDA,kGAAoD;;;;;IA2BzDA,oCAA4D;IAAAA,YAAS;IAAAA,iBAAW;;;;IAApBA,eAAS;IAATA,6BAAS;;;;;IADvEA,+BAA0E;IACxEA,6FAAgF;IAClFA,iBAAM;;;;IADsBA,eAAe;IAAfA,6CAAe;;;;;IAQzCA,+BAAmD;IACjDA,0BAAiC;IACnCA,iBAAM;;;;;IAkBFA,+BAAuD;IAE7CA,YAA8D;IAAAA,iBAAO;IAC3EA,gCAAU;IAAAA,2BAAW;IAAAA,iBAAW;IAElCA,uCAImB;IACrBA,iBAAM;;;;IARIA,eAA8D;IAA9DA,mMAA8D;IAKpEA,eAA0C;IAA1CA,gEAA0C;;;;;;IArBpDA,+BAAgE;IAI9CA,oBAAI;IAAAA,iBAAW;IACzBA,YACF;IAAAA,iBAAiB;IACjBA,yCAAmB;IACjBA,YACF;IAAAA,iBAAoB;IAGtBA,wCAAkB;IAEhBA,qFAUM;IAENA,0BAAG;IAAAA,aAAwC;IAAAA,iBAAI;IAE/CA,gCAA2B;IAEbA,6BAAY;IAAAA,iBAAW;IACjCA,6BAAM;IAAAA,aAAwC;IAAAA,iBAAO;IAEvDA,gCAA0B;IACdA,2BAAU;IAAAA,iBAAW;IAC/BA,6BAAM;IAAAA,aAA+C;IAAAA,iBAAO;IAE9DA,gCAA0B;IACdA,+BAAc;IAAAA,iBAAW;IACnCA,6BAAM;IAAAA,aAAqC;IAAAA,iBAAO;IAEpDA,gCAA0B;IACdA,8BAAa;IAAAA,iBAAW;IAClCA,6BAAM;IAAAA,aAAsC;IAAAA,iBAAO;IAKvDA,gCAA0B;IACdA,sBAAK;IAAAA,iBAAW;IAC1BA,6BAAM;IAAAA,aAAoC;IAAAA,iBAAO;IAIrDA,6CAAuC;IACKA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IAChEA,iCAAU;IAAAA,sBAAK;IAAAA,iBAAW;IAC1BA,aACF;IAAAA,iBAAS;IACTA,mCAAqE;IAA5BA;MAAAA;MAAA;MAAA,OAASA,wCAAiB;IAAA,EAAC;IAClEA,iCAAU;IAAAA,2BAAU;IAAAA,iBAAW;IAC/BA,aACF;IAAAA,iBAAS;IACTA,mCAAiD;IAA9BA;MAAAA;MAAA;MAAA,OAASA,0CAAmB;IAAA,EAAC;IAC9CA,iCAAU;IAAAA,sBAAK;IAAAA,iBAAW;IAC1BA,aACF;IAAAA,iBAAS;;;;IA7DPA,eACF;IADEA,uEACF;IAEEA,eACF;IADEA,wEACF;IAK+BA,eAAsB;IAAtBA,8CAAsB;IAYlDA,eAAwC;IAAxCA,+DAAwC;IAKjCA,eAAwC;IAAxCA,+DAAwC;IAIxCA,eAA+C;IAA/CA,sEAA+C;IAI/CA,eAAqC;IAArCA,4DAAqC;IAIrCA,eAAsC;IAAtCA,6DAAsC;IAOxCA,eAAoC;IAApCA,2DAAoC;IAO1CA,eACF;IADEA,qDACF;IAGEA,eACF;IADEA,4DACF;IAGEA,eACF;IADEA,sDACF;;;;;;IA3HRA,8BAAuE;IAGvBA;MAAAA;MAAA;MAAA,OAASA,+BAAQ;IAAA,EAAC;IAC5DA,gCAAU;IAAAA,0BAAU;IAAAA,iBAAW;IAGjCA,+BAA6B;IACHA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IAC9CA,gCAAU;IAAAA,qBAAK;IAAAA,iBAAW;IAMhCA,4EAIM;IAGNA,gCAA0B;IACQA,aAAsB;IAAAA,iBAAW;IACjEA,gCAAuB;IACAA,aAA6C;IAAAA,iBAAO;IACzEA,iCAAwB;IAAAA,uBAAC;IAAAA,iBAAO;IAChCA,iCAAmB;IAAAA,aAAqC;IAAAA,iBAAO;IAC/DA,iCAAwB;IAAAA,uBAAC;IAAAA,iBAAO;IAChCA,iCAAwB;IAAAA,aAA2C;IAAAA,iBAAO;IAC1EA,iCAAwB;IAAAA,uBAAC;IAAAA,iBAAO;IAChCA,iCAAyB;IAAAA,aAA0C;IAAAA,iBAAO;IAK9EA,+BAA0B;IAAAA,aAAmB;IAAAA,iBAAK;IAGlDA,8BAA2B;IAAAA,aAAqB;IAAAA,iBAAI;IAGpDA,8EAEM;IAGNA,gCAAwE;IACtEA,2BAAqE;IAGrEA,8EAEM;IACRA,iBAAM;IAGNA,gFAqEM;IACRA,iBAAM;;;;IA5H6DA,eAAiC;IAAjCA,kDAAiC;IAK7CA,eAAoC;IAApCA,qDAAoC;IAO5DA,eAAkD;IAAlDA,iFAAkD;IAQ7CA,eAAsB;IAAtBA,6CAAsB;IAE/BA,eAA6C;IAA7CA,oFAA6C;IAE/CA,eAAqC;IAArCA,mEAAqC;IAEhCA,eAA2C;IAA3CA,kFAA2C;IAE1CA,eAA0C;IAA1CA,iFAA0C;IAK7CA,eAAmB;IAAnBA,0CAAmB;IAGlBA,eAAqB;IAArBA,4CAAqB;IAGrBA,eAA6C;IAA7CA,4EAA6C;IAKnCA,eAAkC;IAAlCA,kDAAkC;IACxCA,eAAiC;IAAjCA,yEAAiC;IAGnCA,eAAsB;IAAtBA,8CAAsB;IAMjBA,eAA4B;IAA5BA,oDAA4B;;;ADpEhE,OAAM,MAAOC,oBAAoB;EAS/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACzBC,CAAqB;IAJpB,UAAK,GAALJ,KAAK;IACL,WAAM,GAANC,MAAM;IACN,mBAAc,GAAdC,cAAc;IACd,gBAAW,GAAXC,WAAW;IACZ,MAAC,GAADC,CAAC;IAbV,YAAO,GAAmB,IAAI;IAC9B,cAAS,GAAG,IAAI;IAChB,oBAAe,GAAG,KAAK;IACvB,mBAAc,GAAG,KAAK;IACtB,UAAK,GAAkB,IAAI;IAEnB,aAAQ,GAAG,IAAIV,OAAO,EAAQ;EAQnC;EAEHW,QAAQ;IACN;IACAT,aAAa,CAAC,CACZ,IAAI,CAACO,WAAW,CAACG,gBAAgB,EACjC,IAAI,CAACN,KAAK,CAACO,MAAM,CAClB,CAAC,CAACC,IAAI,CACLb,SAAS,CAAC,IAAI,CAACc,QAAQ,CAAC,CACzB,CAACC,SAAS,CAAC,CAAC,CAACC,eAAe,EAAEJ,MAAM,CAAC,KAAI;MACxC,IAAI,CAACI,eAAe,GAAGA,eAAe;MACtC,MAAMC,IAAI,GAAGL,MAAM,CAAC,MAAM,CAAC;MAC3B,IAAIK,IAAI,EAAE;QACR,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC;;IAE1B,CAAC,CAAC;EACJ;EAEAE,WAAW;IACT,IAAI,CAACL,QAAQ,CAACM,IAAI,EAAE;IACpB,IAAI,CAACN,QAAQ,CAACO,QAAQ,EAAE;EAC1B;EAEQH,WAAW,CAACD,IAAY;IAC9B,IAAI,CAACK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAChB,cAAc,CAACiB,gBAAgB,CAACP,IAAI,CAAC,CAACF,SAAS,CAAC;MACnDK,IAAI,EAAGK,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACH,SAAS,GAAG,KAAK;QAEtB;QACA,IAAI,CAACf,cAAc,CAACmB,iBAAiB,CAACD,OAAO,CAACE,EAAE,CAAC,CAACZ,SAAS,EAAE;QAE7D;QACA,IAAI,CAAC,IAAI,CAACC,eAAe,KAAK,CAACS,OAAO,CAACG,OAAO,IAAIH,OAAO,CAACG,OAAO,CAACC,MAAM,GAAG,GAAG,CAAC,EAAE;UAC/E,IAAI,CAACC,cAAc,GAAG,IAAI;;MAE9B,CAAC;MACDP,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAGA,KAAK,CAACQ,OAAO;QAC1B,IAAI,CAACT,SAAS,GAAG,KAAK;QACtBU,OAAO,CAACT,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAU,YAAY;IACV,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BC,WAAW,EAAE;QAAEC,SAAS,EAAE,IAAI,CAAC9B,MAAM,CAAC+B;MAAG;KAC1C,CAAC;EACJ;EAEAC,eAAe;IACb,IAAI,CAAChC,MAAM,CAAC4B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCC,WAAW,EAAE;QAAEC,SAAS,EAAE,IAAI,CAAC9B,MAAM,CAAC+B;MAAG;KAC1C,CAAC;EACJ;EAEAE,iBAAiB;IACf,IAAI,CAACT,cAAc,GAAG,KAAK;EAC7B;EAEAU,MAAM;IACJ,IAAI,CAAClC,MAAM,CAAC4B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEAO,YAAY;IACV,IAAIC,SAAS,CAACC,KAAK,IAAI,IAAI,CAAClB,OAAO,EAAE;MACnCiB,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,IAAI,CAACnB,OAAO,CAACmB,KAAK;QACzBC,IAAI,EAAE,IAAI,CAACpB,OAAO,CAACqB,OAAO;QAC1BT,GAAG,EAAEU,MAAM,CAACC,QAAQ,CAACC;OACtB,CAAC,CAACC,KAAK,CAAClB,OAAO,CAACT,KAAK,CAAC;KACxB,MAAM,IAAI,IAAI,CAACE,OAAO,EAAE;MACvB;MACAiB,SAAS,CAACS,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;QAC5D;QACArB,OAAO,CAACsB,GAAG,CAAC,yBAAyB,CAAC;MACxC,CAAC,CAAC,CAACJ,KAAK,CAAClB,OAAO,CAACT,KAAK,CAAC;;EAE3B;EAEAgC,UAAU,CAACC,IAAU;IACnB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAChDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAC,iBAAiB;IACf,IAAI,CAAC,IAAI,CAACrC,OAAO,EAAEG,OAAO,EAAE,OAAO,EAAE;IAErC;IACA,IAAI,IAAI,CAACZ,eAAe,EAAE;MACxB,OAAO,IAAI,CAACS,OAAO,CAACG,OAAO;;IAG7B;IACA,MAAMA,OAAO,GAAG,IAAI,CAACH,OAAO,CAACG,OAAO;IACpC,MAAMmC,KAAK,GAAGnC,OAAO,CAACoC,KAAK,CAAC,GAAG,CAAC;IAEhC;IACA,MAAMC,UAAU,GAAGrC,OAAO,CAACoC,KAAK,CAAC,MAAM,CAAC;IACxC,MAAME,oBAAoB,GAAGD,UAAU,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IAChE,MAAMC,aAAa,GAAGN,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IAEnD,OAAOF,oBAAoB,CAACrC,MAAM,GAAGwC,aAAa,CAACxC,MAAM,GACrDqC,oBAAoB,GACpBG,aAAa;EACnB;EAEAC,cAAc;IACZ,OAAO,CAAC,IAAI,CAACtD,eAAe,IAAI,CAAC,CAAC,IAAI,CAACS,OAAO,EAAEG,OAAO,IAAI,IAAI,CAACH,OAAO,CAACG,OAAO,CAACC,MAAM,GAAG,GAAG;EAC9F;EAEA0C,oBAAoB;IAClB,OAAO,CAAC,IAAI,CAACvD,eAAe,IAAI,CAAC,CAAC,IAAI,CAACS,OAAO,EAAEG,OAAO,IAAI,IAAI,CAACH,OAAO,CAACG,OAAO,CAACC,MAAM,GAAG,GAAG;EAC9F;EAEA2C,8BAA8B;IAC5B,IAAI,CAAC,IAAI,CAAC/C,OAAO,EAAEG,OAAO,IAAI,IAAI,CAACZ,eAAe,EAAE,OAAO,GAAG;IAE9D,MAAMyD,aAAa,GAAG,IAAI,CAACX,iBAAiB,EAAE,CAACjC,MAAM;IACrD,MAAM6C,WAAW,GAAG,IAAI,CAACjD,OAAO,CAACG,OAAO,CAACC,MAAM;IAE/C,OAAO8C,IAAI,CAACC,KAAK,CAAEH,aAAa,GAAGC,WAAW,GAAI,GAAG,CAAC;EACxD;;;uBAhJWvE,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAA0E;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCZjC/E,qEAGM;UAGNA,sEAcM;UAGNA,uEA+HM;;;UAtJ0BA,oCAAe;UAMjBA,eAAyB;UAAzBA,kDAAyB;UAiBvBA,eAAqC;UAArCA,kEAAqC", "names": ["Subject", "takeUntil", "combineLatest", "i0", "ArticleViewComponent", "constructor", "route", "router", "articleService", "authService", "t", "ngOnInit", "isAuthenticated$", "params", "pipe", "destroy$", "subscribe", "isAuthenticated", "slug", "loadArticle", "ngOnDestroy", "next", "complete", "isLoading", "error", "getArticleBySlug", "article", "recordArticleView", "id", "content", "length", "showAuthPrompt", "message", "console", "onLoginClick", "navigate", "queryParams", "returnUrl", "url", "onRegisterClick", "onCloseAuthPrompt", "goBack", "shareArticle", "navigator", "share", "title", "text", "excerpt", "window", "location", "href", "catch", "clipboard", "writeText", "then", "log", "formatDate", "date", "Date", "toLocaleDateString", "year", "month", "day", "getContentPreview", "words", "split", "paragraphs", "firstThreeParagraphs", "slice", "join", "first150Words", "shouldShowBlur", "shouldShowAuthPrompt", "getContentCompletionPercentage", "previewLength", "totalLength", "Math", "round", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\article-view\\article-view.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\article-view\\article-view.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subject, takeUntil, combineLatest } from 'rxjs';\r\nimport { Article } from '../../shared/models/article.models';\r\nimport { ArticleService } from '../../shared/services/article.service';\r\nimport { AuthService } from '../../auth/services/auth.service';\r\nimport { TranslationService } from '../../core/i18n/translation.service';\r\n\r\n@Component({\r\n  selector: 'app-article-view',\r\n  templateUrl: './article-view.component.html',\r\n  styleUrls: ['./article-view.component.css']\r\n})\r\nexport class ArticleViewComponent implements OnInit, OnDestroy {\r\n  article: Article | null = null;\r\n  isLoading = true;\r\n  isAuthenticated = false;\r\n  showAuthPrompt = false;\r\n  error: string | null = null;\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private articleService: ArticleService,\r\n    private authService: AuthService,\r\n    public t: TranslationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Get authentication state and article slug\r\n    combineLatest([\r\n      this.authService.isAuthenticated$,\r\n      this.route.params\r\n    ]).pipe(\r\n      takeUntil(this.destroy$)\r\n    ).subscribe(([isAuthenticated, params]) => {\r\n      this.isAuthenticated = isAuthenticated;\r\n      const slug = params['slug'];\r\n      if (slug) {\r\n        this.loadArticle(slug);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private loadArticle(slug: string): void {\r\n    this.isLoading = true;\r\n    this.error = null;\r\n\r\n    this.articleService.getArticleBySlug(slug).subscribe({\r\n      next: (article) => {\r\n        this.article = article;\r\n        this.isLoading = false;\r\n\r\n        // Record article view for analytics\r\n        this.articleService.recordArticleView(article.id).subscribe();\r\n\r\n        // Show auth prompt for anonymous users if content is limited\r\n        if (!this.isAuthenticated && (!article.content || article.content.length < 500)) {\r\n          this.showAuthPrompt = true;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.error = error.message;\r\n        this.isLoading = false;\r\n        console.error('Error loading article:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  onLoginClick(): void {\r\n    this.router.navigate(['/login'], {\r\n      queryParams: { returnUrl: this.router.url }\r\n    });\r\n  }\r\n\r\n  onRegisterClick(): void {\r\n    this.router.navigate(['/register'], {\r\n      queryParams: { returnUrl: this.router.url }\r\n    });\r\n  }\r\n\r\n  onCloseAuthPrompt(): void {\r\n    this.showAuthPrompt = false;\r\n  }\r\n\r\n  goBack(): void {\r\n    this.router.navigate(['/home']);\r\n  }\r\n\r\n  shareArticle(): void {\r\n    if (navigator.share && this.article) {\r\n      navigator.share({\r\n        title: this.article.title,\r\n        text: this.article.excerpt,\r\n        url: window.location.href\r\n      }).catch(console.error);\r\n    } else if (this.article) {\r\n      // Fallback: copy URL to clipboard\r\n      navigator.clipboard.writeText(window.location.href).then(() => {\r\n        // Could show a toast notification here\r\n        console.log('URL copied to clipboard');\r\n      }).catch(console.error);\r\n    }\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    return new Date(date).toLocaleDateString('bg-BG', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  }\r\n\r\n  getContentPreview(): string {\r\n    if (!this.article?.content) return '';\r\n\r\n    // For authenticated users, show full content\r\n    if (this.isAuthenticated) {\r\n      return this.article.content;\r\n    }\r\n\r\n    // For anonymous users, show limited content\r\n    const content = this.article.content;\r\n    const words = content.split(' ');\r\n\r\n    // Show first 150 words or first 3 paragraphs, whichever is shorter\r\n    const paragraphs = content.split('\\n\\n');\r\n    const firstThreeParagraphs = paragraphs.slice(0, 3).join('\\n\\n');\r\n    const first150Words = words.slice(0, 150).join(' ');\r\n\r\n    return firstThreeParagraphs.length < first150Words.length\r\n      ? firstThreeParagraphs\r\n      : first150Words;\r\n  }\r\n\r\n  shouldShowBlur(): boolean {\r\n    return !this.isAuthenticated && !!this.article?.content && this.article.content.length > 500;\r\n  }\r\n\r\n  shouldShowAuthPrompt(): boolean {\r\n    return !this.isAuthenticated && !!this.article?.content && this.article.content.length > 300;\r\n  }\r\n\r\n  getContentCompletionPercentage(): number {\r\n    if (!this.article?.content || this.isAuthenticated) return 100;\r\n\r\n    const previewLength = this.getContentPreview().length;\r\n    const totalLength = this.article.content.length;\r\n\r\n    return Math.round((previewLength / totalLength) * 100);\r\n  }\r\n}\r\n", "<!-- Loading State -->\r\n<div class=\"loading-container\" *ngIf=\"isLoading\">\r\n  <mat-spinner></mat-spinner>\r\n  <p>{{ t.common.loading }}...</p>\r\n</div>\r\n\r\n<!-- Error State -->\r\n<div class=\"error-container\" *ngIf=\"error && !isLoading\">\r\n  <mat-card class=\"error-card\">\r\n    <mat-card-content>\r\n      <div class=\"error-content\">\r\n        <mat-icon class=\"error-icon\">error</mat-icon>\r\n        <h2>{{ t.errors.general }}</h2>\r\n        <p>{{ error }}</p>\r\n        <button mat-raised-button color=\"primary\" (click)=\"goBack()\">\r\n          <mat-icon>arrow_back</mat-icon>\r\n          {{ t.common.back }}\r\n        </button>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n\r\n<!-- Article Content -->\r\n<div class=\"article-container\" *ngIf=\"article && !isLoading && !error\">\r\n  <!-- Article Header -->\r\n  <div class=\"article-header\">\r\n    <button mat-icon-button class=\"back-button\" (click)=\"goBack()\" [attr.aria-label]=\"t.common.back\">\r\n      <mat-icon>arrow_back</mat-icon>\r\n    </button>\r\n\r\n    <div class=\"article-actions\">\r\n      <button mat-icon-button (click)=\"shareArticle()\" [attr.aria-label]=\"t.articles.share\">\r\n        <mat-icon>share</mat-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Featured Image -->\r\n  <div class=\"featured-image\" *ngIf=\"article.featuredImageUrl || article.imageUrl\">\r\n    <img [src]=\"article.featuredImageUrl || article.imageUrl\"\r\n         [alt]=\"article.title\"\r\n         class=\"article-featured-image\">\r\n  </div>\r\n\r\n  <!-- Article Meta -->\r\n  <div class=\"article-meta\">\r\n    <mat-chip class=\"category-chip\">{{ article.category }}</mat-chip>\r\n    <div class=\"meta-info\">\r\n      <span class=\"author\">{{ t.articles.author }}: {{ article.author }}</span>\r\n      <span class=\"separator\">•</span>\r\n      <span class=\"date\">{{ formatDate(article.publishedAt) }}</span>\r\n      <span class=\"separator\">•</span>\r\n      <span class=\"read-time\">{{ article.readTime }} {{ t.home.minRead }}</span>\r\n      <span class=\"separator\">•</span>\r\n      <span class=\"read-count\">{{ article.readCount }} {{ t.home.views }}</span>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Article Title -->\r\n  <h1 class=\"article-title\">{{ article.title }}</h1>\r\n\r\n  <!-- Article Excerpt -->\r\n  <p class=\"article-excerpt\">{{ article.excerpt }}</p>\r\n\r\n  <!-- Article Tags -->\r\n  <div class=\"article-tags\" *ngIf=\"article.tags && article.tags.length > 0\">\r\n    <mat-chip *ngFor=\"let tag of article.tags\" class=\"tag-chip\">{{ tag }}</mat-chip>\r\n  </div>\r\n\r\n  <!-- Article Content -->\r\n  <div class=\"article-content-wrapper\" [class.blurred]=\"shouldShowBlur()\">\r\n    <div class=\"article-content\" [innerHTML]=\"getContentPreview()\"></div>\r\n\r\n    <!-- Blur Overlay for Anonymous Users -->\r\n    <div class=\"blur-overlay\" *ngIf=\"shouldShowBlur()\">\r\n      <div class=\"fade-gradient\"></div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Authentication Prompt Overlay -->\r\n  <div class=\"auth-prompt-overlay\" *ngIf=\"shouldShowAuthPrompt()\">\r\n    <mat-card class=\"auth-prompt-card\">\r\n      <mat-card-header>\r\n        <mat-card-title>\r\n          <mat-icon>lock</mat-icon>\r\n          {{ t.articles.preview.signInToRead }}\r\n        </mat-card-title>\r\n        <mat-card-subtitle>\r\n          {{ t.articles.preview.joinCommunity }}\r\n        </mat-card-subtitle>\r\n      </mat-card-header>\r\n\r\n      <mat-card-content>\r\n        <!-- Reading Progress -->\r\n        <div class=\"reading-progress\" *ngIf=\"!isAuthenticated\">\r\n          <div class=\"progress-info\">\r\n            <span>Прочетохте {{ getContentCompletionPercentage() }}% от статията</span>\r\n            <mat-icon>trending_up</mat-icon>\r\n          </div>\r\n          <mat-progress-bar\r\n            mode=\"determinate\"\r\n            [value]=\"getContentCompletionPercentage()\"\r\n            color=\"accent\">\r\n          </mat-progress-bar>\r\n        </div>\r\n\r\n        <p>{{ t.articles.preview.valuableContent }}</p>\r\n\r\n        <div class=\"auth-benefits\">\r\n          <div class=\"benefit-item\">\r\n            <mat-icon>auto_stories</mat-icon>\r\n            <span>{{ t.articles.preview.unlimitedAccess }}</span>\r\n          </div>\r\n          <div class=\"benefit-item\">\r\n            <mat-icon>psychology</mat-icon>\r\n            <span>{{ t.articles.preview.connectWithAstrologers }}</span>\r\n          </div>\r\n          <div class=\"benefit-item\">\r\n            <mat-icon>bookmark_added</mat-icon>\r\n            <span>{{ t.articles.preview.saveArticles }}</span>\r\n          </div>\r\n          <div class=\"benefit-item\">\r\n            <mat-icon>notifications</mat-icon>\r\n            <span>{{ t.articles.preview.notifications }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Social Proof -->\r\n        <div class=\"social-proof\">\r\n          <mat-icon>group</mat-icon>\r\n          <span>{{ t.articles.preview.socialProof }}</span>\r\n        </div>\r\n      </mat-card-content>\r\n\r\n      <mat-card-actions class=\"auth-actions\">\r\n        <button mat-raised-button color=\"primary\" (click)=\"onLoginClick()\">\r\n          <mat-icon>login</mat-icon>\r\n          {{ t.auth.signIn }}\r\n        </button>\r\n        <button mat-raised-button color=\"accent\" (click)=\"onRegisterClick()\">\r\n          <mat-icon>person_add</mat-icon>\r\n          {{ t.auth.createAccount }}\r\n        </button>\r\n        <button mat-button (click)=\"onCloseAuthPrompt()\">\r\n          <mat-icon>close</mat-icon>\r\n          {{ t.common.close }}\r\n        </button>\r\n      </mat-card-actions>\r\n    </mat-card>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}