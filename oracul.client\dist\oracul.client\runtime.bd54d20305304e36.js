(()=>{"use strict";var e,v={},g={};function r(e){var i=g[e];if(void 0!==i)return i.exports;var t=g[e]={exports:{}};return v[e].call(t.exports,t,t.exports,r),t.exports}r.m=v,e=[],r.O=(i,t,n,f)=>{if(!t){var a=1/0;for(o=0;o<e.length;o++){for(var[t,n,f]=e[o],d=!0,l=0;l<t.length;l++)(!1&f||a>=f)&&Object.keys(r.O).every(b=>r.O[b](t[l]))?t.splice(l--,1):(d=!1,f<a&&(a=f));if(d){e.splice(o--,1);var c=n();void 0!==c&&(i=c)}}return i}f=f||0;for(var o=e.length;o>0&&e[o-1][2]>f;o--)e[o]=e[o-1];e[o]=[t,n,f]},(()=>{var i,e=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__;r.t=function(t,n){if(1&n&&(t=this(t)),8&n||"object"==typeof t&&t&&(4&n&&t.__esModule||16&n&&"function"==typeof t.then))return t;var f=Object.create(null);r.r(f);var o={};i=i||[null,e({}),e([]),e(e)];for(var a=2&n&&t;"object"==typeof a&&!~i.indexOf(a);a=e(a))Object.getOwnPropertyNames(a).forEach(d=>o[d]=()=>t[d]);return o.default=()=>t,r.d(f,o),f}})(),r.d=(e,i)=>{for(var t in i)r.o(i,t)&&!r.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:i[t]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce((i,t)=>(r.f[t](e,i),i),[])),r.u=e=>e+"."+{50:"8f2faed2eb36f336",971:"4ed578a4eb68c613"}[e]+".js",r.miniCssF=e=>{},r.o=(e,i)=>Object.prototype.hasOwnProperty.call(e,i),(()=>{var e={},i="oracul.client:";r.l=(t,n,f,o)=>{if(e[t])e[t].push(n);else{var a,d;if(void 0!==f)for(var l=document.getElementsByTagName("script"),c=0;c<l.length;c++){var u=l[c];if(u.getAttribute("src")==t||u.getAttribute("data-webpack")==i+f){a=u;break}}a||(d=!0,(a=document.createElement("script")).type="module",a.charset="utf-8",a.timeout=120,r.nc&&a.setAttribute("nonce",r.nc),a.setAttribute("data-webpack",i+f),a.src=r.tu(t)),e[t]=[n];var s=(m,b)=>{a.onerror=a.onload=null,clearTimeout(p);var _=e[t];if(delete e[t],a.parentNode&&a.parentNode.removeChild(a),_&&_.forEach(y=>y(b)),m)return m(b)},p=setTimeout(s.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=s.bind(null,a.onerror),a.onload=s.bind(null,a.onload),d&&document.head.appendChild(a)}}})(),r.r=e=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;r.tt=()=>(void 0===e&&(e={createScriptURL:i=>i},typeof trustedTypes<"u"&&trustedTypes.createPolicy&&(e=trustedTypes.createPolicy("angular#bundler",e))),e)})(),r.tu=e=>r.tt().createScriptURL(e),r.p="",(()=>{var e={666:0};r.f.j=(n,f)=>{var o=r.o(e,n)?e[n]:void 0;if(0!==o)if(o)f.push(o[2]);else if(666!=n){var a=new Promise((u,s)=>o=e[n]=[u,s]);f.push(o[2]=a);var d=r.p+r.u(n),l=new Error;r.l(d,u=>{if(r.o(e,n)&&(0!==(o=e[n])&&(e[n]=void 0),o)){var s=u&&("load"===u.type?"missing":u.type),p=u&&u.target&&u.target.src;l.message="Loading chunk "+n+" failed.\n("+s+": "+p+")",l.name="ChunkLoadError",l.type=s,l.request=p,o[1](l)}},"chunk-"+n,n)}else e[n]=0},r.O.j=n=>0===e[n];var i=(n,f)=>{var l,c,[o,a,d]=f,u=0;if(o.some(p=>0!==e[p])){for(l in a)r.o(a,l)&&(r.m[l]=a[l]);if(d)var s=d(r)}for(n&&n(f);u<o.length;u++)r.o(e,c=o[u])&&e[c]&&e[c][0](),e[c]=0;return r.O(s)},t=self.webpackChunkoracul_client=self.webpackChunkoracul_client||[];t.forEach(i.bind(null,0)),t.push=i.bind(null,t.push.bind(t))})()})();