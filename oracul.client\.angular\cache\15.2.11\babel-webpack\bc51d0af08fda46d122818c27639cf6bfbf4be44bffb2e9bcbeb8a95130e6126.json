{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport let VirtualTimeScheduler = /*#__PURE__*/(() => {\n  class VirtualTimeScheduler extends AsyncScheduler {\n    constructor(schedulerActionCtor = VirtualAction, maxFrames = Infinity) {\n      super(schedulerActionCtor, () => this.frame);\n      this.maxFrames = maxFrames;\n      this.frame = 0;\n      this.index = -1;\n    }\n    flush() {\n      const {\n        actions,\n        maxFrames\n      } = this;\n      let error;\n      let action;\n      while ((action = actions[0]) && action.delay <= maxFrames) {\n        actions.shift();\n        this.frame = action.delay;\n        if (error = action.execute(action.state, action.delay)) {\n          break;\n        }\n      }\n      if (error) {\n        while (action = actions.shift()) {\n          action.unsubscribe();\n        }\n        throw error;\n      }\n    }\n  }\n  VirtualTimeScheduler.frameTimeFactor = 10;\n  return VirtualTimeScheduler;\n})();\nexport class VirtualAction extends AsyncAction {\n  constructor(scheduler, work, index = scheduler.index += 1) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.index = index;\n    this.active = true;\n    this.index = scheduler.index = index;\n  }\n  schedule(state, delay = 0) {\n    if (Number.isFinite(delay)) {\n      if (!this.id) {\n        return super.schedule(state, delay);\n      }\n      this.active = false;\n      const action = new VirtualAction(this.scheduler, this.work);\n      this.add(action);\n      return action.schedule(state, delay);\n    } else {\n      return Subscription.EMPTY;\n    }\n  }\n  requestAsyncId(scheduler, id, delay = 0) {\n    this.delay = scheduler.frame + delay;\n    const {\n      actions\n    } = scheduler;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return 1;\n  }\n  recycleAsyncId(scheduler, id, delay = 0) {\n    return undefined;\n  }\n  _execute(state, delay) {\n    if (this.active === true) {\n      return super._execute(state, delay);\n    }\n  }\n  static sortActions(a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  }\n}\n//# sourceMappingURL=VirtualTimeScheduler.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}