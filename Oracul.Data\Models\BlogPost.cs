using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Blog post entity for astrology articles and insights
    /// </summary>
    public class BlogPost : BaseEntity
    {
        [Required]
        public int UserProfileId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Excerpt { get; set; } = string.Empty;

        public string? Content { get; set; }

        /// <summary>
        /// Publication status of the article
        /// </summary>
        [Required]
        public ArticleStatus Status { get; set; } = ArticleStatus.Draft;

        /// <summary>
        /// Date when the article was published (null for drafts)
        /// </summary>
        public DateTime? PublishedAt { get; set; }

        /// <summary>
        /// Date when the article was last saved as draft
        /// </summary>
        public DateTime? LastSavedAt { get; set; }

        public int ReadCount { get; set; } = 0;

        [Required]
        [MaxLength(150)]
        public string Slug { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? FeaturedImageUrl { get; set; }

        /// <summary>
        /// SEO meta description
        /// </summary>
        [MaxLength(160)]
        public string? MetaDescription { get; set; }

        /// <summary>
        /// SEO keywords
        /// </summary>
        [MaxLength(500)]
        public string? MetaKeywords { get; set; }

        /// <summary>
        /// Estimated reading time in minutes
        /// </summary>
        public int EstimatedReadTime { get; set; } = 0;

        /// <summary>
        /// Whether the article is featured
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// Whether comments are allowed on this article
        /// </summary>
        public bool AllowComments { get; set; } = true;

        /// <summary>
        /// Article category for organization
        /// </summary>
        [MaxLength(100)]
        public string? Category { get; set; }

        // Navigation properties
        public virtual UserProfile UserProfile { get; set; } = null!;
        public virtual ICollection<BlogPostTag> BlogPostTags { get; set; } = new List<BlogPostTag>();
    }

    /// <summary>
    /// Article publication status
    /// </summary>
    public enum ArticleStatus
    {
        /// <summary>
        /// Article is saved as draft and not visible to public
        /// </summary>
        Draft = 0,

        /// <summary>
        /// Article is published and visible to public
        /// </summary>
        Published = 1,

        /// <summary>
        /// Article is scheduled for future publication
        /// </summary>
        Scheduled = 2,

        /// <summary>
        /// Article is archived and not visible to public
        /// </summary>
        Archived = 3
    }
}
