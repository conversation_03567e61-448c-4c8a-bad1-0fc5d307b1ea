{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost,1433;Database=Oracle2DB;User Id=sa;Password=**********;TrustServerCertificate=True;"
    //"DefaultConnection":"Server=tcp:axsion.database.windows.net,1433;Initial Catalog=Oracle2DB;Persist Security Info=False;User ID=asm;Password=********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  },
  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForSecurity123456789",
    "Issuer": "OraculApp",
    "Audience": "OraculUsers",
    "AccessTokenExpirationMinutes": 15,
    "RefreshTokenExpirationDays": 7
  },
  "EmailSettings": {
    "SmtpHost": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-app-password",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromName": "Oracul App"
  },
  "Authentication": {
    "Google": {
      "ClientId": "your-google-client-id",
      "ClientSecret": "your-google-client-secret"
    },
    "Facebook": {
      "AppId": "your-facebook-app-id",
      "AppSecret": "your-facebook-app-secret"
    }
  },
  "AppSettings": {
    "ClientUrl": "http://localhost:4200"
  },
  "BlobStorage": {
    "ConnectionString": "UseDevelopmentStorage=true",
    "ContainerName": "oracul-files",
    "BaseUrl": "https://your-storage-account.blob.core.windows.net/",
    "MaxFileSizeBytes": ********,
    "AllowedImageTypes": ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"],
    "AllowedDocumentTypes": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}
