using Microsoft.EntityFrameworkCore;
using Oracul.Data.Data;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;

namespace Oracul.Data.Repositories
{
    /// <summary>
    /// Profile repository implementation with astrology-focused profile operations
    /// </summary>
    public class ProfileRepository : Repository<UserProfile>, IProfileRepository
    {
        public ProfileRepository(OraculDbContext context) : base(context)
        {
        }

        public async Task<UserProfile?> GetByUserIdAsync(int userId)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.UserId == userId);
        }

        public async Task<UserProfile?> GetBySlugAsync(string slug)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.Slug == slug);
        }

        public async Task<UserProfile?> GetByUsernameAsync(string username)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.Username == username);
        }

        public async Task<UserProfile?> GetCompleteProfileAsync(int profileId)
        {
            return await _dbSet
                .Include(p => p.User)
                .Include(p => p.Location)
                .Include(p => p.ContactInfo)
                    .ThenInclude(ci => ci!.BusinessAddress)
                .Include(p => p.ContactInfo)
                    .ThenInclude(ci => ci!.PhoneNumbers)
                .Include(p => p.Skills)
                    .ThenInclude(s => s.SkillEndorsements)
                .Include(p => p.BlogPosts)
                    .ThenInclude(bp => bp.BlogPostTags)
                .Include(p => p.Achievements)
                .Include(p => p.Certifications)
                .Include(p => p.Experiences)
                    .ThenInclude(e => e.WorkAchievements)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.PortfolioImages)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.PortfolioTechnologies)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.ClientTestimonial)
                .Include(p => p.SocialLinks)
                .FirstOrDefaultAsync(p => p.Id == profileId);
        }

        public async Task<UserProfile?> GetCompleteProfileBySlugAsync(string slug)
        {
            return await _dbSet
                .Include(p => p.User)
                .Include(p => p.Location)
                .Include(p => p.ContactInfo)
                    .ThenInclude(ci => ci!.BusinessAddress)
                .Include(p => p.ContactInfo)
                    .ThenInclude(ci => ci!.PhoneNumbers)
                .Include(p => p.Skills)

                    .ThenInclude(s => s.SkillEndorsements)
                .Include(p => p.BlogPosts)
                    .ThenInclude(bp => bp.BlogPostTags)
                .Include(p => p.Achievements)
                .Include(p => p.Certifications)
                .Include(p => p.Experiences)
                    .ThenInclude(e => e.WorkAchievements)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.PortfolioImages)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.PortfolioTechnologies)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.ClientTestimonial)
                .Include(p => p.SocialLinks)
                .FirstOrDefaultAsync(p => p.Slug == slug);
        }

        public async Task<UserProfile?> GetCompleteProfileByUserIdAsync(int userId)
        {
            return await _dbSet
                .Include(p => p.User)
                .Include(p => p.Location)
                .Include(p => p.ContactInfo)
                    .ThenInclude(ci => ci!.BusinessAddress)
                .Include(p => p.ContactInfo)
                    .ThenInclude(ci => ci!.PhoneNumbers)
                .Include(p => p.Skills)
                    .ThenInclude(s => s.SkillEndorsements)
                .Include(p => p.BlogPosts)
                    .ThenInclude(bp => bp.BlogPostTags)
                .Include(p => p.Achievements)
                .Include(p => p.Certifications)
                .Include(p => p.Experiences)
                    .ThenInclude(e => e.WorkAchievements)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.PortfolioImages)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.PortfolioTechnologies)
                .Include(p => p.PortfolioItems)
                    .ThenInclude(pi => pi.ClientTestimonial)
                .Include(p => p.SocialLinks)
                .FirstOrDefaultAsync(p => p.UserId == userId);
        }

        public async Task<IEnumerable<UserProfile>> GetPublicProfilesAsync(int page = 1, int pageSize = 20)
        {
            return await _dbSet
                .Where(p => p.IsPublic)
                .Include(p => p.User)
                .Include(p => p.Location)
                .Include(p => p.Skills.Take(5))
                .OrderByDescending(p => p.ProfileViews)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> SearchProfilesAsync(string searchTerm, int page = 1, int pageSize = 20)
        {
            var lowerSearchTerm = searchTerm.ToLower();
            
            return await _dbSet
                .Where(p => p.IsPublic && (
                    p.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    p.LastName.ToLower().Contains(lowerSearchTerm) ||
                    p.Username.ToLower().Contains(lowerSearchTerm) ||
                    p.ProfessionalTitle!.ToLower().Contains(lowerSearchTerm) ||
                    p.Headline!.ToLower().Contains(lowerSearchTerm) ||
                    p.Summary!.ToLower().Contains(lowerSearchTerm) ||
                    p.Skills.Any(s => s.Name.ToLower().Contains(lowerSearchTerm))
                ))
                .Include(p => p.User)
                .Include(p => p.Location)
                .Include(p => p.Skills.Take(5))
                .OrderByDescending(p => p.ProfileViews)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> GetProfilesBySkillAsync(string skillName, int page = 1, int pageSize = 20)
        {
            return await _dbSet
                .Where(p => p.IsPublic && p.Skills.Any(s => s.Name.ToLower() == skillName.ToLower()))
                .Include(p => p.User)
                .Include(p => p.Location)
                .Include(p => p.Skills.Take(5))
                .OrderByDescending(p => p.ProfileViews)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> GetProfilesByLocationAsync(string location, int page = 1, int pageSize = 20)
        {
            var lowerLocation = location.ToLower();
            
            return await _dbSet
                .Where(p => p.IsPublic && p.Location != null && (
                    p.Location.City!.ToLower().Contains(lowerLocation) ||
                    p.Location.State!.ToLower().Contains(lowerLocation) ||
                    p.Location.Country!.ToLower().Contains(lowerLocation) ||
                    p.Location.DisplayLocation!.ToLower().Contains(lowerLocation)
                ))
                .Include(p => p.User)
                .Include(p => p.Location)
                .Include(p => p.Skills.Take(5))
                .OrderByDescending(p => p.ProfileViews)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task IncrementProfileViewAsync(int profileId)
        {
            var profile = await _dbSet.FindAsync(profileId);
            if (profile != null)
            {
                profile.ProfileViews++;
                profile.LastViewedAt = DateTime.UtcNow;
                _context.Update(profile);
            }
        }

        public async Task<int> GetProfileViewCountAsync(int profileId)
        {
            var profile = await _dbSet.FindAsync(profileId);
            return profile?.ProfileViews ?? 0;
        }

        public async Task<bool> IsSlugAvailableAsync(string slug, int? excludeProfileId = null)
        {
            var query = _dbSet.Where(p => p.Slug == slug);
            
            if (excludeProfileId.HasValue)
            {
                query = query.Where(p => p.Id != excludeProfileId.Value);
            }
            
            return !await query.AnyAsync();
        }

        public async Task<bool> IsUsernameAvailableAsync(string username, int? excludeProfileId = null)
        {
            var query = _dbSet.Where(p => p.Username == username);
            
            if (excludeProfileId.HasValue)
            {
                query = query.Where(p => p.Id != excludeProfileId.Value);
            }
            
            return !await query.AnyAsync();
        }

        public async Task<int> CalculateProfileCompletionPercentageAsync(int profileId)
        {
            var profile = await GetCompleteProfileAsync(profileId);
            if (profile == null) return 0;

            int totalFields = 20; // Total number of profile fields to check
            int completedFields = 0;

            // Basic information (5 fields)
            if (!string.IsNullOrEmpty(profile.FirstName)) completedFields++;
            if (!string.IsNullOrEmpty(profile.LastName)) completedFields++;
            if (!string.IsNullOrEmpty(profile.ProfessionalTitle)) completedFields++;
            if (!string.IsNullOrEmpty(profile.Headline)) completedFields++;
            if (!string.IsNullOrEmpty(profile.Summary)) completedFields++;

            // Photos (2 fields)
            if (!string.IsNullOrEmpty(profile.ProfilePhotoUrl)) completedFields++;
            if (!string.IsNullOrEmpty(profile.CoverPhotoUrl)) completedFields++;

            // Location (1 field)
            if (profile.Location != null) completedFields++;

            // Contact info (2 fields)
            if (profile.ContactInfo != null)
            {
                if (!string.IsNullOrEmpty(profile.ContactInfo.Email)) completedFields++;
                if (profile.ContactInfo.PhoneNumbers.Any()) completedFields++;
            }

            // Skills (2 fields)
            if (profile.Skills.Any()) completedFields++;
            if (profile.Skills.Count >= 3) completedFields++; // Bonus for having multiple skills

            // Experience (2 fields)
            if (profile.Experiences.Any()) completedFields++;
            if (profile.Experiences.Count >= 2) completedFields++; // Bonus for multiple experiences

            // Portfolio (2 fields)
            if (profile.PortfolioItems.Any()) completedFields++;
            if (profile.PortfolioItems.Count >= 2) completedFields++; // Bonus for multiple items

            // Certifications (1 field)
            if (profile.Certifications.Any()) completedFields++;

            // Achievements (1 field)
            if (profile.Achievements.Any()) completedFields++;

            // Social links (1 field)
            if (profile.SocialLinks.Any()) completedFields++;

            // Blog posts (1 field)
            if (profile.BlogPosts.Any()) completedFields++;

            return (int)Math.Round((double)completedFields / totalFields * 100);
        }

        public async Task UpdateProfileCompletionPercentageAsync(int profileId)
        {
            var profile = await _dbSet.FindAsync(profileId);
            if (profile != null)
            {
                profile.ProfileCompletionPercentage = await CalculateProfileCompletionPercentageAsync(profileId);
                _context.Update(profile);
            }
        }

        public async Task<IEnumerable<UserProfile>> GetProfilesByUserIdsAsync(IEnumerable<int> userIds)
        {
            return await _dbSet
                .Where(p => userIds.Contains(p.UserId))
                .Include(p => p.User)
                .Include(p => p.Location)
                .Include(p => p.Skills.Take(5))
                .ToListAsync();
        }
    }
}
