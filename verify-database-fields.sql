-- SQL Script to verify registration field storage in the database
-- Run this script in SQL Server Management Studio or similar tool

PRINT 'Verifying Registration Field Storage in Database'
PRINT '================================================'

-- Check Users table
PRINT ''
PRINT '1. Users Table:'
PRINT '---------------'
SELECT 
    Id,
    FirstName,
    LastName,
    Email,
    PhoneNumber,
    CASE WHEN PasswordHash IS NOT NULL THEN 'YES' ELSE 'NO' END AS HasPasswordHash,
    IsActive,
    EmailConfirmed,
    CASE WHEN EmailConfirmationToken IS NOT NULL THEN 'YES' ELSE 'NO' END AS HasConfirmationToken,
    CreatedAt
FROM Users
ORDER BY CreatedAt DESC

-- Check UserRoles and role assignments
PRINT ''
PRINT '2. User Roles:'
PRINT '--------------'
SELECT 
    u.Email,
    u.FirstName + ' ' + u.LastName AS FullName,
    r.Name AS RoleName,
    ur.AssignedAt
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
ORDER BY u.CreatedAt DESC

-- Check UserProfiles table (for oracle users)
PRINT ''
PRINT '3. User Profiles:'
PRINT '----------------'
SELECT 
    up.Id,
    u.Email,
    up.Username,
    up.Slug,
    up.FirstName,
    up.LastName,
    up.ProfessionalTitle,
    up.Headline,
    LEFT(up.Summary, 50) + '...' AS SummaryPreview,
    up.IsPublic,
    up.ProfileCompletionPercentage,
    up.ProfileViews,
    up.CreatedAt
FROM UserProfiles up
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY up.CreatedAt DESC

-- Check ProfileLocations
PRINT ''
PRINT '4. Profile Locations:'
PRINT '--------------------'
SELECT 
    pl.Id,
    u.Email,
    pl.City,
    pl.State,
    pl.Country,
    pl.DisplayLocation
FROM ProfileLocations pl
INNER JOIN UserProfiles up ON pl.UserProfileId = up.Id
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY pl.CreatedAt DESC

-- Check ContactInformations
PRINT ''
PRINT '5. Contact Information:'
PRINT '----------------------'
SELECT 
    ci.Id,
    u.Email,
    ci.Email AS ContactEmail,
    ci.IsEmailPublic,
    ci.Website,
    ci.PortfolioUrl
FROM ContactInformations ci
INNER JOIN UserProfiles up ON ci.UserProfileId = up.Id
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY ci.CreatedAt DESC

-- Check BusinessAddresses
PRINT ''
PRINT '6. Business Addresses:'
PRINT '---------------------'
SELECT 
    ba.Id,
    u.Email,
    ba.Street,
    ba.City,
    ba.State,
    ba.PostalCode,
    ba.Country,
    ba.IsPublic
FROM BusinessAddresses ba
INNER JOIN ContactInformations ci ON ba.ContactInformationId = ci.Id
INNER JOIN UserProfiles up ON ci.UserProfileId = up.Id
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY ba.CreatedAt DESC

-- Check ProfileSkills
PRINT ''
PRINT '7. Profile Skills:'
PRINT '-----------------'
SELECT 
    ps.Id,
    u.Email,
    ps.Name AS SkillName,
    ps.Category,
    ps.ProficiencyLevel,
    ps.Endorsements
FROM ProfileSkills ps
INNER JOIN UserProfiles up ON ps.UserProfileId = up.Id
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY u.Email, ps.Name

-- Summary statistics
PRINT ''
PRINT '8. Summary Statistics:'
PRINT '---------------------'
SELECT 
    'Total Users' AS Metric,
    COUNT(*) AS Count
FROM Users
UNION ALL
SELECT 
    'Users with Oracle Role' AS Metric,
    COUNT(DISTINCT u.Id) AS Count
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
WHERE r.Name = 'Oracle'
UNION ALL
SELECT 
    'Users with Profiles' AS Metric,
    COUNT(*) AS Count
FROM UserProfiles
UNION ALL
SELECT 
    'Profiles with Locations' AS Metric,
    COUNT(*) AS Count
FROM ProfileLocations
UNION ALL
SELECT 
    'Profiles with Contact Info' AS Metric,
    COUNT(*) AS Count
FROM ContactInformations
UNION ALL
SELECT 
    'Profiles with Business Address' AS Metric,
    COUNT(*) AS Count
FROM BusinessAddresses
UNION ALL
SELECT 
    'Total Skills Recorded' AS Metric,
    COUNT(*) AS Count
FROM ProfileSkills

-- Check for any data integrity issues
PRINT ''
PRINT '9. Data Integrity Check:'
PRINT '-----------------------'

-- Users without roles
SELECT 
    'Users without roles' AS Issue,
    COUNT(*) AS Count
FROM Users u
LEFT JOIN UserRoles ur ON u.Id = ur.UserId
WHERE ur.UserId IS NULL

UNION ALL

-- Oracle users without profiles
SELECT 
    'Oracle users without profiles' AS Issue,
    COUNT(*) AS Count
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
LEFT JOIN UserProfiles up ON u.Id = up.UserId
WHERE r.Name = 'Oracle' AND up.UserId IS NULL

UNION ALL

-- Profiles without locations
SELECT 
    'Profiles without locations' AS Issue,
    COUNT(*) AS Count
FROM UserProfiles up
LEFT JOIN ProfileLocations pl ON up.Id = pl.UserProfileId
WHERE pl.UserProfileId IS NULL

UNION ALL

-- Profiles without skills
SELECT 
    'Profiles without skills' AS Issue,
    COUNT(*) AS Count
FROM UserProfiles up
LEFT JOIN ProfileSkills ps ON up.Id = ps.UserProfileId
WHERE ps.UserProfileId IS NULL

PRINT ''
PRINT 'Database verification completed!'
PRINT 'Check the results above to ensure all registration fields are stored correctly.'
