import { Injectable, Injector } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse, HttpClient } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, filter, take, switchMap, tap } from 'rxjs/operators';
import { TokenService } from '../services/token.service';
import { AuthResponse } from '../models/auth.models';
import { environment } from '../../../environments/environment';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  private httpClient?: HttpClient;

  constructor(
    private tokenService: TokenService,
    private injector: Injector
  ) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    // Add auth header if user is authenticated
    const authReq = this.addAuthHeader(req);

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401 && !authReq.url.includes('/auth/')) {
          return this.handle401Error(authReq, next);
        }
        return throwError(error);
      })
    );
  }

  private addAuthHeader(req: HttpRequest<any>): HttpRequest<any> {
    const token = this.tokenService.getToken();

    if (token && !req.url.includes('/auth/login') && !req.url.includes('/auth/register')) {
      return req.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
    }

    return req;
  }

  private handle401Error(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.refreshToken().pipe(
        switchMap((response: AuthResponse) => {
          this.isRefreshing = false;
          if (response.success && response.accessToken) {
            this.refreshTokenSubject.next(response.accessToken);
            return next.handle(this.addAuthHeader(req));
          } else {
            this.tokenService.clearAllTokens();
            return throwError('Token refresh failed');
          }
        }),
        catchError((error) => {
          this.isRefreshing = false;
          this.tokenService.clearAllTokens();
          return throwError(error);
        })
      );
    } else {
      // Wait for refresh to complete
      return this.refreshTokenSubject.pipe(
        filter(token => token != null),
        take(1),
        switchMap(() => next.handle(this.addAuthHeader(req)))
      );
    }
  }

  private refreshToken(): Observable<AuthResponse> {
    const refreshToken = this.tokenService.getRefreshToken();
    if (!refreshToken) {
      return throwError('No refresh token available');
    }

    // Lazy load HttpClient to avoid circular dependency
    if (!this.httpClient) {
      this.httpClient = this.injector.get(HttpClient);
    }

    return this.httpClient.post<AuthResponse>(`${environment.apiUrl}/auth/refresh-token`, { refreshToken })
      .pipe(
        tap(response => {
          if (response.success && response.accessToken) {
            const rememberMe = this.tokenService.getRememberMe();
            this.tokenService.setToken(response.accessToken, rememberMe);
            if (response.refreshToken) {
              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);
            }
            if (response.user) {
              this.tokenService.setUser(response.user, rememberMe);
            }
          }
        }),
        catchError(error => {
          this.tokenService.clearAllTokens();
          return throwError(error);
        })
      );
  }
}
