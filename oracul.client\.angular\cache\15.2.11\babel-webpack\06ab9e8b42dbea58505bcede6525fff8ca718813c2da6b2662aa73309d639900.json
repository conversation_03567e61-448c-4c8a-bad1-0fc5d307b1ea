{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, filter, take, switchMap, tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/token.service\";\nexport let AuthInterceptor = /*#__PURE__*/(() => {\n  class AuthInterceptor {\n    constructor(tokenService, injector) {\n      this.tokenService = tokenService;\n      this.injector = injector;\n      this.isRefreshing = false;\n      this.refreshTokenSubject = new BehaviorSubject(null);\n    }\n    intercept(req, next) {\n      // Add auth header if user is authenticated\n      const authReq = this.addAuthHeader(req);\n      return next.handle(authReq).pipe(catchError(error => {\n        if (error.status === 401 && !authReq.url.includes('/auth/')) {\n          return this.handle401Error(authReq, next);\n        }\n        return throwError(error);\n      }));\n    }\n    addAuthHeader(req) {\n      const token = this.tokenService.getToken();\n      if (token && !req.url.includes('/auth/login') && !req.url.includes('/auth/register')) {\n        return req.clone({\n          setHeaders: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n      }\n      return req;\n    }\n    handle401Error(req, next) {\n      if (!this.isRefreshing) {\n        this.isRefreshing = true;\n        this.refreshTokenSubject.next(null);\n        return this.refreshToken().pipe(switchMap(response => {\n          this.isRefreshing = false;\n          if (response.success && response.accessToken) {\n            this.refreshTokenSubject.next(response.accessToken);\n            return next.handle(this.addAuthHeader(req));\n          } else {\n            this.tokenService.clearAllTokens();\n            return throwError('Token refresh failed');\n          }\n        }), catchError(error => {\n          this.isRefreshing = false;\n          this.tokenService.clearAllTokens();\n          return throwError(error);\n        }));\n      } else {\n        // Wait for refresh to complete\n        return this.refreshTokenSubject.pipe(filter(token => token != null), take(1), switchMap(() => next.handle(this.addAuthHeader(req))));\n      }\n    }\n    refreshToken() {\n      const refreshToken = this.tokenService.getRefreshToken();\n      if (!refreshToken) {\n        return throwError('No refresh token available');\n      }\n      // Lazy load HttpClient to avoid circular dependency\n      if (!this.httpClient) {\n        this.httpClient = this.injector.get(HttpClient);\n      }\n      return this.httpClient.post(`${environment.apiUrl}/auth/refresh-token`, {\n        refreshToken\n      }).pipe(tap(response => {\n        if (response.success && response.accessToken) {\n          const rememberMe = this.tokenService.getRememberMe();\n          this.tokenService.setToken(response.accessToken, rememberMe);\n          if (response.refreshToken) {\n            this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n          }\n          if (response.user) {\n            this.tokenService.setUser(response.user, rememberMe);\n          }\n        }\n      }), catchError(error => {\n        this.tokenService.clearAllTokens();\n        return throwError(error);\n      }));\n    }\n    static {\n      this.ɵfac = function AuthInterceptor_Factory(t) {\n        return new (t || AuthInterceptor)(i0.ɵɵinject(i1.TokenService), i0.ɵɵinject(i0.Injector));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthInterceptor,\n        factory: AuthInterceptor.ɵfac\n      });\n    }\n  }\n  return AuthInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}