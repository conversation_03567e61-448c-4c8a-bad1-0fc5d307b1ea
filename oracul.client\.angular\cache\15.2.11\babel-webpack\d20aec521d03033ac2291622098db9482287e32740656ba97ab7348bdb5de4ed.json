{"ast": null, "code": "import { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../shared/services/article.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"@angular/material/snack-bar\";\nfunction ArticleManagementComponent_mat_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction ArticleManagementComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 13);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u0417\\u0430\\u0440\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\\u0442\\u0435...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ArticleManagementComponent_div_21_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"\\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const article_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(article_r20.category);\n  }\n}\nfunction ArticleManagementComponent_div_21_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 31)(1, \"div\", 32)(2, \"span\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ArticleManagementComponent_div_21_td_4_span_4_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const article_r20 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r20.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", article_r20.category);\n  }\n}\nfunction ArticleManagementComponent_div_21_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 36);\n    i0.ɵɵtext(1, \"\\u0421\\u0442\\u0430\\u0442\\u0443\\u0441\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37)(1, \"mat-chip\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const article_r23 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"color\", ctx_r7.getStatusColor(article_r23.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", article_r23.status, \" \");\n  }\n}\nfunction ArticleManagementComponent_div_21_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 36);\n    i0.ɵɵtext(1, \"\\u041A\\u0430\\u0442\\u0435\\u0433\\u043E\\u0440\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const article_r24 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", article_r24.category || \"\\u0411\\u0435\\u0437 \\u043A\\u0430\\u0442\\u0435\\u0433\\u043E\\u0440\\u0438\\u044F\", \" \");\n  }\n}\nfunction ArticleManagementComponent_div_21_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"\\u0414\\u0430\\u0442\\u0430 \\u043D\\u0430 \\u043F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const article_r25 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, article_r25.publishedAt, \"dd.MM.yyyy HH:mm\"), \" \");\n  }\n}\nfunction ArticleManagementComponent_div_21_td_13_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtext(1, \"\\u041D\\u0435 \\u0435 \\u043F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u043D\\u0430\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37);\n    i0.ɵɵtemplate(1, ArticleManagementComponent_div_21_td_13_div_1_Template, 3, 4, \"div\", 39);\n    i0.ɵɵtemplate(2, ArticleManagementComponent_div_21_td_13_ng_template_2_Template, 2, 0, \"ng-template\", null, 40, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const article_r25 = ctx.$implicit;\n    const _r27 = i0.ɵɵreference(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", article_r25.publishedAt)(\"ngIfElse\", _r27);\n  }\n}\nfunction ArticleManagementComponent_div_21_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"\\u041F\\u0440\\u0435\\u0433\\u043B\\u0435\\u0434\\u0438\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37)(1, \"div\", 42)(2, \"mat-icon\", 43);\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const article_r30 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(article_r30.readCount);\n  }\n}\nfunction ArticleManagementComponent_div_21_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 36);\n    i0.ɵɵtext(1, \"\\u0414\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_19_ng_template_6_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const article_r34 = i0.ɵɵnextContext().article;\n      const ctx_r37 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r37.publishArticle(article_r34.id));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"publish\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u041F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u0439\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_19_ng_template_6_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const article_r34 = i0.ɵɵnextContext().article;\n      const ctx_r40 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r40.unpublishArticle(article_r34.id));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"unpublished\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u0421\\u043A\\u0440\\u0438\\u0439\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_19_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const article_r34 = restoredCtx.article;\n      const ctx_r43 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r43.viewArticle(article_r34));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u041F\\u0440\\u0435\\u0433\\u043B\\u0435\\u0434\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const article_r34 = restoredCtx.article;\n      const ctx_r45 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r45.editArticle(article_r34.id));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"\\u0420\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, ArticleManagementComponent_div_21_td_19_ng_template_6_button_10_Template, 5, 0, \"button\", 48);\n    i0.ɵɵtemplate(11, ArticleManagementComponent_div_21_td_19_ng_template_6_button_11_Template, 5, 0, \"button\", 48);\n    i0.ɵɵelement(12, \"mat-divider\");\n    i0.ɵɵelementStart(13, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const article_r34 = restoredCtx.article;\n      const ctx_r46 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r46.deleteArticle(article_r34.id, article_r34.title));\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"\\u0418\\u0437\\u0442\\u0440\\u0438\\u0439\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const article_r34 = ctx.article;\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.canPublish(article_r34.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.canUnpublish(article_r34.status));\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    article: a0\n  };\n};\nfunction ArticleManagementComponent_div_21_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37)(1, \"button\", 44)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 45);\n    i0.ɵɵtemplate(6, ArticleManagementComponent_div_21_td_19_ng_template_6_Template, 18, 2, \"ng-template\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const article_r31 = ctx.$implicit;\n    const _r32 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r32)(\"matMenuTriggerData\", i0.ɵɵpureFunction1(2, _c0, article_r31));\n  }\n}\nfunction ArticleManagementComponent_div_21_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 50);\n  }\n}\nfunction ArticleManagementComponent_div_21_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 51);\n  }\n}\nfunction ArticleManagementComponent_div_21_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"article\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u041D\\u044F\\u043C\\u0430 \\u043D\\u0430\\u043C\\u0435\\u0440\\u0435\\u043D\\u0438 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u0417\\u0430\\u043F\\u043E\\u0447\\u043D\\u0435\\u0442\\u0435 \\u0434\\u0430 \\u0441\\u044A\\u0437\\u0434\\u0430\\u0432\\u0430\\u0442\\u0435 \\u0441\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435, \\u043A\\u0430\\u0442\\u043E \\u043D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 \\u0431\\u0443\\u0442\\u043E\\u043D\\u0430 \\\"\\u041D\\u043E\\u0432\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\\".\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_div_22_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.createNewArticle());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u0421\\u044A\\u0437\\u0434\\u0430\\u0439 \\u043F\\u044A\\u0440\\u0432\\u0430\\u0442\\u0430 \\u0441\\u0438 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function () {\n  return [5, 10, 25, 50];\n};\nfunction ArticleManagementComponent_div_21_mat_paginator_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-paginator\", 54);\n    i0.ɵɵlistener(\"page\", function ArticleManagementComponent_div_21_mat_paginator_23_Template_mat_paginator_page_0_listener($event) {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.onPageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"length\", ctx_r19.totalCount)(\"pageSize\", ctx_r19.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(3, _c1));\n  }\n}\nfunction ArticleManagementComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"table\", 15);\n    i0.ɵɵelementContainerStart(2, 16);\n    i0.ɵɵtemplate(3, ArticleManagementComponent_div_21_th_3_Template, 2, 0, \"th\", 17);\n    i0.ɵɵtemplate(4, ArticleManagementComponent_div_21_td_4_Template, 5, 2, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 19);\n    i0.ɵɵtemplate(6, ArticleManagementComponent_div_21_th_6_Template, 2, 0, \"th\", 20);\n    i0.ɵɵtemplate(7, ArticleManagementComponent_div_21_td_7_Template, 3, 2, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 22);\n    i0.ɵɵtemplate(9, ArticleManagementComponent_div_21_th_9_Template, 2, 0, \"th\", 20);\n    i0.ɵɵtemplate(10, ArticleManagementComponent_div_21_td_10_Template, 2, 1, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 23);\n    i0.ɵɵtemplate(12, ArticleManagementComponent_div_21_th_12_Template, 2, 0, \"th\", 17);\n    i0.ɵɵtemplate(13, ArticleManagementComponent_div_21_td_13_Template, 4, 2, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 24);\n    i0.ɵɵtemplate(15, ArticleManagementComponent_div_21_th_15_Template, 2, 0, \"th\", 17);\n    i0.ɵɵtemplate(16, ArticleManagementComponent_div_21_td_16_Template, 6, 1, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 25);\n    i0.ɵɵtemplate(18, ArticleManagementComponent_div_21_th_18_Template, 2, 0, \"th\", 20);\n    i0.ɵɵtemplate(19, ArticleManagementComponent_div_21_td_19_Template, 7, 4, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(20, ArticleManagementComponent_div_21_tr_20_Template, 1, 0, \"tr\", 26);\n    i0.ɵɵtemplate(21, ArticleManagementComponent_div_21_tr_21_Template, 1, 0, \"tr\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ArticleManagementComponent_div_21_div_22_Template, 11, 0, \"div\", 28);\n    i0.ɵɵtemplate(23, ArticleManagementComponent_div_21_mat_paginator_23_Template, 1, 4, \"mat-paginator\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r2.dataSource);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.dataSource.data.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.dataSource.data.length > 0);\n  }\n}\nexport let ArticleManagementComponent = /*#__PURE__*/(() => {\n  class ArticleManagementComponent {\n    constructor(articleService, router, dialog, snackBar) {\n      this.articleService = articleService;\n      this.router = router;\n      this.dialog = dialog;\n      this.snackBar = snackBar;\n      this.displayedColumns = ['title', 'status', 'category', 'publishedAt', 'readCount', 'actions'];\n      this.dataSource = new MatTableDataSource();\n      this.isLoading = false;\n      this.totalCount = 0;\n      this.pageSize = 10;\n      this.currentPage = 0;\n      this.selectedStatus = '';\n      this.statusOptions = [{\n        value: '',\n        label: 'Всички статии'\n      }, {\n        value: 'Draft',\n        label: 'Чернови'\n      }, {\n        value: 'Published',\n        label: 'Публикувани'\n      }, {\n        value: 'Scheduled',\n        label: 'Планирани'\n      }, {\n        value: 'Archived',\n        label: 'Архивирани'\n      }];\n    }\n    ngOnInit() {\n      this.loadArticles();\n    }\n    ngAfterViewInit() {\n      this.dataSource.paginator = this.paginator;\n      this.dataSource.sort = this.sort;\n    }\n    loadArticles() {\n      this.isLoading = true;\n      this.articleService.getMyArticles(this.currentPage + 1, this.pageSize, this.selectedStatus).subscribe({\n        next: response => {\n          this.dataSource.data = response.articles.map(article => ({\n            id: article.id,\n            title: article.title,\n            status: this.getStatusLabel(article.status || 'Draft'),\n            category: article.category,\n            publishedAt: article.publishedAt ? new Date(article.publishedAt) : undefined,\n            lastSavedAt: article.lastSavedAt ? new Date(article.lastSavedAt) : undefined,\n            readCount: article.readCount || 0,\n            estimatedReadTime: article.estimatedReadTime || 0\n          }));\n          this.totalCount = response.totalCount;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading articles:', error);\n          this.snackBar.open('Грешка при зареждането на статиите', 'Затвори', {\n            duration: 3000\n          });\n          this.isLoading = false;\n        }\n      });\n    }\n    onStatusFilterChange() {\n      this.currentPage = 0;\n      this.loadArticles();\n    }\n    onPageChange(event) {\n      this.currentPage = event.pageIndex;\n      this.pageSize = event.pageSize;\n      this.loadArticles();\n    }\n    createNewArticle() {\n      this.router.navigate(['/articles/editor']);\n    }\n    editArticle(articleId) {\n      this.router.navigate(['/articles/editor', articleId]);\n    }\n    viewArticle(article) {\n      // TODO: Navigate to article view\n      this.snackBar.open('Функцията за преглед ще бъде добавена скоро', 'Затвори', {\n        duration: 3000\n      });\n    }\n    publishArticle(articleId) {\n      this.articleService.publishArticle(articleId).subscribe({\n        next: () => {\n          this.snackBar.open('Статията е публикувана успешно', 'Затвори', {\n            duration: 3000\n          });\n          this.loadArticles();\n        },\n        error: error => {\n          console.error('Error publishing article:', error);\n          this.snackBar.open('Грешка при публикуването на статията', 'Затвори', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    unpublishArticle(articleId) {\n      this.articleService.unpublishArticle(articleId).subscribe({\n        next: () => {\n          this.snackBar.open('Статията е скрита успешно', 'Затвори', {\n            duration: 3000\n          });\n          this.loadArticles();\n        },\n        error: error => {\n          console.error('Error unpublishing article:', error);\n          this.snackBar.open('Грешка при скриването на статията', 'Затвори', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    deleteArticle(articleId, articleTitle) {\n      const dialogRef = this.dialog.open(ConfirmDeleteDialogComponent, {\n        width: '400px',\n        data: {\n          title: articleTitle\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result) {\n          this.articleService.deleteArticle(articleId).subscribe({\n            next: () => {\n              this.snackBar.open('Статията е изтрита успешно', 'Затвори', {\n                duration: 3000\n              });\n              this.loadArticles();\n            },\n            error: error => {\n              console.error('Error deleting article:', error);\n              this.snackBar.open('Грешка при изтриването на статията', 'Затвори', {\n                duration: 3000\n              });\n            }\n          });\n        }\n      });\n    }\n    getStatusLabel(status) {\n      const statusMap = {\n        'Draft': 'Чернова',\n        'Published': 'Публикувана',\n        'Scheduled': 'Планирана',\n        'Archived': 'Архивирана'\n      };\n      return statusMap[status] || status;\n    }\n    getStatusColor(status) {\n      const colorMap = {\n        'Чернова': 'accent',\n        'Публикувана': 'primary',\n        'Планирана': 'warn',\n        'Архивирана': ''\n      };\n      return colorMap[status] || '';\n    }\n    canPublish(status) {\n      return status === 'Чернова' || status === 'Архивирана';\n    }\n    canUnpublish(status) {\n      return status === 'Публикувана';\n    }\n    static {\n      this.ɵfac = function ArticleManagementComponent_Factory(t) {\n        return new (t || ArticleManagementComponent)(i0.ɵɵdirectiveInject(i1.ArticleService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ArticleManagementComponent,\n        selectors: [[\"app-article-management\"]],\n        viewQuery: function ArticleManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 22,\n        vars: 4,\n        consts: [[1, \"article-management-container\"], [1, \"management-card\"], [1, \"toolbar\"], [1, \"toolbar-left\"], [\"appearance\", \"outline\", 1, \"status-filter\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"toolbar-right\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [\"mode\", \"indeterminate\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"articles-table\", 3, \"dataSource\"], [\"matColumnDef\", \"title\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", \"class\", \"title-cell\", 4, \"matCellDef\"], [\"matColumnDef\", \"status\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"category\"], [\"matColumnDef\", \"publishedAt\"], [\"matColumnDef\", \"readCount\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"page\", 4, \"ngIf\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\", 1, \"title-cell\"], [1, \"title-content\"], [1, \"article-title\"], [\"class\", \"article-category\", 4, \"ngIf\"], [1, \"article-category\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"selected\", \"\", 3, \"color\"], [4, \"ngIf\", \"ngIfElse\"], [\"notPublished\", \"\"], [1, \"not-published\"], [1, \"read-stats\"], [1, \"stats-icon\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\", \"matMenuTriggerData\"], [\"actionMenu\", \"matMenu\"], [\"matMenuContent\", \"\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", 1, \"delete-action\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"], [1, \"no-data-icon\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"page\"]],\n        template: function ArticleManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n            i0.ɵɵtext(4, \"\\u0423\\u043F\\u0440\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n            i0.ɵɵtext(6, \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0432\\u0430\\u0439\\u0442\\u0435, \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u0439\\u0442\\u0435 \\u0438 \\u0443\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u0432\\u0430\\u0439\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0438\\u0442\\u0435 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 2)(9, \"div\", 3)(10, \"mat-form-field\", 4)(11, \"mat-label\");\n            i0.ɵɵtext(12, \"\\u0424\\u0438\\u043B\\u0442\\u044A\\u0440 \\u043F\\u043E \\u0441\\u0442\\u0430\\u0442\\u0443\\u0441\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-select\", 5);\n            i0.ɵɵlistener(\"valueChange\", function ArticleManagementComponent_Template_mat_select_valueChange_13_listener($event) {\n              return ctx.selectedStatus = $event;\n            })(\"selectionChange\", function ArticleManagementComponent_Template_mat_select_selectionChange_13_listener() {\n              return ctx.onStatusFilterChange();\n            });\n            i0.ɵɵtemplate(14, ArticleManagementComponent_mat_option_14_Template, 2, 2, \"mat-option\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"div\", 7)(16, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function ArticleManagementComponent_Template_button_click_16_listener() {\n              return ctx.createNewArticle();\n            });\n            i0.ɵɵelementStart(17, \"mat-icon\");\n            i0.ɵɵtext(18, \"add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(19, \" \\u041D\\u043E\\u0432\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(20, ArticleManagementComponent_div_20_Template, 4, 0, \"div\", 9);\n            i0.ɵɵtemplate(21, ArticleManagementComponent_div_21_Template, 24, 5, \"div\", 10);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"value\", ctx.selectedStatus);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        styles: [\".article-management-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.management-card[_ngcontent-%COMP%]{margin-bottom:20px}.toolbar[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px;flex-wrap:wrap;gap:16px}.toolbar-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.toolbar-right[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.status-filter[_ngcontent-%COMP%]{min-width:200px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#666}.table-container[_ngcontent-%COMP%]{width:100%;overflow-x:auto}.articles-table[_ngcontent-%COMP%]{width:100%;min-width:800px}.title-cell[_ngcontent-%COMP%]{max-width:300px}.title-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.article-title[_ngcontent-%COMP%]{font-weight:500;color:#3f2f4e;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.article-category[_ngcontent-%COMP%]{font-size:12px;color:#666;background-color:#e6dbec;padding:2px 8px;border-radius:12px;display:inline-block;max-width:-moz-fit-content;max-width:fit-content}.not-published[_ngcontent-%COMP%]{color:#999;font-style:italic}.read-stats[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.stats-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#666}.delete-action[_ngcontent-%COMP%]{color:#f44336}.delete-action[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#f44336}.no-data[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;color:#666}.no-data-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;color:#ccc;margin-bottom:16px}.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:16px 0 8px;color:#3f2f4e}.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:24px;max-width:400px;margin-left:auto;margin-right:auto}  .mat-chip.mat-primary{background-color:#67455c;color:#fff}  .mat-chip.mat-accent{background-color:#d2a6d0;color:#3f2f4e}  .mat-chip.mat-warn{background-color:#ff9800;color:#fff}.mat-table[_ngcontent-%COMP%]{background:transparent}.mat-header-cell[_ngcontent-%COMP%]{color:#3f2f4e;font-weight:600;border-bottom:2px solid #e6dbec}.mat-cell[_ngcontent-%COMP%]{border-bottom:1px solid #f0f0f0}.mat-row[_ngcontent-%COMP%]:hover{background-color:#fafafa}@media (max-width: 768px){.article-management-container[_ngcontent-%COMP%]{padding:10px}.toolbar[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.toolbar-left[_ngcontent-%COMP%], .toolbar-right[_ngcontent-%COMP%]{justify-content:center}.status-filter[_ngcontent-%COMP%]{min-width:auto;width:100%}.table-container[_ngcontent-%COMP%]{margin:0 -10px}.articles-table[_ngcontent-%COMP%]{min-width:600px}.title-cell[_ngcontent-%COMP%]{max-width:200px}}@media (max-width: 480px){.articles-table[_ngcontent-%COMP%]{min-width:500px}.title-cell[_ngcontent-%COMP%]{max-width:150px}.article-title[_ngcontent-%COMP%]{font-size:14px}.article-category[_ngcontent-%COMP%]{font-size:10px;padding:1px 6px}}.mat-menu-panel[_ngcontent-%COMP%]{min-width:180px}.mat-menu-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.mat-menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:0}.mat-paginator[_ngcontent-%COMP%]{background:transparent;border-top:1px solid #e0e0e0;margin-top:16px}.mat-card-header[_ngcontent-%COMP%]{margin-bottom:16px}.mat-card-title[_ngcontent-%COMP%]{color:#3f2f4e;font-size:24px;font-weight:500}.mat-card-subtitle[_ngcontent-%COMP%]{color:#666;margin-top:4px}\"]\n      });\n    }\n  }\n  return ArticleManagementComponent;\n})();\nexport let ConfirmDeleteDialogComponent = /*#__PURE__*/(() => {\n  class ConfirmDeleteDialogComponent {\n    constructor(dialogRef, data) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n    }\n    onCancel() {\n      this.dialogRef.close(false);\n    }\n    onConfirm() {\n      this.dialogRef.close(true);\n    }\n    static {\n      this.ɵfac = function ConfirmDeleteDialogComponent_Factory(t) {\n        return new (t || ConfirmDeleteDialogComponent)(i0.ɵɵdirectiveInject(i3.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ConfirmDeleteDialogComponent,\n        selectors: [[\"app-confirm-delete-dialog\"]],\n        decls: 15,\n        vars: 1,\n        consts: [[\"mat-dialog-title\", \"\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n        template: function ConfirmDeleteDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"h2\", 0);\n            i0.ɵɵtext(1, \"\\u041F\\u043E\\u0442\\u0432\\u044A\\u0440\\u0436\\u0434\\u0435\\u043D\\u0438\\u0435 \\u0437\\u0430 \\u0438\\u0437\\u0442\\u0440\\u0438\\u0432\\u0430\\u043D\\u0435\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"mat-dialog-content\")(3, \"p\");\n            i0.ɵɵtext(4, \"\\u0421\\u0438\\u0433\\u0443\\u0440\\u043D\\u0438 \\u043B\\u0438 \\u0441\\u0442\\u0435, \\u0447\\u0435 \\u0438\\u0441\\u043A\\u0430\\u0442\\u0435 \\u0434\\u0430 \\u0438\\u0437\\u0442\\u0440\\u0438\\u0435\\u0442\\u0435 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430 \\\"\");\n            i0.ɵɵelementStart(5, \"strong\");\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(7, \"\\\"?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\");\n            i0.ɵɵtext(9, \"\\u0422\\u043E\\u0432\\u0430 \\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u0435 \\u043D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0431\\u044A\\u0434\\u0435 \\u043E\\u0442\\u043C\\u0435\\u043D\\u0435\\u043D\\u043E.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"mat-dialog-actions\", 1)(11, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function ConfirmDeleteDialogComponent_Template_button_click_11_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(12, \"\\u041E\\u0442\\u043A\\u0430\\u0437\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function ConfirmDeleteDialogComponent_Template_button_click_13_listener() {\n              return ctx.onConfirm();\n            });\n            i0.ɵɵtext(14, \"\\u0418\\u0437\\u0442\\u0440\\u0438\\u0439\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.data.title);\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return ConfirmDeleteDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}