# 🔧 Step-by-Step Azure DevOps Pipeline Creation Guide

## 🎯 **What We're Creating**
- Backend pipeline for .NET API deployment
- Frontend pipeline for Angular app deployment
- Both deploying to your Azure App Services

---

## 📋 **STEP 1: Access Azure DevOps**

1. **Open your browser** and go to: `https://dev.azure.com`
2. **Sign in** with your Microsoft account
3. **Select your organization** (or create one if you don't have it)
4. **Select your project** (or create a new project)

---

## 🔗 **STEP 2: Create Service Connection**

### **2.1 Navigate to Service Connections**
1. In your Azure DevOps project, look at the **bottom left corner**
2. Click **"Project settings"** (gear icon)
3. In the left menu, under **"Pipelines"**, click **"Service connections"**

### **2.2 Create New Connection**
1. Click the **blue "Create service connection"** button
2. In the popup, select **"Azure Resource Manager"**
3. Click **"Next"**

### **2.3 Choose Authentication Method**
1. Select **"Service principal (automatic)"**
2. Click **"Next"**

### **2.4 Configure Connection Details**
Fill in these exact values:
- **Scope level**: Select **"Subscription"**
- **Subscription**: Choose the one with ID `6dd88dbc-9333-4643-834f-978cc8b01dc8`
- **Resource group**: Select **"egishe"**
- **Service connection name**: Type exactly: `Azure-Service-Connection`
- **Description**: Type: `Connection for Oracul app deployment`
- **Security**: ✅ Check **"Grant access permission to all pipelines"**

### **2.5 Save Connection**
1. Click **"Save"**
2. Wait 1-2 minutes for Azure to create the connection
3. You should see it listed with status **"Ready"**

---

## 🌍 **STEP 3: Create Environment**

### **3.1 Navigate to Environments**
1. In the left menu, click **"Pipelines"**
2. Under Pipelines, click **"Environments"**

### **3.2 Create Production Environment**
1. Click **"Create environment"** (blue button)
2. **Name**: Type exactly `production`
3. **Description**: Type `Production environment for Oracul`
4. **Resource**: Leave as **"None"**
5. Click **"Create"**

---

## 📦 **STEP 4: Create Backend Pipeline**

### **4.1 Navigate to Pipelines**
1. In the left menu, click **"Pipelines"**
2. Click **"Pipelines"** (the main one, not sub-items)
3. Click **"Create Pipeline"** or **"New pipeline"** (blue button)

### **4.2 Choose Code Location**
**If your code is in Azure Repos:**
1. Click **"Azure Repos Git"**
2. Select your repository from the list
3. Click **"Continue"**

**If your code is in GitHub:**
1. Click **"GitHub"**
2. Authorize Azure DevOps to access GitHub
3. Select your repository
4. Click **"Continue"**

### **4.3 Configure Pipeline**
1. Select **"Existing Azure Pipelines YAML file"**
2. **Branch**: Select **"main"** (or your default branch)
3. **Path**: Click the dropdown and select **"/azure-pipelines-backend.yml"**
4. Click **"Continue"**

### **4.4 Review and Create**
1. You'll see the YAML content displayed
2. Click **"Save and run"** (blue button)
3. In the popup:
   - **Commit message**: Type `Add backend pipeline`
   - **Commit directly to the main branch**: Leave selected
4. Click **"Save and run"**

### **4.5 Monitor First Run**
1. You'll be taken to the pipeline run page
2. Watch the pipeline execute (it may take 5-10 minutes)
3. If it fails, check the logs by clicking on the failed stage

---

## 🌐 **STEP 5: Create Frontend Pipeline**

### **5.1 Create Second Pipeline**
1. Go back to **Pipelines** → **Pipelines**
2. Click **"New pipeline"** again

### **5.2 Repeat Configuration**
1. Select the same code location (Azure Repos Git or GitHub)
2. Select the same repository
3. Click **"Continue"**
4. Select **"Existing Azure Pipelines YAML file"**
5. **Branch**: Select **"main"**
6. **Path**: Select **"/azure-pipelines-frontend.yml"**
7. Click **"Continue"**

### **5.3 Save and Run**
1. Review the YAML content
2. Click **"Save and run"**
3. **Commit message**: Type `Add frontend pipeline`
4. Click **"Save and run"**

---

## 🏷️ **STEP 6: Rename Pipelines**

### **6.1 Rename Backend Pipeline**
1. Go to **Pipelines** → **Pipelines**
2. Find your first pipeline (it will have a generic name)
3. Click on the pipeline name
4. Click the **"..."** (three dots) in the top right
5. Select **"Rename/move"**
6. **Name**: Type `Oracul-Backend-Pipeline`
7. Click **"Save"**

### **6.2 Rename Frontend Pipeline**
1. Go back to the pipelines list
2. Find your second pipeline
3. Click on it and repeat the rename process
4. **Name**: Type `Oracul-Frontend-Pipeline`
5. Click **"Save"**

---

## 🧪 **STEP 7: Test Your Pipelines**

### **7.1 Trigger Backend Pipeline**
1. Make a small change to any file in your backend code
2. Commit and push to your main branch
3. Go to **Pipelines** → **Pipelines**
4. Click on **"Oracul-Backend-Pipeline"**
5. You should see a new run starting

### **7.2 Monitor Execution**
Watch for these stages:
1. **Build** stage should complete successfully
2. **DeployProduction** stage should deploy to your App Service
3. Check that EF migrations run successfully

### **7.3 Trigger Frontend Pipeline**
1. Make a small change to frontend code
2. Commit and push
3. Monitor **"Oracul-Frontend-Pipeline"**

---

## ✅ **STEP 8: Verify Deployment**

### **8.1 Check Azure Portal**
1. Go to `https://portal.azure.com`
2. Navigate to your App Services:
   - Search for `ora-be`
   - Search for `ora-fe`
3. Check that both are running

### **8.2 Test Your Applications**
1. **Backend**: Open `https://fb-be-h5eqc6fwa9acd6ew.northeurope-01.azurewebsites.net`
2. **Frontend**: Open `https://ora-fe-dvdegtdvdgajcfb9.northeurope-01.azurewebsites.net`

---

## 🚨 **Common Issues & Solutions**

### **Issue: "Service connection not found"**
**Solution**: Go back to Step 2 and ensure the service connection name is exactly `Azure-Service-Connection`

### **Issue: "Environment not found"**
**Solution**: Go back to Step 3 and ensure the environment name is exactly `production`

### **Issue: "YAML file not found"**
**Solution**: Ensure the YAML files are in your repository root and named exactly:
- `azure-pipelines-backend.yml`
- `azure-pipelines-frontend.yml`

### **Issue: "Deployment failed"**
**Solution**: 
1. Check that your App Services exist in Azure
2. Verify the names match: `ora-be` and `ora-fe`
3. Check the resource group is `egishe`

---

## 🎯 **Success Checklist**

- [ ] Service connection created and shows "Ready"
- [ ] Environment "production" created
- [ ] Backend pipeline created and named "Oracul-Backend-Pipeline"
- [ ] Frontend pipeline created and named "Oracul-Frontend-Pipeline"
- [ ] Both pipelines run successfully
- [ ] Backend deployed to ora-be App Service
- [ ] Frontend deployed to ora-fe App Service
- [ ] Both applications accessible via URLs

**🎉 Done! Your pipelines are now set up and working!**

---

## 📸 **Visual Guide - What You'll See**

### **Service Connection Screen**
```
┌─────────────────────────────────────────┐
│ Create a service connection             │
├─────────────────────────────────────────┤
│ ○ Azure Resource Manager               │ ← Select this
│ ○ GitHub                               │
│ ○ Docker Registry                      │
│                                        │
│                    [Next]              │
└─────────────────────────────────────────┘
```

### **Pipeline Creation Screen**
```
┌─────────────────────────────────────────┐
│ Where is your code?                     │
├─────────────────────────────────────────┤
│ ○ Azure Repos Git                      │ ← Select this
│ ○ GitHub                               │
│ ○ Bitbucket Cloud                      │
│ ○ GitHub Enterprise Server             │
│ ○ Subversion                           │
│ ○ Other Git                            │
└─────────────────────────────────────────┘
```

### **YAML File Selection**
```
┌─────────────────────────────────────────┐
│ Configure your pipeline                 │
├─────────────────────────────────────────┤
│ ○ Starter pipeline                     │
│ ○ Existing Azure Pipelines YAML file  │ ← Select this
│                                        │
│ Branch: [main ▼]                       │
│ Path: [/azure-pipelines-backend.yml ▼] │
│                                        │
│                    [Continue]          │
└─────────────────────────────────────────┘
```

---

## 🔍 **Exact Button Names & Locations**

### **In Azure DevOps Navigation:**
- **Project Settings**: Bottom left corner (gear icon)
- **Service connections**: Left menu under "Pipelines"
- **Environments**: Left menu under "Pipelines"
- **Pipelines**: Main left menu item

### **Button Colors & Text:**
- **Blue buttons**: "Create service connection", "New pipeline", "Save and run"
- **Dropdown menus**: Have ▼ arrow on the right
- **Three dots menu**: "..." in top right of pipeline pages

### **Exact Text to Type:**
- Service connection name: `Azure-Service-Connection`
- Environment name: `production`
- Backend pipeline name: `Oracul-Backend-Pipeline`
- Frontend pipeline name: `Oracul-Frontend-Pipeline`

---

## ⏱️ **Expected Timing**
- **Service connection creation**: 1-2 minutes
- **Environment creation**: Instant
- **Pipeline creation**: 1-2 minutes each
- **First pipeline run**: 5-15 minutes
- **Total setup time**: 20-30 minutes

**🎉 Your pipelines will now automatically deploy when you push code to main or develop branches!**
