using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;
using Oracul.Server.Models;
using System.Security.Claims;

namespace Oracul.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ArticleController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ArticleController> _logger;

        public ArticleController(IUnitOfWork unitOfWork, ILogger<ArticleController> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Get featured articles for home page
        /// </summary>
        [HttpGet("featured")]
        public async Task<ActionResult<ArticleListResponse>> GetFeaturedArticles([FromQuery] int count = 6)
        {
            try
            {
                var articles = await _unitOfWork.BlogPosts.GetFeaturedArticlesAsync(count);
                var articleDtos = articles.Select(MapToArticlePreview).ToList();

                return Ok(new ArticleListResponse
                {
                    Articles = articleDtos,
                    TotalCount = articleDtos.Count,
                    Page = 1,
                    PageSize = count,
                    TotalPages = 1
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured articles");
                return StatusCode(500, new { message = "Грешка при зареждането на препоръчаните статии" });
            }
        }

        /// <summary>
        /// Get article by slug - returns full content for authenticated users, limited for anonymous
        /// </summary>
        [HttpGet("slug/{slug}")]
        public async Task<ActionResult<ArticleDto>> GetArticleBySlug(string slug)
        {
            try
            {
                var article = await _unitOfWork.BlogPosts.GetBySlugAsync(slug);
                if (article == null)
                {
                    return NotFound(new { message = "Статията не е намерена" });
                }

                var isAuthenticated = User.Identity?.IsAuthenticated ?? false;
                var articleDto = MapToArticleDto(article, isAuthenticated);

                // Record view if user is authenticated
                if (isAuthenticated)
                {
                    await RecordArticleViewAsync(article.Id);
                }

                return Ok(articleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving article with slug {Slug}", slug);
                return StatusCode(500, new { message = "Грешка при зареждането на статията" });
            }
        }

        /// <summary>
        /// Get article preview by slug - always returns limited content
        /// </summary>
        [HttpGet("preview/{slug}")]
        public async Task<ActionResult<ArticlePreviewDto>> GetArticlePreview(string slug)
        {
            try
            {
                var article = await _unitOfWork.BlogPosts.GetBySlugAsync(slug);
                if (article == null)
                {
                    return NotFound(new { message = "Статията не е намерена" });
                }

                var previewDto = MapToArticlePreview(article);
                return Ok(previewDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving article preview with slug {Slug}", slug);
                return StatusCode(500, new { message = "Грешка при зареждането на прегледа на статията" });
            }
        }

        /// <summary>
        /// Search articles with filters
        /// </summary>
        [HttpPost("search")]
        public async Task<ActionResult<ArticleListResponse>> SearchArticles([FromBody] ArticleSearchRequest request)
        {
            try
            {
                var (articles, totalCount) = await _unitOfWork.BlogPosts.SearchArticlesAsync(
                    request.SearchTerm,
                    request.Category,
                    request.AuthorId,
                    request.Page ?? 1,
                    request.PageSize ?? 10,
                    request.SortBy ?? "publishedAt",
                    request.SortOrder ?? "desc"
                );

                var articleDtos = articles.Select(MapToArticlePreview).ToList();
                var totalPages = (int)Math.Ceiling((double)totalCount / (request.PageSize ?? 10));

                return Ok(new ArticleListResponse
                {
                    Articles = articleDtos,
                    TotalCount = totalCount,
                    Page = request.Page ?? 1,
                    PageSize = request.PageSize ?? 10,
                    TotalPages = totalPages
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching articles");
                return StatusCode(500, new { message = "Грешка при търсенето на статии" });
            }
        }

        /// <summary>
        /// Get articles by category
        /// </summary>
        [HttpGet("category")]
        public async Task<ActionResult<ArticleListResponse>> GetArticlesByCategory(
            [FromQuery] string category,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var (articles, totalCount) = await _unitOfWork.BlogPosts.GetByCategoryAsync(category, page, pageSize);
                var articleDtos = articles.Select(MapToArticlePreview).ToList();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                return Ok(new ArticleListResponse
                {
                    Articles = articleDtos,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = totalPages
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles by category {Category}", category);
                return StatusCode(500, new { message = "Грешка при зареждането на статиите по категория" });
            }
        }

        /// <summary>
        /// Record article view for analytics
        /// </summary>
        [HttpPost("{id}/view")]
        public async Task<ActionResult> RecordArticleView(int id)
        {
            try
            {
                await RecordArticleViewAsync(id);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording view for article {ArticleId}", id);
                return StatusCode(500, new { message = "Грешка при записването на прегледа" });
            }
        }

        // CRUD Operations for Article Management

        /// <summary>
        /// Get user's articles for management
        /// </summary>
        [HttpGet("my-articles")]
        [Authorize]
        public async Task<ActionResult<ArticleListResponse>> GetMyArticles(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? status = null)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Потребителят не е удостоверен" });
                }

                var userProfile = await _unitOfWork.Profiles.GetByUserIdAsync(userId.Value);
                if (userProfile == null)
                {
                    return NotFound(new { message = "Профилът не е намерен" });
                }

                var (articles, totalCount) = await _unitOfWork.BlogPosts.GetByAuthorAsync(userProfile.Id, page, pageSize);

                // Filter by status if provided
                if (!string.IsNullOrEmpty(status) && Enum.TryParse<ArticleStatus>(status, true, out var statusEnum))
                {
                    articles = articles.Where(a => a.Status == statusEnum);
                    totalCount = articles.Count();
                }

                var articleDtos = articles.Select(MapToArticlePreview).ToList();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                return Ok(new ArticleListResponse
                {
                    Articles = articleDtos,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = totalPages
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user articles");
                return StatusCode(500, new { message = "Грешка при зареждането на статиите" });
            }
        }

        /// <summary>
        /// Get article by ID for editing
        /// </summary>
        [HttpGet("{id}")]
        [Authorize]
        public async Task<ActionResult<ArticleManagementDto>> GetArticleForEdit(int id)
        {
            try
            {
                var article = await _unitOfWork.BlogPosts.GetByIdAsync(id);
                if (article == null)
                {
                    return NotFound(new { message = "Статията не е намерена" });
                }

                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Потребителят не е удостоверен" });
                }

                var userProfile = await _unitOfWork.Profiles.GetByUserIdAsync(userId.Value);
                if (userProfile == null || article.UserProfileId != userProfile.Id)
                {
                    return Forbid("Нямате права за редактиране на тази статия");
                }

                var articleDto = MapToArticleManagement(article);
                return Ok(articleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving article {ArticleId} for edit", id);
                return StatusCode(500, new { message = "Грешка при зареждането на статията" });
            }
        }

        /// <summary>
        /// Create a new article
        /// </summary>
        [HttpPost]
        [Authorize]
        public async Task<ActionResult<ArticleManagementDto>> CreateArticle([FromBody] CreateArticleRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Потребителят не е удостоверен" });
                }

                var userProfile = await _unitOfWork.Profiles.GetByUserIdAsync(userId.Value);
                if (userProfile == null)
                {
                    return NotFound(new { message = "Профилът не е намерен" });
                }

                // Generate slug from title
                var slug = GenerateSlug(request.Title);
                var isSlugAvailable = await _unitOfWork.BlogPosts.IsSlugAvailableAsync(slug);
                if (!isSlugAvailable)
                {
                    slug = $"{slug}-{DateTime.UtcNow.Ticks}";
                }

                var article = new BlogPost
                {
                    UserProfileId = userProfile.Id,
                    Title = request.Title,
                    Excerpt = request.Excerpt,
                    Content = request.Content,
                    Category = request.Category,
                    FeaturedImageUrl = request.FeaturedImageUrl,
                    MetaDescription = request.MetaDescription,
                    MetaKeywords = request.MetaKeywords,
                    AllowComments = request.AllowComments,
                    IsFeatured = request.IsFeatured,
                    Status = request.SaveAsDraft ? ArticleStatus.Draft : ArticleStatus.Published,
                    PublishedAt = request.SaveAsDraft ? null : DateTime.UtcNow,
                    LastSavedAt = DateTime.UtcNow,
                    Slug = slug,
                    EstimatedReadTime = CalculateReadTime(request.Content),
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.BlogPosts.AddAsync(article);

                // Add tags
                if (request.Tags.Any())
                {
                    var dbContext = _unitOfWork.BlogPosts.GetDbContext();
                    foreach (var tag in request.Tags)
                    {
                        var blogPostTag = new BlogPostTag
                        {
                            BlogPostId = article.Id,
                            Tag = tag,
                            CreatedAt = DateTime.UtcNow
                        };
                        await dbContext.Set<BlogPostTag>().AddAsync(blogPostTag);
                    }
                }

                await _unitOfWork.SaveChangesAsync();

                // Reload with includes
                var createdArticle = await _unitOfWork.BlogPosts.GetByIdAsync(article.Id);
                var articleDto = MapToArticleManagement(createdArticle!);

                return CreatedAtAction(nameof(GetArticleForEdit), new { id = article.Id }, articleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating article");
                return StatusCode(500, new { message = "Грешка при създаването на статията" });
            }
        }

        /// <summary>
        /// Update an existing article
        /// </summary>
        [HttpPut("{id}")]
        [Authorize]
        public async Task<ActionResult<ArticleManagementDto>> UpdateArticle(int id, [FromBody] UpdateArticleRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var article = await _unitOfWork.BlogPosts.GetByIdAsync(id);
                if (article == null)
                {
                    return NotFound(new { message = "Статията не е намерена" });
                }

                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Потребителят не е удостоверен" });
                }

                var userProfile = await _unitOfWork.Profiles.GetByUserIdAsync(userId.Value);
                if (userProfile == null || article.UserProfileId != userProfile.Id)
                {
                    return Forbid("Нямате права за редактиране на тази статия");
                }

                // Update article properties
                article.Title = request.Title;
                article.Excerpt = request.Excerpt;
                article.Content = request.Content;
                article.Category = request.Category;
                article.FeaturedImageUrl = request.FeaturedImageUrl;
                article.MetaDescription = request.MetaDescription;
                article.MetaKeywords = request.MetaKeywords;
                article.AllowComments = request.AllowComments;
                article.IsFeatured = request.IsFeatured;
                article.EstimatedReadTime = CalculateReadTime(request.Content);
                article.LastSavedAt = DateTime.UtcNow;
                article.UpdatedAt = DateTime.UtcNow;

                // Update slug if title changed
                var newSlug = GenerateSlug(request.Title);
                if (newSlug != article.Slug)
                {
                    var isSlugAvailable = await _unitOfWork.BlogPosts.IsSlugAvailableAsync(newSlug, id);
                    if (isSlugAvailable)
                    {
                        article.Slug = newSlug;
                    }
                }

                _unitOfWork.BlogPosts.Update(article);

                // Update tags
                var dbContext = _unitOfWork.BlogPosts.GetDbContext();
                var existingTags = await dbContext.Set<BlogPostTag>()
                    .Where(t => t.BlogPostId == id)
                    .ToListAsync();

                // Remove existing tags
                foreach (var tag in existingTags)
                {
                    dbContext.Set<BlogPostTag>().Remove(tag);
                }

                // Add new tags
                if (request.Tags.Any())
                {
                    foreach (var tag in request.Tags)
                    {
                        var blogPostTag = new BlogPostTag
                        {
                            BlogPostId = id,
                            Tag = tag,
                            CreatedAt = DateTime.UtcNow
                        };
                        await dbContext.Set<BlogPostTag>().AddAsync(blogPostTag);
                    }
                }

                await _unitOfWork.SaveChangesAsync();

                // Reload with includes
                var updatedArticle = await _unitOfWork.BlogPosts.GetByIdAsync(id);
                var articleDto = MapToArticleManagement(updatedArticle!);

                return Ok(articleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating article {ArticleId}", id);
                return StatusCode(500, new { message = "Грешка при обновяването на статията" });
            }
        }

        /// <summary>
        /// Delete an article
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize]
        public async Task<ActionResult> DeleteArticle(int id)
        {
            try
            {
                var article = await _unitOfWork.BlogPosts.GetByIdAsync(id);
                if (article == null)
                {
                    return NotFound(new { message = "Статията не е намерена" });
                }

                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Потребителят не е удостоверен" });
                }

                var userProfile = await _unitOfWork.Profiles.GetByUserIdAsync(userId.Value);
                if (userProfile == null || article.UserProfileId != userProfile.Id)
                {
                    return Forbid("Нямате права за изтриване на тази статия");
                }

                // Delete associated tags
                var dbContext = _unitOfWork.BlogPosts.GetDbContext();
                var tags = await dbContext.Set<BlogPostTag>()
                    .Where(t => t.BlogPostId == id)
                    .ToListAsync();
                foreach (var tag in tags)
                {
                    dbContext.Set<BlogPostTag>().Remove(tag);
                }

                // Delete the article
                _unitOfWork.BlogPosts.Remove(article);
                await _unitOfWork.SaveChangesAsync();

                return Ok(new { message = "Статията е изтрита успешно" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting article {ArticleId}", id);
                return StatusCode(500, new { message = "Грешка при изтриването на статията" });
            }
        }

        /// <summary>
        /// Publish or unpublish an article
        /// </summary>
        [HttpPost("{id}/publish")]
        [Authorize]
        public async Task<ActionResult<ArticleManagementDto>> PublishArticle(int id, [FromBody] PublishArticleRequest? request = null)
        {
            try
            {
                var article = await _unitOfWork.BlogPosts.GetByIdAsync(id);
                if (article == null)
                {
                    return NotFound(new { message = "Статията не е намерена" });
                }

                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Потребителят не е удостоверен" });
                }

                var userProfile = await _unitOfWork.Profiles.GetByUserIdAsync(userId.Value);
                if (userProfile == null || article.UserProfileId != userProfile.Id)
                {
                    return Forbid("Нямате права за публикуване на тази статия");
                }

                var publishAt = request?.PublishAt ?? DateTime.UtcNow;

                if (publishAt <= DateTime.UtcNow)
                {
                    article.Status = ArticleStatus.Published;
                    article.PublishedAt = DateTime.UtcNow;
                }
                else
                {
                    article.Status = ArticleStatus.Scheduled;
                    article.PublishedAt = publishAt;
                }

                article.UpdatedAt = DateTime.UtcNow;
                _unitOfWork.BlogPosts.Update(article);
                await _unitOfWork.SaveChangesAsync();

                var articleDto = MapToArticleManagement(article);
                return Ok(articleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing article {ArticleId}", id);
                return StatusCode(500, new { message = "Грешка при публикуването на статията" });
            }
        }

        /// <summary>
        /// Unpublish an article (set to draft)
        /// </summary>
        [HttpPost("{id}/unpublish")]
        [Authorize]
        public async Task<ActionResult<ArticleManagementDto>> UnpublishArticle(int id)
        {
            try
            {
                var article = await _unitOfWork.BlogPosts.GetByIdAsync(id);
                if (article == null)
                {
                    return NotFound(new { message = "Статията не е намерена" });
                }

                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Потребителят не е удостоверен" });
                }

                var userProfile = await _unitOfWork.Profiles.GetByUserIdAsync(userId.Value);
                if (userProfile == null || article.UserProfileId != userProfile.Id)
                {
                    return Forbid("Нямате права за редактиране на тази статия");
                }

                article.Status = ArticleStatus.Draft;
                article.PublishedAt = null;
                article.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.BlogPosts.Update(article);
                await _unitOfWork.SaveChangesAsync();

                var articleDto = MapToArticleManagement(article);
                return Ok(articleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unpublishing article {ArticleId}", id);
                return StatusCode(500, new { message = "Грешка при скриването на статията" });
            }
        }

        private async Task RecordArticleViewAsync(int articleId)
        {
            var article = await _unitOfWork.BlogPosts.GetByIdAsync(articleId);
            if (article != null)
            {
                article.ReadCount++;
                _unitOfWork.BlogPosts.Update(article);
                await _unitOfWork.SaveChangesAsync();
            }
        }

        private static ArticleDto MapToArticleDto(BlogPost blogPost, bool includeFullContent)
        {
            return new ArticleDto
            {
                Id = blogPost.Id,
                Title = blogPost.Title,
                Excerpt = blogPost.Excerpt,
                Content = includeFullContent ? blogPost.Content : GetLimitedContent(blogPost.Content),
                Author = GetAuthorName(blogPost.UserProfile),
                PublishedAt = blogPost.PublishedAt ?? blogPost.CreatedAt,
                ReadTime = CalculateReadTime(blogPost.Content),
                Category = GetCategoryFromTags(blogPost.BlogPostTags),
                ImageUrl = blogPost.FeaturedImageUrl ?? GetDefaultImageUrl(),
                Slug = blogPost.Slug,
                ReadCount = blogPost.ReadCount,
                Tags = blogPost.BlogPostTags?.Select(t => t.Tag).ToList() ?? new List<string>(),
                FeaturedImageUrl = blogPost.FeaturedImageUrl,
                UserProfileId = blogPost.UserProfileId
            };
        }

        private static ArticlePreviewDto MapToArticlePreview(BlogPost blogPost)
        {
            return new ArticlePreviewDto
            {
                Id = blogPost.Id,
                Title = blogPost.Title,
                Excerpt = blogPost.Excerpt,
                Author = GetAuthorName(blogPost.UserProfile),
                PublishedAt = blogPost.PublishedAt ?? blogPost.CreatedAt,
                ReadTime = CalculateReadTime(blogPost.Content),
                Category = GetCategoryFromTags(blogPost.BlogPostTags),
                ImageUrl = blogPost.FeaturedImageUrl ?? GetDefaultImageUrl(),
                Slug = blogPost.Slug,
                ReadCount = blogPost.ReadCount,
                Tags = blogPost.BlogPostTags?.Select(t => t.Tag).ToList() ?? new List<string>(),
                FeaturedImageUrl = blogPost.FeaturedImageUrl
            };
        }

        private static string GetLimitedContent(string? content)
        {
            if (string.IsNullOrEmpty(content)) return string.Empty;

            // Return first 150 words or first 3 paragraphs, whichever is shorter
            var words = content.Split(' ');
            var paragraphs = content.Split(new[] { "\n\n" }, StringSplitOptions.RemoveEmptyEntries);

            var first150Words = string.Join(" ", words.Take(150));
            var firstThreeParagraphs = string.Join("\n\n", paragraphs.Take(3));

            return first150Words.Length < firstThreeParagraphs.Length ? first150Words : firstThreeParagraphs;
        }

        private static string GetAuthorName(UserProfile? userProfile)
        {
            if (userProfile?.User != null)
            {
                return $"{userProfile.User.FirstName} {userProfile.User.LastName}";
            }
            return "Неизвестен автор";
        }

        private static int CalculateReadTime(string? content)
        {
            if (string.IsNullOrEmpty(content)) return 1;

            var wordCount = content.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
            var readTime = Math.Max(1, (int)Math.Ceiling(wordCount / 200.0)); // 200 words per minute
            return readTime;
        }

        private static string GetCategoryFromTags(ICollection<BlogPostTag>? tags)
        {
            return tags?.FirstOrDefault()?.Tag ?? "Общи";
        }

        private static string GetDefaultImageUrl()
        {
            return "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop";
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null && int.TryParse(userIdClaim.Value, out var userId) ? userId : null;
        }

        private static string GenerateSlug(string title)
        {
            if (string.IsNullOrEmpty(title))
                return string.Empty;

            // Convert to lowercase and replace spaces with hyphens
            var slug = title.ToLowerInvariant()
                .Replace(" ", "-")
                .Replace(".", "")
                .Replace(",", "")
                .Replace("!", "")
                .Replace("?", "")
                .Replace(":", "")
                .Replace(";", "")
                .Replace("'", "")
                .Replace("\"", "");

            // Remove any remaining non-alphanumeric characters except hyphens
            slug = System.Text.RegularExpressions.Regex.Replace(slug, @"[^a-z0-9\-]", "");

            // Remove multiple consecutive hyphens
            slug = System.Text.RegularExpressions.Regex.Replace(slug, @"-+", "-");

            // Remove leading and trailing hyphens
            slug = slug.Trim('-');

            return slug;
        }

        private static ArticleManagementDto MapToArticleManagement(BlogPost blogPost)
        {
            return new ArticleManagementDto
            {
                Id = blogPost.Id,
                Title = blogPost.Title,
                Excerpt = blogPost.Excerpt,
                Content = blogPost.Content,
                Category = blogPost.Category,
                Tags = blogPost.BlogPostTags?.Select(t => t.Tag).ToList() ?? new List<string>(),
                FeaturedImageUrl = blogPost.FeaturedImageUrl,
                MetaDescription = blogPost.MetaDescription,
                MetaKeywords = blogPost.MetaKeywords,
                AllowComments = blogPost.AllowComments,
                IsFeatured = blogPost.IsFeatured,
                Status = blogPost.Status.ToString(),
                PublishedAt = blogPost.PublishedAt,
                LastSavedAt = blogPost.LastSavedAt,
                CreatedAt = blogPost.CreatedAt,
                UpdatedAt = blogPost.UpdatedAt,
                Slug = blogPost.Slug,
                ReadCount = blogPost.ReadCount,
                EstimatedReadTime = blogPost.EstimatedReadTime,
                UserProfileId = blogPost.UserProfileId,
                Author = GetAuthorName(blogPost.UserProfile)
            };
        }
    }
}
