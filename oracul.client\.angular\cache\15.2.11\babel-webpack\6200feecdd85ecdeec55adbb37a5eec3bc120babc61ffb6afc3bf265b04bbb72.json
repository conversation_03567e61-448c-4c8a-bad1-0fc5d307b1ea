{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { ArticlePreviewDialogComponent } from '../components/article-preview-dialog/article-preview-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"../../shared/services/article.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"ngx-quill\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/checkbox\";\nimport * as i17 from \"../components/image-upload/image-upload.component\";\nfunction ArticleEditorComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 9);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u0417\\u0430\\u0440\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ArticleEditorComponent_form_7_mat_error_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u0438\\u0435\\u0442\\u043E \\u0435 \\u0437\\u0430\\u0434\\u044A\\u043B\\u0436\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleEditorComponent_form_7_mat_error_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u0438\\u0435\\u0442\\u043E \\u043D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0431\\u044A\\u0434\\u0435 \\u043F\\u043E-\\u0434\\u044A\\u043B\\u0433\\u043E \\u043E\\u0442 200 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleEditorComponent_form_7_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u041A\\u0440\\u0430\\u0442\\u043A\\u043E\\u0442\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u0435 \\u0437\\u0430\\u0434\\u044A\\u043B\\u0436\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleEditorComponent_form_7_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u041A\\u0440\\u0430\\u0442\\u043A\\u043E\\u0442\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0431\\u044A\\u0434\\u0435 \\u043F\\u043E-\\u0434\\u044A\\u043B\\u0433\\u043E \\u043E\\u0442 500 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleEditorComponent_form_7_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r9, \" \");\n  }\n}\nfunction ArticleEditorComponent_form_7_mat_error_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Meta \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435\\u0442\\u043E \\u043D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0431\\u044A\\u0434\\u0435 \\u043F\\u043E-\\u0434\\u044A\\u043B\\u0433\\u043E \\u043E\\u0442 160 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleEditorComponent_form_7_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u041A\\u043B\\u044E\\u0447\\u043E\\u0432\\u0438\\u0442\\u0435 \\u0434\\u0443\\u043C\\u0438 \\u043D\\u0435 \\u043C\\u043E\\u0433\\u0430\\u0442 \\u0434\\u0430 \\u0431\\u044A\\u0434\\u0430\\u0442 \\u043F\\u043E-\\u0434\\u044A\\u043B\\u0433\\u0438 \\u043E\\u0442 500 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleEditorComponent_form_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 10)(1, \"mat-form-field\", 11)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"\\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u0438\\u0435 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 12);\n    i0.ɵɵtemplate(5, ArticleEditorComponent_form_7_mat_error_5_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵtemplate(6, ArticleEditorComponent_form_7_mat_error_6_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 11)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"\\u041A\\u0440\\u0430\\u0442\\u043A\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"textarea\", 14);\n    i0.ɵɵtemplate(11, ArticleEditorComponent_form_7_mat_error_11_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵtemplate(12, ArticleEditorComponent_form_7_mat_error_12_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-form-field\", 11)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"\\u041A\\u0430\\u0442\\u0435\\u0433\\u043E\\u0440\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-select\", 15)(17, \"mat-option\", 16);\n    i0.ɵɵtext(18, \"\\u0411\\u0435\\u0437 \\u043A\\u0430\\u0442\\u0435\\u0433\\u043E\\u0440\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ArticleEditorComponent_form_7_mat_option_19_Template, 2, 2, \"mat-option\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"mat-form-field\", 11)(21, \"mat-label\");\n    i0.ɵɵtext(22, \"\\u0422\\u0430\\u0433\\u043E\\u0432\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"input\", 18);\n    i0.ɵɵelementStart(24, \"mat-hint\");\n    i0.ɵɵtext(25, \"\\u0420\\u0430\\u0437\\u0434\\u0435\\u043B\\u0435\\u0442\\u0435 \\u0442\\u0430\\u0433\\u043E\\u0432\\u0435\\u0442\\u0435 \\u0441\\u044A\\u0441 \\u0437\\u0430\\u043F\\u0435\\u0442\\u0430\\u044F (\\u043D\\u0430\\u043F\\u0440. \\u0430\\u0441\\u0442\\u0440\\u043E\\u043B\\u043E\\u0433\\u0438\\u044F, \\u0442\\u0430\\u0440\\u043E, \\u0445\\u043E\\u0440\\u043E\\u0441\\u043A\\u043E\\u043F)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 19)(27, \"label\", 20);\n    i0.ɵɵtext(28, \"\\u0421\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"quill-editor\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 22)(31, \"label\", 20);\n    i0.ɵɵtext(32, \"\\u041E\\u0441\\u043D\\u043E\\u0432\\u043D\\u0430 \\u0441\\u043D\\u0438\\u043C\\u043A\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"app-image-upload\", 23);\n    i0.ɵɵlistener(\"imageUploaded\", function ArticleEditorComponent_form_7_Template_app_image_upload_imageUploaded_33_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onFeaturedImageUploaded($event));\n    })(\"imageRemoved\", function ArticleEditorComponent_form_7_Template_app_image_upload_imageRemoved_33_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onFeaturedImageRemoved());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"mat-card\", 24)(35, \"mat-card-subtitle\");\n    i0.ɵɵtext(36, \"SEO \\u043D\\u0430\\u0441\\u0442\\u0440\\u043E\\u0439\\u043A\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"mat-form-field\", 11)(38, \"mat-label\");\n    i0.ɵɵtext(39, \"Meta \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(40, \"textarea\", 25);\n    i0.ɵɵtemplate(41, ArticleEditorComponent_form_7_mat_error_41_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"mat-form-field\", 11)(43, \"mat-label\");\n    i0.ɵɵtext(44, \"\\u041A\\u043B\\u044E\\u0447\\u043E\\u0432\\u0438 \\u0434\\u0443\\u043C\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(45, \"input\", 26);\n    i0.ɵɵtemplate(46, ArticleEditorComponent_form_7_mat_error_46_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 27)(48, \"mat-checkbox\", 28);\n    i0.ɵɵtext(49, \" \\u0420\\u0430\\u0437\\u0440\\u0435\\u0448\\u0438 \\u043A\\u043E\\u043C\\u0435\\u043D\\u0442\\u0430\\u0440\\u0438 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"mat-checkbox\", 29);\n    i0.ɵɵtext(51, \" \\u041F\\u0440\\u0435\\u043F\\u043E\\u0440\\u044A\\u0447\\u0430\\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.articleForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.articleForm.get(\"title\")) == null ? null : tmp_1_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.articleForm.get(\"title\")) == null ? null : tmp_2_0.hasError(\"maxlength\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.articleForm.get(\"excerpt\")) == null ? null : tmp_3_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.articleForm.get(\"excerpt\")) == null ? null : tmp_4_0.hasError(\"maxlength\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"modules\", ctx_r1.quillConfig);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"currentImageUrl\", (tmp_7_0 = ctx_r1.articleForm.get(\"featuredImageUrl\")) == null ? null : tmp_7_0.value);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx_r1.articleForm.get(\"metaDescription\")) == null ? null : tmp_8_0.hasError(\"maxlength\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx_r1.articleForm.get(\"metaKeywords\")) == null ? null : tmp_9_0.hasError(\"maxlength\"));\n  }\n}\nexport let ArticleEditorComponent = /*#__PURE__*/(() => {\n  class ArticleEditorComponent {\n    constructor(fb, route, router, snackBar, dialog, articleService) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.articleService = articleService;\n      this.isEditMode = false;\n      this.isLoading = false;\n      this.isSaving = false;\n      // Quill editor configuration\n      this.quillConfig = {\n        toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{\n          'header': 1\n        }, {\n          'header': 2\n        }], [{\n          'list': 'ordered'\n        }, {\n          'list': 'bullet'\n        }], [{\n          'script': 'sub'\n        }, {\n          'script': 'super'\n        }], [{\n          'indent': '-1'\n        }, {\n          'indent': '+1'\n        }], [{\n          'direction': 'rtl'\n        }], [{\n          'size': ['small', false, 'large', 'huge']\n        }], [{\n          'header': [1, 2, 3, 4, 5, 6, false]\n        }], [{\n          'color': []\n        }, {\n          'background': []\n        }], [{\n          'font': []\n        }], [{\n          'align': []\n        }], ['clean'], ['link', 'image', 'video']]\n      };\n      this.categories = ['Астрология', 'Таро', 'Нумерология', 'Медитация', 'Кристали', 'Духовност', 'Хороскопи', 'Сънища', 'Енергия', 'Чакри'];\n      this.articleForm = this.createForm();\n    }\n    ngOnInit() {\n      this.route.params.subscribe(params => {\n        if (params['id']) {\n          this.isEditMode = true;\n          this.articleId = +params['id'];\n          this.loadArticle();\n        }\n      });\n    }\n    createForm() {\n      return this.fb.group({\n        title: ['', [Validators.required, Validators.maxLength(200)]],\n        excerpt: ['', [Validators.required, Validators.maxLength(500)]],\n        content: [''],\n        category: [''],\n        tags: [''],\n        featuredImageUrl: [''],\n        metaDescription: ['', Validators.maxLength(160)],\n        metaKeywords: ['', Validators.maxLength(500)],\n        allowComments: [true],\n        isFeatured: [false]\n      });\n    }\n    loadArticle() {\n      if (!this.articleId) return;\n      this.isLoading = true;\n      this.articleService.getArticleForEdit(this.articleId).subscribe({\n        next: article => {\n          this.populateForm(article);\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading article:', error);\n          this.snackBar.open('Грешка при зареждането на статията', 'Затвори', {\n            duration: 3000\n          });\n          this.isLoading = false;\n        }\n      });\n    }\n    populateForm(article) {\n      this.articleForm.patchValue({\n        title: article.title,\n        excerpt: article.excerpt,\n        content: article.content,\n        category: article.category,\n        tags: article.tags.join(', '),\n        featuredImageUrl: article.featuredImageUrl,\n        metaDescription: article.metaDescription,\n        metaKeywords: article.metaKeywords,\n        allowComments: article.allowComments,\n        isFeatured: article.isFeatured\n      });\n    }\n    onSaveDraft() {\n      this.saveArticle(true);\n    }\n    onPublish() {\n      this.saveArticle(false);\n    }\n    saveArticle(saveAsDraft) {\n      if (this.articleForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.isSaving = true;\n      const formValue = this.articleForm.value;\n      const tags = this.parseTags(formValue.tags);\n      if (this.isEditMode && this.articleId) {\n        const updateRequest = {\n          title: formValue.title,\n          excerpt: formValue.excerpt,\n          content: formValue.content,\n          category: formValue.category,\n          tags: tags,\n          featuredImageUrl: formValue.featuredImageUrl,\n          metaDescription: formValue.metaDescription,\n          metaKeywords: formValue.metaKeywords,\n          allowComments: formValue.allowComments,\n          isFeatured: formValue.isFeatured\n        };\n        this.articleService.updateArticle(this.articleId, updateRequest).subscribe({\n          next: article => {\n            this.snackBar.open('Статията е обновена успешно', 'Затвори', {\n              duration: 3000\n            });\n            if (!saveAsDraft) {\n              this.publishArticle(article.id);\n            } else {\n              this.router.navigate(['/articles/manage']);\n            }\n            this.isSaving = false;\n          },\n          error: error => {\n            console.error('Error updating article:', error);\n            this.snackBar.open('Грешка при обновяването на статията', 'Затвори', {\n              duration: 3000\n            });\n            this.isSaving = false;\n          }\n        });\n      } else {\n        const createRequest = {\n          title: formValue.title,\n          excerpt: formValue.excerpt,\n          content: formValue.content,\n          category: formValue.category,\n          tags: tags,\n          featuredImageUrl: formValue.featuredImageUrl,\n          metaDescription: formValue.metaDescription,\n          metaKeywords: formValue.metaKeywords,\n          allowComments: formValue.allowComments,\n          isFeatured: formValue.isFeatured,\n          saveAsDraft: saveAsDraft\n        };\n        this.articleService.createArticle(createRequest).subscribe({\n          next: article => {\n            this.snackBar.open('Статията е създадена успешно', 'Затвори', {\n              duration: 3000\n            });\n            this.router.navigate(['/articles/manage']);\n            this.isSaving = false;\n          },\n          error: error => {\n            console.error('Error creating article:', error);\n            this.snackBar.open('Грешка при създаването на статията', 'Затвори', {\n              duration: 3000\n            });\n            this.isSaving = false;\n          }\n        });\n      }\n    }\n    publishArticle(articleId) {\n      this.articleService.publishArticle(articleId).subscribe({\n        next: () => {\n          this.snackBar.open('Статията е публикувана успешно', 'Затвори', {\n            duration: 3000\n          });\n          this.router.navigate(['/articles/manage']);\n        },\n        error: error => {\n          console.error('Error publishing article:', error);\n          this.snackBar.open('Грешка при публикуването на статията', 'Затвори', {\n            duration: 3000\n          });\n        }\n      });\n    }\n    parseTags(tagsString) {\n      if (!tagsString) return [];\n      return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);\n    }\n    markFormGroupTouched() {\n      Object.keys(this.articleForm.controls).forEach(key => {\n        const control = this.articleForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    onCancel() {\n      this.router.navigate(['/articles/manage']);\n    }\n    onPreview() {\n      if (this.articleForm.invalid) {\n        this.markFormGroupTouched();\n        this.snackBar.open('Моля, попълнете задължителните полета', 'Затвори', {\n          duration: 3000\n        });\n        return;\n      }\n      const formValue = this.articleForm.value;\n      const tags = this.parseTags(formValue.tags);\n      const previewData = {\n        title: formValue.title,\n        excerpt: formValue.excerpt,\n        content: formValue.content,\n        category: formValue.category,\n        tags: tags,\n        featuredImageUrl: formValue.featuredImageUrl,\n        author: 'Вие',\n        estimatedReadTime: this.calculateReadTime(formValue.content)\n      };\n      this.dialog.open(ArticlePreviewDialogComponent, {\n        width: '90vw',\n        maxWidth: '800px',\n        maxHeight: '90vh',\n        data: previewData\n      });\n    }\n    calculateReadTime(content) {\n      if (!content) return 1;\n      // Remove HTML tags for word count\n      const textContent = content.replace(/<[^>]*>/g, '');\n      const words = textContent.trim().split(/\\s+/).length;\n      // Average reading speed is 200-250 words per minute\n      const wordsPerMinute = 225;\n      const readTime = Math.ceil(words / wordsPerMinute);\n      return Math.max(1, readTime); // Minimum 1 minute\n    }\n\n    onFeaturedImageUploaded(imageUrl) {\n      this.articleForm.patchValue({\n        featuredImageUrl: imageUrl\n      });\n    }\n    onFeaturedImageRemoved() {\n      this.articleForm.patchValue({\n        featuredImageUrl: ''\n      });\n    }\n    static {\n      this.ɵfac = function ArticleEditorComponent_Factory(t) {\n        return new (t || ArticleEditorComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.ArticleService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ArticleEditorComponent,\n        selectors: [[\"app-article-editor\"]],\n        decls: 23,\n        vars: 9,\n        consts: [[1, \"article-editor-container\"], [1, \"editor-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [1, \"loading-container\"], [\"mode\", \"indeterminate\"], [3, \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"title\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0437\\u0430\\u0433\\u043B\\u0430\\u0432\\u0438\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"excerpt\", \"rows\", \"3\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u043A\\u0440\\u0430\\u0442\\u043A\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430\"], [\"formControlName\", \"category\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"tags\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0442\\u0430\\u0433\\u043E\\u0432\\u0435, \\u0440\\u0430\\u0437\\u0434\\u0435\\u043B\\u0435\\u043D\\u0438 \\u0441\\u044A\\u0441 \\u0437\\u0430\\u043F\\u0435\\u0442\\u0430\\u044F\"], [1, \"content-editor\"], [1, \"editor-label\"], [\"formControlName\", \"content\", \"placeholder\", \"\\u041D\\u0430\\u043F\\u0438\\u0448\\u0435\\u0442\\u0435 \\u0441\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435\\u0442\\u043E \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430 \\u0442\\u0443\\u043A...\", 1, \"full-width\", 3, \"modules\"], [1, \"featured-image-section\"], [\"placeholder\", \"\\u041A\\u0430\\u0447\\u0435\\u0442\\u0435 \\u043E\\u0441\\u043D\\u043E\\u0432\\u043D\\u0430 \\u0441\\u043D\\u0438\\u043C\\u043A\\u0430 \\u0437\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430\", 3, \"currentImageUrl\", \"imageUploaded\", \"imageRemoved\"], [1, \"seo-section\"], [\"matInput\", \"\", \"formControlName\", \"metaDescription\", \"rows\", \"2\", \"placeholder\", \"\\u041E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u0437\\u0430 \\u0442\\u044A\\u0440\\u0441\\u0430\\u0447\\u043A\\u0438\\u0442\\u0435 (\\u0434\\u043E 160 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430)\"], [\"matInput\", \"\", \"formControlName\", \"metaKeywords\", \"placeholder\", \"\\u043A\\u043B\\u044E\\u0447\\u043E\\u0432\\u0430 \\u0434\\u0443\\u043C\\u0430 1, \\u043A\\u043B\\u044E\\u0447\\u043E\\u0432\\u0430 \\u0434\\u0443\\u043C\\u0430 2, \\u043A\\u043B\\u044E\\u0447\\u043E\\u0432\\u0430 \\u0434\\u0443\\u043C\\u0430 3\"], [1, \"options-section\"], [\"formControlName\", \"allowComments\"], [\"formControlName\", \"isFeatured\"], [3, \"value\"]],\n        template: function ArticleEditorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"mat-card-content\");\n            i0.ɵɵtemplate(6, ArticleEditorComponent_div_6_Template, 4, 0, \"div\", 2);\n            i0.ɵɵtemplate(7, ArticleEditorComponent_form_7_Template, 52, 10, \"form\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"mat-card-actions\", 4)(9, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function ArticleEditorComponent_Template_button_click_9_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(10, \" \\u041E\\u0442\\u043A\\u0430\\u0437 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function ArticleEditorComponent_Template_button_click_11_listener() {\n              return ctx.onPreview();\n            });\n            i0.ɵɵelementStart(12, \"mat-icon\");\n            i0.ɵɵtext(13, \"visibility\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(14, \" \\u041F\\u0440\\u0435\\u0433\\u043B\\u0435\\u0434 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function ArticleEditorComponent_Template_button_click_15_listener() {\n              return ctx.onSaveDraft();\n            });\n            i0.ɵɵelementStart(16, \"mat-icon\");\n            i0.ɵɵtext(17, \"save\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function ArticleEditorComponent_Template_button_click_19_listener() {\n              return ctx.onPublish();\n            });\n            i0.ɵɵelementStart(20, \"mat-icon\");\n            i0.ɵɵtext(21, \"publish\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"\\u0420\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\" : \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u043D\\u043E\\u0432\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isSaving);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isSaving || ctx.articleForm.invalid);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.isSaving || ctx.articleForm.invalid);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isSaving ? \"\\u0417\\u0430\\u043F\\u0438\\u0441\\u0432\\u0430\\u043D\\u0435...\" : \"\\u0417\\u0430\\u043F\\u0430\\u0437\\u0438 \\u043A\\u0430\\u0442\\u043E \\u0447\\u0435\\u0440\\u043D\\u043E\\u0432\\u0430\", \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.isSaving || ctx.articleForm.invalid);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isSaving ? \"\\u041F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u043D\\u0435...\" : \"\\u041F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u0439\", \" \");\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.QuillEditorComponent, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatButton, i10.MatIcon, i11.MatProgressSpinner, i12.MatFormField, i12.MatLabel, i12.MatHint, i12.MatError, i13.MatInput, i14.MatSelect, i15.MatOption, i16.MatCheckbox, i17.ImageUploadComponent],\n        styles: [\".article-editor-container[_ngcontent-%COMP%]{max-width:1000px;margin:0 auto;padding:20px}.editor-card[_ngcontent-%COMP%]{margin-bottom:20px}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#666}.content-editor[_ngcontent-%COMP%]{margin-bottom:16px}.editor-label[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:500;color:#3f2f4e;margin-bottom:8px}.featured-image-section[_ngcontent-%COMP%]{margin-bottom:16px}.seo-section[_ngcontent-%COMP%]{margin:20px 0;padding:16px;background-color:#f8f9fa}.options-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin:20px 0}.options-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{margin-bottom:8px}  .ql-editor{min-height:300px;font-family:Roboto,sans-serif;font-size:14px;line-height:1.6}  .ql-toolbar{border-top:1px solid #ccc;border-left:1px solid #ccc;border-right:1px solid #ccc;background-color:#f8f9fa}  .ql-container{border-bottom:1px solid #ccc;border-left:1px solid #ccc;border-right:1px solid #ccc}  .ql-editor.ql-blank:before{color:#999;font-style:italic}  .ql-toolbar .ql-stroke{stroke:#3f2f4e}  .ql-toolbar .ql-fill{fill:#3f2f4e}  .ql-toolbar button:hover .ql-stroke{stroke:#d2a6d0}  .ql-toolbar button:hover .ql-fill{fill:#d2a6d0}  .ql-toolbar button.ql-active .ql-stroke{stroke:#67455c}  .ql-toolbar button.ql-active .ql-fill{fill:#67455c}@media (max-width: 768px){.article-editor-container[_ngcontent-%COMP%]{padding:10px}.editor-card[_ngcontent-%COMP%]{margin:0}  .ql-toolbar{padding:8px}  .ql-editor{min-height:200px}}mat-card-actions[_ngcontent-%COMP%]{padding:16px 24px;gap:8px}mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:8px}mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child{margin-left:0}.mat-form-field.ng-invalid[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%], .mat-form-field.ng-invalid[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{color:#f44336}  .ql-editor::-webkit-scrollbar{width:8px}  .ql-editor::-webkit-scrollbar-track{background:#f1f1f1}  .ql-editor::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:4px}  .ql-editor::-webkit-scrollbar-thumb:hover{background:#a8a8a8}\"]\n      });\n    }\n  }\n  return ArticleEditorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}