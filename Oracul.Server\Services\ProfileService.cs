using Microsoft.EntityFrameworkCore;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;
using Oracul.Server.Models;

namespace Oracul.Server.Services
{
    /// <summary>
    /// Service for managing astrology-focused user profiles
    /// </summary>
    public class ProfileService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProfileService> _logger;

        public ProfileService(IUnitOfWork unitOfWork, ILogger<ProfileService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Get profile by ID with complete data
        /// </summary>
        public async Task<ApiResponse<UserProfileDto>> GetProfileAsync(int profileId)
        {
            try
            {
                var profile = await _unitOfWork.Profiles.GetCompleteProfileAsync(profileId);
                
                if (profile == null)
                {
                    return new ApiResponse<UserProfileDto>
                    {
                        Success = false,
                        Message = "Профилът не е намерен",
                        Data = null
                    };
                }

                var profileDto = MapToDto(profile);
                
                return new ApiResponse<UserProfileDto>
                {
                    Success = true,
                    Message = "Профилът е зареден успешно",
                    Data = profileDto
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при зареждане на профил с ID {ProfileId}", profileId);
                return new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Възникна грешка при зареждане на профила",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// Get profile by slug with complete data
        /// </summary>
        public async Task<ApiResponse<UserProfileDto>> GetProfileBySlugAsync(string slug)
        {
            try
            {
                var profile = await _unitOfWork.Profiles.GetCompleteProfileBySlugAsync(slug);
                
                if (profile == null)
                {
                    return new ApiResponse<UserProfileDto>
                    {
                        Success = false,
                        Message = "Профилът не е намерен",
                        Data = null
                    };
                }

                var profileDto = MapToDto(profile);
                
                return new ApiResponse<UserProfileDto>
                {
                    Success = true,
                    Message = "Профилът е зареден успешно",
                    Data = profileDto
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при зареждане на профил с slug {Slug}", slug);
                return new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Възникна грешка при зареждане на профила",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// Get current user's profile
        /// </summary>
        public async Task<ApiResponse<UserProfileDto>> GetCurrentUserProfileAsync(int userId)
        {
            try
            {
                var profile = await _unitOfWork.Profiles.GetCompleteProfileByUserIdAsync(userId);
                
                if (profile == null)
                {
                    return new ApiResponse<UserProfileDto>
                    {
                        Success = false,
                        Message = "Профилът не е намерен",
                        Data = null
                    };
                }

                var profileDto = MapToDto(profile);
                
                return new ApiResponse<UserProfileDto>
                {
                    Success = true,
                    Message = "Профилът е зареден успешно",
                    Data = profileDto
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при зареждане на профил за потребител {UserId}", userId);
                return new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Възникна грешка при зареждане на профила",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// Create a new profile
        /// </summary>
        public async Task<ApiResponse<UserProfileDto>> CreateProfileAsync(int userId, CreateProfileRequest request)
        {
            try
            {
                // Check if user already has a profile
                var existingProfile = await _unitOfWork.Profiles.GetByUserIdAsync(userId);
                if (existingProfile != null)
                {
                    return new ApiResponse<UserProfileDto>
                    {
                        Success = false,
                        Message = "Потребителят вече има профил",
                        Errors = new List<string> { "Един потребител може да има само един профил" }
                    };
                }

                // Check if username is available
                var isUsernameAvailable = await _unitOfWork.Profiles.IsUsernameAvailableAsync(request.Username);
                if (!isUsernameAvailable)
                {
                    return new ApiResponse<UserProfileDto>
                    {
                        Success = false,
                        Message = "Потребителското име не е налично",
                        Errors = new List<string> { "Моля, изберете друго потребителско име" }
                    };
                }

                // Generate slug
                var slug = GenerateSlug(request.FirstName, request.LastName);
                var isSlugAvailable = await _unitOfWork.Profiles.IsSlugAvailableAsync(slug);
                if (!isSlugAvailable)
                {
                    slug = $"{slug}-{DateTime.UtcNow.Ticks}";
                }

                // Create profile entity
                var profile = new UserProfile
                {
                    UserId = userId,
                    Username = request.Username,
                    Slug = slug,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    ProfessionalTitle = request.ProfessionalTitle,
                    Headline = request.Headline,
                    IsPublic = request.IsPublic,
                    ProfileCompletionPercentage = 0
                };

                _unitOfWork.Profiles.Add(profile);
                await _unitOfWork.SaveChangesAsync();

                // Calculate initial completion percentage
                await _unitOfWork.Profiles.UpdateProfileCompletionPercentageAsync(profile.Id);
                await _unitOfWork.SaveChangesAsync();

                // Get complete profile for response
                var completeProfile = await _unitOfWork.Profiles.GetCompleteProfileAsync(profile.Id);
                var profileDto = MapToDto(completeProfile!);

                return new ApiResponse<UserProfileDto>
                {
                    Success = true,
                    Message = "Профилът е създаден успешно",
                    Data = profileDto
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при създаване на профил за потребител {UserId}", userId);
                return new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Възникна грешка при създаване на профила",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// Update an existing profile
        /// </summary>
        public async Task<ApiResponse<UserProfileDto>> UpdateProfileAsync(int userId, UpdateProfileRequest request)
        {
            try
            {
                var profile = await _unitOfWork.Profiles.GetCompleteProfileByUserIdAsync(userId);
                
                if (profile == null)
                {
                    return new ApiResponse<UserProfileDto>
                    {
                        Success = false,
                        Message = "Профилът не е намерен",
                        Data = null
                    };
                }

                // Update basic fields
                if (!string.IsNullOrEmpty(request.Username) && request.Username != profile.Username)
                {
                    var isUsernameAvailable = await _unitOfWork.Profiles.IsUsernameAvailableAsync(request.Username, profile.Id);
                    if (!isUsernameAvailable)
                    {
                        return new ApiResponse<UserProfileDto>
                        {
                            Success = false,
                            Message = "Потребителското име не е налично",
                            Errors = new List<string> { "Моля, изберете друго потребителско име" }
                        };
                    }
                    profile.Username = request.Username;
                }

                if (!string.IsNullOrEmpty(request.FirstName)) profile.FirstName = request.FirstName;
                if (!string.IsNullOrEmpty(request.LastName)) profile.LastName = request.LastName;
                if (request.ProfilePhotoUrl != null) profile.ProfilePhotoUrl = request.ProfilePhotoUrl;
                if (request.CoverPhotoUrl != null) profile.CoverPhotoUrl = request.CoverPhotoUrl;
                if (request.ProfessionalTitle != null) profile.ProfessionalTitle = request.ProfessionalTitle;
                if (request.Headline != null) profile.Headline = request.Headline;
                if (request.Summary != null) profile.Summary = request.Summary;
                if (request.IsPublic.HasValue) profile.IsPublic = request.IsPublic.Value;

                // Update location if provided
                if (request.Location != null)
                {
                    if (profile.Location == null)
                    {
                        profile.Location = new ProfileLocation
                        {
                            UserProfileId = profile.Id
                        };
                    }

                    profile.Location.City = request.Location.City;
                    profile.Location.State = request.Location.State;
                    profile.Location.Country = request.Location.Country;
                    profile.Location.DisplayLocation = request.Location.DisplayLocation;
                }

                // Update contact info if provided
                if (request.ContactInfo != null)
                {
                    if (profile.ContactInfo == null)
                    {
                        profile.ContactInfo = new ContactInformation
                        {
                            UserProfileId = profile.Id
                        };
                    }

                    profile.ContactInfo.Email = request.ContactInfo.Email;
                    profile.ContactInfo.IsEmailPublic = request.ContactInfo.IsEmailPublic;
                    profile.ContactInfo.Website = request.ContactInfo.Website;
                    profile.ContactInfo.PortfolioUrl = request.ContactInfo.PortfolioUrl;

                    // Update business address if provided
                    if (request.ContactInfo.BusinessAddress != null)
                    {
                        if (profile.ContactInfo.BusinessAddress == null)
                        {
                            profile.ContactInfo.BusinessAddress = new BusinessAddress
                            {
                                ContactInformationId = profile.ContactInfo.Id
                            };
                        }

                        var ba = profile.ContactInfo.BusinessAddress;
                        ba.Street = request.ContactInfo.BusinessAddress.Street;
                        ba.City = request.ContactInfo.BusinessAddress.City;
                        ba.State = request.ContactInfo.BusinessAddress.State;
                        ba.PostalCode = request.ContactInfo.BusinessAddress.PostalCode;
                        ba.Country = request.ContactInfo.BusinessAddress.Country;
                        ba.IsPublic = request.ContactInfo.BusinessAddress.IsPublic;
                    }
                }

                _unitOfWork.Profiles.Update(profile);
                await _unitOfWork.SaveChangesAsync();

                // Update completion percentage
                await _unitOfWork.Profiles.UpdateProfileCompletionPercentageAsync(profile.Id);
                await _unitOfWork.SaveChangesAsync();

                // Get updated complete profile
                var updatedProfile = await _unitOfWork.Profiles.GetCompleteProfileAsync(profile.Id);
                var profileDto = MapToDto(updatedProfile!);

                return new ApiResponse<UserProfileDto>
                {
                    Success = true,
                    Message = "Профилът е обновен успешно",
                    Data = profileDto
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при обновяване на профил за потребител {UserId}", userId);
                return new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Възникна грешка при обновяване на профила",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// Delete a profile
        /// </summary>
        public async Task<ApiResponse<bool>> DeleteProfileAsync(int userId)
        {
            try
            {
                var profile = await _unitOfWork.Profiles.GetByUserIdAsync(userId);
                
                if (profile == null)
                {
                    return new ApiResponse<bool>
                    {
                        Success = false,
                        Message = "Профилът не е намерен",
                        Data = false
                    };
                }

                _unitOfWork.Profiles.Remove(profile);
                await _unitOfWork.SaveChangesAsync();

                return new ApiResponse<bool>
                {
                    Success = true,
                    Message = "Профилът е изтрит успешно",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при изтриване на профил за потребител {UserId}", userId);
                return new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Възникна грешка при изтриване на профила",
                    Errors = new List<string> { ex.Message },
                    Data = false
                };
            }
        }

        /// <summary>
        /// Search profiles
        /// </summary>
        public async Task<ApiResponse<ProfileSearchResult>> SearchProfilesAsync(ProfileSearchRequest request)
        {
            try
            {
                IEnumerable<UserProfile> profiles;

                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    profiles = await _unitOfWork.Profiles.SearchProfilesAsync(request.SearchTerm, request.Page, request.PageSize);
                }
                else if (!string.IsNullOrEmpty(request.Location))
                {
                    profiles = await _unitOfWork.Profiles.GetProfilesByLocationAsync(request.Location, request.Page, request.PageSize);
                }
                else if (request.Skills.Any())
                {
                    // For simplicity, search by first skill only
                    profiles = await _unitOfWork.Profiles.GetProfilesBySkillAsync(request.Skills.First(), request.Page, request.PageSize);
                }
                else
                {
                    profiles = await _unitOfWork.Profiles.GetPublicProfilesAsync(request.Page, request.PageSize);
                }

                var profileDtos = profiles.Select(MapToDto).ToList();
                var totalCount = profileDtos.Count; // This is simplified - in real implementation, you'd get total count separately

                var result = new ProfileSearchResult
                {
                    Profiles = profileDtos,
                    TotalCount = totalCount,
                    CurrentPage = request.Page,
                    PageSize = request.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
                };

                return new ApiResponse<ProfileSearchResult>
                {
                    Success = true,
                    Message = "Търсенето е завършено успешно",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при търсене на профили");
                return new ApiResponse<ProfileSearchResult>
                {
                    Success = false,
                    Message = "Възникна грешка при търсене на профили",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// Record profile view
        /// </summary>
        public async Task<ApiResponse<bool>> RecordProfileViewAsync(ProfileViewRequest request)
        {
            try
            {
                await _unitOfWork.Profiles.IncrementProfileViewAsync(request.ProfileId);
                await _unitOfWork.SaveChangesAsync();

                return new ApiResponse<bool>
                {
                    Success = true,
                    Message = "Прегледът е записан успешно",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при записване на преглед на профил {ProfileId}", request.ProfileId);
                return new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Възникна грешка при записване на прегледа",
                    Errors = new List<string> { ex.Message },
                    Data = false
                };
            }
        }

        /// <summary>
        /// Generate URL-friendly slug from name
        /// </summary>
        private static string GenerateSlug(string firstName, string lastName)
        {
            var slug = $"{firstName}-{lastName}".ToLower()
                .Replace(" ", "-")
                .Replace("ъ", "a")
                .Replace("ь", "y")
                .Replace("я", "ya")
                .Replace("ю", "yu")
                .Replace("щ", "sht")
                .Replace("ш", "sh")
                .Replace("ч", "ch")
                .Replace("ц", "ts")
                .Replace("ж", "zh")
                .Replace("й", "y");

            // Remove any non-alphanumeric characters except hyphens
            return System.Text.RegularExpressions.Regex.Replace(slug, @"[^a-z0-9\-]", "");
        }

        /// <summary>
        /// Map UserProfile entity to DTO
        /// </summary>
        private static UserProfileDto MapToDto(UserProfile profile)
        {
            return new UserProfileDto
            {
                Id = profile.Id,
                UserId = profile.UserId,
                Username = profile.Username,
                Slug = profile.Slug,
                IsPublic = profile.IsPublic,
                ProfileCompletionPercentage = profile.ProfileCompletionPercentage,
                ProfilePhotoUrl = profile.ProfilePhotoUrl,
                CoverPhotoUrl = profile.CoverPhotoUrl,
                FirstName = profile.FirstName,
                LastName = profile.LastName,
                ProfessionalTitle = profile.ProfessionalTitle,
                Headline = profile.Headline,
                Summary = profile.Summary,
                ProfileViews = profile.ProfileViews,
                LastViewedAt = profile.LastViewedAt,
                CreatedAt = profile.CreatedAt,
                UpdatedAt = profile.UpdatedAt ?? profile.CreatedAt,

                Location = profile.Location != null ? new ProfileLocationDto
                {
                    City = profile.Location.City,
                    State = profile.Location.State,
                    Country = profile.Location.Country,
                    DisplayLocation = profile.Location.DisplayLocation
                } : null,

                ContactInfo = profile.ContactInfo != null ? new ContactInformationDto
                {
                    Email = profile.ContactInfo.Email,
                    IsEmailPublic = profile.ContactInfo.IsEmailPublic,
                    Website = profile.ContactInfo.Website,
                    PortfolioUrl = profile.ContactInfo.PortfolioUrl,
                    BusinessAddress = profile.ContactInfo.BusinessAddress != null ? new BusinessAddressDto
                    {
                        Street = profile.ContactInfo.BusinessAddress.Street,
                        City = profile.ContactInfo.BusinessAddress.City,
                        State = profile.ContactInfo.BusinessAddress.State,
                        PostalCode = profile.ContactInfo.BusinessAddress.PostalCode,
                        Country = profile.ContactInfo.BusinessAddress.Country,
                        IsPublic = profile.ContactInfo.BusinessAddress.IsPublic
                    } : null,
                    PhoneNumbers = profile.ContactInfo.PhoneNumbers.Select(pn => new PhoneNumberDto
                    {
                        Id = pn.Id,
                        Number = pn.Number,
                        Type = pn.Type,
                        IsPublic = pn.IsPublic,
                        IsPrimary = pn.IsPrimary
                    }).ToList()
                } : null,

                Skills = profile.Skills.Select(s => new ProfileSkillDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Category = s.Category,
                    Endorsements = s.Endorsements,
                    ProficiencyLevel = s.ProficiencyLevel,
                    IsEndorsedByCurrentUser = false // This would need to be calculated based on current user
                }).ToList(),

                BlogPosts = profile.BlogPosts.Select(bp => new BlogPostDto
                {
                    Id = bp.Id,
                    Title = bp.Title,
                    Excerpt = bp.Excerpt,
                    Content = bp.Content,
                    PublishedAt = bp.PublishedAt ?? bp.CreatedAt,
                    ReadCount = bp.ReadCount,
                    FeaturedImageUrl = bp.FeaturedImageUrl,
                    Slug = bp.Slug,
                    Tags = bp.BlogPostTags.Select(t => t.Tag).ToList()
                }).ToList(),

                Achievements = profile.Achievements.Select(a => new AchievementDto
                {
                    Id = a.Id,
                    Title = a.Title,
                    Description = a.Description,
                    AchievedAt = a.AchievedAt,
                    Organization = a.Organization,
                    ImageUrl = a.ImageUrl,
                    VerificationUrl = a.VerificationUrl
                }).ToList(),

                Certifications = profile.Certifications.Select(c => new CertificationDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    IssuingOrganization = c.IssuingOrganization,
                    IssueDate = c.IssueDate,
                    ExpirationDate = c.ExpirationDate,
                    CredentialId = c.CredentialId,
                    CredentialUrl = c.CredentialUrl,
                    ImageUrl = c.ImageUrl
                }).ToList(),

                Experiences = profile.Experiences.Select(e => new WorkExperienceDto
                {
                    Id = e.Id,
                    Company = e.Company,
                    Position = e.Position,
                    StartDate = e.StartDate,
                    EndDate = e.EndDate,
                    IsCurrent = e.IsCurrent,
                    Description = e.Description,
                    Location = e.Location,
                    CompanyLogoUrl = e.CompanyLogoUrl,
                    Achievements = e.WorkAchievements.Select(wa => wa.Achievement).ToList()
                }).ToList(),

                PortfolioItems = profile.PortfolioItems.Select(pi => new PortfolioItemDto
                {
                    Id = pi.Id,
                    Title = pi.Title,
                    Description = pi.Description,
                    ProjectUrl = pi.ProjectUrl,
                    GithubUrl = pi.GithubUrl,
                    CompletedAt = pi.CompletedAt,
                    ClientName = pi.ClientName,
                    Category = pi.Category,
                    ImageUrls = pi.PortfolioImages.OrderBy(img => img.DisplayOrder).Select(img => img.ImageUrl).ToList(),
                    Technologies = pi.PortfolioTechnologies.Select(pt => pt.Technology).ToList(),
                    Testimonial = pi.ClientTestimonial != null ? new ClientTestimonialDto
                    {
                        Id = pi.ClientTestimonial.Id,
                        ClientName = pi.ClientTestimonial.ClientName,
                        ClientTitle = pi.ClientTestimonial.ClientTitle,
                        ClientCompany = pi.ClientTestimonial.ClientCompany,
                        TestimonialText = pi.ClientTestimonial.TestimonialText,
                        Rating = pi.ClientTestimonial.Rating,
                        ClientPhotoUrl = pi.ClientTestimonial.ClientPhotoUrl,
                        GivenAt = pi.ClientTestimonial.GivenAt
                    } : null
                }).ToList(),

                SocialLinks = profile.SocialLinks.Select(sl => new SocialLinkDto
                {
                    Id = sl.Id,
                    Platform = sl.Platform,
                    Url = sl.Url,
                    DisplayName = sl.DisplayName,
                    IsPublic = sl.IsPublic
                }).ToList()
            };
        }
    }
}
