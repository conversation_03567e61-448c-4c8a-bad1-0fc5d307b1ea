{"ast": null, "code": "import { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../shared/services/article.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/form-field\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/table\";\nimport * as i15 from \"@angular/material/paginator\";\nimport * as i16 from \"@angular/material/sort\";\nimport * as i17 from \"@angular/material/menu\";\nimport * as i18 from \"@angular/material/divider\";\nfunction ArticleManagementComponent_mat_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction ArticleManagementComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 13);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u0417\\u0430\\u0440\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\\u0442\\u0435...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ArticleManagementComponent_div_21_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"\\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const article_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(article_r20.category);\n  }\n}\nfunction ArticleManagementComponent_div_21_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 31)(1, \"div\", 32)(2, \"span\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ArticleManagementComponent_div_21_td_4_span_4_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const article_r20 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r20.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", article_r20.category);\n  }\n}\nfunction ArticleManagementComponent_div_21_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 36);\n    i0.ɵɵtext(1, \"\\u0421\\u0442\\u0430\\u0442\\u0443\\u0441\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37)(1, \"mat-chip\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const article_r23 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"color\", ctx_r7.getStatusColor(article_r23.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", article_r23.status, \" \");\n  }\n}\nfunction ArticleManagementComponent_div_21_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 36);\n    i0.ɵɵtext(1, \"\\u041A\\u0430\\u0442\\u0435\\u0433\\u043E\\u0440\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const article_r24 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", article_r24.category || \"\\u0411\\u0435\\u0437 \\u043A\\u0430\\u0442\\u0435\\u0433\\u043E\\u0440\\u0438\\u044F\", \" \");\n  }\n}\nfunction ArticleManagementComponent_div_21_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"\\u0414\\u0430\\u0442\\u0430 \\u043D\\u0430 \\u043F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const article_r25 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, article_r25.publishedAt, \"dd.MM.yyyy HH:mm\"), \" \");\n  }\n}\nfunction ArticleManagementComponent_div_21_td_13_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtext(1, \"\\u041D\\u0435 \\u0435 \\u043F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u043D\\u0430\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37);\n    i0.ɵɵtemplate(1, ArticleManagementComponent_div_21_td_13_div_1_Template, 3, 4, \"div\", 39);\n    i0.ɵɵtemplate(2, ArticleManagementComponent_div_21_td_13_ng_template_2_Template, 2, 0, \"ng-template\", null, 40, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const article_r25 = ctx.$implicit;\n    const _r27 = i0.ɵɵreference(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", article_r25.publishedAt)(\"ngIfElse\", _r27);\n  }\n}\nfunction ArticleManagementComponent_div_21_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵtext(1, \"\\u041F\\u0440\\u0435\\u0433\\u043B\\u0435\\u0434\\u0438\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37)(1, \"div\", 42)(2, \"mat-icon\", 43);\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const article_r30 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(article_r30.readCount);\n  }\n}\nfunction ArticleManagementComponent_div_21_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 36);\n    i0.ɵɵtext(1, \"\\u0414\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_19_ng_template_6_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const article_r34 = i0.ɵɵnextContext().article;\n      const ctx_r37 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r37.publishArticle(article_r34.id));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"publish\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u041F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u0439\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_19_ng_template_6_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const article_r34 = i0.ɵɵnextContext().article;\n      const ctx_r40 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r40.unpublishArticle(article_r34.id));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"unpublished\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u0421\\u043A\\u0440\\u0438\\u0439\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ArticleManagementComponent_div_21_td_19_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const article_r34 = restoredCtx.article;\n      const ctx_r43 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r43.viewArticle(article_r34));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u041F\\u0440\\u0435\\u0433\\u043B\\u0435\\u0434\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const article_r34 = restoredCtx.article;\n      const ctx_r45 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r45.editArticle(article_r34.id));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"\\u0420\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, ArticleManagementComponent_div_21_td_19_ng_template_6_button_10_Template, 5, 0, \"button\", 48);\n    i0.ɵɵtemplate(11, ArticleManagementComponent_div_21_td_19_ng_template_6_button_11_Template, 5, 0, \"button\", 48);\n    i0.ɵɵelement(12, \"mat-divider\");\n    i0.ɵɵelementStart(13, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_td_19_ng_template_6_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const article_r34 = restoredCtx.article;\n      const ctx_r46 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r46.deleteArticle(article_r34.id, article_r34.title));\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"\\u0418\\u0437\\u0442\\u0440\\u0438\\u0439\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const article_r34 = ctx.article;\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.canPublish(article_r34.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.canUnpublish(article_r34.status));\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    article: a0\n  };\n};\nfunction ArticleManagementComponent_div_21_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 37)(1, \"button\", 44)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 45);\n    i0.ɵɵtemplate(6, ArticleManagementComponent_div_21_td_19_ng_template_6_Template, 18, 2, \"ng-template\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const article_r31 = ctx.$implicit;\n    const _r32 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r32)(\"matMenuTriggerData\", i0.ɵɵpureFunction1(2, _c0, article_r31));\n  }\n}\nfunction ArticleManagementComponent_div_21_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 50);\n  }\n}\nfunction ArticleManagementComponent_div_21_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 51);\n  }\n}\nfunction ArticleManagementComponent_div_21_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"article\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u041D\\u044F\\u043C\\u0430 \\u043D\\u0430\\u043C\\u0435\\u0440\\u0435\\u043D\\u0438 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u0417\\u0430\\u043F\\u043E\\u0447\\u043D\\u0435\\u0442\\u0435 \\u0434\\u0430 \\u0441\\u044A\\u0437\\u0434\\u0430\\u0432\\u0430\\u0442\\u0435 \\u0441\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435, \\u043A\\u0430\\u0442\\u043E \\u043D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 \\u0431\\u0443\\u0442\\u043E\\u043D\\u0430 \\\"\\u041D\\u043E\\u0432\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\\".\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ArticleManagementComponent_div_21_div_22_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.createNewArticle());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u0421\\u044A\\u0437\\u0434\\u0430\\u0439 \\u043F\\u044A\\u0440\\u0432\\u0430\\u0442\\u0430 \\u0441\\u0438 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function () {\n  return [5, 10, 25, 50];\n};\nfunction ArticleManagementComponent_div_21_mat_paginator_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-paginator\", 54);\n    i0.ɵɵlistener(\"page\", function ArticleManagementComponent_div_21_mat_paginator_23_Template_mat_paginator_page_0_listener($event) {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.onPageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"length\", ctx_r19.totalCount)(\"pageSize\", ctx_r19.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(3, _c1));\n  }\n}\nfunction ArticleManagementComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"table\", 15);\n    i0.ɵɵelementContainerStart(2, 16);\n    i0.ɵɵtemplate(3, ArticleManagementComponent_div_21_th_3_Template, 2, 0, \"th\", 17);\n    i0.ɵɵtemplate(4, ArticleManagementComponent_div_21_td_4_Template, 5, 2, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 19);\n    i0.ɵɵtemplate(6, ArticleManagementComponent_div_21_th_6_Template, 2, 0, \"th\", 20);\n    i0.ɵɵtemplate(7, ArticleManagementComponent_div_21_td_7_Template, 3, 2, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 22);\n    i0.ɵɵtemplate(9, ArticleManagementComponent_div_21_th_9_Template, 2, 0, \"th\", 20);\n    i0.ɵɵtemplate(10, ArticleManagementComponent_div_21_td_10_Template, 2, 1, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 23);\n    i0.ɵɵtemplate(12, ArticleManagementComponent_div_21_th_12_Template, 2, 0, \"th\", 17);\n    i0.ɵɵtemplate(13, ArticleManagementComponent_div_21_td_13_Template, 4, 2, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 24);\n    i0.ɵɵtemplate(15, ArticleManagementComponent_div_21_th_15_Template, 2, 0, \"th\", 17);\n    i0.ɵɵtemplate(16, ArticleManagementComponent_div_21_td_16_Template, 6, 1, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 25);\n    i0.ɵɵtemplate(18, ArticleManagementComponent_div_21_th_18_Template, 2, 0, \"th\", 20);\n    i0.ɵɵtemplate(19, ArticleManagementComponent_div_21_td_19_Template, 7, 4, \"td\", 21);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(20, ArticleManagementComponent_div_21_tr_20_Template, 1, 0, \"tr\", 26);\n    i0.ɵɵtemplate(21, ArticleManagementComponent_div_21_tr_21_Template, 1, 0, \"tr\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ArticleManagementComponent_div_21_div_22_Template, 11, 0, \"div\", 28);\n    i0.ɵɵtemplate(23, ArticleManagementComponent_div_21_mat_paginator_23_Template, 1, 4, \"mat-paginator\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r2.dataSource);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.dataSource.data.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.dataSource.data.length > 0);\n  }\n}\nexport class ArticleManagementComponent {\n  constructor(articleService, router, dialog, snackBar) {\n    this.articleService = articleService;\n    this.router = router;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.displayedColumns = ['title', 'status', 'category', 'publishedAt', 'readCount', 'actions'];\n    this.dataSource = new MatTableDataSource();\n    this.isLoading = false;\n    this.totalCount = 0;\n    this.pageSize = 10;\n    this.currentPage = 0;\n    this.selectedStatus = '';\n    this.statusOptions = [{\n      value: '',\n      label: 'Всички статии'\n    }, {\n      value: 'Draft',\n      label: 'Чернови'\n    }, {\n      value: 'Published',\n      label: 'Публикувани'\n    }, {\n      value: 'Scheduled',\n      label: 'Планирани'\n    }, {\n      value: 'Archived',\n      label: 'Архивирани'\n    }];\n  }\n  ngOnInit() {\n    this.loadArticles();\n  }\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n  loadArticles() {\n    this.isLoading = true;\n    this.articleService.getMyArticles(this.currentPage + 1, this.pageSize, this.selectedStatus).subscribe({\n      next: response => {\n        this.dataSource.data = response.articles.map(article => ({\n          id: article.id,\n          title: article.title,\n          status: this.getStatusLabel(article.status || 'Draft'),\n          category: article.category,\n          publishedAt: article.publishedAt ? new Date(article.publishedAt) : undefined,\n          lastSavedAt: article.lastSavedAt ? new Date(article.lastSavedAt) : undefined,\n          readCount: article.readCount || 0,\n          estimatedReadTime: article.estimatedReadTime || 0\n        }));\n        this.totalCount = response.totalCount;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading articles:', error);\n        this.snackBar.open('Грешка при зареждането на статиите', 'Затвори', {\n          duration: 3000\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  onStatusFilterChange() {\n    this.currentPage = 0;\n    this.loadArticles();\n  }\n  onPageChange(event) {\n    this.currentPage = event.pageIndex;\n    this.pageSize = event.pageSize;\n    this.loadArticles();\n  }\n  createNewArticle() {\n    this.router.navigate(['/articles/editor']);\n  }\n  editArticle(articleId) {\n    this.router.navigate(['/articles/editor', articleId]);\n  }\n  viewArticle(article) {\n    // TODO: Navigate to article view\n    this.snackBar.open('Функцията за преглед ще бъде добавена скоро', 'Затвори', {\n      duration: 3000\n    });\n  }\n  publishArticle(articleId) {\n    this.articleService.publishArticle(articleId).subscribe({\n      next: () => {\n        this.snackBar.open('Статията е публикувана успешно', 'Затвори', {\n          duration: 3000\n        });\n        this.loadArticles();\n      },\n      error: error => {\n        console.error('Error publishing article:', error);\n        this.snackBar.open('Грешка при публикуването на статията', 'Затвори', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  unpublishArticle(articleId) {\n    this.articleService.unpublishArticle(articleId).subscribe({\n      next: () => {\n        this.snackBar.open('Статията е скрита успешно', 'Затвори', {\n          duration: 3000\n        });\n        this.loadArticles();\n      },\n      error: error => {\n        console.error('Error unpublishing article:', error);\n        this.snackBar.open('Грешка при скриването на статията', 'Затвори', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  deleteArticle(articleId, articleTitle) {\n    const dialogRef = this.dialog.open(ConfirmDeleteDialogComponent, {\n      width: '400px',\n      data: {\n        title: articleTitle\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.articleService.deleteArticle(articleId).subscribe({\n          next: () => {\n            this.snackBar.open('Статията е изтрита успешно', 'Затвори', {\n              duration: 3000\n            });\n            this.loadArticles();\n          },\n          error: error => {\n            console.error('Error deleting article:', error);\n            this.snackBar.open('Грешка при изтриването на статията', 'Затвори', {\n              duration: 3000\n            });\n          }\n        });\n      }\n    });\n  }\n  getStatusLabel(status) {\n    const statusMap = {\n      'Draft': 'Чернова',\n      'Published': 'Публикувана',\n      'Scheduled': 'Планирана',\n      'Archived': 'Архивирана'\n    };\n    return statusMap[status] || status;\n  }\n  getStatusColor(status) {\n    const colorMap = {\n      'Чернова': 'accent',\n      'Публикувана': 'primary',\n      'Планирана': 'warn',\n      'Архивирана': ''\n    };\n    return colorMap[status] || '';\n  }\n  canPublish(status) {\n    return status === 'Чернова' || status === 'Архивирана';\n  }\n  canUnpublish(status) {\n    return status === 'Публикувана';\n  }\n  static {\n    this.ɵfac = function ArticleManagementComponent_Factory(t) {\n      return new (t || ArticleManagementComponent)(i0.ɵɵdirectiveInject(i1.ArticleService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ArticleManagementComponent,\n      selectors: [[\"app-article-management\"]],\n      viewQuery: function ArticleManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(MatSort, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        }\n      },\n      decls: 22,\n      vars: 4,\n      consts: [[1, \"article-management-container\"], [1, \"management-card\"], [1, \"toolbar\"], [1, \"toolbar-left\"], [\"appearance\", \"outline\", 1, \"status-filter\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"toolbar-right\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [\"mode\", \"indeterminate\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"articles-table\", 3, \"dataSource\"], [\"matColumnDef\", \"title\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", \"class\", \"title-cell\", 4, \"matCellDef\"], [\"matColumnDef\", \"status\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"category\"], [\"matColumnDef\", \"publishedAt\"], [\"matColumnDef\", \"readCount\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"page\", 4, \"ngIf\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\", 1, \"title-cell\"], [1, \"title-content\"], [1, \"article-title\"], [\"class\", \"article-category\", 4, \"ngIf\"], [1, \"article-category\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"selected\", \"\", 3, \"color\"], [4, \"ngIf\", \"ngIfElse\"], [\"notPublished\", \"\"], [1, \"not-published\"], [1, \"read-stats\"], [1, \"stats-icon\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\", \"matMenuTriggerData\"], [\"actionMenu\", \"matMenu\"], [\"matMenuContent\", \"\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", 1, \"delete-action\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"], [1, \"no-data-icon\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"page\"]],\n      template: function ArticleManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4, \"\\u0423\\u043F\\u0440\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n          i0.ɵɵtext(6, \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0432\\u0430\\u0439\\u0442\\u0435, \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u0439\\u0442\\u0435 \\u0438 \\u0443\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u0432\\u0430\\u0439\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0438\\u0442\\u0435 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 2)(9, \"div\", 3)(10, \"mat-form-field\", 4)(11, \"mat-label\");\n          i0.ɵɵtext(12, \"\\u0424\\u0438\\u043B\\u0442\\u044A\\u0440 \\u043F\\u043E \\u0441\\u0442\\u0430\\u0442\\u0443\\u0441\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-select\", 5);\n          i0.ɵɵlistener(\"valueChange\", function ArticleManagementComponent_Template_mat_select_valueChange_13_listener($event) {\n            return ctx.selectedStatus = $event;\n          })(\"selectionChange\", function ArticleManagementComponent_Template_mat_select_selectionChange_13_listener() {\n            return ctx.onStatusFilterChange();\n          });\n          i0.ɵɵtemplate(14, ArticleManagementComponent_mat_option_14_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ArticleManagementComponent_Template_button_click_16_listener() {\n            return ctx.createNewArticle();\n          });\n          i0.ɵɵelementStart(17, \"mat-icon\");\n          i0.ɵɵtext(18, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" \\u041D\\u043E\\u0432\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(20, ArticleManagementComponent_div_20_Template, 4, 0, \"div\", 9);\n          i0.ɵɵtemplate(21, ArticleManagementComponent_div_21_Template, 24, 5, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"value\", ctx.selectedStatus);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.MatChip, i10.MatProgressSpinner, i11.MatFormField, i11.MatLabel, i12.MatSelect, i13.MatOption, i14.MatTable, i14.MatHeaderCellDef, i14.MatHeaderRowDef, i14.MatColumnDef, i14.MatCellDef, i14.MatRowDef, i14.MatHeaderCell, i14.MatCell, i14.MatHeaderRow, i14.MatRow, i15.MatPaginator, i16.MatSort, i16.MatSortHeader, i17.MatMenu, i17.MatMenuItem, i17.MatMenuContent, i17.MatMenuTrigger, i18.MatDivider, i5.DatePipe],\n      styles: [\".article-management-container[_ngcontent-%COMP%] {\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n  padding: 20px;\\r\\n}\\r\\n\\r\\n.management-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.toolbar[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  margin-bottom: 20px;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.toolbar-left[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.toolbar-right[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.status-filter[_ngcontent-%COMP%] {\\r\\n  min-width: 200px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  padding: 40px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin-top: 16px;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n.table-container[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  overflow-x: auto;\\r\\n}\\r\\n\\r\\n.articles-table[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  min-width: 800px;\\r\\n}\\r\\n\\r\\n.title-cell[_ngcontent-%COMP%] {\\r\\n  max-width: 300px;\\r\\n}\\r\\n\\r\\n.title-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 4px;\\r\\n}\\r\\n\\r\\n.article-title[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: #3f2f4e;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  white-space: nowrap;\\r\\n}\\r\\n\\r\\n.article-category[_ngcontent-%COMP%] {\\r\\n  font-size: 12px;\\r\\n  color: #666;\\r\\n  background-color: #e6dbec;\\r\\n  padding: 2px 8px;\\r\\n  border-radius: 12px;\\r\\n  display: inline-block;\\r\\n  max-width: -moz-fit-content;\\r\\n  max-width: fit-content;\\r\\n}\\r\\n\\r\\n.not-published[_ngcontent-%COMP%] {\\r\\n  color: #999;\\r\\n  font-style: italic;\\r\\n}\\r\\n\\r\\n.read-stats[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 4px;\\r\\n}\\r\\n\\r\\n.stats-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 16px;\\r\\n  width: 16px;\\r\\n  height: 16px;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n.delete-action[_ngcontent-%COMP%] {\\r\\n  color: #f44336;\\r\\n}\\r\\n\\r\\n.delete-action[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  color: #f44336;\\r\\n}\\r\\n\\r\\n.no-data[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n  padding: 60px 20px;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n.no-data-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 64px;\\r\\n  width: 64px;\\r\\n  height: 64px;\\r\\n  color: #ccc;\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  margin: 16px 0 8px 0;\\r\\n  color: #3f2f4e;\\r\\n}\\r\\n\\r\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 24px;\\r\\n  max-width: 400px;\\r\\n  margin-left: auto;\\r\\n  margin-right: auto;\\r\\n}\\r\\n\\r\\n\\r\\n  .mat-chip.mat-primary {\\r\\n  background-color: #67455c;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n  .mat-chip.mat-accent {\\r\\n  background-color: #d2a6d0;\\r\\n  color: #3f2f4e;\\r\\n}\\r\\n\\r\\n  .mat-chip.mat-warn {\\r\\n  background-color: #ff9800;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-table[_ngcontent-%COMP%] {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n.mat-header-cell[_ngcontent-%COMP%] {\\r\\n  color: #3f2f4e;\\r\\n  font-weight: 600;\\r\\n  border-bottom: 2px solid #e6dbec;\\r\\n}\\r\\n\\r\\n.mat-cell[_ngcontent-%COMP%] {\\r\\n  border-bottom: 1px solid #f0f0f0;\\r\\n}\\r\\n\\r\\n.mat-row[_ngcontent-%COMP%]:hover {\\r\\n  background-color: #fafafa;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .article-management-container[_ngcontent-%COMP%] {\\r\\n    padding: 10px;\\r\\n  }\\r\\n  \\r\\n  .toolbar[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: stretch;\\r\\n  }\\r\\n  \\r\\n  .toolbar-left[_ngcontent-%COMP%], .toolbar-right[_ngcontent-%COMP%] {\\r\\n    justify-content: center;\\r\\n  }\\r\\n  \\r\\n  .status-filter[_ngcontent-%COMP%] {\\r\\n    min-width: auto;\\r\\n    width: 100%;\\r\\n  }\\r\\n  \\r\\n  .table-container[_ngcontent-%COMP%] {\\r\\n    margin: 0 -10px;\\r\\n  }\\r\\n  \\r\\n  .articles-table[_ngcontent-%COMP%] {\\r\\n    min-width: 600px;\\r\\n  }\\r\\n  \\r\\n  .title-cell[_ngcontent-%COMP%] {\\r\\n    max-width: 200px;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (max-width: 480px) {\\r\\n  .articles-table[_ngcontent-%COMP%] {\\r\\n    min-width: 500px;\\r\\n  }\\r\\n  \\r\\n  .title-cell[_ngcontent-%COMP%] {\\r\\n    max-width: 150px;\\r\\n  }\\r\\n  \\r\\n  .article-title[_ngcontent-%COMP%] {\\r\\n    font-size: 14px;\\r\\n  }\\r\\n  \\r\\n  .article-category[_ngcontent-%COMP%] {\\r\\n    font-size: 10px;\\r\\n    padding: 1px 6px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.mat-menu-panel[_ngcontent-%COMP%] {\\r\\n  min-width: 180px;\\r\\n}\\r\\n\\r\\n.mat-menu-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.mat-menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-paginator[_ngcontent-%COMP%] {\\r\\n  background: transparent;\\r\\n  border-top: 1px solid #e0e0e0;\\r\\n  margin-top: 16px;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-card-header[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.mat-card-title[_ngcontent-%COMP%] {\\r\\n  color: #3f2f4e;\\r\\n  font-size: 24px;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.mat-card-subtitle[_ngcontent-%COMP%] {\\r\\n  color: #666;\\r\\n  margin-top: 4px;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport class ConfirmDeleteDialogComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  onCancel() {\n    this.dialogRef.close(false);\n  }\n  onConfirm() {\n    this.dialogRef.close(true);\n  }\n  static {\n    this.ɵfac = function ConfirmDeleteDialogComponent_Factory(t) {\n      return new (t || ConfirmDeleteDialogComponent)(i0.ɵɵdirectiveInject(i3.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConfirmDeleteDialogComponent,\n      selectors: [[\"app-confirm-delete-dialog\"]],\n      decls: 15,\n      vars: 1,\n      consts: [[\"mat-dialog-title\", \"\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n      template: function ConfirmDeleteDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1, \"\\u041F\\u043E\\u0442\\u0432\\u044A\\u0440\\u0436\\u0434\\u0435\\u043D\\u0438\\u0435 \\u0437\\u0430 \\u0438\\u0437\\u0442\\u0440\\u0438\\u0432\\u0430\\u043D\\u0435\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"mat-dialog-content\")(3, \"p\");\n          i0.ɵɵtext(4, \"\\u0421\\u0438\\u0433\\u0443\\u0440\\u043D\\u0438 \\u043B\\u0438 \\u0441\\u0442\\u0435, \\u0447\\u0435 \\u0438\\u0441\\u043A\\u0430\\u0442\\u0435 \\u0434\\u0430 \\u0438\\u0437\\u0442\\u0440\\u0438\\u0435\\u0442\\u0435 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430 \\\"\");\n          i0.ɵɵelementStart(5, \"strong\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \"\\\"?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"\\u0422\\u043E\\u0432\\u0430 \\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u0435 \\u043D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0431\\u044A\\u0434\\u0435 \\u043E\\u0442\\u043C\\u0435\\u043D\\u0435\\u043D\\u043E.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"mat-dialog-actions\", 1)(11, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function ConfirmDeleteDialogComponent_Template_button_click_11_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(12, \"\\u041E\\u0442\\u043A\\u0430\\u0437\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function ConfirmDeleteDialogComponent_Template_button_click_13_listener() {\n            return ctx.onConfirm();\n          });\n          i0.ɵɵtext(14, \"\\u0418\\u0437\\u0442\\u0440\\u0438\\u0439\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.data.title);\n        }\n      },\n      dependencies: [i7.MatButton, i3.MatDialogTitle, i3.MatDialogContent, i3.MatDialogActions],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAASA,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAiMhD,SAASC,eAAe,QAAsB,0BAA0B;;;;;;;;;;;;;;;;;;;;;;ICtL1DC,sCAAwE;IACtEA,YACF;IAAAA,iBAAa;;;;IAFoCA,uCAAsB;IACrEA,eACF;IADEA,gDACF;;;;;IAcRA,+BAAiD;IAC/CA,2CAAkE;IAClEA,yBAAG;IAAAA,uIAAwB;IAAAA,iBAAI;;;;;IAS3BA,8BAAsD;IAAAA,gEAAQ;IAAAA,iBAAK;;;;;IAI/DA,gCAAwD;IAAAA,YAAsB;IAAAA,iBAAO;;;;IAA7BA,eAAsB;IAAtBA,0CAAsB;;;;;IAHlFA,8BAA0D;IAE1BA,YAAmB;IAAAA,iBAAO;IACtDA,0FAAqF;IACvFA,iBAAM;;;;IAFwBA,eAAmB;IAAnBA,uCAAmB;IACfA,eAAsB;IAAtBA,2CAAsB;;;;;IAO1DA,8BAAsC;IAAAA,oDAAM;IAAAA,iBAAK;;;;;IACjDA,8BAAuC;IAEnCA,YACF;IAAAA,iBAAW;;;;;IAFDA,eAAwC;IAAxCA,iEAAwC;IAChDA,eACF;IADEA,mDACF;;;;;IAMFA,8BAAsC;IAAAA,sEAAS;IAAAA,iBAAK;;;;;IACpDA,8BAAuC;IACrCA,YACF;IAAAA,iBAAK;;;;IADHA,eACF;IADEA,oIACF;;;;;IAKAA,8BAAsD;IAAAA,wHAAmB;IAAAA,iBAAK;;;;;IAE5EA,2BAAoD;IAClDA,YACF;;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,kGACF;;;;;IAEEA,gCAA4B;IAAAA,sGAAgB;IAAAA,iBAAO;;;;;IALvDA,8BAAuC;IACrCA,yFAEM;IACNA,0IAEc;IAChBA,iBAAK;;;;;IANGA,eAA2B;IAA3BA,8CAA2B;;;;;IAWnCA,8BAAsD;IAAAA,gEAAQ;IAAAA,iBAAK;;;;;IACnEA,8BAAuC;IAENA,0BAAU;IAAAA,iBAAW;IAClDA,4BAAM;IAAAA,YAAuB;IAAAA,iBAAO;;;;IAA9BA,eAAuB;IAAvBA,2CAAuB;;;;;IAOjCA,8BAAsC;IAAAA,gEAAQ;IAAAA,iBAAK;;;;;;IAmB7CA,kCAC2C;IADrBA;MAAAA;MAAA;MAAA;MAAA,OAASA,qDAA0B;IAAA,EAAC;IAExDA,gCAAU;IAAAA,uBAAO;IAAAA,iBAAW;IAC5BA,4BAAM;IAAAA,4EAAU;IAAAA,iBAAO;;;;;;IAGzBA,kCAC6C;IADvBA;MAAAA;MAAA;MAAA;MAAA,OAASA,uDAA4B;IAAA,EAAC;IAE1DA,gCAAU;IAAAA,2BAAW;IAAAA,iBAAW;IAChCA,4BAAM;IAAAA,8CAAK;IAAAA,iBAAO;;;;;;IAnBpBA,kCAAqD;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IAClDA,gCAAU;IAAAA,0BAAU;IAAAA,iBAAW;IAC/BA,4BAAM;IAAAA,0DAAO;IAAAA,iBAAO;IAGtBA,kCAAwD;IAAlCA;MAAA;MAAA;MAAA;MAAA,OAASA,kDAAuB;IAAA,EAAC;IACrDA,gCAAU;IAAAA,oBAAI;IAAAA,iBAAW;IACzBA,4BAAM;IAAAA,kFAAW;IAAAA,iBAAO;IAG1BA,+GAIS;IAETA,+GAIS;IAETA,+BAA2B;IAE3BA,mCAC8B;IADRA;MAAA;MAAA;MAAA;MAAA,OAASA,uEAAwC;IAAA,EAAC;IAEtEA,iCAAU;IAAAA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,qDAAM;IAAAA,iBAAO;;;;;IAhBZA,gBAAgC;IAAhCA,6DAAgC;IAMhCA,eAAkC;IAAlCA,+DAAkC;;;;;;;;;;IAzBjDA,8BAAuC;IAGzBA,yBAAS;IAAAA,iBAAW;IAGhCA,0CAAgC;IAC9BA,0GA8Bc;IAChBA,iBAAW;;;;;IArCaA,eAAgC;IAAhCA,wCAAgC;;;;;IAyC5DA,yBAA4D;;;;;IAC5DA,yBAAkE;;;;;;IAIpEA,+BAA0D;IACzBA,uBAAO;IAAAA,iBAAW;IACjDA,0BAAI;IAAAA,8HAAoB;IAAAA,iBAAK;IAC7BA,yBAAG;IAAAA,gYAAuE;IAAAA,iBAAI;IAC9EA,iCAAuE;IAA7BA;MAAAA;MAAA;MAAA,OAASA,yCAAkB;IAAA,EAAC;IACpEA,gCAAU;IAAAA,mBAAG;IAAAA,iBAAW;IACxBA,oJACF;IAAAA,iBAAS;;;;;;;;;IAIXA,yCAMuB;IADrBA;MAAAA;MAAA;MAAA,OAAQA,2CAAoB;IAAA,EAAC;IAE/BA,iBAAgB;;;;IALdA,2CAAqB;;;;;IAvHzBA,+BAAgD;IAI5CA,iCAAmC;IACjCA,iFAAmE;IACnEA,iFAKK;IACPA,0BAAe;IAGfA,iCAAoC;IAClCA,iFAAiD;IACjDA,iFAIK;IACPA,0BAAe;IAGfA,iCAAsC;IACpCA,iFAAoD;IACpDA,mFAEK;IACPA,0BAAe;IAGfA,kCAAyC;IACvCA,mFAA8E;IAC9EA,mFAOK;IACPA,0BAAe;IAGfA,kCAAuC;IACrCA,mFAAmE;IACnEA,mFAKK;IACPA,0BAAe;IAGfA,kCAAqC;IACnCA,mFAAmD;IACnDA,mFAuCK;IACPA,0BAAe;IAEfA,mFAA4D;IAC5DA,mFAAkE;IACpEA,iBAAQ;IAGRA,sFAQM;IAGNA,yGAOgB;IAClBA,iBAAM;;;;IA5HaA,eAAyB;IAAzBA,8CAAyB;IAoGpBA,gBAAiC;IAAjCA,yDAAiC;IACpBA,eAA0B;IAA1BA,0DAA0B;IAIvDA,eAAkC;IAAlCA,0DAAkC;IAYrCA,eAAgC;IAAhCA,wDAAgC;;;ADhI3C,OAAM,MAAOC,0BAA0B;EAqBrCC,YACUC,cAA8B,EAC9BC,MAAc,EACdC,MAAiB,EACjBC,QAAqB;IAHrB,mBAAc,GAAdH,cAAc;IACd,WAAM,GAANC,MAAM;IACN,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IAxBlB,qBAAgB,GAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,CAAC;IACnG,eAAU,GAAG,IAAIV,kBAAkB,EAAmB;IAKtD,cAAS,GAAG,KAAK;IACjB,eAAU,GAAG,CAAC;IACd,aAAQ,GAAG,EAAE;IACb,gBAAW,GAAG,CAAC;IACf,mBAAc,GAAG,EAAE;IAEnB,kBAAa,GAAG,CACd;MAAEW,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAe,CAAE,EACrC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAS,CAAE,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAa,CAAE,EAC5C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAY,CAAE,CAC3C;EAOE;EAEHC,QAAQ;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,eAAe;IACb,IAAI,CAACC,UAAU,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS;IAC1C,IAAI,CAACD,UAAU,CAACE,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEAJ,YAAY;IACV,IAAI,CAACK,SAAS,GAAG,IAAI;IAErB,IAAI,CAACZ,cAAc,CAACa,aAAa,CAC/B,IAAI,CAACC,WAAW,GAAG,CAAC,EACpB,IAAI,CAACC,QAAQ,EACb,IAAI,CAACC,cAAc,CACpB,CAACC,SAAS,CAAC;MACVC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACV,UAAU,CAACW,IAAI,GAAGD,QAAQ,CAACE,QAAQ,CAACC,GAAG,CAACC,OAAO,KAAK;UACvDC,EAAE,EAAED,OAAO,CAACC,EAAE;UACdC,KAAK,EAAEF,OAAO,CAACE,KAAK;UACpBC,MAAM,EAAE,IAAI,CAACC,cAAc,CAACJ,OAAO,CAACG,MAAM,IAAI,OAAO,CAAC;UACtDE,QAAQ,EAAEL,OAAO,CAACK,QAAQ;UAC1BC,WAAW,EAAEN,OAAO,CAACM,WAAW,GAAG,IAAIC,IAAI,CAACP,OAAO,CAACM,WAAW,CAAC,GAAGE,SAAS;UAC5EC,WAAW,EAAET,OAAO,CAACS,WAAW,GAAG,IAAIF,IAAI,CAACP,OAAO,CAACS,WAAW,CAAC,GAAGD,SAAS;UAC5EE,SAAS,EAAEV,OAAO,CAACU,SAAS,IAAI,CAAC;UACjCC,iBAAiB,EAAEX,OAAO,CAACW,iBAAiB,IAAI;SACjD,CAAC,CAAC;QACH,IAAI,CAACC,UAAU,GAAGhB,QAAQ,CAACgB,UAAU;QACrC,IAAI,CAACvB,SAAS,GAAG,KAAK;MACxB,CAAC;MACDwB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACjC,QAAQ,CAACmC,IAAI,CAAC,oCAAoC,EAAE,SAAS,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACvF,IAAI,CAAC3B,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA4B,oBAAoB;IAClB,IAAI,CAAC1B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACP,YAAY,EAAE;EACrB;EAEAkC,YAAY,CAACC,KAAU;IACrB,IAAI,CAAC5B,WAAW,GAAG4B,KAAK,CAACC,SAAS;IAClC,IAAI,CAAC5B,QAAQ,GAAG2B,KAAK,CAAC3B,QAAQ;IAC9B,IAAI,CAACR,YAAY,EAAE;EACrB;EAEAqC,gBAAgB;IACd,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAC,WAAW,CAACC,SAAiB;IAC3B,IAAI,CAAC9C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,kBAAkB,EAAEE,SAAS,CAAC,CAAC;EACvD;EAEAC,WAAW,CAACzB,OAAwB;IAClC;IACA,IAAI,CAACpB,QAAQ,CAACmC,IAAI,CAAC,6CAA6C,EAAE,SAAS,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EAClG;EAEAU,cAAc,CAACF,SAAiB;IAC9B,IAAI,CAAC/C,cAAc,CAACiD,cAAc,CAACF,SAAS,CAAC,CAAC9B,SAAS,CAAC;MACtDC,IAAI,EAAE,MAAK;QACT,IAAI,CAACf,QAAQ,CAACmC,IAAI,CAAC,gCAAgC,EAAE,SAAS,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACnF,IAAI,CAAChC,YAAY,EAAE;MACrB,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACjC,QAAQ,CAACmC,IAAI,CAAC,sCAAsC,EAAE,SAAS,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC3F;KACD,CAAC;EACJ;EAEAW,gBAAgB,CAACH,SAAiB;IAChC,IAAI,CAAC/C,cAAc,CAACkD,gBAAgB,CAACH,SAAS,CAAC,CAAC9B,SAAS,CAAC;MACxDC,IAAI,EAAE,MAAK;QACT,IAAI,CAACf,QAAQ,CAACmC,IAAI,CAAC,2BAA2B,EAAE,SAAS,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC9E,IAAI,CAAChC,YAAY,EAAE;MACrB,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACjC,QAAQ,CAACmC,IAAI,CAAC,mCAAmC,EAAE,SAAS,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACxF;KACD,CAAC;EACJ;EAEAY,aAAa,CAACJ,SAAiB,EAAEK,YAAoB;IACnD,MAAMC,SAAS,GAAG,IAAI,CAACnD,MAAM,CAACoC,IAAI,CAACgB,4BAA4B,EAAE;MAC/DC,KAAK,EAAE,OAAO;MACdnC,IAAI,EAAE;QAAEK,KAAK,EAAE2B;MAAY;KAC5B,CAAC;IAEFC,SAAS,CAACG,WAAW,EAAE,CAACvC,SAAS,CAACwC,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV,IAAI,CAACzD,cAAc,CAACmD,aAAa,CAACJ,SAAS,CAAC,CAAC9B,SAAS,CAAC;UACrDC,IAAI,EAAE,MAAK;YACT,IAAI,CAACf,QAAQ,CAACmC,IAAI,CAAC,4BAA4B,EAAE,SAAS,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;YAC/E,IAAI,CAAChC,YAAY,EAAE;UACrB,CAAC;UACD6B,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;YAC/C,IAAI,CAACjC,QAAQ,CAACmC,IAAI,CAAC,oCAAoC,EAAE,SAAS,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE,CAAC;UACzF;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEQZ,cAAc,CAACD,MAAc;IACnC,MAAMgC,SAAS,GAA8B;MAC3C,OAAO,EAAE,SAAS;MAClB,WAAW,EAAE,aAAa;MAC1B,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE;KACb;IACD,OAAOA,SAAS,CAAChC,MAAM,CAAC,IAAIA,MAAM;EACpC;EAEAiC,cAAc,CAACjC,MAAc;IAC3B,MAAMkC,QAAQ,GAA8B;MAC1C,SAAS,EAAE,QAAQ;MACnB,aAAa,EAAE,SAAS;MACxB,WAAW,EAAE,MAAM;MACnB,YAAY,EAAE;KACf;IACD,OAAOA,QAAQ,CAAClC,MAAM,CAAC,IAAI,EAAE;EAC/B;EAEAmC,UAAU,CAACnC,MAAc;IACvB,OAAOA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,YAAY;EACxD;EAEAoC,YAAY,CAACpC,MAAc;IACzB,OAAOA,MAAM,KAAK,aAAa;EACjC;;;uBArKW5B,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAAiE;MAAAC;QAAA;yBAI1BtE,YAAY;yBACZC,OAAO;;;;;;;;;;;;;UC/BpBE,8BAA0C;UAGpBA,8HAAoB;UAAAA,iBAAiB;UACrDA,yCAAmB;UAAAA,gTAAqD;UAAAA,iBAAoB;UAG9FA,wCAAkB;UAKCA,uGAAgB;UAAAA,iBAAY;UACvCA,sCAAkF;UAAtEA;YAAA;UAAA,EAA0B;YAAA,OAAoBoE,0BAAsB;UAAA,EAA1C;UACpCpE,2FAEa;UACfA,iBAAa;UAIjBA,+BAA2B;UACiBA;YAAA,OAASoE,sBAAkB;UAAA,EAAC;UACpEpE,iCAAU;UAAAA,oBAAG;UAAAA,iBAAW;UACxBA,gFACF;UAAAA,iBAAS;UAKbA,6EAGM;UAGNA,+EA6HM;UACRA,iBAAmB;;;UArJCA,gBAA0B;UAA1BA,0CAA0B;UACLA,eAAgB;UAAhBA,2CAAgB;UAgBjDA,eAAe;UAAfA,oCAAe;UAMfA,eAAgB;UAAhBA,qCAAgB;;;;;;;;ADgL5B,OAAM,MAAOyD,4BAA4B;EACvCvD,YACSsD,SAAqD,EAC5BjC,IAAuB;IADhD,cAAS,GAATiC,SAAS;IACgB,SAAI,GAAJjC,IAAI;EACnC;EAEH8C,QAAQ;IACN,IAAI,CAACb,SAAS,CAACc,KAAK,CAAC,KAAK,CAAC;EAC7B;EAEAC,SAAS;IACP,IAAI,CAACf,SAAS,CAACc,KAAK,CAAC,IAAI,CAAC;EAC5B;;;uBAZWb,4BAA4B,8DAG7B1D,eAAe;IAAA;EAAA;;;YAHd0D,4BAA4B;MAAAS;MAAAM;MAAAC;MAAAC;MAAAC;QAAA;UAXrC3E,6BAAqB;UAAAA,4JAAyB;UAAAA,iBAAK;UACnDA,0CAAoB;UACfA,+PAAgD;UAAAA,8BAAQ;UAAAA,YAAgB;UAAAA,iBAAS;UAAAA,mBAAE;UAAAA,iBAAI;UAC1FA,yBAAG;UAAAA,uNAAuC;UAAAA,iBAAI;UAEhDA,8CAAgC;UACXA;YAAA,OAASoE,cAAU;UAAA,EAAC;UAACpE,+CAAK;UAAAA,iBAAS;UACtDA,kCAA6D;UAAtBA;YAAA,OAASoE,eAAW;UAAA,EAAC;UAACpE,qDAAM;UAAAA,iBAAS;;;UALjBA,eAAgB;UAAhBA,oCAAgB", "names": ["MatTableDataSource", "MatPaginator", "MatSort", "MAT_DIALOG_DATA", "i0", "ArticleManagementComponent", "constructor", "articleService", "router", "dialog", "snackBar", "value", "label", "ngOnInit", "loadArticles", "ngAfterViewInit", "dataSource", "paginator", "sort", "isLoading", "getMyArticles", "currentPage", "pageSize", "selectedStatus", "subscribe", "next", "response", "data", "articles", "map", "article", "id", "title", "status", "getStatusLabel", "category", "publishedAt", "Date", "undefined", "lastSavedAt", "readCount", "estimatedReadTime", "totalCount", "error", "console", "open", "duration", "onStatusFilterChange", "onPageChange", "event", "pageIndex", "createNewArticle", "navigate", "editArticle", "articleId", "viewArticle", "publishArticle", "unpublishArticle", "deleteArticle", "articleTitle", "dialogRef", "ConfirmDeleteDialogComponent", "width", "afterClosed", "result", "statusMap", "getStatusColor", "colorMap", "canPublish", "canUnpublish", "selectors", "viewQuery", "ctx", "onCancel", "close", "onConfirm", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\article-management\\article-management.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\article-management\\article-management.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MatTableDataSource } from '@angular/material/table';\r\nimport { MatPaginator } from '@angular/material/paginator';\r\nimport { MatSort } from '@angular/material/sort';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Router } from '@angular/router';\r\nimport { ArticleService } from '../../shared/services/article.service';\r\nimport { ArticleManagementDto } from '../../shared/models/article.models';\r\n\r\nexport interface ArticleListItem {\r\n  id: number;\r\n  title: string;\r\n  status: string;\r\n  category?: string;\r\n  publishedAt?: Date;\r\n  lastSavedAt?: Date;\r\n  readCount: number;\r\n  estimatedReadTime: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-article-management',\r\n  templateUrl: './article-management.component.html',\r\n  styleUrls: ['./article-management.component.css']\r\n})\r\nexport class ArticleManagementComponent implements OnInit {\r\n  displayedColumns: string[] = ['title', 'status', 'category', 'publishedAt', 'readCount', 'actions'];\r\n  dataSource = new MatTableDataSource<ArticleListItem>();\r\n\r\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\r\n  @ViewChild(MatSort) sort!: MatSort;\r\n\r\n  isLoading = false;\r\n  totalCount = 0;\r\n  pageSize = 10;\r\n  currentPage = 0;\r\n  selectedStatus = '';\r\n\r\n  statusOptions = [\r\n    { value: '', label: 'Всички статии' },\r\n    { value: 'Draft', label: 'Чернови' },\r\n    { value: 'Published', label: 'Публикувани' },\r\n    { value: 'Scheduled', label: 'Планирани' },\r\n    { value: 'Archived', label: 'Архивирани' }\r\n  ];\r\n\r\n  constructor(\r\n    private articleService: ArticleService,\r\n    private router: Router,\r\n    private dialog: MatDialog,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadArticles();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.dataSource.paginator = this.paginator;\r\n    this.dataSource.sort = this.sort;\r\n  }\r\n\r\n  loadArticles(): void {\r\n    this.isLoading = true;\r\n\r\n    this.articleService.getMyArticles(\r\n      this.currentPage + 1,\r\n      this.pageSize,\r\n      this.selectedStatus\r\n    ).subscribe({\r\n      next: (response) => {\r\n        this.dataSource.data = response.articles.map(article => ({\r\n          id: article.id,\r\n          title: article.title,\r\n          status: this.getStatusLabel(article.status || 'Draft'),\r\n          category: article.category,\r\n          publishedAt: article.publishedAt ? new Date(article.publishedAt) : undefined,\r\n          lastSavedAt: article.lastSavedAt ? new Date(article.lastSavedAt) : undefined,\r\n          readCount: article.readCount || 0,\r\n          estimatedReadTime: article.estimatedReadTime || 0\r\n        }));\r\n        this.totalCount = response.totalCount;\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading articles:', error);\r\n        this.snackBar.open('Грешка при зареждането на статиите', 'Затвори', { duration: 3000 });\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onStatusFilterChange(): void {\r\n    this.currentPage = 0;\r\n    this.loadArticles();\r\n  }\r\n\r\n  onPageChange(event: any): void {\r\n    this.currentPage = event.pageIndex;\r\n    this.pageSize = event.pageSize;\r\n    this.loadArticles();\r\n  }\r\n\r\n  createNewArticle(): void {\r\n    this.router.navigate(['/articles/editor']);\r\n  }\r\n\r\n  editArticle(articleId: number): void {\r\n    this.router.navigate(['/articles/editor', articleId]);\r\n  }\r\n\r\n  viewArticle(article: ArticleListItem): void {\r\n    // TODO: Navigate to article view\r\n    this.snackBar.open('Функцията за преглед ще бъде добавена скоро', 'Затвори', { duration: 3000 });\r\n  }\r\n\r\n  publishArticle(articleId: number): void {\r\n    this.articleService.publishArticle(articleId).subscribe({\r\n      next: () => {\r\n        this.snackBar.open('Статията е публикувана успешно', 'Затвори', { duration: 3000 });\r\n        this.loadArticles();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error publishing article:', error);\r\n        this.snackBar.open('Грешка при публикуването на статията', 'Затвори', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  unpublishArticle(articleId: number): void {\r\n    this.articleService.unpublishArticle(articleId).subscribe({\r\n      next: () => {\r\n        this.snackBar.open('Статията е скрита успешно', 'Затвори', { duration: 3000 });\r\n        this.loadArticles();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error unpublishing article:', error);\r\n        this.snackBar.open('Грешка при скриването на статията', 'Затвори', { duration: 3000 });\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteArticle(articleId: number, articleTitle: string): void {\r\n    const dialogRef = this.dialog.open(ConfirmDeleteDialogComponent, {\r\n      width: '400px',\r\n      data: { title: articleTitle }\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe(result => {\r\n      if (result) {\r\n        this.articleService.deleteArticle(articleId).subscribe({\r\n          next: () => {\r\n            this.snackBar.open('Статията е изтрита успешно', 'Затвори', { duration: 3000 });\r\n            this.loadArticles();\r\n          },\r\n          error: (error) => {\r\n            console.error('Error deleting article:', error);\r\n            this.snackBar.open('Грешка при изтриването на статията', 'Затвори', { duration: 3000 });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private getStatusLabel(status: string): string {\r\n    const statusMap: { [key: string]: string } = {\r\n      'Draft': 'Чернова',\r\n      'Published': 'Публикувана',\r\n      'Scheduled': 'Планирана',\r\n      'Archived': 'Архивирана'\r\n    };\r\n    return statusMap[status] || status;\r\n  }\r\n\r\n  getStatusColor(status: string): string {\r\n    const colorMap: { [key: string]: string } = {\r\n      'Чернова': 'accent',\r\n      'Публикувана': 'primary',\r\n      'Планирана': 'warn',\r\n      'Архивирана': ''\r\n    };\r\n    return colorMap[status] || '';\r\n  }\r\n\r\n  canPublish(status: string): boolean {\r\n    return status === 'Чернова' || status === 'Архивирана';\r\n  }\r\n\r\n  canUnpublish(status: string): boolean {\r\n    return status === 'Публикувана';\r\n  }\r\n}\r\n\r\n// Confirm Delete Dialog Component\r\nimport { Inject } from '@angular/core';\r\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\r\n\r\n@Component({\r\n  selector: 'app-confirm-delete-dialog',\r\n  template: `\r\n    <h2 mat-dialog-title>Потвърждение за изтриване</h2>\r\n    <mat-dialog-content>\r\n      <p>Сигурни ли сте, че искате да изтриете статията \"<strong>{{ data.title }}</strong>\"?</p>\r\n      <p>Това действие не може да бъде отменено.</p>\r\n    </mat-dialog-content>\r\n    <mat-dialog-actions align=\"end\">\r\n      <button mat-button (click)=\"onCancel()\">Отказ</button>\r\n      <button mat-raised-button color=\"warn\" (click)=\"onConfirm()\">Изтрий</button>\r\n    </mat-dialog-actions>\r\n  `\r\n})\r\nexport class ConfirmDeleteDialogComponent {\r\n  constructor(\r\n    public dialogRef: MatDialogRef<ConfirmDeleteDialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: { title: string }\r\n  ) {}\r\n\r\n  onCancel(): void {\r\n    this.dialogRef.close(false);\r\n  }\r\n\r\n  onConfirm(): void {\r\n    this.dialogRef.close(true);\r\n  }\r\n}\r\n", "<div class=\"article-management-container\">\r\n  <mat-card class=\"management-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>Управление на статии</mat-card-title>\r\n      <mat-card-subtitle>Създавайте, редактирайте и управлявайте вашите статии</mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <!-- Toolbar -->\r\n      <div class=\"toolbar\">\r\n        <div class=\"toolbar-left\">\r\n          <mat-form-field appearance=\"outline\" class=\"status-filter\">\r\n            <mat-label>Филтър по статус</mat-label>\r\n            <mat-select [(value)]=\"selectedStatus\" (selectionChange)=\"onStatusFilterChange()\">\r\n              <mat-option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\r\n                {{ option.label }}\r\n              </mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n        </div>\r\n        \r\n        <div class=\"toolbar-right\">\r\n          <button mat-raised-button color=\"primary\" (click)=\"createNewArticle()\">\r\n            <mat-icon>add</mat-icon>\r\n            Нова статия\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Loading Spinner -->\r\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\r\n        <mat-progress-spinner mode=\"indeterminate\"></mat-progress-spinner>\r\n        <p>Зареждане на статиите...</p>\r\n      </div>\r\n\r\n      <!-- Articles Table -->\r\n      <div *ngIf=\"!isLoading\" class=\"table-container\">\r\n        <table mat-table [dataSource]=\"dataSource\" matSort class=\"articles-table\">\r\n          \r\n          <!-- Title Column -->\r\n          <ng-container matColumnDef=\"title\">\r\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>Заглавие</th>\r\n            <td mat-cell *matCellDef=\"let article\" class=\"title-cell\">\r\n              <div class=\"title-content\">\r\n                <span class=\"article-title\">{{ article.title }}</span>\r\n                <span class=\"article-category\" *ngIf=\"article.category\">{{ article.category }}</span>\r\n              </div>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Status Column -->\r\n          <ng-container matColumnDef=\"status\">\r\n            <th mat-header-cell *matHeaderCellDef>Статус</th>\r\n            <td mat-cell *matCellDef=\"let article\">\r\n              <mat-chip [color]=\"getStatusColor(article.status)\" selected>\r\n                {{ article.status }}\r\n              </mat-chip>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Category Column -->\r\n          <ng-container matColumnDef=\"category\">\r\n            <th mat-header-cell *matHeaderCellDef>Категория</th>\r\n            <td mat-cell *matCellDef=\"let article\">\r\n              {{ article.category || 'Без категория' }}\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Published Date Column -->\r\n          <ng-container matColumnDef=\"publishedAt\">\r\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>Дата на публикуване</th>\r\n            <td mat-cell *matCellDef=\"let article\">\r\n              <div *ngIf=\"article.publishedAt; else notPublished\">\r\n                {{ article.publishedAt | date:'dd.MM.yyyy HH:mm' }}\r\n              </div>\r\n              <ng-template #notPublished>\r\n                <span class=\"not-published\">Не е публикувана</span>\r\n              </ng-template>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Read Count Column -->\r\n          <ng-container matColumnDef=\"readCount\">\r\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>Прегледи</th>\r\n            <td mat-cell *matCellDef=\"let article\">\r\n              <div class=\"read-stats\">\r\n                <mat-icon class=\"stats-icon\">visibility</mat-icon>\r\n                <span>{{ article.readCount }}</span>\r\n              </div>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Actions Column -->\r\n          <ng-container matColumnDef=\"actions\">\r\n            <th mat-header-cell *matHeaderCellDef>Действия</th>\r\n            <td mat-cell *matCellDef=\"let article\">\r\n              <button mat-icon-button [matMenuTriggerFor]=\"actionMenu\" \r\n                      [matMenuTriggerData]=\"{article: article}\">\r\n                <mat-icon>more_vert</mat-icon>\r\n              </button>\r\n              \r\n              <mat-menu #actionMenu=\"matMenu\">\r\n                <ng-template matMenuContent let-article=\"article\">\r\n                  <button mat-menu-item (click)=\"viewArticle(article)\">\r\n                    <mat-icon>visibility</mat-icon>\r\n                    <span>Преглед</span>\r\n                  </button>\r\n                  \r\n                  <button mat-menu-item (click)=\"editArticle(article.id)\">\r\n                    <mat-icon>edit</mat-icon>\r\n                    <span>Редактиране</span>\r\n                  </button>\r\n                  \r\n                  <button mat-menu-item (click)=\"publishArticle(article.id)\" \r\n                          *ngIf=\"canPublish(article.status)\">\r\n                    <mat-icon>publish</mat-icon>\r\n                    <span>Публикувай</span>\r\n                  </button>\r\n                  \r\n                  <button mat-menu-item (click)=\"unpublishArticle(article.id)\" \r\n                          *ngIf=\"canUnpublish(article.status)\">\r\n                    <mat-icon>unpublished</mat-icon>\r\n                    <span>Скрий</span>\r\n                  </button>\r\n                  \r\n                  <mat-divider></mat-divider>\r\n                  \r\n                  <button mat-menu-item (click)=\"deleteArticle(article.id, article.title)\" \r\n                          class=\"delete-action\">\r\n                    <mat-icon>delete</mat-icon>\r\n                    <span>Изтрий</span>\r\n                  </button>\r\n                </ng-template>\r\n              </mat-menu>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\r\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\r\n        </table>\r\n\r\n        <!-- No Data Message -->\r\n        <div *ngIf=\"dataSource.data.length === 0\" class=\"no-data\">\r\n          <mat-icon class=\"no-data-icon\">article</mat-icon>\r\n          <h3>Няма намерени статии</h3>\r\n          <p>Започнете да създавате съдържание, като натиснете бутона \"Нова статия\".</p>\r\n          <button mat-raised-button color=\"primary\" (click)=\"createNewArticle()\">\r\n            <mat-icon>add</mat-icon>\r\n            Създай първата си статия\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Paginator -->\r\n        <mat-paginator \r\n          *ngIf=\"dataSource.data.length > 0\"\r\n          [length]=\"totalCount\"\r\n          [pageSize]=\"pageSize\"\r\n          [pageSizeOptions]=\"[5, 10, 25, 50]\"\r\n          (page)=\"onPageChange($event)\"\r\n          showFirstLastButtons>\r\n        </mat-paginator>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}