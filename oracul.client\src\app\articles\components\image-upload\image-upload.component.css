.image-upload-container {
  width: 100%;
  margin-bottom: 16px;
}

.current-image {
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.uploaded-image {
  max-width: 100%;
  max-height: 300px;
  width: auto;
  height: auto;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 0 8px;
}

.remove-button {
  color: white;
}

.upload-area {
  border: 2px dashed #d2a6d0;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #67455c;
  background-color: #f5f5f5;
}

.upload-area.drag-over {
  border-color: #67455c;
  background-color: #e6dbec;
  transform: scale(1.02);
}

.upload-area.uploading {
  border-color: #d2a6d0;
  background-color: #f9f9f9;
  cursor: not-allowed;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-loading p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.upload-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #d2a6d0;
}

.upload-text {
  font-size: 18px;
  font-weight: 500;
  color: #3f2f4e;
  margin: 0;
}

.upload-hint {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.file-input {
  display: none;
}

.file-requirements {
  font-size: 12px;
  color: #999;
  margin: 8px 0 0 0;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .upload-area {
    padding: 30px 15px;
  }
  
  .upload-icon {
    font-size: 36px;
    width: 36px;
    height: 36px;
  }
  
  .upload-text {
    font-size: 16px;
  }
  
  .uploaded-image {
    max-height: 200px;
  }
}

/* Animation for drag and drop */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.upload-area.drag-over {
  animation: pulse 1s infinite;
}
