<div class="article-editor-container">
  <mat-card class="editor-card">
    <mat-card-header>
      <mat-card-title>
        {{ isEditMode ? 'Редактиране на статия' : 'Създаване на нова статия' }}
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-progress-spinner mode="indeterminate"></mat-progress-spinner>
        <p>Зареждане на статията...</p>
      </div>

      <form [formGroup]="articleForm" *ngIf="!isLoading">
        <!-- Title -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Заглавие *</mat-label>
          <input matInput formControlName="title" placeholder="Въведете заглавие на статията">
          <mat-error *ngIf="articleForm.get('title')?.hasError('required')">
            Заглавието е задължително
          </mat-error>
          <mat-error *ngIf="articleForm.get('title')?.hasError('maxlength')">
            Заглавието не може да бъде по-дълго от 200 символа
          </mat-error>
        </mat-form-field>

        <!-- Excerpt -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Кратко описание *</mat-label>
          <textarea matInput formControlName="excerpt" rows="3"
                    placeholder="Въведете кратко описание на статията"></textarea>
          <mat-error *ngIf="articleForm.get('excerpt')?.hasError('required')">
            Краткото описание е задължително
          </mat-error>
          <mat-error *ngIf="articleForm.get('excerpt')?.hasError('maxlength')">
            Краткото описание не може да бъде по-дълго от 500 символа
          </mat-error>
        </mat-form-field>

        <!-- Category -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Категория</mat-label>
          <mat-select formControlName="category">
            <mat-option value="">Без категория</mat-option>
            <mat-option *ngFor="let category of categories" [value]="category">
              {{ category }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Tags -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Тагове</mat-label>
          <input matInput formControlName="tags"
                 placeholder="Въведете тагове, разделени със запетая">
          <mat-hint>Разделете таговете със запетая (напр. астрология, таро, хороскоп)</mat-hint>
        </mat-form-field>

        <!-- Content Editor -->
        <div class="content-editor">
          <label class="editor-label">Съдържание</label>
          <quill-editor
            formControlName="content"
            [modules]="quillConfig"
            placeholder="Напишете съдържанието на статията тук..."
            class="full-width">
          </quill-editor>
        </div>

        <!-- Featured Image -->
        <div class="featured-image-section">
          <label class="editor-label">Основна снимка</label>
          <app-image-upload
            [currentImageUrl]="articleForm.get('featuredImageUrl')?.value"
            placeholder="Качете основна снимка за статията"
            (imageUploaded)="onFeaturedImageUploaded($event)"
            (imageRemoved)="onFeaturedImageRemoved()">
          </app-image-upload>
        </div>

        <!-- SEO Section -->
        <mat-card class="seo-section">
          <mat-card-subtitle>SEO настройки</mat-card-subtitle>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Meta описание</mat-label>
            <textarea matInput formControlName="metaDescription" rows="2"
                      placeholder="Описание за търсачките (до 160 символа)"></textarea>
            <mat-error *ngIf="articleForm.get('metaDescription')?.hasError('maxlength')">
              Meta описанието не може да бъде по-дълго от 160 символа
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Ключови думи</mat-label>
            <input matInput formControlName="metaKeywords"
                   placeholder="ключова дума 1, ключова дума 2, ключова дума 3">
            <mat-error *ngIf="articleForm.get('metaKeywords')?.hasError('maxlength')">
              Ключовите думи не могат да бъдат по-дълги от 500 символа
            </mat-error>
          </mat-form-field>
        </mat-card>

        <!-- Options -->
        <div class="options-section">
          <mat-checkbox formControlName="allowComments">
            Разреши коментари
          </mat-checkbox>

          <mat-checkbox formControlName="isFeatured">
            Препоръчана статия
          </mat-checkbox>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-button (click)="onCancel()" [disabled]="isSaving">
        Отказ
      </button>

      <button mat-button (click)="onPreview()" [disabled]="isSaving || articleForm.invalid">
        <mat-icon>visibility</mat-icon>
        Преглед
      </button>

      <button mat-raised-button color="accent" (click)="onSaveDraft()"
              [disabled]="isSaving || articleForm.invalid">
        <mat-icon>save</mat-icon>
        {{ isSaving ? 'Записване...' : 'Запази като чернова' }}
      </button>

      <button mat-raised-button color="primary" (click)="onPublish()"
              [disabled]="isSaving || articleForm.invalid">
        <mat-icon>publish</mat-icon>
        {{ isSaving ? 'Публикуване...' : 'Публикувай' }}
      </button>
    </mat-card-actions>
  </mat-card>
</div>
