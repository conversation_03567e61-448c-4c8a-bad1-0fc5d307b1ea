<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
  <mat-spinner></mat-spinner>
  <p>{{ t.common.loading }}...</p>
</div>

<!-- Error State -->
<div class="error-container" *ngIf="error && !isLoading">
  <mat-card class="error-card">
    <mat-card-content>
      <div class="error-content">
        <mat-icon class="error-icon">error</mat-icon>
        <h2>{{ t.errors.general }}</h2>
        <p>{{ error }}</p>
        <button mat-raised-button color="primary" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
          {{ t.common.back }}
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>

<!-- Article Content -->
<div class="article-container" *ngIf="article && !isLoading && !error">
  <!-- Article Header -->
  <div class="article-header">
    <button mat-icon-button class="back-button" (click)="goBack()" [attr.aria-label]="t.common.back">
      <mat-icon>arrow_back</mat-icon>
    </button>

    <div class="article-actions">
      <button mat-icon-button (click)="shareArticle()" [attr.aria-label]="t.articles.share">
        <mat-icon>share</mat-icon>
      </button>
    </div>
  </div>

  <!-- Featured Image -->
  <div class="featured-image" *ngIf="article.featuredImageUrl || article.imageUrl">
    <img [src]="article.featuredImageUrl || article.imageUrl"
         [alt]="article.title"
         class="article-featured-image">
  </div>

  <!-- Article Meta -->
  <div class="article-meta">
    <mat-chip class="category-chip">{{ article.category }}</mat-chip>
    <div class="meta-info">
      <span class="author">{{ t.articles.author }}: {{ article.author }}</span>
      <span class="separator">•</span>
      <span class="date">{{ formatDate(article.publishedAt) }}</span>
      <span class="separator">•</span>
      <span class="read-time">{{ article.readTime }} {{ t.home.minRead }}</span>
      <span class="separator">•</span>
      <span class="read-count">{{ article.readCount }} {{ t.home.views }}</span>
    </div>
  </div>

  <!-- Article Title -->
  <h1 class="article-title">{{ article.title }}</h1>

  <!-- Article Excerpt -->
  <p class="article-excerpt">{{ article.excerpt }}</p>

  <!-- Article Tags -->
  <div class="article-tags" *ngIf="article.tags && article.tags.length > 0">
    <mat-chip *ngFor="let tag of article.tags" class="tag-chip">{{ tag }}</mat-chip>
  </div>

  <!-- Article Content -->
  <div class="article-content-wrapper" [class.blurred]="shouldShowBlur()">
    <div class="article-content" [innerHTML]="getContentPreview()"></div>

    <!-- Blur Overlay for Anonymous Users -->
    <div class="blur-overlay" *ngIf="shouldShowBlur()">
      <div class="fade-gradient"></div>
    </div>
  </div>

  <!-- Authentication Prompt Overlay -->
  <div class="auth-prompt-overlay" *ngIf="shouldShowAuthPrompt()">
    <mat-card class="auth-prompt-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>lock</mat-icon>
          {{ t.articles.preview.signInToRead }}
        </mat-card-title>
        <mat-card-subtitle>
          {{ t.articles.preview.joinCommunity }}
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <!-- Reading Progress -->
        <div class="reading-progress" *ngIf="!isAuthenticated">
          <div class="progress-info">
            <span>Прочетохте {{ getContentCompletionPercentage() }}% от статията</span>
            <mat-icon>trending_up</mat-icon>
          </div>
          <mat-progress-bar
            mode="determinate"
            [value]="getContentCompletionPercentage()"
            color="accent">
          </mat-progress-bar>
        </div>

        <p>{{ t.articles.preview.valuableContent }}</p>

        <div class="auth-benefits">
          <div class="benefit-item">
            <mat-icon>auto_stories</mat-icon>
            <span>{{ t.articles.preview.unlimitedAccess }}</span>
          </div>
          <div class="benefit-item">
            <mat-icon>psychology</mat-icon>
            <span>{{ t.articles.preview.connectWithAstrologers }}</span>
          </div>
          <div class="benefit-item">
            <mat-icon>bookmark_added</mat-icon>
            <span>{{ t.articles.preview.saveArticles }}</span>
          </div>
          <div class="benefit-item">
            <mat-icon>notifications</mat-icon>
            <span>{{ t.articles.preview.notifications }}</span>
          </div>
        </div>

        <!-- Social Proof -->
        <div class="social-proof">
          <mat-icon>group</mat-icon>
          <span>{{ t.articles.preview.socialProof }}</span>
        </div>
      </mat-card-content>

      <mat-card-actions class="auth-actions">
        <button mat-raised-button color="primary" (click)="onLoginClick()">
          <mat-icon>login</mat-icon>
          {{ t.auth.signIn }}
        </button>
        <button mat-raised-button color="accent" (click)="onRegisterClick()">
          <mat-icon>person_add</mat-icon>
          {{ t.auth.createAccount }}
        </button>
        <button mat-button (click)="onCloseAuthPrompt()">
          <mat-icon>close</mat-icon>
          {{ t.common.close }}
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
