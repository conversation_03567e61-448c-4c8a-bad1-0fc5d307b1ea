{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../profile/services/profile.service\";\nimport * as i2 from \"../shared/services/article.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../core/i18n/translation.service\";\nimport * as i5 from \"../shared/services/avatar.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../shared/directives/lazy-load-image.directive\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/chips\";\nfunction HomeComponent_div_31_mat_card_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r7.name, \" \");\n  }\n}\nfunction HomeComponent_div_31_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_31_mat_card_1_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const profile_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.viewProfile(profile_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵelement(2, \"img\", 35);\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"h3\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 38);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 39)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 40)(14, \"p\", 41);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 42)(17, \"div\", 43);\n    i0.ɵɵtemplate(18, HomeComponent_div_31_mat_card_1_span_18_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 45)(20, \"div\", 46)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 46)(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"thumb_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"mat-card-actions\")(31, \"button\", 47)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 48)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const profile_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"appLazyLoadImage\", profile_r5.profilePhotoUrl)(\"fallbackFirstName\", profile_r5.firstName)(\"fallbackLastName\", profile_r5.lastName)(\"alt\", profile_r5.firstName + \" \" + profile_r5.lastName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", profile_r5.firstName, \" \", profile_r5.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(profile_r5.professionalTitle);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(profile_r5.location == null ? null : profile_r5.location.displayLocation);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(profile_r5.headline);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", profile_r5.skills.slice(0, 3));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", profile_r5.profileViews, \" \", ctx_r4.t.home.views, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.getTotalEndorsements(profile_r5), \" \", ctx_r4.t.home.endorsements, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.t.home.viewProfile, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.t.common.contact, \" \");\n  }\n}\nfunction HomeComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, HomeComponent_div_31_mat_card_1_Template, 39, 16, \"mat-card\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredProfiles);\n  }\n}\nfunction HomeComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.t.common.loading, \"...\");\n  }\n}\nfunction HomeComponent_mat_card_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 51);\n    i0.ɵɵlistener(\"click\", function HomeComponent_mat_card_43_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const article_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.readArticle(article_r10));\n    });\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementStart(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\")(6, \"div\", 53)(7, \"span\", 54);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 55);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"p\", 57);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-chip\", 58);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 47)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"read_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 59)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 59)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"bookmark_border\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const article_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", article_r10.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", article_r10.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r10.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"By \", article_r10.author, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, article_r10.publishedAt, \"MMM d, y\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", article_r10.readTime, \" \", ctx_r2.t.home.minRead, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r10.excerpt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(article_r10.category);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.home.readArticle, \" \");\n  }\n}\nfunction HomeComponent_mat_card_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 60);\n    i0.ɵɵlistener(\"click\", function HomeComponent_mat_card_54_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const sign_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.viewHoroscope(sign_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"div\", 61)(3, \"span\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-title\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"p\", 63);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 64)(13, \"div\", 65)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"local_fire_department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 65)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"casino\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 65)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"palette\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"mat-card-actions\")(29, \"button\", 47)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"read_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const sign_r13 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(sign_r13.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sign_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sign_r13.dateRange);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(sign_r13.todayPrediction);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(sign_r13.element);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.t.home.luckyNumber, \": \", sign_r13.luckyNumber, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(sign_r13.luckyColor);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.home.fullReading, \" \");\n  }\n}\nexport class HomeComponent {\n  constructor(profileService, articleService, router, t, avatarService) {\n    this.profileService = profileService;\n    this.articleService = articleService;\n    this.router = router;\n    this.t = t;\n    this.avatarService = avatarService;\n    this.featuredProfiles = [];\n    this.featuredArticles = [];\n    this.horoscopeSigns = [];\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    this.loadHomeData();\n  }\n  loadHomeData() {\n    this.isLoading = true;\n    // Load featured profiles from backend\n    this.profileService.getPublicProfiles(1, 6).subscribe({\n      next: result => {\n        this.featuredProfiles = result.profiles;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading featured profiles:', error);\n        // Fallback to single profile if public profiles fail\n        this.profileService.getProfile('luna-starweaver').subscribe({\n          next: profile => {\n            this.featuredProfiles = [profile];\n            this.isLoading = false;\n          },\n          error: err => {\n            console.error('Error loading profile:', err);\n            this.featuredProfiles = [];\n            this.isLoading = false;\n          }\n        });\n      }\n    });\n    // Load featured articles\n    this.loadFeaturedArticles();\n    // Load horoscope data (still using mock data for now)\n    this.horoscopeSigns = this.getMockHoroscope();\n  }\n  loadFeaturedArticles() {\n    this.articleService.getFeaturedArticles(6).subscribe({\n      next: articles => {\n        this.featuredArticles = articles;\n      },\n      error: error => {\n        console.error('Error loading featured articles from database:', error);\n        // Don't fall back to mock data - show empty state or error message\n        this.featuredArticles = [];\n      }\n    });\n  }\n  getMockHoroscope() {\n    return [{\n      id: 1,\n      name: 'Овен',\n      symbol: '♈',\n      element: 'Огън',\n      dateRange: '21 март - 19 април',\n      todayPrediction: 'Вашата огнена енергия е на върха си днес. Насочете тази страст към творчески проекти и нови начинания.',\n      luckyNumber: 7,\n      luckyColor: 'Червен',\n      compatibility: ['Лъв', 'Стрелец', 'Близнаци']\n    }, {\n      id: 2,\n      name: 'Телец',\n      symbol: '♉',\n      element: 'Земя',\n      dateRange: '20 април - 20 май',\n      todayPrediction: 'Фокусирайте се върху стабилността и комфорта днес. Вашата практична природа ще ви води към мъдри финансови решения.',\n      luckyNumber: 3,\n      luckyColor: 'Зелен',\n      compatibility: ['Дева', 'Козирог', 'Рак']\n    }, {\n      id: 3,\n      name: 'Близнаци',\n      symbol: '♊',\n      element: 'Въздух',\n      dateRange: '21 май - 20 юни',\n      todayPrediction: 'Комуникацията е ключова днес. Вашият ум и чар ще отворят нови врати и ще укрепят отношенията.',\n      luckyNumber: 5,\n      luckyColor: 'Жълт',\n      compatibility: ['Везни', 'Водолей', 'Овен']\n    }, {\n      id: 4,\n      name: 'Рак',\n      symbol: '♋',\n      element: 'Вода',\n      dateRange: '21 юни - 22 юли',\n      todayPrediction: 'Доверете се на интуицията си днес. Вашата емоционална интелигентност ще ви помогне да навигирате сложни ситуации с грация.',\n      luckyNumber: 2,\n      luckyColor: 'Сребърен',\n      compatibility: ['Скорпион', 'Риби', 'Телец']\n    }, {\n      id: 5,\n      name: 'Leo',\n      symbol: '♌',\n      element: 'Fire',\n      dateRange: 'Jul 23 - Aug 22',\n      todayPrediction: 'Your natural leadership shines bright today. Take center stage and inspire others with your confidence.',\n      luckyNumber: 1,\n      luckyColor: 'Gold',\n      compatibility: ['Aries', 'Sagittarius', 'Gemini']\n    }, {\n      id: 6,\n      name: 'Virgo',\n      symbol: '♍',\n      element: 'Earth',\n      dateRange: 'Aug 23 - Sep 22',\n      todayPrediction: 'Attention to detail pays off today. Your analytical skills will help you solve problems others cannot.',\n      luckyNumber: 6,\n      luckyColor: 'Navy Blue',\n      compatibility: ['Taurus', 'Capricorn', 'Cancer']\n    }];\n  }\n  navigateToLogin() {\n    console.log('Navigating to login...');\n    this.router.navigate(['/login']);\n  }\n  navigateToRegister() {\n    console.log('Navigating to register...');\n    this.router.navigate(['/register']);\n  }\n  viewProfile(profile) {\n    this.router.navigate(['/profile', profile.slug]);\n  }\n  readArticle(article) {\n    // Navigate to article view page\n    this.router.navigate(['/articles', article.slug]);\n  }\n  viewHoroscope(sign) {\n    // For now, just navigate to login to view detailed horoscope\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: `/horoscope/${sign.name.toLowerCase()}`\n      }\n    });\n  }\n  searchProfiles() {\n    this.router.navigate(['/profiles/search']);\n  }\n  getTotalEndorsements(profile) {\n    return profile.skills.reduce((sum, skill) => sum + skill.endorsements, 0);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ProfileService), i0.ɵɵdirectiveInject(i2.ArticleService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.TranslationService), i0.ɵɵdirectiveInject(i5.AvatarService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 70,\n      vars: 19,\n      consts: [[\"id\", \"hero\", 1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"cta-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"secondary-button\", 3, \"click\"], [1, \"hero-image\"], [\"src\", \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop\", \"alt\", \"Mystical cosmic background\", 1, \"hero-img\"], [\"id\", \"astrologers\", 1, \"featured-section\"], [1, \"section-container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"section-subtitle\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"view-all-btn\", 3, \"click\"], [\"class\", \"profiles-grid\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"id\", \"articles\", 1, \"articles-section\"], [1, \"articles-grid\"], [\"class\", \"article-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"horoscope\", 1, \"horoscope-section\"], [1, \"horoscope-grid\"], [\"class\", \"horoscope-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"cta-section\"], [1, \"cta-content\"], [1, \"cta-title\"], [1, \"cta-subtitle\"], [1, \"cta-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"cta-primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"cta-secondary\", 3, \"click\"], [1, \"profiles-grid\"], [\"class\", \"profile-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-card\", 3, \"click\"], [1, \"profile-header\"], [\"loadingClass\", \"home-avatar-loading\", \"errorClass\", \"home-avatar-error\", \"loadedClass\", \"home-avatar-loaded\", 1, \"profile-avatar\", 3, \"appLazyLoadImage\", \"fallbackFirstName\", \"fallbackLastName\", \"alt\"], [1, \"profile-info\"], [1, \"profile-name\"], [1, \"profile-title\"], [1, \"profile-location\"], [1, \"profile-content\"], [1, \"profile-headline\"], [1, \"profile-skills\"], [1, \"skills-container\"], [\"class\", \"skill-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-stats\"], [1, \"stat\"], [\"mat-button\", \"\", \"color\", \"primary\"], [\"mat-button\", \"\", \"color\", \"accent\"], [1, \"skill-chip\"], [1, \"loading-container\"], [1, \"article-card\", 3, \"click\"], [\"mat-card-image\", \"\", 1, \"article-image\", 3, \"src\", \"alt\"], [1, \"article-meta\"], [1, \"author\"], [1, \"date\"], [1, \"read-time\"], [1, \"article-excerpt\"], [1, \"category-chip\"], [\"mat-icon-button\", \"\"], [1, \"horoscope-card\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"sign-avatar\"], [1, \"sign-symbol\"], [1, \"prediction\"], [1, \"sign-details\"], [1, \"detail\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_8_listener() {\n            return ctx.navigateToRegister();\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_12_listener() {\n            return ctx.navigateToLogin();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 8);\n          i0.ɵɵelement(17, \"img\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"section\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"h2\", 13)(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"people\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 14);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_27_listener() {\n            return ctx.searchProfiles();\n          });\n          i0.ɵɵelementStart(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, HomeComponent_div_31_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(32, HomeComponent_div_32_Template, 4, 1, \"div\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"section\", 18)(34, \"div\", 11)(35, \"div\", 12)(36, \"h2\", 13)(37, \"mat-icon\");\n          i0.ɵɵtext(38, \"article\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\", 14);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 19);\n          i0.ɵɵtemplate(43, HomeComponent_mat_card_43_Template, 30, 13, \"mat-card\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"section\", 21)(45, \"div\", 11)(46, \"div\", 12)(47, \"h2\", 13)(48, \"mat-icon\");\n          i0.ɵɵtext(49, \"brightness_7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\", 14);\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 22);\n          i0.ɵɵtemplate(54, HomeComponent_mat_card_54_Template, 33, 9, \"mat-card\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"section\", 24)(56, \"div\", 25)(57, \"h2\", 26);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\", 27);\n          i0.ɵɵtext(60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 28)(62, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_62_listener() {\n            return ctx.navigateToRegister();\n          });\n          i0.ɵɵelementStart(63, \"mat-icon\");\n          i0.ɵɵtext(64, \"star\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_66_listener() {\n            return ctx.navigateToLogin();\n          });\n          i0.ɵɵelementStart(67, \"mat-icon\");\n          i0.ɵɵtext(68, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.home.heroTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.heroSubtitle, \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.startJourney, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.common.login, \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.featuredAstrologers, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.featuredAstrologersSubtitle);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.browseAllAstrologers, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.cosmicWisdomArticles, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.cosmicWisdomSubtitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.featuredArticles);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.dailyHoroscope, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.home.dailyHoroscopeSubtitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.horoscopeSigns);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.t.home.ctaTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.ctaSubtitle, \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.createFreeAccount, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.home.alreadyMember, \" \");\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.LazyLoadImageDirective, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardActions, i9.MatCardAvatar, i9.MatCardContent, i9.MatCardHeader, i9.MatCardImage, i9.MatCardSubtitle, i9.MatCardTitle, i10.MatIcon, i11.MatProgressSpinner, i12.MatChip, i6.DatePipe],\n      styles: [\".hero-section[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-primary);\\r\\n  color: white;\\r\\n  padding: 80px 20px; \\r\\n  min-height: 500px;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.hero-content[_ngcontent-%COMP%] {\\r\\n  max-width: 1200px;\\r\\n  width: 100%;\\r\\n  display: grid;\\r\\n  grid-template-columns: 1fr 1fr;\\r\\n  gap: 60px;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.hero-text[_ngcontent-%COMP%] {\\r\\n  max-width: 500px;\\r\\n}\\r\\n\\r\\n.hero-title[_ngcontent-%COMP%] {\\r\\n  font-size: 3.5rem;\\r\\n  font-weight: 700;\\r\\n  margin-bottom: 20px;\\r\\n  line-height: 1.2;\\r\\n}\\r\\n\\r\\n.hero-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  margin-bottom: 40px;\\r\\n  opacity: 0.9;\\r\\n  line-height: 1.6;\\r\\n}\\r\\n\\r\\n.hero-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 20px;\\r\\n  flex-wrap: wrap;\\r\\n}\\r\\n\\r\\n.cta-button[_ngcontent-%COMP%], .secondary-button[_ngcontent-%COMP%] {\\r\\n  padding: 12px 32px;\\r\\n  font-size: 1.1rem;\\r\\n  font-weight: 600;\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n.hero-image[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.hero-img[_ngcontent-%COMP%] {\\r\\n  max-width: 100%;\\r\\n  height: auto;\\r\\n  border-radius: 16px;\\r\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\\r\\n}\\r\\n\\r\\n\\r\\n.featured-section[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%], .horoscope-section[_ngcontent-%COMP%] {\\r\\n  padding: 80px 20px;\\r\\n  background: var(--theme-background);\\r\\n}\\r\\n\\r\\n.section-container[_ngcontent-%COMP%] {\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.section-header[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n  margin-bottom: 60px;\\r\\n}\\r\\n\\r\\n.section-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 12px;\\r\\n  font-size: 2.5rem;\\r\\n  font-weight: 700;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.section-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin-bottom: 30px;\\r\\n}\\r\\n\\r\\n.view-all-btn[_ngcontent-%COMP%] {\\r\\n  margin-top: 20px;\\r\\n}\\r\\n\\r\\n\\r\\n.profiles-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\r\\n  gap: 30px;\\r\\n  margin-bottom: 40px;\\r\\n}\\r\\n\\r\\n.profile-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.profile-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-8px);\\r\\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.profile-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  padding: 20px;\\r\\n  background: var(--theme-accent-light);\\r\\n}\\r\\n\\r\\n.profile-avatar[_ngcontent-%COMP%] {\\r\\n  width: 60px;\\r\\n  height: 60px;\\r\\n  border-radius: 50%;\\r\\n  object-fit: cover;\\r\\n  border: 3px solid var(--theme-accent);\\r\\n  transition: opacity 0.3s ease, filter 0.3s ease;\\r\\n}\\r\\n\\r\\n.profile-avatar.home-avatar-loading[_ngcontent-%COMP%] {\\r\\n  opacity: 0.7;\\r\\n  filter: blur(1px);\\r\\n}\\r\\n\\r\\n.profile-avatar.home-avatar-loaded[_ngcontent-%COMP%] {\\r\\n  opacity: 1;\\r\\n  filter: none;\\r\\n}\\r\\n\\r\\n.profile-avatar.home-avatar-error[_ngcontent-%COMP%] {\\r\\n  opacity: 0.8;\\r\\n  filter: grayscale(20%);\\r\\n}\\r\\n\\r\\n.profile-info[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n.profile-name[_ngcontent-%COMP%] {\\r\\n  font-size: 1.3rem;\\r\\n  font-weight: 600;\\r\\n  margin: 0 0 4px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n}\\r\\n\\r\\n.profile-title[_ngcontent-%COMP%] {\\r\\n  font-size: 1rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n  margin: 0 0 8px 0;\\r\\n}\\r\\n\\r\\n.profile-location[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 4px;\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.profile-content[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n}\\r\\n\\r\\n.profile-headline[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.5;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.profile-skills[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.skills-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.skill-chip[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-accent);\\r\\n  color: var(--theme-text-primary);\\r\\n  padding: 4px 12px;\\r\\n  border-radius: 16px;\\r\\n  font-size: 0.85rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.profile-stats[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.stat[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 6px;\\r\\n  font-size: 0.9rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.stat[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 18px;\\r\\n  width: 18px;\\r\\n  height: 18px;\\r\\n}\\r\\n\\r\\n\\r\\n.articles-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\r\\n  gap: 30px;\\r\\n}\\r\\n\\r\\n.article-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.article-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-4px);\\r\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);\\r\\n}\\r\\n\\r\\n.article-image[_ngcontent-%COMP%] {\\r\\n  height: 200px;\\r\\n  object-fit: cover;\\r\\n}\\r\\n\\r\\n.article-meta[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 4px;\\r\\n  font-size: 0.85rem;\\r\\n}\\r\\n\\r\\n.author[_ngcontent-%COMP%] {\\r\\n  font-weight: 600;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.date[_ngcontent-%COMP%], .read-time[_ngcontent-%COMP%] {\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.article-excerpt[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.6;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.category-chip[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-primary);\\r\\n  color: white;\\r\\n  font-size: 0.8rem;\\r\\n}\\r\\n\\r\\n\\r\\n.horoscope-grid[_ngcontent-%COMP%] {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\r\\n  gap: 24px;\\r\\n}\\r\\n\\r\\n.horoscope-card[_ngcontent-%COMP%] {\\r\\n  cursor: pointer;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n  border-radius: 16px;\\r\\n  border: 2px solid var(--theme-accent-light);\\r\\n}\\r\\n\\r\\n.horoscope-card[_ngcontent-%COMP%]:hover {\\r\\n  transform: translateY(-4px);\\r\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);\\r\\n  border-color: var(--theme-accent);\\r\\n}\\r\\n\\r\\n.sign-avatar[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-primary);\\r\\n  color: white;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  width: 50px;\\r\\n  height: 50px;\\r\\n  border-radius: 50%;\\r\\n}\\r\\n\\r\\n.sign-symbol[_ngcontent-%COMP%] {\\r\\n  font-size: 1.5rem;\\r\\n  font-weight: bold;\\r\\n}\\r\\n\\r\\n.prediction[_ngcontent-%COMP%] {\\r\\n  font-size: 0.95rem;\\r\\n  line-height: 1.5;\\r\\n  color: var(--theme-text-primary);\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.sign-details[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.detail[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 6px;\\r\\n  font-size: 0.85rem;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n.detail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 16px;\\r\\n  width: 16px;\\r\\n  height: 16px;\\r\\n}\\r\\n\\r\\n\\r\\n.cta-section[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-secondary);\\r\\n  color: white;\\r\\n  padding: 80px 20px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.cta-content[_ngcontent-%COMP%] {\\r\\n  max-width: 800px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.cta-title[_ngcontent-%COMP%] {\\r\\n  font-size: 2.5rem;\\r\\n  font-weight: 700;\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.cta-subtitle[_ngcontent-%COMP%] {\\r\\n  font-size: 1.2rem;\\r\\n  margin-bottom: 40px;\\r\\n  opacity: 0.9;\\r\\n  line-height: 1.6;\\r\\n}\\r\\n\\r\\n.cta-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  gap: 20px;\\r\\n  flex-wrap: wrap;\\r\\n}\\r\\n\\r\\n.cta-primary[_ngcontent-%COMP%], .cta-secondary[_ngcontent-%COMP%] {\\r\\n  padding: 12px 32px;\\r\\n  font-size: 1.1rem;\\r\\n  font-weight: 600;\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 20px;\\r\\n  padding: 60px 20px;\\r\\n  color: var(--theme-text-secondary);\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  \\r\\n  .nav-links[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n\\r\\n  .nav-actions[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n\\r\\n  .mobile-menu-toggle[_ngcontent-%COMP%] {\\r\\n    display: flex;\\r\\n  }\\r\\n\\r\\n  .nav-container[_ngcontent-%COMP%] {\\r\\n    padding: 0 16px;\\r\\n  }\\r\\n\\r\\n  .hero-section[_ngcontent-%COMP%] {\\r\\n    padding: 120px 20px 80px; \\r\\n  }\\r\\n\\r\\n  .hero-content[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n    gap: 40px;\\r\\n    text-align: center;\\r\\n  }\\r\\n\\r\\n  .hero-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2.5rem;\\r\\n  }\\r\\n\\r\\n  .profiles-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n\\r\\n  .articles-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: 1fr;\\r\\n  }\\r\\n\\r\\n  .horoscope-grid[_ngcontent-%COMP%] {\\r\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\r\\n  }\\r\\n\\r\\n  .section-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n\\r\\n  .cta-title[_ngcontent-%COMP%] {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n\\r\\n  .hero-actions[_ngcontent-%COMP%], .cta-actions[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: center;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;IAmEcA,gCAA0E;IACxEA,YACF;IAAAA,iBAAO;;;;IADLA,eACF;IADEA,8CACF;;;;;;IA3BRA,oCAAuG;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,6CAAoB;IAAA,EAAC;IACpGA,+BAA4B;IAC1BA,0BAOsC;IACtCA,+BAA0B;IACCA,YAA8C;IAAAA,iBAAK;IAC5EA,6BAAyB;IAAAA,YAA+B;IAAAA,iBAAI;IAC5DA,+BAA8B;IAClBA,4BAAW;IAAAA,iBAAW;IAChCA,6BAAM;IAAAA,aAAuC;IAAAA,iBAAO;IAK1DA,gCAA6B;IACCA,aAAsB;IAAAA,iBAAI;IAEtDA,gCAA4B;IAExBA,qFAEO;IACTA,iBAAM;IAGRA,gCAA2B;IAEbA,2BAAU;IAAAA,iBAAW;IAC/BA,6BAAM;IAAAA,aAA6C;IAAAA,iBAAO;IAE5DA,gCAAkB;IACNA,yBAAQ;IAAAA,iBAAW;IAC7BA,6BAAM;IAAAA,aAA6D;IAAAA,iBAAO;IAKhFA,yCAAkB;IAEJA,2BAAU;IAAAA,iBAAW;IAC/BA,aACF;IAAAA,iBAAS;IACTA,mCAAkC;IACtBA,wBAAO;IAAAA,iBAAW;IAC5BA,aACF;IAAAA,iBAAS;;;;;IAjDJA,eAA4C;IAA5CA,6DAA4C;IAStBA,eAA8C;IAA9CA,6EAA8C;IAC9CA,eAA+B;IAA/BA,kDAA+B;IAGhDA,eAAuC;IAAvCA,8FAAuC;IAMrBA,eAAsB;IAAtBA,yCAAsB;IAItBA,eAA6B;IAA7BA,uDAA6B;IAS/CA,eAA6C;IAA7CA,gFAA6C;IAI7CA,eAA6D;IAA7DA,uGAA6D;IAQrEA,eACF;IADEA,0DACF;IAGEA,eACF;IADEA,wDACF;;;;;IApDNA,+BAA8C;IAC5CA,kFAqDW;IACbA,iBAAM;;;;IAtD+CA,eAAmB;IAAnBA,iDAAmB;;;;;IAyDxEA,+BAAiD;IAC/CA,8BAA2B;IAC3BA,yBAAG;IAAAA,YAAyB;IAAAA,iBAAI;;;;IAA7BA,eAAyB;IAAzBA,yDAAyB;;;;;;IAiB5BA,oCAAuG;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IACpGA,0BAAyF;IAEzFA,uCAAiB;IACCA,YAAmB;IAAAA,iBAAiB;IACpDA,yCAAmB;IAEMA,YAAuB;IAAAA,iBAAO;IACnDA,gCAAmB;IAAAA,aAA2C;;IAAAA,iBAAO;IACrEA,iCAAwB;IAAAA,aAA2C;IAAAA,iBAAO;IAKhFA,yCAAkB;IACWA,aAAqB;IAAAA,iBAAI;IACpDA,qCAAgC;IAAAA,aAAsB;IAAAA,iBAAW;IAGnEA,yCAAkB;IAEJA,0BAAS;IAAAA,iBAAW;IAC9BA,aACF;IAAAA,iBAAS;IACTA,mCAAwB;IACZA,sBAAK;IAAAA,iBAAW;IAE5BA,mCAAwB;IACZA,gCAAe;IAAAA,iBAAW;;;;;IA3BpBA,eAAwB;IAAxBA,4DAAwB;IAG1BA,eAAmB;IAAnBA,uCAAmB;IAGVA,eAAuB;IAAvBA,oDAAuB;IACzBA,eAA2C;IAA3CA,iFAA2C;IACtCA,eAA2C;IAA3CA,+EAA2C;IAM5CA,eAAqB;IAArBA,yCAAqB;IAChBA,eAAsB;IAAtBA,0CAAsB;IAMpDA,eACF;IADEA,0DACF;;;;;;IAyBJA,oCAAmG;IAA9BA;MAAA;MAAA;MAAA;MAAA,OAASA,8CAAmB;IAAA,EAAC;IAChGA,uCAAiB;IAEaA,YAAiB;IAAAA,iBAAO;IAEpDA,sCAAgB;IAAAA,YAAe;IAAAA,iBAAiB;IAChDA,yCAAmB;IAAAA,YAAoB;IAAAA,iBAAoB;IAG7DA,wCAAkB;IACMA,aAA0B;IAAAA,iBAAI;IAEpDA,gCAA0B;IAEZA,sCAAqB;IAAAA,iBAAW;IAC1CA,6BAAM;IAAAA,aAAkB;IAAAA,iBAAO;IAEjCA,gCAAoB;IACRA,uBAAM;IAAAA,iBAAW;IAC3BA,6BAAM;IAAAA,aAAgD;IAAAA,iBAAO;IAE/DA,gCAAoB;IACRA,wBAAO;IAAAA,iBAAW;IAC5BA,6BAAM;IAAAA,aAAqB;IAAAA,iBAAO;IAKxCA,yCAAkB;IAEJA,0BAAS;IAAAA,iBAAW;IAC9BA,aACF;IAAAA,iBAAS;;;;;IA7BmBA,eAAiB;IAAjBA,qCAAiB;IAE7BA,eAAe;IAAfA,mCAAe;IACZA,eAAoB;IAApBA,wCAAoB;IAIjBA,eAA0B;IAA1BA,8CAA0B;IAKtCA,eAAkB;IAAlBA,sCAAkB;IAIlBA,eAAgD;IAAhDA,oFAAgD;IAIhDA,eAAqB;IAArBA,yCAAqB;IAQ7BA,eACF;IADEA,0DACF;;;AC3KV,OAAM,MAAOC,aAAa;EAMxBC,YACUC,cAA8B,EAC9BC,cAA8B,EAC9BC,MAAc,EACfC,CAAqB,EACrBC,aAA4B;IAJ3B,mBAAc,GAAdJ,cAAc;IACd,mBAAc,GAAdC,cAAc;IACd,WAAM,GAANC,MAAM;IACP,MAAC,GAADC,CAAC;IACD,kBAAa,GAAbC,aAAa;IAVtB,qBAAgB,GAAkB,EAAE;IACpC,qBAAgB,GAAqB,EAAE;IACvC,mBAAc,GAAoB,EAAE;IACpC,cAAS,GAAG,IAAI;EAQb;EAEHC,QAAQ;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAY;IAClB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACP,cAAc,CAACQ,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,SAAS,CAAC;MACpDC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACC,gBAAgB,GAAGD,MAAM,CAACE,QAAQ;QACvC,IAAI,CAACN,SAAS,GAAG,KAAK;MACxB,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD;QACA,IAAI,CAACd,cAAc,CAACgB,UAAU,CAAC,iBAAiB,CAAC,CAACP,SAAS,CAAC;UAC1DC,IAAI,EAAGO,OAAO,IAAI;YAChB,IAAI,CAACL,gBAAgB,GAAG,CAACK,OAAO,CAAC;YACjC,IAAI,CAACV,SAAS,GAAG,KAAK;UACxB,CAAC;UACDO,KAAK,EAAGI,GAAG,IAAI;YACbH,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEI,GAAG,CAAC;YAC5C,IAAI,CAACN,gBAAgB,GAAG,EAAE;YAC1B,IAAI,CAACL,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;KACD,CAAC;IAEF;IACA,IAAI,CAACY,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,gBAAgB,EAAE;EAC/C;EAEQF,oBAAoB;IAC1B,IAAI,CAAClB,cAAc,CAACqB,mBAAmB,CAAC,CAAC,CAAC,CAACb,SAAS,CAAC;MACnDC,IAAI,EAAGa,QAAQ,IAAI;QACjB,IAAI,CAACC,gBAAgB,GAAGD,QAAQ;MAClC,CAAC;MACDT,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE;QACA,IAAI,CAACU,gBAAgB,GAAG,EAAE;MAC5B;KACD,CAAC;EACJ;EAIQH,gBAAgB;IACtB,OAAO,CACL;MACEI,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,oBAAoB;MAC/BC,eAAe,EAAE,wGAAwG;MACzHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU;KAC7C,EACD;MACER,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,mBAAmB;MAC9BC,eAAe,EAAE,qHAAqH;MACtIC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,OAAO;MACnBC,aAAa,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK;KACzC,EACD;MACER,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,+FAA+F;MAChHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM;KAC3C,EACD;MACER,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,4HAA4H;MAC7IC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,UAAU;MACtBC,aAAa,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO;KAC5C,EACD;MACER,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,yGAAyG;MAC1HC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ;KACjD,EACD;MACER,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,iBAAiB;MAC5BC,eAAe,EAAE,wGAAwG;MACzHC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,WAAW;MACvBC,aAAa,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ;KAChD,CACF;EACH;EAEAC,eAAe;IACbnB,OAAO,CAACoB,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,kBAAkB;IAChBtB,OAAO,CAACoB,GAAG,CAAC,2BAA2B,CAAC;IACxC,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAE,WAAW,CAACrB,OAAoB;IAC9B,IAAI,CAACf,MAAM,CAACkC,QAAQ,CAAC,CAAC,UAAU,EAAEnB,OAAO,CAACsB,IAAI,CAAC,CAAC;EAClD;EAEAC,WAAW,CAACC,OAAuB;IACjC;IACA,IAAI,CAACvC,MAAM,CAACkC,QAAQ,CAAC,CAAC,WAAW,EAAEK,OAAO,CAACF,IAAI,CAAC,CAAC;EACnD;EAEAG,aAAa,CAACC,IAAmB;IAC/B;IACA,IAAI,CAACzC,MAAM,CAACkC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BQ,WAAW,EAAE;QAAEC,SAAS,EAAE,cAAcF,IAAI,CAACjB,IAAI,CAACoB,WAAW,EAAE;MAAE;KAClE,CAAC;EACJ;EAEAC,cAAc;IACZ,IAAI,CAAC7C,MAAM,CAACkC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAY,oBAAoB,CAAC/B,OAAoB;IACvC,OAAOA,OAAO,CAACgC,MAAM,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACC,YAAY,EAAE,CAAC,CAAC;EAC3E;;;uBAzKWvD,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAwD;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UD1B1B7D,kCAAwC;UAGXA,YAAsB;UAAAA,iBAAK;UAClDA,4BAAyB;UACvBA,YACF;UAAAA,iBAAI;UACJA,8BAA0B;UACqCA;YAAA,OAAS8D,wBAAoB;UAAA,EAAC;UACzF9D,gCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,aACF;UAAAA,iBAAS;UACTA,kCAAgG;UAA5BA;YAAA,OAAS8D,qBAAiB;UAAA,EAAC;UAC7F9D,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,aACF;UAAAA,iBAAS;UAGbA,+BAAwB;UACtBA,0BACuD;UACzDA,iBAAM;UAKVA,oCAAmD;UAIjCA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAwC;UAAAA,iBAAI;UACxEA,mCAA2F;UAAhDA;YAAA,OAAS8D,oBAAgB;UAAA,EAAC;UACnE9D,iCAAU;UAAAA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAS;UAGXA,iEAuDM;UAGNA,iEAGM;UACRA,iBAAM;UAIRA,oCAAgD;UAI9BA,wBAAO;UAAAA,iBAAW;UAC5BA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAiC;UAAAA,iBAAI;UAGnEA,gCAA2B;UACzBA,6EA+BW;UACbA,iBAAM;UAKVA,oCAAkD;UAIhCA,6BAAY;UAAAA,iBAAW;UACjCA,aACF;UAAAA,iBAAK;UACLA,8BAA4B;UAAAA,aAAmC;UAAAA,iBAAI;UAGrEA,gCAA4B;UAC1BA,4EAkCW;UACbA,iBAAM;UAKVA,oCAA6B;UAEHA,aAAqB;UAAAA,iBAAK;UAChDA,8BAAwB;UACtBA,aACF;UAAAA,iBAAI;UACJA,gCAAyB;UACuCA;YAAA,OAAS8D,wBAAoB;UAAA,EAAC;UAC1F9D,iCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,aACF;UAAAA,iBAAS;UACTA,mCAA6F;UAA5BA;YAAA,OAAS8D,qBAAiB;UAAA,EAAC;UAC1F9D,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,aACF;UAAAA,iBAAS;;;UAxNcA,eAAsB;UAAtBA,0CAAsB;UAE3CA,eACF;UADEA,wDACF;UAIIA,eACF;UADEA,wDACF;UAGEA,eACF;UADEA,mDACF;UAgBAA,eACF;UADEA,+DACF;UAC4BA,eAAwC;UAAxCA,4DAAwC;UAGlEA,eACF;UADEA,gEACF;UAG0BA,eAAgB;UAAhBA,qCAAgB;UA0DZA,eAAe;UAAfA,oCAAe;UAa3CA,eACF;UADEA,gEACF;UAC4BA,eAAiC;UAAjCA,qDAAiC;UAIVA,eAAmB;UAAnBA,8CAAmB;UA0CpEA,eACF;UADEA,0DACF;UAC4BA,eAAmC;UAAnCA,uDAAmC;UAIbA,eAAiB;UAAjBA,4CAAiB;UA0C/CA,eAAqB;UAArBA,yCAAqB;UAEzCA,eACF;UADEA,uDACF;UAIIA,eACF;UADEA,6DACF;UAGEA,eACF;UADEA,yDACF", "names": ["i0", "HomeComponent", "constructor", "profileService", "articleService", "router", "t", "avatarService", "ngOnInit", "loadHomeData", "isLoading", "getPublicProfiles", "subscribe", "next", "result", "featuredPro<PERSON>les", "profiles", "error", "console", "getProfile", "profile", "err", "loadFeaturedArticles", "horoscopeSigns", "getMockHoroscope", "getFeaturedArticles", "articles", "featuredArticles", "id", "name", "symbol", "element", "date<PERSON><PERSON><PERSON>", "todayPrediction", "<PERSON><PERSON><PERSON><PERSON>", "luckyColor", "compatibility", "navigateToLogin", "log", "navigate", "navigateToRegister", "viewProfile", "slug", "readArticle", "article", "viewHoroscope", "sign", "queryParams", "returnUrl", "toLowerCase", "searchProfiles", "getTotalEndorsements", "skills", "reduce", "sum", "skill", "endorsements", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\home\\home.component.html", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\home\\home.component.ts"], "sourcesContent": ["<!-- Hero Section -->\r\n<section id=\"hero\" class=\"hero-section\">\r\n  <div class=\"hero-content\">\r\n    <div class=\"hero-text\">\r\n      <h1 class=\"hero-title\">{{ t.home.heroTitle }}</h1>\r\n      <p class=\"hero-subtitle\">\r\n        {{ t.home.heroSubtitle }}\r\n      </p>\r\n      <div class=\"hero-actions\">\r\n        <button mat-raised-button color=\"primary\" class=\"cta-button\" (click)=\"navigateToRegister()\" type=\"button\">\r\n          <mat-icon>star</mat-icon>\r\n          {{ t.home.startJourney }}\r\n        </button>\r\n        <button mat-stroked-button color=\"primary\" class=\"secondary-button\" (click)=\"navigateToLogin()\">\r\n          <mat-icon>login</mat-icon>\r\n          {{ t.common.login }}\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"hero-image\">\r\n      <img src=\"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop\"\r\n           alt=\"Mystical cosmic background\" class=\"hero-img\">\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Featured Astrologers Section -->\r\n<section id=\"astrologers\" class=\"featured-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>people</mat-icon>\r\n        {{ t.home.featuredAstrologers }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.featuredAstrologersSubtitle }}</p>\r\n      <button mat-stroked-button color=\"primary\" (click)=\"searchProfiles()\" class=\"view-all-btn\">\r\n        <mat-icon>search</mat-icon>\r\n        {{ t.home.browseAllAstrologers }}\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"profiles-grid\" *ngIf=\"!isLoading\">\r\n      <mat-card class=\"profile-card\" *ngFor=\"let profile of featuredProfiles\" (click)=\"viewProfile(profile)\">\r\n        <div class=\"profile-header\">\r\n          <img [appLazyLoadImage]=\"profile.profilePhotoUrl\"\r\n               [fallbackFirstName]=\"profile.firstName\"\r\n               [fallbackLastName]=\"profile.lastName\"\r\n               [alt]=\"profile.firstName + ' ' + profile.lastName\"\r\n               class=\"profile-avatar\"\r\n               loadingClass=\"home-avatar-loading\"\r\n               errorClass=\"home-avatar-error\"\r\n               loadedClass=\"home-avatar-loaded\">\r\n          <div class=\"profile-info\">\r\n            <h3 class=\"profile-name\">{{ profile.firstName }} {{ profile.lastName }}</h3>\r\n            <p class=\"profile-title\">{{ profile.professionalTitle }}</p>\r\n            <div class=\"profile-location\">\r\n              <mat-icon>location_on</mat-icon>\r\n              <span>{{ profile.location?.displayLocation }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"profile-content\">\r\n          <p class=\"profile-headline\">{{ profile.headline }}</p>\r\n\r\n          <div class=\"profile-skills\">\r\n            <div class=\"skills-container\">\r\n              <span *ngFor=\"let skill of profile.skills.slice(0, 3)\" class=\"skill-chip\">\r\n                {{ skill.name }}\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"profile-stats\">\r\n            <div class=\"stat\">\r\n              <mat-icon>visibility</mat-icon>\r\n              <span>{{ profile.profileViews }} {{ t.home.views }}</span>\r\n            </div>\r\n            <div class=\"stat\">\r\n              <mat-icon>thumb_up</mat-icon>\r\n              <span>{{ getTotalEndorsements(profile) }} {{ t.home.endorsements }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>visibility</mat-icon>\r\n            {{ t.home.viewProfile }}\r\n          </button>\r\n          <button mat-button color=\"accent\">\r\n            <mat-icon>message</mat-icon>\r\n            {{ t.common.contact }}\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div class=\"loading-container\" *ngIf=\"isLoading\">\r\n      <mat-spinner></mat-spinner>\r\n      <p>{{ t.common.loading }}...</p>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Articles Section -->\r\n<section id=\"articles\" class=\"articles-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>article</mat-icon>\r\n        {{ t.home.cosmicWisdomArticles }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.cosmicWisdomSubtitle }}</p>\r\n    </div>\r\n\r\n    <div class=\"articles-grid\">\r\n      <mat-card class=\"article-card\" *ngFor=\"let article of featuredArticles\" (click)=\"readArticle(article)\">\r\n        <img mat-card-image [src]=\"article.imageUrl\" [alt]=\"article.title\" class=\"article-image\">\r\n\r\n        <mat-card-header>\r\n          <mat-card-title>{{ article.title }}</mat-card-title>\r\n          <mat-card-subtitle>\r\n            <div class=\"article-meta\">\r\n              <span class=\"author\">By {{ article.author }}</span>\r\n              <span class=\"date\">{{ article.publishedAt | date:'MMM d, y' }}</span>\r\n              <span class=\"read-time\">{{ article.readTime }} {{ t.home.minRead }}</span>\r\n            </div>\r\n          </mat-card-subtitle>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <p class=\"article-excerpt\">{{ article.excerpt }}</p>\r\n          <mat-chip class=\"category-chip\">{{ article.category }}</mat-chip>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>read_more</mat-icon>\r\n            {{ t.home.readArticle }}\r\n          </button>\r\n          <button mat-icon-button>\r\n            <mat-icon>share</mat-icon>\r\n          </button>\r\n          <button mat-icon-button>\r\n            <mat-icon>bookmark_border</mat-icon>\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Daily Horoscope Section -->\r\n<section id=\"horoscope\" class=\"horoscope-section\">\r\n  <div class=\"section-container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">\r\n        <mat-icon>brightness_7</mat-icon>\r\n        {{ t.home.dailyHoroscope }}\r\n      </h2>\r\n      <p class=\"section-subtitle\">{{ t.home.dailyHoroscopeSubtitle }}</p>\r\n    </div>\r\n\r\n    <div class=\"horoscope-grid\">\r\n      <mat-card class=\"horoscope-card\" *ngFor=\"let sign of horoscopeSigns\" (click)=\"viewHoroscope(sign)\">\r\n        <mat-card-header>\r\n          <div mat-card-avatar class=\"sign-avatar\">\r\n            <span class=\"sign-symbol\">{{ sign.symbol }}</span>\r\n          </div>\r\n          <mat-card-title>{{ sign.name }}</mat-card-title>\r\n          <mat-card-subtitle>{{ sign.dateRange }}</mat-card-subtitle>\r\n        </mat-card-header>\r\n\r\n        <mat-card-content>\r\n          <p class=\"prediction\">{{ sign.todayPrediction }}</p>\r\n\r\n          <div class=\"sign-details\">\r\n            <div class=\"detail\">\r\n              <mat-icon>local_fire_department</mat-icon>\r\n              <span>{{ sign.element }}</span>\r\n            </div>\r\n            <div class=\"detail\">\r\n              <mat-icon>casino</mat-icon>\r\n              <span>{{ t.home.luckyNumber }}: {{ sign.luckyNumber }}</span>\r\n            </div>\r\n            <div class=\"detail\">\r\n              <mat-icon>palette</mat-icon>\r\n              <span>{{ sign.luckyColor }}</span>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n\r\n        <mat-card-actions>\r\n          <button mat-button color=\"primary\">\r\n            <mat-icon>read_more</mat-icon>\r\n            {{ t.home.fullReading }}\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Call to Action Section -->\r\n<section class=\"cta-section\">\r\n  <div class=\"cta-content\">\r\n    <h2 class=\"cta-title\">{{ t.home.ctaTitle }}</h2>\r\n    <p class=\"cta-subtitle\">\r\n      {{ t.home.ctaSubtitle }}\r\n    </p>\r\n    <div class=\"cta-actions\">\r\n      <button mat-raised-button color=\"primary\" class=\"cta-primary\" (click)=\"navigateToRegister()\" type=\"button\">\r\n        <mat-icon>star</mat-icon>\r\n        {{ t.home.createFreeAccount }}\r\n      </button>\r\n      <button mat-stroked-button color=\"primary\" class=\"cta-secondary\" (click)=\"navigateToLogin()\">\r\n        <mat-icon>login</mat-icon>\r\n        {{ t.home.alreadyMember }}\r\n      </button>\r\n    </div>\r\n  </div>\r\n</section>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Observable } from 'rxjs';\r\nimport { UserProfile } from '../profile/models/profile.models';\r\nimport { ProfileService } from '../profile/services/profile.service';\r\nimport { TranslationService } from '../core/i18n/translation.service';\r\nimport { AvatarService } from '../shared/services/avatar.service';\r\nimport { ArticleService } from '../shared/services/article.service';\r\nimport { ArticlePreview } from '../shared/models/article.models';\r\n\r\nexport interface HoroscopeSign {\r\n  id: number;\r\n  name: string;\r\n  symbol: string;\r\n  element: string;\r\n  dateRange: string;\r\n  todayPrediction: string;\r\n  luckyNumber: number;\r\n  luckyColor: string;\r\n  compatibility: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.css']\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  featuredProfiles: UserProfile[] = [];\r\n  featuredArticles: ArticlePreview[] = [];\r\n  horoscopeSigns: HoroscopeSign[] = [];\r\n  isLoading = true;\r\n\r\n  constructor(\r\n    private profileService: ProfileService,\r\n    private articleService: ArticleService,\r\n    private router: Router,\r\n    public t: TranslationService,\r\n    public avatarService: AvatarService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadHomeData();\r\n  }\r\n\r\n  private loadHomeData(): void {\r\n    this.isLoading = true;\r\n\r\n    // Load featured profiles from backend\r\n    this.profileService.getPublicProfiles(1, 6).subscribe({\r\n      next: (result) => {\r\n        this.featuredProfiles = result.profiles;\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading featured profiles:', error);\r\n        // Fallback to single profile if public profiles fail\r\n        this.profileService.getProfile('luna-starweaver').subscribe({\r\n          next: (profile) => {\r\n            this.featuredProfiles = [profile];\r\n            this.isLoading = false;\r\n          },\r\n          error: (err) => {\r\n            console.error('Error loading profile:', err);\r\n            this.featuredProfiles = [];\r\n            this.isLoading = false;\r\n          }\r\n        });\r\n      }\r\n    });\r\n\r\n    // Load featured articles\r\n    this.loadFeaturedArticles();\r\n\r\n    // Load horoscope data (still using mock data for now)\r\n    this.horoscopeSigns = this.getMockHoroscope();\r\n  }\r\n\r\n  private loadFeaturedArticles(): void {\r\n    this.articleService.getFeaturedArticles(6).subscribe({\r\n      next: (articles) => {\r\n        this.featuredArticles = articles;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading featured articles from database:', error);\r\n        // Don't fall back to mock data - show empty state or error message\r\n        this.featuredArticles = [];\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n\r\n  private getMockHoroscope(): HoroscopeSign[] {\r\n    return [\r\n      {\r\n        id: 1,\r\n        name: 'Овен',\r\n        symbol: '♈',\r\n        element: 'Огън',\r\n        dateRange: '21 март - 19 април',\r\n        todayPrediction: 'Вашата огнена енергия е на върха си днес. Насочете тази страст към творчески проекти и нови начинания.',\r\n        luckyNumber: 7,\r\n        luckyColor: 'Червен',\r\n        compatibility: ['Лъв', 'Стрелец', 'Близнаци']\r\n      },\r\n      {\r\n        id: 2,\r\n        name: 'Телец',\r\n        symbol: '♉',\r\n        element: 'Земя',\r\n        dateRange: '20 април - 20 май',\r\n        todayPrediction: 'Фокусирайте се върху стабилността и комфорта днес. Вашата практична природа ще ви води към мъдри финансови решения.',\r\n        luckyNumber: 3,\r\n        luckyColor: 'Зелен',\r\n        compatibility: ['Дева', 'Козирог', 'Рак']\r\n      },\r\n      {\r\n        id: 3,\r\n        name: 'Близнаци',\r\n        symbol: '♊',\r\n        element: 'Въздух',\r\n        dateRange: '21 май - 20 юни',\r\n        todayPrediction: 'Комуникацията е ключова днес. Вашият ум и чар ще отворят нови врати и ще укрепят отношенията.',\r\n        luckyNumber: 5,\r\n        luckyColor: 'Жълт',\r\n        compatibility: ['Везни', 'Водолей', 'Овен']\r\n      },\r\n      {\r\n        id: 4,\r\n        name: 'Рак',\r\n        symbol: '♋',\r\n        element: 'Вода',\r\n        dateRange: '21 юни - 22 юли',\r\n        todayPrediction: 'Доверете се на интуицията си днес. Вашата емоционална интелигентност ще ви помогне да навигирате сложни ситуации с грация.',\r\n        luckyNumber: 2,\r\n        luckyColor: 'Сребърен',\r\n        compatibility: ['Скорпион', 'Риби', 'Телец']\r\n      },\r\n      {\r\n        id: 5,\r\n        name: 'Leo',\r\n        symbol: '♌',\r\n        element: 'Fire',\r\n        dateRange: 'Jul 23 - Aug 22',\r\n        todayPrediction: 'Your natural leadership shines bright today. Take center stage and inspire others with your confidence.',\r\n        luckyNumber: 1,\r\n        luckyColor: 'Gold',\r\n        compatibility: ['Aries', 'Sagittarius', 'Gemini']\r\n      },\r\n      {\r\n        id: 6,\r\n        name: 'Virgo',\r\n        symbol: '♍',\r\n        element: 'Earth',\r\n        dateRange: 'Aug 23 - Sep 22',\r\n        todayPrediction: 'Attention to detail pays off today. Your analytical skills will help you solve problems others cannot.',\r\n        luckyNumber: 6,\r\n        luckyColor: 'Navy Blue',\r\n        compatibility: ['Taurus', 'Capricorn', 'Cancer']\r\n      }\r\n    ];\r\n  }\r\n\r\n  navigateToLogin(): void {\r\n    console.log('Navigating to login...');\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  navigateToRegister(): void {\r\n    console.log('Navigating to register...');\r\n    this.router.navigate(['/register']);\r\n  }\r\n\r\n  viewProfile(profile: UserProfile): void {\r\n    this.router.navigate(['/profile', profile.slug]);\r\n  }\r\n\r\n  readArticle(article: ArticlePreview): void {\r\n    // Navigate to article view page\r\n    this.router.navigate(['/articles', article.slug]);\r\n  }\r\n\r\n  viewHoroscope(sign: HoroscopeSign): void {\r\n    // For now, just navigate to login to view detailed horoscope\r\n    this.router.navigate(['/login'], {\r\n      queryParams: { returnUrl: `/horoscope/${sign.name.toLowerCase()}` }\r\n    });\r\n  }\r\n\r\n  searchProfiles(): void {\r\n    this.router.navigate(['/profiles/search']);\r\n  }\r\n\r\n  getTotalEndorsements(profile: UserProfile): number {\r\n    return profile.skills.reduce((sum, skill) => sum + skill.endorsements, 0);\r\n  }\r\n\r\n\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}