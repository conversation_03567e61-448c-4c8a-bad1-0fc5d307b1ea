{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, NgModule } from '@angular/core';\nconst defaultModules = {\n  toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{\n    header: 1\n  }, {\n    header: 2\n  }], [{\n    list: 'ordered'\n  }, {\n    list: 'bullet'\n  }], [{\n    script: 'sub'\n  }, {\n    script: 'super'\n  }], [{\n    indent: '-1'\n  }, {\n    indent: '+1'\n  }], [{\n    direction: 'rtl'\n  }], [{\n    size: ['small', false, 'large', 'huge']\n  }], [{\n    header: [1, 2, 3, 4, 5, 6, false]\n  }], [{\n    color: []\n  }, {\n    background: []\n  }], [{\n    font: []\n  }], [{\n    align: []\n  }], ['clean'], ['link', 'image', 'video'] // link and image, video\n  ]\n};\n\nconst QUILL_CONFIG_TOKEN = new InjectionToken('config', {\n  providedIn: 'root',\n  factory: () => ({\n    modules: defaultModules\n  })\n});\n\n/**\n * This `NgModule` provides a global Quill config on the root level, e.g., in `AppModule`.\n * But this eliminates the need to import the entire `ngx-quill` library into the main bundle.\n * The `quill-editor` itself may be rendered in any lazy-loaded module, but importing `QuillModule`\n * into the `AppModule` will bundle the `ngx-quill` into the vendor.\n */\nclass QuillConfigModule {\n  static forRoot(config) {\n    return {\n      ngModule: QuillConfigModule,\n      providers: [{\n        provide: QUILL_CONFIG_TOKEN,\n        useValue: config\n      }]\n    };\n  }\n}\nQuillConfigModule.ɵfac = function QuillConfigModule_Factory(t) {\n  return new (t || QuillConfigModule)();\n};\nQuillConfigModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: QuillConfigModule\n});\nQuillConfigModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillConfigModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ngx-quill/config\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QUILL_CONFIG_TOKEN, QuillConfigModule, defaultModules };", "map": {"version": 3, "names": ["i0", "InjectionToken", "NgModule", "defaultModules", "toolbar", "header", "list", "script", "indent", "direction", "size", "color", "background", "font", "align", "QUILL_CONFIG_TOKEN", "providedIn", "factory", "modules", "QuillConfigModule", "forRoot", "config", "ngModule", "providers", "provide", "useValue", "ɵfac", "ɵmod", "ɵinj", "type"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/ngx-quill/fesm2020/ngx-quill-config.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, NgModule } from '@angular/core';\n\nconst defaultModules = {\n    toolbar: [\n        ['bold', 'italic', 'underline', 'strike'],\n        ['blockquote', 'code-block'],\n        [{ header: 1 }, { header: 2 }],\n        [{ list: 'ordered' }, { list: 'bullet' }],\n        [{ script: 'sub' }, { script: 'super' }],\n        [{ indent: '-1' }, { indent: '+1' }],\n        [{ direction: 'rtl' }],\n        [{ size: ['small', false, 'large', 'huge'] }],\n        [{ header: [1, 2, 3, 4, 5, 6, false] }],\n        [\n            { color: [] },\n            { background: [] }\n        ],\n        [{ font: [] }],\n        [{ align: [] }],\n        ['clean'],\n        ['link', 'image', 'video'] // link and image, video\n    ]\n};\n\nconst QUILL_CONFIG_TOKEN = new InjectionToken('config', {\n    providedIn: 'root',\n    factory: () => ({ modules: defaultModules })\n});\n\n/**\n * This `NgModule` provides a global Quill config on the root level, e.g., in `AppModule`.\n * But this eliminates the need to import the entire `ngx-quill` library into the main bundle.\n * The `quill-editor` itself may be rendered in any lazy-loaded module, but importing `QuillModule`\n * into the `AppModule` will bundle the `ngx-quill` into the vendor.\n */\nclass QuillConfigModule {\n    static forRoot(config) {\n        return {\n            ngModule: QuillConfigModule,\n            providers: [{ provide: QUILL_CONFIG_TOKEN, useValue: config }],\n        };\n    }\n}\nQuillConfigModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillConfigModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nQuillConfigModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillConfigModule });\nQuillConfigModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillConfigModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillConfigModule, decorators: [{\n            type: NgModule\n        }] });\n\n/*\n * Public API Surface of ngx-quill/config\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QUILL_CONFIG_TOKEN, QuillConfigModule, defaultModules };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,QAAQ,QAAQ,eAAe;AAExD,MAAMC,cAAc,GAAG;EACnBC,OAAO,EAAE,CACL,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,EACzC,CAAC,YAAY,EAAE,YAAY,CAAC,EAC5B,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,EAAE;IAAEA,MAAM,EAAE;EAAE,CAAC,CAAC,EAC9B,CAAC;IAAEC,IAAI,EAAE;EAAU,CAAC,EAAE;IAAEA,IAAI,EAAE;EAAS,CAAC,CAAC,EACzC,CAAC;IAAEC,MAAM,EAAE;EAAM,CAAC,EAAE;IAAEA,MAAM,EAAE;EAAQ,CAAC,CAAC,EACxC,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC,EAAE;IAAEA,MAAM,EAAE;EAAK,CAAC,CAAC,EACpC,CAAC;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC,EACtB,CAAC;IAAEC,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM;EAAE,CAAC,CAAC,EAC7C,CAAC;IAAEL,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;EAAE,CAAC,CAAC,EACvC,CACI;IAAEM,KAAK,EAAE;EAAG,CAAC,EACb;IAAEC,UAAU,EAAE;EAAG,CAAC,CACrB,EACD,CAAC;IAAEC,IAAI,EAAE;EAAG,CAAC,CAAC,EACd,CAAC;IAAEC,KAAK,EAAE;EAAG,CAAC,CAAC,EACf,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;EAAA;AAEnC,CAAC;;AAED,MAAMC,kBAAkB,GAAG,IAAId,cAAc,CAAC,QAAQ,EAAE;EACpDe,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAEf;EAAe,CAAC;AAC/C,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,iBAAiB,CAAC;EACpB,OAAOC,OAAO,CAACC,MAAM,EAAE;IACnB,OAAO;MACHC,QAAQ,EAAEH,iBAAiB;MAC3BI,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAET,kBAAkB;QAAEU,QAAQ,EAAEJ;MAAO,CAAC;IACjE,CAAC;EACL;AACJ;AACAF,iBAAiB,CAACO,IAAI;EAAA,iBAAwFP,iBAAiB;AAAA,CAAkD;AACjLA,iBAAiB,CAACQ,IAAI,kBAD8E3B,EAAE;EAAA,MACSmB;AAAiB,EAAG;AACnIA,iBAAiB,CAACS,IAAI,kBAF8E5B,EAAE,qBAE6B;AACnI;EAAA,mDAHoGA,EAAE,mBAGXmB,iBAAiB,EAAc,CAAC;IAC/GU,IAAI,EAAE3B;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASa,kBAAkB,EAAEI,iBAAiB,EAAEhB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}