<div class="preview-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>Преглед на статията</h2>
    <button mat-icon-button (click)="onClose()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div mat-dialog-content class="dialog-content">
    <article class="article-preview">
      <!-- Featured Image -->
      <div class="article-image" *ngIf="data.featuredImageUrl">
        <img [src]="getImageUrl()" [alt]="data.title" class="featured-image">
      </div>

      <!-- Article Header -->
      <header class="article-header">
        <div class="article-meta">
          <span class="category" *ngIf="data.category">{{ data.category }}</span>
          <span class="read-time">
            <mat-icon>schedule</mat-icon>
            {{ getReadTime() }} мин четене
          </span>
        </div>
        
        <h1 class="article-title">{{ data.title }}</h1>
        
        <div class="article-info">
          <span class="author">от {{ data.author }}</span>
          <span class="publish-date">{{ 'Днес' }}</span>
        </div>
      </header>

      <!-- Article Excerpt -->
      <div class="article-excerpt">
        <p>{{ data.excerpt }}</p>
      </div>

      <!-- Article Content -->
      <div class="article-content" *ngIf="data.content">
        <div [innerHTML]="data.content" class="content-html"></div>
      </div>

      <!-- Tags -->
      <div class="article-tags" *ngIf="data.tags && data.tags.length > 0">
        <h4>Тагове:</h4>
        <div class="tags-container">
          <mat-chip *ngFor="let tag of data.tags" class="tag-chip">
            {{ tag }}
          </mat-chip>
        </div>
      </div>
    </article>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-button (click)="onClose()">Затвори</button>
    <button mat-raised-button color="primary" (click)="onClose()">
      <mat-icon>edit</mat-icon>
      Продължи редактирането
    </button>
  </div>
</div>
