# 🚀 Azure DevOps Pipeline Setup Guide

## 📋 **Prerequisites**

Before starting, ensure you have:
- ✅ Azure subscription with App Services created (`ora-be`, `ora-fe`)
- ✅ Azure DevOps organization and project
- ✅ Source code repository (Azure Repos, GitHub, etc.)
- ✅ YAML pipeline files in your repository root

---

## 🔧 **Step 1: Create Azure Service Connection**

### **1.1 Navigate to Service Connections**
1. Open your Azure DevOps project
2. Click **Project Settings** (bottom left corner)
3. Under **Pipelines**, click **Service connections**

### **1.2 Create New Service Connection**
1. Click **+ New service connection**
2. Select **Azure Resource Manager**
3. Click **Next**

### **1.3 Configure Authentication Method**
1. Select **Service principal (automatic)**
2. Click **Next**

### **1.4 Configure Connection Details**
1. **Scope level**: Select **Subscription**
2. **Subscription**: Choose your subscription (`6dd88dbc-9333-4643-834f-978cc8b01dc8`)
3. **Resource group**: Select `egishe`
4. **Service connection name**: Enter `Azure-Service-Connection`
5. **Description**: Enter `Azure connection for Oracul deployment`
6. ✅ Check **Grant access permission to all pipelines**
7. Click **Save**

### **1.5 Verify Connection**
- Wait for the connection to be created (may take 1-2 minutes)
- Verify it appears in the service connections list
- Status should show as **Ready**

---

## 🌍 **Step 2: Create Environments**

### **2.1 Navigate to Environments**
1. In your Azure DevOps project, go to **Pipelines**
2. Click **Environments** in the left menu

### **2.2 Create Production Environment**
1. Click **+ New environment**
2. **Name**: Enter `production`
3. **Description**: Enter `Production environment for Oracul application`
4. **Resource**: Select **None** (we'll use App Services directly)
5. Click **Create**

### **2.3 Configure Environment (Optional)**
1. Click on the `production` environment
2. Click **...** (three dots) → **Approvals and checks**
3. Add approvals if you want manual deployment approval:
   - Click **+** → **Approvals**
   - Add approvers (your email or team)
   - Click **Create**

---

## 📦 **Step 3: Create Backend Pipeline**

### **3.1 Navigate to Pipelines**
1. Go to **Pipelines** → **Pipelines**
2. Click **+ New pipeline**

### **3.2 Select Source**
1. Choose your source:
   - **Azure Repos Git** (if using Azure Repos)
   - **GitHub** (if using GitHub)
   - **Other Git** (for other providers)

### **3.3 Select Repository**
1. Select your repository containing the Oracul code
2. Click **Continue**

### **3.4 Configure Pipeline**
1. Select **Existing Azure Pipelines YAML file**
2. **Branch**: Select `main` (or your default branch)
3. **Path**: Select `/azure-pipelines-backend.yml`
4. Click **Continue**

### **3.5 Review and Run**
1. Review the YAML content
2. Click **Save and run**
3. **Commit message**: Enter `Add backend pipeline`
4. **Commit directly to main branch** (or create new branch)
5. Click **Save and run**

---

## 🌐 **Step 4: Create Frontend Pipeline**

### **4.1 Create Second Pipeline**
1. Go to **Pipelines** → **Pipelines**
2. Click **+ New pipeline**

### **4.2 Repeat Configuration**
1. Select same source and repository
2. Choose **Existing Azure Pipelines YAML file**
3. **Path**: Select `/azure-pipelines-frontend.yml`
4. Click **Continue**

### **4.3 Save and Run**
1. Review the YAML content
2. Click **Save and run**
3. **Commit message**: Enter `Add frontend pipeline`
4. Click **Save and run**

---

## 🔧 **Step 5: Configure Pipeline Settings**

### **5.1 Rename Pipelines**
1. Go to **Pipelines** → **Pipelines**
2. Click on the backend pipeline
3. Click **...** (three dots) → **Rename/move**
4. **Name**: `Oracul-Backend-Pipeline`
5. **Folder**: Leave empty or create `Oracul`
6. Click **Save**

Repeat for frontend pipeline:
- **Name**: `Oracul-Frontend-Pipeline`

### **5.2 Configure Branch Policies (Optional)**
1. Go to **Repos** → **Branches**
2. Click **...** next to `main` branch → **Branch policies**
3. Enable **Require a minimum number of reviewers**
4. Enable **Check for linked work items**
5. Add **Build validation**:
   - Click **+** → Select your pipelines
   - **Build expiration**: `12 hours`
   - Click **Save**

---

## 🧪 **Step 6: Test Pipeline Execution**

### **6.1 Trigger Backend Pipeline**
1. Make a small change to backend code (e.g., add comment)
2. Commit and push to `main` or `develop` branch
3. Go to **Pipelines** → **Pipelines**
4. Click on **Oracul-Backend-Pipeline**
5. Monitor the execution

### **6.2 Monitor Pipeline Stages**
Watch for these stages:
1. **Build Stage**:
   - ✅ Restore packages
   - ✅ Build solution
   - ✅ Run tests
   - ✅ Publish artifacts

2. **Deploy Stage**:
   - ✅ Download artifacts
   - ✅ Deploy to App Service
   - ✅ Run EF migrations

### **6.3 Trigger Frontend Pipeline**
1. Make a change to frontend code
2. Commit and push
3. Monitor **Oracul-Frontend-Pipeline** execution

---

## 🔍 **Step 7: Verify Deployment**

### **7.1 Check App Services**
1. Go to Azure Portal
2. Navigate to your App Services:
   - **Backend**: `ora-be`
   - **Frontend**: `ora-fe`
3. Check **Deployment Center** for latest deployments
4. Verify applications are running

### **7.2 Test Applications**
1. **Backend API**: Visit `https://fb-be-h5eqc6fwa9acd6ew.northeurope-01.azurewebsites.net`
2. **Frontend App**: Visit `https://ora-fe-dvdegtdvdgajcfb9.northeurope-01.azurewebsites.net`
3. Test API endpoints and frontend functionality

### **7.3 Check Database**
1. Verify EF migrations were applied
2. Check that database schema is up to date
3. Verify seeded data is present

---

## 🚨 **Troubleshooting Common Issues**

### **Issue 1: Service Connection Failed**
**Solution**:
- Verify Azure subscription permissions
- Ensure you have Contributor role on subscription
- Try recreating the service connection

### **Issue 2: Pipeline Permission Denied**
**Solution**:
- Go to pipeline → **Edit** → **...** → **Security**
- Add your user with **Administrator** permissions

### **Issue 3: App Service Deployment Failed**
**Solution**:
- Check App Service exists and is running
- Verify resource group name is correct
- Check deployment logs in Azure Portal

### **Issue 4: Database Migration Failed**
**Solution**:
- Verify connection string is correct
- Check SQL Server firewall rules
- Ensure database exists

### **Issue 5: Build Failed**
**Solution**:
- Check build logs for specific errors
- Verify .NET version compatibility
- Check NuGet package restore issues

---

## 📊 **Step 8: Monitor and Maintain**

### **8.1 Set Up Notifications**
1. Go to **Project Settings** → **Notifications**
2. Create subscription for:
   - **Build completed**
   - **Release deployment completed**
   - **Build failed**

### **8.2 Review Pipeline Analytics**
1. Go to **Pipelines** → **Analytics**
2. Monitor:
   - Success rate
   - Duration trends
   - Failure analysis

### **8.3 Regular Maintenance**
- Review and update YAML files as needed
- Update Azure service connection if credentials change
- Monitor App Service performance and scaling

---

## ✅ **Success Checklist**

- [ ] Azure Service Connection created and verified
- [ ] Production environment configured
- [ ] Backend pipeline created and running
- [ ] Frontend pipeline created and running
- [ ] Both applications deployed successfully
- [ ] Database migrations applied
- [ ] Applications accessible via URLs
- [ ] Notifications configured

**🎉 Congratulations! Your Azure DevOps pipelines are now set up and ready for continuous deployment!**

---

## 📝 **Quick Reference Card**

### **Essential Information**
- **Subscription ID**: `6dd88dbc-9333-4643-834f-978cc8b01dc8`
- **Resource Group**: `egishe`
- **Backend App Service**: `ora-be`
- **Frontend App Service**: `ora-fe`
- **Service Connection Name**: `Azure-Service-Connection`
- **Environment Name**: `production`

### **Pipeline Files**
- **Backend**: `/azure-pipelines-backend.yml`
- **Frontend**: `/azure-pipelines-frontend.yml`

### **URLs After Deployment**
- **Backend API**: `https://fb-be-h5eqc6fwa9acd6ew.northeurope-01.azurewebsites.net`
- **Frontend App**: `https://ora-fe-dvdegtdvdgajcfb9.northeurope-01.azurewebsites.net`

### **Key Navigation Paths**
- **Service Connections**: Project Settings → Pipelines → Service connections
- **Environments**: Pipelines → Environments
- **Create Pipeline**: Pipelines → Pipelines → New pipeline
- **Monitor Deployments**: Pipelines → Pipelines → [Pipeline Name]
