# 🔧 Separated Frontend and Backend Setup

## ✅ **Configuration Complete**

The Oracul application has been successfully configured with **separated frontend and backend** architecture, removing the proxy configuration for direct API communication.

## 🌐 **Architecture Overview**

### **Frontend (Angular)**
- **Port**: `4200`
- **URL**: http://localhost:4200
- **API Calls**: Direct HTTP requests to backend
- **No Proxy**: Removed proxy.conf.js configuration

### **Backend (ASP.NET Core)**
- **Port**: `5144`
- **URL**: http://localhost:5144
- **API Base**: http://localhost:5144/api
- **CORS**: Configured to allow requests from Angular frontend

## 📁 **Configuration Changes Made**

### **1. Removed Angular Proxy**
- ❌ **Deleted**: `oracul.client/src/proxy.conf.js`
- ✅ **Updated**: `oracul.client/angular.json` - removed `proxyConfig` option

### **2. Updated Angular Services**
All Angular services now use full backend URLs:

```typescript
// Before (with proxy)
private readonly API_URL = '/api/auth';

// After (direct calls)
private readonly API_URL = `${environment.apiUrl}/auth`;
```

**Updated Services:**
- ✅ `AuthService` - uses `${environment.apiUrl}/auth`
- ✅ `ProfileService` - uses `${environment.apiUrl}/profile`
- ✅ `ArticleService` - uses `${environment.apiUrl}/article`
- ✅ `FileUploadService` - uses `${environment.apiUrl}/fileupload`
- ✅ `AuthInterceptor` - uses full URLs for token refresh
- ✅ `DashboardComponent` - uses full URL for weather forecast

### **3. Environment Configuration**
```typescript
// oracul.client/src/environments/environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:5144/api',
  oauth: {
    google: {
      clientId: 'your-google-client-id.apps.googleusercontent.com'
    },
    facebook: {
      appId: 'your-facebook-app-id'
    }
  }
};
```

### **4. Backend CORS Configuration**
```csharp
// Oracul.Server/Program.cs
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAngularApp", policy =>
    {
        policy.WithOrigins(
                "http://localhost:4200",   // Angular dev server
                "https://localhost:4200"   // Angular dev server with SSL
              )
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});
```

## 🚀 **Starting the Application**

### **Option 1: Manual Start (Recommended)**
```bash
# Terminal 1: Start Backend
cd Oracul.Server
dotnet run

# Terminal 2: Start Frontend
cd oracul.client
ng serve --port 4200
```

### **Option 2: Using PowerShell Script**
```bash
# Run the development script
./start-dev.ps1
```

## 🔍 **Verification Steps**

After starting both services:

1. **Backend API**: Visit http://localhost:5144/api
2. **Frontend App**: Visit http://localhost:4200
3. **Test API Connection**: Visit http://localhost:4200/test-api
4. **Check Browser Network Tab**: Verify requests go directly to `localhost:5144`

## 🎯 **Benefits of Separated Architecture**

### ✅ **Production Ready**
- Mirrors production deployment architecture
- Frontend and backend can be deployed independently
- Better scalability and maintenance

### ✅ **Development Flexibility**
- Frontend and backend can run on different machines
- Easier debugging of network requests
- Clear separation of concerns

### ✅ **CORS Handling**
- Proper CORS configuration for cross-origin requests
- Better understanding of security implications
- Production-ready security setup

## 🔧 **API Endpoints**

All API calls now use full URLs:

| Service | Endpoint | Full URL |
|---------|----------|----------|
| Authentication | `/auth/*` | `http://localhost:5144/api/auth/*` |
| Profiles | `/profile/*` | `http://localhost:5144/api/profile/*` |
| Articles | `/article/*` | `http://localhost:5144/api/article/*` |
| File Upload | `/fileupload/*` | `http://localhost:5144/api/fileupload/*` |
| Weather | `/weatherforecast` | `http://localhost:5144/weatherforecast` |

## 🐛 **Troubleshooting**

### **CORS Errors**
- Verify backend is running on port 5144
- Check CORS policy includes `http://localhost:4200`
- Ensure `UseCors("AllowAngularApp")` is called in Program.cs

### **Connection Refused**
- Confirm backend is running: `curl http://localhost:5144/api`
- Check firewall settings
- Verify ports are not blocked

### **Authentication Issues**
- Check JWT token is included in requests
- Verify AuthInterceptor is properly configured
- Ensure token refresh uses full URL

## 🎯 **Next Steps**

The separated frontend and backend architecture is now complete and ready for:
- ✅ Development with clear separation
- ✅ Production deployment preparation
- ✅ Independent scaling and maintenance
- ✅ Enhanced security configuration

---

**Happy Coding!** 🌟
