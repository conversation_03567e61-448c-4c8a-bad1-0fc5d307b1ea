{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let FileUploadService = /*#__PURE__*/(() => {\n  class FileUploadService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = `${environment.apiUrl}/fileupload`;\n    }\n    /**\r\n     * Upload an image file\r\n     */\n    uploadImage(file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      return this.http.post(`${this.API_URL}/image`, formData).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Delete an uploaded image\r\n     */\n    deleteImage(fileName) {\n      return this.http.delete(`${this.API_URL}/image?fileName=${encodeURIComponent(fileName)}`).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Validate image file before upload\r\n     */\n    validateImageFile(file) {\n      // Check file type\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n      if (!allowedTypes.includes(file.type)) {\n        return {\n          valid: false,\n          error: 'Неподдържан формат на файла. Разрешени са: JPG, PNG, GIF, WebP'\n        };\n      }\n      // Check file size (max 5MB)\n      const maxSize = 5 * 1024 * 1024; // 5MB\n      if (file.size > maxSize) {\n        return {\n          valid: false,\n          error: 'Файлът е твърде голям. Максималният размер е 5MB'\n        };\n      }\n      return {\n        valid: true\n      };\n    }\n    /**\r\n     * Get full URL for uploaded image\r\n     */\n    getImageUrl(relativePath) {\n      if (relativePath.startsWith('http')) {\n        return relativePath; // Already a full URL\n      }\n\n      return `${environment.apiUrl.replace('/api', '')}${relativePath}`;\n    }\n    handleError(error) {\n      let errorMessage = 'Възникна неочаквана грешка при качването на файла';\n      if (error.error && error.error.message) {\n        errorMessage = error.error.message;\n      } else if (error.status === 0) {\n        errorMessage = 'Няма връзка със сървъра';\n      } else if (error.status === 401) {\n        errorMessage = 'Необходима е автентификация за качване на файлове';\n      } else if (error.status === 403) {\n        errorMessage = 'Нямате права за качване на файлове';\n      } else if (error.status === 413) {\n        errorMessage = 'Файлът е твърде голям';\n      } else if (error.status >= 500) {\n        errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\n      }\n      console.error('File upload service error:', error);\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function FileUploadService_Factory(t) {\n        return new (t || FileUploadService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FileUploadService,\n        factory: FileUploadService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FileUploadService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}