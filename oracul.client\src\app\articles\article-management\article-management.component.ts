import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { ArticleService } from '../../shared/services/article.service';
import { ArticleManagementDto } from '../../shared/models/article.models';

export interface ArticleListItem {
  id: number;
  title: string;
  status: string;
  category?: string;
  publishedAt?: Date;
  lastSavedAt?: Date;
  readCount: number;
  estimatedReadTime: number;
}

@Component({
  selector: 'app-article-management',
  templateUrl: './article-management.component.html',
  styleUrls: ['./article-management.component.css']
})
export class ArticleManagementComponent implements OnInit {
  displayedColumns: string[] = ['title', 'status', 'category', 'publishedAt', 'readCount', 'actions'];
  dataSource = new MatTableDataSource<ArticleListItem>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  isLoading = false;
  totalCount = 0;
  pageSize = 10;
  currentPage = 0;
  selectedStatus = '';

  statusOptions = [
    { value: '', label: 'Всички статии' },
    { value: 'Draft', label: 'Чернови' },
    { value: 'Published', label: 'Публикувани' },
    { value: 'Scheduled', label: 'Планирани' },
    { value: 'Archived', label: 'Архивирани' }
  ];

  constructor(
    private articleService: ArticleService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadArticles();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadArticles(): void {
    this.isLoading = true;

    this.articleService.getMyArticles(
      this.currentPage + 1,
      this.pageSize,
      this.selectedStatus
    ).subscribe({
      next: (response) => {
        this.dataSource.data = response.articles.map(article => ({
          id: article.id,
          title: article.title,
          status: this.getStatusLabel(article.status || 'Draft'),
          category: article.category,
          publishedAt: article.publishedAt ? new Date(article.publishedAt) : undefined,
          lastSavedAt: article.lastSavedAt ? new Date(article.lastSavedAt) : undefined,
          readCount: article.readCount || 0,
          estimatedReadTime: article.estimatedReadTime || 0
        }));
        this.totalCount = response.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading articles:', error);
        this.snackBar.open('Грешка при зареждането на статиите', 'Затвори', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  onStatusFilterChange(): void {
    this.currentPage = 0;
    this.loadArticles();
  }

  onPageChange(event: any): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadArticles();
  }

  createNewArticle(): void {
    this.router.navigate(['/articles/editor']);
  }

  editArticle(articleId: number): void {
    this.router.navigate(['/articles/editor', articleId]);
  }

  viewArticle(article: ArticleListItem): void {
    // TODO: Navigate to article view
    this.snackBar.open('Функцията за преглед ще бъде добавена скоро', 'Затвори', { duration: 3000 });
  }

  publishArticle(articleId: number): void {
    this.articleService.publishArticle(articleId).subscribe({
      next: () => {
        this.snackBar.open('Статията е публикувана успешно', 'Затвори', { duration: 3000 });
        this.loadArticles();
      },
      error: (error) => {
        console.error('Error publishing article:', error);
        this.snackBar.open('Грешка при публикуването на статията', 'Затвори', { duration: 3000 });
      }
    });
  }

  unpublishArticle(articleId: number): void {
    this.articleService.unpublishArticle(articleId).subscribe({
      next: () => {
        this.snackBar.open('Статията е скрита успешно', 'Затвори', { duration: 3000 });
        this.loadArticles();
      },
      error: (error) => {
        console.error('Error unpublishing article:', error);
        this.snackBar.open('Грешка при скриването на статията', 'Затвори', { duration: 3000 });
      }
    });
  }

  deleteArticle(articleId: number, articleTitle: string): void {
    const dialogRef = this.dialog.open(ConfirmDeleteDialogComponent, {
      width: '400px',
      data: { title: articleTitle }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.articleService.deleteArticle(articleId).subscribe({
          next: () => {
            this.snackBar.open('Статията е изтрита успешно', 'Затвори', { duration: 3000 });
            this.loadArticles();
          },
          error: (error) => {
            console.error('Error deleting article:', error);
            this.snackBar.open('Грешка при изтриването на статията', 'Затвори', { duration: 3000 });
          }
        });
      }
    });
  }

  private getStatusLabel(status: string): string {
    const statusMap: { [key: string]: string } = {
      'Draft': 'Чернова',
      'Published': 'Публикувана',
      'Scheduled': 'Планирана',
      'Archived': 'Архивирана'
    };
    return statusMap[status] || status;
  }

  getStatusColor(status: string): string {
    const colorMap: { [key: string]: string } = {
      'Чернова': 'accent',
      'Публикувана': 'primary',
      'Планирана': 'warn',
      'Архивирана': ''
    };
    return colorMap[status] || '';
  }

  canPublish(status: string): boolean {
    return status === 'Чернова' || status === 'Архивирана';
  }

  canUnpublish(status: string): boolean {
    return status === 'Публикувана';
  }
}

// Confirm Delete Dialog Component
import { Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-confirm-delete-dialog',
  template: `
    <h2 mat-dialog-title>Потвърждение за изтриване</h2>
    <mat-dialog-content>
      <p>Сигурни ли сте, че искате да изтриете статията "<strong>{{ data.title }}</strong>"?</p>
      <p>Това действие не може да бъде отменено.</p>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Отказ</button>
      <button mat-raised-button color="warn" (click)="onConfirm()">Изтрий</button>
    </mat-dialog-actions>
  `
})
export class ConfirmDeleteDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfirmDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { title: string }
  ) {}

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.dialogRef.close(true);
  }
}
