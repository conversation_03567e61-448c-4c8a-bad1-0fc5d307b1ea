{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nfunction ArticlePreviewDialogComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r0.getImageUrl(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.data.title);\n  }\n}\nfunction ArticlePreviewDialogComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.data.category);\n  }\n}\nfunction ArticlePreviewDialogComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.data.content, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ArticlePreviewDialogComponent_div_28_mat_chip_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r5 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r5, \" \");\n  }\n}\nfunction ArticlePreviewDialogComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"h4\");\n    i0.ɵɵtext(2, \"\\u0422\\u0430\\u0433\\u043E\\u0432\\u0435:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵtemplate(4, ArticlePreviewDialogComponent_div_28_mat_chip_4_Template, 2, 1, \"mat-chip\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.data.tags);\n  }\n}\nexport class ArticlePreviewDialogComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  onClose() {\n    this.dialogRef.close();\n  }\n  calculateReadTime(content) {\n    if (!content) return 0;\n    // Remove HTML tags for word count\n    const textContent = content.replace(/<[^>]*>/g, '');\n    const words = textContent.trim().split(/\\s+/).length;\n    // Average reading speed is 200-250 words per minute\n    const wordsPerMinute = 225;\n    const readTime = Math.ceil(words / wordsPerMinute);\n    return Math.max(1, readTime); // Minimum 1 minute\n  }\n\n  getReadTime() {\n    return this.data.estimatedReadTime || this.calculateReadTime(this.data.content);\n  }\n  getImageUrl() {\n    if (!this.data.featuredImageUrl) {\n      return 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop';\n    }\n    if (this.data.featuredImageUrl.startsWith('http')) {\n      return this.data.featuredImageUrl;\n    }\n    // Assume it's a relative path from our API\n    return `${environment.apiUrl.replace('/api', '')}${this.data.featuredImageUrl}`;\n  }\n  static {\n    this.ɵfac = function ArticlePreviewDialogComponent_Factory(t) {\n      return new (t || ArticlePreviewDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ArticlePreviewDialogComponent,\n      selectors: [[\"app-article-preview-dialog\"]],\n      decls: 36,\n      vars: 9,\n      consts: [[1, \"preview-dialog\"], [1, \"dialog-header\"], [\"mat-dialog-title\", \"\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [\"mat-dialog-content\", \"\", 1, \"dialog-content\"], [1, \"article-preview\"], [\"class\", \"article-image\", 4, \"ngIf\"], [1, \"article-header\"], [1, \"article-meta\"], [\"class\", \"category\", 4, \"ngIf\"], [1, \"read-time\"], [1, \"article-title\"], [1, \"article-info\"], [1, \"author\"], [1, \"publish-date\"], [1, \"article-excerpt\"], [\"class\", \"article-content\", 4, \"ngIf\"], [\"class\", \"article-tags\", 4, \"ngIf\"], [\"mat-dialog-actions\", \"\", 1, \"dialog-actions\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"article-image\"], [1, \"featured-image\", 3, \"src\", \"alt\"], [1, \"category\"], [1, \"article-content\"], [1, \"content-html\", 3, \"innerHTML\"], [1, \"article-tags\"], [1, \"tags-container\"], [\"class\", \"tag-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"tag-chip\"]],\n      template: function ArticlePreviewDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n          i0.ɵɵtext(3, \"\\u041F\\u0440\\u0435\\u0433\\u043B\\u0435\\u0434 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function ArticlePreviewDialogComponent_Template_button_click_4_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"close\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"article\", 5);\n          i0.ɵɵtemplate(9, ArticlePreviewDialogComponent_div_9_Template, 2, 2, \"div\", 6);\n          i0.ɵɵelementStart(10, \"header\", 7)(11, \"div\", 8);\n          i0.ɵɵtemplate(12, ArticlePreviewDialogComponent_span_12_Template, 2, 1, \"span\", 9);\n          i0.ɵɵelementStart(13, \"span\", 10)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"h1\", 11);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 12)(20, \"span\", 13);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"span\", 14);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 15)(25, \"p\");\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(27, ArticlePreviewDialogComponent_div_27_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(28, ArticlePreviewDialogComponent_div_28_Template, 5, 1, \"div\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 18)(30, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ArticlePreviewDialogComponent_Template_button_click_30_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵtext(31, \"\\u0417\\u0430\\u0442\\u0432\\u043E\\u0440\\u0438\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function ArticlePreviewDialogComponent_Template_button_click_32_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelementStart(33, \"mat-icon\");\n          i0.ɵɵtext(34, \"edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(35, \" \\u041F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438 \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435\\u0442\\u043E \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.data.featuredImageUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.data.category);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getReadTime(), \" \\u043C\\u0438\\u043D \\u0447\\u0435\\u0442\\u0435\\u043D\\u0435 \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.data.title);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\\u043E\\u0442 \", ctx.data.author, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(\"\\u0414\\u043D\\u0435\\u0441\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.data.excerpt);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.data.content);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.data.tags && ctx.data.tags.length > 0);\n        }\n      },\n      styles: [\".preview-dialog[_ngcontent-%COMP%] {\\r\\n  max-width: 800px;\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.dialog-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 16px 24px;\\r\\n  border-bottom: 1px solid #e0e0e0;\\r\\n}\\r\\n\\r\\n.dialog-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  color: #3f2f4e;\\r\\n}\\r\\n\\r\\n.close-button[_ngcontent-%COMP%] {\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n.dialog-content[_ngcontent-%COMP%] {\\r\\n  padding: 0;\\r\\n  max-height: 70vh;\\r\\n  overflow-y: auto;\\r\\n}\\r\\n\\r\\n.article-preview[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n}\\r\\n\\r\\n.article-image[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 24px;\\r\\n  border-radius: 8px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.featured-image[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  height: 300px;\\r\\n  object-fit: cover;\\r\\n  display: block;\\r\\n}\\r\\n\\r\\n.article-header[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 24px;\\r\\n}\\r\\n\\r\\n.article-meta[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  margin-bottom: 12px;\\r\\n}\\r\\n\\r\\n.category[_ngcontent-%COMP%] {\\r\\n  background-color: #e6dbec;\\r\\n  color: #3f2f4e;\\r\\n  padding: 4px 12px;\\r\\n  border-radius: 16px;\\r\\n  font-size: 12px;\\r\\n  font-weight: 500;\\r\\n  text-transform: uppercase;\\r\\n}\\r\\n\\r\\n.read-time[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 4px;\\r\\n  color: #666;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.read-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 16px;\\r\\n  width: 16px;\\r\\n  height: 16px;\\r\\n}\\r\\n\\r\\n.article-title[_ngcontent-%COMP%] {\\r\\n  font-size: 32px;\\r\\n  font-weight: 700;\\r\\n  color: #3f2f4e;\\r\\n  line-height: 1.2;\\r\\n  margin: 0 0 16px 0;\\r\\n}\\r\\n\\r\\n.article-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  color: #666;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.author[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.article-excerpt[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 24px;\\r\\n  padding: 16px;\\r\\n  background-color: #f8f9fa;\\r\\n  border-left: 4px solid #d2a6d0;\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n.article-excerpt[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  font-size: 16px;\\r\\n  line-height: 1.6;\\r\\n  color: #555;\\r\\n  font-style: italic;\\r\\n}\\r\\n\\r\\n.article-content[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 32px;\\r\\n}\\r\\n\\r\\n.content-html[_ngcontent-%COMP%] {\\r\\n  font-size: 16px;\\r\\n  line-height: 1.7;\\r\\n  color: #333;\\r\\n}\\r\\n\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\r\\n  color: #3f2f4e;\\r\\n  margin-top: 24px;\\r\\n  margin-bottom: 12px;\\r\\n}\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] { font-size: 28px; }\\r\\n.content-html[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] { font-size: 24px; }\\r\\n.content-html[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] { font-size: 20px; }\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n  padding-left: 24px;\\r\\n}\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 8px;\\r\\n}\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\r\\n  border-left: 4px solid #d2a6d0;\\r\\n  padding-left: 16px;\\r\\n  margin: 16px 0;\\r\\n  font-style: italic;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\r\\n  max-width: 100%;\\r\\n  height: auto;\\r\\n  border-radius: 8px;\\r\\n  margin: 16px 0;\\r\\n}\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\r\\n  background-color: #f5f5f5;\\r\\n  padding: 2px 6px;\\r\\n  border-radius: 4px;\\r\\n  font-family: 'Courier New', monospace;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.content-html[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\r\\n  background-color: #f5f5f5;\\r\\n  padding: 16px;\\r\\n  border-radius: 8px;\\r\\n  overflow-x: auto;\\r\\n  margin: 16px 0;\\r\\n}\\r\\n\\r\\n.article-tags[_ngcontent-%COMP%] {\\r\\n  border-top: 1px solid #e0e0e0;\\r\\n  padding-top: 24px;\\r\\n}\\r\\n\\r\\n.article-tags[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 12px 0;\\r\\n  color: #3f2f4e;\\r\\n  font-size: 16px;\\r\\n}\\r\\n\\r\\n.tags-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.tag-chip[_ngcontent-%COMP%] {\\r\\n  background-color: #e6dbec;\\r\\n  color: #3f2f4e;\\r\\n}\\r\\n\\r\\n.dialog-actions[_ngcontent-%COMP%] {\\r\\n  padding: 16px 24px;\\r\\n  border-top: 1px solid #e0e0e0;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .preview-dialog[_ngcontent-%COMP%] {\\r\\n    max-width: 100vw;\\r\\n    height: 100vh;\\r\\n  }\\r\\n  \\r\\n  .dialog-content[_ngcontent-%COMP%] {\\r\\n    max-height: calc(100vh - 120px);\\r\\n  }\\r\\n  \\r\\n  .article-preview[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n  \\r\\n  .article-title[_ngcontent-%COMP%] {\\r\\n    font-size: 24px;\\r\\n  }\\r\\n  \\r\\n  .featured-image[_ngcontent-%COMP%] {\\r\\n    height: 200px;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAASA,eAAe,QAAsB,0BAA0B;AACxE,SAASC,WAAW,QAAQ,sCAAsC;;;;;ICS5DC,+BAAyD;IACvDA,0BAAqE;IACvEA,iBAAM;;;;IADCA,eAAqB;IAArBA,4DAAqB;;;;;IAMxBA,gCAA6C;IAAAA,YAAmB;IAAAA,iBAAO;;;;IAA1BA,eAAmB;IAAnBA,0CAAmB;;;;;IAqBpEA,+BAAkD;IAChDA,0BAA2D;IAC7DA,iBAAM;;;;IADCA,eAA0B;IAA1BA,kEAA0B;;;;;IAO7BA,oCAAyD;IACvDA,YACF;IAAAA,iBAAW;;;;IADTA,eACF;IADEA,uCACF;;;;;IALJA,+BAAoE;IAC9DA,qDAAO;IAAAA,iBAAK;IAChBA,+BAA4B;IAC1BA,gGAEW;IACbA,iBAAM;;;;IAHsBA,eAAY;IAAZA,0CAAY;;;AD3BhD,OAAM,MAAOC,6BAA6B;EACxCC,YACSC,SAAsD,EAC7BC,IAAwB;IADjD,cAAS,GAATD,SAAS;IACgB,SAAI,GAAJC,IAAI;EACnC;EAEHC,OAAO;IACL,IAAI,CAACF,SAAS,CAACG,KAAK,EAAE;EACxB;EAEAC,iBAAiB,CAACC,OAAgB;IAChC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IAEtB;IACA,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IACnD,MAAMC,KAAK,GAAGF,WAAW,CAACG,IAAI,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM;IAEpD;IACA,MAAMC,cAAc,GAAG,GAAG;IAC1B,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACP,KAAK,GAAGI,cAAc,CAAC;IAElD,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC;EAChC;;EAEAI,WAAW;IACT,OAAO,IAAI,CAAChB,IAAI,CAACiB,iBAAiB,IAAI,IAAI,CAACd,iBAAiB,CAAC,IAAI,CAACH,IAAI,CAACI,OAAO,CAAC;EACjF;EAEAc,WAAW;IACT,IAAI,CAAC,IAAI,CAAClB,IAAI,CAACmB,gBAAgB,EAAE;MAC/B,OAAO,mFAAmF;;IAG5F,IAAI,IAAI,CAACnB,IAAI,CAACmB,gBAAgB,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MACjD,OAAO,IAAI,CAACpB,IAAI,CAACmB,gBAAgB;;IAGnC;IACA,OAAO,GAAGxB,WAAW,CAAC0B,MAAM,CAACf,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,IAAI,CAACN,IAAI,CAACmB,gBAAgB,EAAE;EACjF;;;uBAvCWtB,6BAA6B,8DAG9BH,eAAe;IAAA;EAAA;;;YAHdG,6BAA6B;MAAAyB;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCpB1C9B,8BAA4B;UAEHA,wHAAmB;UAAAA,iBAAK;UAC7CA,iCAAiE;UAAzCA;YAAA,OAAS+B,aAAS;UAAA,EAAC;UACzC/B,gCAAU;UAAAA,qBAAK;UAAAA,iBAAW;UAI9BA,8BAA+C;UAG3CA,8EAEM;UAGNA,kCAA+B;UAE3BA,kFAAuE;UACvEA,iCAAwB;UACZA,yBAAQ;UAAAA,iBAAW;UAC7BA,aACF;UAAAA,iBAAO;UAGTA,+BAA0B;UAAAA,aAAgB;UAAAA,iBAAK;UAE/CA,gCAA0B;UACHA,aAAoB;UAAAA,iBAAO;UAChDA,iCAA2B;UAAAA,aAAY;UAAAA,iBAAO;UAKlDA,gCAA6B;UACxBA,aAAkB;UAAAA,iBAAI;UAI3BA,iFAEM;UAGNA,iFAOM;UACRA,iBAAU;UAGZA,gCAA+C;UAC1BA;YAAA,OAAS+B,aAAS;UAAA,EAAC;UAAC/B,2DAAO;UAAAA,iBAAS;UACvDA,mCAA8D;UAApBA;YAAA,OAAS+B,aAAS;UAAA,EAAC;UAC3D/B,iCAAU;UAAAA,qBAAI;UAAAA,iBAAW;UACzBA,kJACF;UAAAA,iBAAS;;;UAjDqBA,eAA2B;UAA3BA,gDAA2B;UAO3BA,eAAmB;UAAnBA,wCAAmB;UAGzCA,eACF;UADEA,0GACF;UAGwBA,eAAgB;UAAhBA,oCAAgB;UAGnBA,eAAoB;UAApBA,2DAAoB;UACdA,eAAY;UAAZA,gDAAY;UAMtCA,eAAkB;UAAlBA,sCAAkB;UAIOA,eAAkB;UAAlBA,uCAAkB;UAKrBA,eAAuC;UAAvCA,gEAAuC", "names": ["MAT_DIALOG_DATA", "environment", "i0", "ArticlePreviewDialogComponent", "constructor", "dialogRef", "data", "onClose", "close", "calculateReadTime", "content", "textContent", "replace", "words", "trim", "split", "length", "wordsPerMinute", "readTime", "Math", "ceil", "max", "getReadTime", "estimatedReadTime", "getImageUrl", "featuredImageUrl", "startsWith", "apiUrl", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\components\\article-preview-dialog\\article-preview-dialog.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\components\\article-preview-dialog\\article-preview-dialog.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\r\nimport { environment } from '../../../../environments/environment';\r\n\r\nexport interface ArticlePreviewData {\r\n  title: string;\r\n  excerpt: string;\r\n  content?: string;\r\n  category?: string;\r\n  tags: string[];\r\n  featuredImageUrl?: string;\r\n  author: string;\r\n  estimatedReadTime: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-article-preview-dialog',\r\n  templateUrl: './article-preview-dialog.component.html',\r\n  styleUrls: ['./article-preview-dialog.component.css']\r\n})\r\nexport class ArticlePreviewDialogComponent {\r\n  constructor(\r\n    public dialogRef: MatDialogRef<ArticlePreviewDialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: ArticlePreviewData\r\n  ) {}\r\n\r\n  onClose(): void {\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  calculateReadTime(content?: string): number {\r\n    if (!content) return 0;\r\n\r\n    // Remove HTML tags for word count\r\n    const textContent = content.replace(/<[^>]*>/g, '');\r\n    const words = textContent.trim().split(/\\s+/).length;\r\n\r\n    // Average reading speed is 200-250 words per minute\r\n    const wordsPerMinute = 225;\r\n    const readTime = Math.ceil(words / wordsPerMinute);\r\n\r\n    return Math.max(1, readTime); // Minimum 1 minute\r\n  }\r\n\r\n  getReadTime(): number {\r\n    return this.data.estimatedReadTime || this.calculateReadTime(this.data.content);\r\n  }\r\n\r\n  getImageUrl(): string {\r\n    if (!this.data.featuredImageUrl) {\r\n      return 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop';\r\n    }\r\n\r\n    if (this.data.featuredImageUrl.startsWith('http')) {\r\n      return this.data.featuredImageUrl;\r\n    }\r\n\r\n    // Assume it's a relative path from our API\r\n    return `${environment.apiUrl.replace('/api', '')}${this.data.featuredImageUrl}`;\r\n  }\r\n}\r\n", "<div class=\"preview-dialog\">\r\n  <div class=\"dialog-header\">\r\n    <h2 mat-dialog-title>Преглед на статията</h2>\r\n    <button mat-icon-button (click)=\"onClose()\" class=\"close-button\">\r\n      <mat-icon>close</mat-icon>\r\n    </button>\r\n  </div>\r\n\r\n  <div mat-dialog-content class=\"dialog-content\">\r\n    <article class=\"article-preview\">\r\n      <!-- Featured Image -->\r\n      <div class=\"article-image\" *ngIf=\"data.featuredImageUrl\">\r\n        <img [src]=\"getImageUrl()\" [alt]=\"data.title\" class=\"featured-image\">\r\n      </div>\r\n\r\n      <!-- Article Header -->\r\n      <header class=\"article-header\">\r\n        <div class=\"article-meta\">\r\n          <span class=\"category\" *ngIf=\"data.category\">{{ data.category }}</span>\r\n          <span class=\"read-time\">\r\n            <mat-icon>schedule</mat-icon>\r\n            {{ getReadTime() }} мин четене\r\n          </span>\r\n        </div>\r\n        \r\n        <h1 class=\"article-title\">{{ data.title }}</h1>\r\n        \r\n        <div class=\"article-info\">\r\n          <span class=\"author\">от {{ data.author }}</span>\r\n          <span class=\"publish-date\">{{ 'Днес' }}</span>\r\n        </div>\r\n      </header>\r\n\r\n      <!-- Article Excerpt -->\r\n      <div class=\"article-excerpt\">\r\n        <p>{{ data.excerpt }}</p>\r\n      </div>\r\n\r\n      <!-- Article Content -->\r\n      <div class=\"article-content\" *ngIf=\"data.content\">\r\n        <div [innerHTML]=\"data.content\" class=\"content-html\"></div>\r\n      </div>\r\n\r\n      <!-- Tags -->\r\n      <div class=\"article-tags\" *ngIf=\"data.tags && data.tags.length > 0\">\r\n        <h4>Тагове:</h4>\r\n        <div class=\"tags-container\">\r\n          <mat-chip *ngFor=\"let tag of data.tags\" class=\"tag-chip\">\r\n            {{ tag }}\r\n          </mat-chip>\r\n        </div>\r\n      </div>\r\n    </article>\r\n  </div>\r\n\r\n  <div mat-dialog-actions class=\"dialog-actions\">\r\n    <button mat-button (click)=\"onClose()\">Затвори</button>\r\n    <button mat-raised-button color=\"primary\" (click)=\"onClose()\">\r\n      <mat-icon>edit</mat-icon>\r\n      Продължи редактирането\r\n    </button>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}