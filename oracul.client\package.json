{"name": "oracul.client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:dev": "ng serve --port 4200 --host localhost", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "start:windows": "ng serve --port 4200 --host localhost", "start:default": "ng serve --port 4200 --host localhost"}, "private": true, "dependencies": {"@angular/animations": "^15.2.0", "@angular/cdk": "^15.2.9", "@angular/common": "^15.2.0", "@angular/compiler": "^15.2.0", "@angular/core": "^15.2.0", "@angular/forms": "^15.2.0", "@angular/material": "^15.2.9", "@angular/platform-browser": "^15.2.0", "@angular/platform-browser-dynamic": "^15.2.0", "@angular/router": "^15.2.0", "@google-cloud/local-auth": "^3.0.1", "google-auth-library": "^9.15.1", "jest-editor-support": "*", "ngx-quill": "^20.0.0", "quill": "^1.3.7", "run-script-os": "*", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.7", "@angular/cli": "~15.2.7", "@angular/compiler-cli": "^15.2.0", "@types/jasmine": "~4.3.0", "@types/quill": "^2.0.14", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.9.4"}}