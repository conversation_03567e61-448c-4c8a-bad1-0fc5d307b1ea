{"ast": null, "code": "import { FormGroup, Validators, FormArray } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/profile.service\";\nimport * as i3 from \"../../../auth/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/checkbox\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/slide-toggle\";\nfunction ProfileEditComponent_div_0_mat_icon_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 77);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"save\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_spinner_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 78);\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"camera_alt\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 79);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"photo_camera\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_spinner_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 78);\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"camera_alt\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 79);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"photo_camera\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getErrorMessage(\"firstName\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.getErrorMessage(\"lastName\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.getErrorMessage(\"contactInfo.email\"), \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_130_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r32.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r32.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 24)(2, \"mat-form-field\", 81)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 83)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-select\", 84);\n    i0.ɵɵtemplate(10, ProfileEditComponent_div_0_div_130_mat_option_10_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 85)(12, \"mat-checkbox\", 52);\n    i0.ɵɵtext(13, \"Public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-checkbox\", 86);\n    i0.ɵɵtext(15, \"Primary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_130_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r34);\n      const i_r30 = restoredCtx.index;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.removePhoneNumber(i_r30));\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const i_r30 = ctx.index;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r30);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.phoneTypeOptions);\n  }\n}\nfunction ProfileEditComponent_div_0_div_169_mat_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const platform_r38 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", platform_r38.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", platform_r38.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_169_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 24)(2, \"mat-form-field\", 90)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Platform\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-select\", 91);\n    i0.ɵɵtemplate(6, ProfileEditComponent_div_0_div_169_mat_option_6_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 92)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 94)(12, \"mat-checkbox\", 52);\n    i0.ɵɵtext(13, \"Public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_169_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const i_r36 = restoredCtx.index;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.removeSocialLink(i_r36));\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"delete\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const i_r36 = ctx.index;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r36);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.platformOptions);\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_mat_error_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Skill name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_mat_option_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r47 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r47.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r47.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const level_r48 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", level_r48.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", level_r48.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Proficiency level is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_182_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96)(2, \"mat-form-field\", 97)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Skill Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 98);\n    i0.ɵɵtemplate(6, ProfileEditComponent_div_0_div_182_mat_error_6_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-form-field\", 99)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-select\", 100)(11, \"mat-option\", 101);\n    i0.ɵɵtext(12, \"Select Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ProfileEditComponent_div_0_div_182_mat_option_13_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 102)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"Proficiency Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-select\", 103);\n    i0.ɵɵtemplate(18, ProfileEditComponent_div_0_div_182_mat_option_18_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ProfileEditComponent_div_0_div_182_mat_error_19_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_182_Template_button_click_20_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const i_r42 = restoredCtx.index;\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.removeSkill(i_r42));\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const skill_r41 = ctx.$implicit;\n    const i_r42 = ctx.index;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    let tmp_4_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r42);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = skill_r41.get(\"name\")) == null ? null : tmp_1_0.hasError(\"required\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.skillCategoryOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.proficiencyLevelOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = skill_r41.get(\"proficiencyLevel\")) == null ? null : tmp_4_0.hasError(\"required\"));\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_202_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Rate must be positive \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_203_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Rate cannot exceed 10,000 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_210_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Rate must be positive \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_211_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Rate cannot exceed 10,000 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_option_216_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const currency_r51 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", currency_r51.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", currency_r51.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_mat_error_217_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Currency is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Service name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r60 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r60.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r60.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Description is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Price is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Price must be positive \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_mat_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const currency_r61 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", currency_r61.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", currency_r61.label, \" \");\n  }\n}\nfunction ProfileEditComponent_div_0_div_226_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-slide-toggle\", 107);\n    i0.ɵɵtext(5, \" Active \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 108)(7, \"mat-form-field\", 109)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Service Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 110);\n    i0.ɵɵtemplate(11, ProfileEditComponent_div_0_div_226_mat_error_11_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-form-field\", 111)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-select\", 100)(16, \"mat-option\", 101);\n    i0.ɵɵtext(17, \"Select Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProfileEditComponent_div_0_div_226_mat_option_18_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-form-field\", 112)(20, \"mat-label\");\n    i0.ɵɵtext(21, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"textarea\", 113);\n    i0.ɵɵtemplate(23, ProfileEditComponent_div_0_div_226_mat_error_23_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 114)(25, \"mat-form-field\", 115)(26, \"mat-label\");\n    i0.ɵɵtext(27, \"Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 116);\n    i0.ɵɵelementStart(29, \"span\", 62);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, ProfileEditComponent_div_0_div_226_mat_error_31_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵtemplate(32, ProfileEditComponent_div_0_div_226_mat_error_32_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 64)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"Currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"mat-select\", 65);\n    i0.ɵɵtemplate(37, ProfileEditComponent_div_0_div_226_mat_option_37_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"mat-form-field\", 117)(39, \"mat-label\");\n    i0.ɵɵtext(40, \"Duration (minutes)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(41, \"input\", 118);\n    i0.ɵɵelementStart(42, \"mat-hint\");\n    i0.ɵɵtext(43, \"Optional - leave blank if not applicable\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_div_226_Template_button_click_44_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r63);\n      const i_r53 = restoredCtx.index;\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.removeServiceOffering(i_r53));\n    });\n    i0.ɵɵelementStart(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const service_r52 = ctx.$implicit;\n    const i_r53 = ctx.index;\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    let tmp_2_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r53);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Service \", i_r53 + 1, \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = service_r52.get(\"name\")) == null ? null : tmp_2_0.hasError(\"required\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.serviceCategoryOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = service_r52.get(\"description\")) == null ? null : tmp_4_0.hasError(\"required\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(((tmp_5_0 = service_r52.get(\"currency\")) == null ? null : tmp_5_0.value) || \"BGN\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = service_r52.get(\"price\")) == null ? null : tmp_6_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = service_r52.get(\"price\")) == null ? null : tmp_7_0.hasError(\"min\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.currencyOptions);\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_244_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 79);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_mat_icon_245_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"save\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileEditComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h1\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 4)(7, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.onCancel());\n    });\n    i0.ɵɵtext(8, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.onSubmit());\n    });\n    i0.ɵɵtemplate(10, ProfileEditComponent_div_0_mat_icon_10_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(11, ProfileEditComponent_div_0_mat_icon_11_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"form\", 8)(14, \"mat-card\", 9)(15, \"mat-card-header\")(16, \"mat-card-title\");\n    i0.ɵɵtext(17, \"Profile Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-card-content\")(19, \"div\", 10)(20, \"div\", 11)(21, \"div\", 12);\n    i0.ɵɵelement(22, \"img\", 13);\n    i0.ɵɵelementStart(23, \"div\", 14);\n    i0.ɵɵtemplate(24, ProfileEditComponent_div_0_mat_spinner_24_Template, 1, 0, \"mat-spinner\", 15);\n    i0.ɵɵtemplate(25, ProfileEditComponent_div_0_mat_icon_25_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"input\", 16, 17);\n    i0.ɵɵlistener(\"change\", function ProfileEditComponent_div_0_Template_input_change_26_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.onProfilePhotoSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const _r6 = i0.ɵɵreference(27);\n      return i0.ɵɵresetView(_r6.click());\n    });\n    i0.ɵɵtemplate(29, ProfileEditComponent_div_0_mat_icon_29_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(30, ProfileEditComponent_div_0_mat_icon_30_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\", 18);\n    i0.ɵɵtext(33, \"Max 5MB \\u2022 JPEG, PNG, GIF, WebP\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 19)(35, \"div\", 20)(36, \"div\", 21)(37, \"div\", 14);\n    i0.ɵɵtemplate(38, ProfileEditComponent_div_0_mat_spinner_38_Template, 1, 0, \"mat-spinner\", 15);\n    i0.ɵɵtemplate(39, ProfileEditComponent_div_0_mat_icon_39_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"input\", 16, 22);\n    i0.ɵɵlistener(\"change\", function ProfileEditComponent_div_0_Template_input_change_40_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.onCoverPhotoSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const _r11 = i0.ɵɵreference(41);\n      return i0.ɵɵresetView(_r11.click());\n    });\n    i0.ɵɵtemplate(43, ProfileEditComponent_div_0_mat_icon_43_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(44, ProfileEditComponent_div_0_mat_icon_44_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"p\", 18);\n    i0.ɵɵtext(47, \"Max 10MB \\u2022 JPEG, PNG, GIF, WebP\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(48, \"mat-card\", 23)(49, \"mat-card-header\")(50, \"mat-card-title\");\n    i0.ɵɵtext(51, \"Basic Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"mat-card-content\")(53, \"div\", 24)(54, \"mat-form-field\", 25)(55, \"mat-label\");\n    i0.ɵɵtext(56, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(57, \"input\", 26);\n    i0.ɵɵtemplate(58, ProfileEditComponent_div_0_mat_error_58_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"mat-form-field\", 25)(60, \"mat-label\");\n    i0.ɵɵtext(61, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(62, \"input\", 27);\n    i0.ɵɵtemplate(63, ProfileEditComponent_div_0_mat_error_63_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"mat-form-field\", 28)(65, \"mat-label\");\n    i0.ɵɵtext(66, \"Professional Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(67, \"input\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"mat-form-field\", 28)(69, \"mat-label\");\n    i0.ɵɵtext(70, \"Professional Headline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(71, \"textarea\", 30);\n    i0.ɵɵelementStart(72, \"mat-hint\", 31);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"mat-form-field\", 28)(75, \"mat-label\");\n    i0.ɵɵtext(76, \"Professional Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(77, \"textarea\", 32);\n    i0.ɵɵelementStart(78, \"mat-hint\", 31);\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(80, \"mat-card\", 33)(81, \"mat-card-header\")(82, \"mat-card-title\");\n    i0.ɵɵtext(83, \"Location\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"mat-card-content\")(85, \"div\", 24)(86, \"mat-form-field\", 25)(87, \"mat-label\");\n    i0.ɵɵtext(88, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(89, \"input\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"mat-form-field\", 25)(91, \"mat-label\");\n    i0.ɵɵtext(92, \"State/Province\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(93, \"input\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(94, \"div\", 24)(95, \"mat-form-field\", 25)(96, \"mat-label\");\n    i0.ɵɵtext(97, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(98, \"input\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"mat-form-field\", 25)(100, \"mat-label\");\n    i0.ɵɵtext(101, \"Display Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(102, \"input\", 37);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(103, \"mat-card\", 38)(104, \"mat-card-header\")(105, \"mat-card-title\");\n    i0.ɵɵtext(106, \"Contact Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"mat-card-content\")(108, \"div\", 24)(109, \"mat-form-field\", 25)(110, \"mat-label\");\n    i0.ɵɵtext(111, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(112, \"input\", 39);\n    i0.ɵɵtemplate(113, ProfileEditComponent_div_0_mat_error_113_Template, 2, 1, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"div\", 40)(115, \"mat-checkbox\", 41);\n    i0.ɵɵtext(116, \" Make email public \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(117, \"div\", 24)(118, \"mat-form-field\", 25)(119, \"mat-label\");\n    i0.ɵɵtext(120, \"Website\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(121, \"input\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"mat-form-field\", 25)(123, \"mat-label\");\n    i0.ɵɵtext(124, \"Portfolio URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(125, \"input\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(126, \"div\", 44)(127, \"h4\");\n    i0.ɵɵtext(128, \"Phone Numbers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(129, \"div\", 45);\n    i0.ɵɵtemplate(130, ProfileEditComponent_div_0_div_130_Template, 19, 2, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_131_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.addPhoneNumber());\n    });\n    i0.ɵɵelementStart(132, \"mat-icon\");\n    i0.ɵɵtext(133, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(134, \" Add Phone Number \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(135, \"div\", 48)(136, \"h4\");\n    i0.ɵɵtext(137, \"Business Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(138, \"mat-form-field\", 28)(139, \"mat-label\");\n    i0.ɵɵtext(140, \"Street Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(141, \"input\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(142, \"div\", 24)(143, \"mat-form-field\", 50)(144, \"mat-label\");\n    i0.ɵɵtext(145, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(146, \"input\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(147, \"mat-form-field\", 50)(148, \"mat-label\");\n    i0.ɵɵtext(149, \"State\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(150, \"input\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(151, \"mat-form-field\", 50)(152, \"mat-label\");\n    i0.ɵɵtext(153, \"Postal Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(154, \"input\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(155, \"div\", 24)(156, \"mat-form-field\", 25)(157, \"mat-label\");\n    i0.ɵɵtext(158, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(159, \"input\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(160, \"div\", 40)(161, \"mat-checkbox\", 52);\n    i0.ɵɵtext(162, \" Make business address public \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(163, \"mat-card\", 23)(164, \"mat-card-header\")(165, \"mat-card-title\");\n    i0.ɵɵtext(166, \"Social Links\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(167, \"mat-card-content\")(168, \"div\", 53);\n    i0.ɵɵtemplate(169, ProfileEditComponent_div_0_div_169_Template, 17, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(170, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_170_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.addSocialLink());\n    });\n    i0.ɵɵelementStart(171, \"mat-icon\");\n    i0.ɵɵtext(172, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(173, \" Add Social Link \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(174, \"mat-card\", 23)(175, \"mat-card-header\")(176, \"mat-card-title\");\n    i0.ɵɵtext(177, \"Skills & Expertise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(178, \"mat-card-subtitle\");\n    i0.ɵɵtext(179, \"Add your skills and areas of expertise\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(180, \"mat-card-content\")(181, \"div\", 55);\n    i0.ɵɵtemplate(182, ProfileEditComponent_div_0_div_182_Template, 23, 5, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(183, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_183_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.addSkill());\n    });\n    i0.ɵɵelementStart(184, \"mat-icon\");\n    i0.ɵɵtext(185, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(186, \" Add Skill \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(187, \"mat-card\", 23)(188, \"mat-card-header\")(189, \"mat-card-title\");\n    i0.ɵɵtext(190, \"Consultation Rates\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(191, \"mat-card-subtitle\");\n    i0.ɵɵtext(192, \"Set your pricing for consultations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(193, \"mat-card-content\")(194, \"div\", 58)(195, \"div\", 59)(196, \"mat-form-field\", 60)(197, \"mat-label\");\n    i0.ɵɵtext(198, \"Hourly Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(199, \"input\", 61);\n    i0.ɵɵelementStart(200, \"span\", 62);\n    i0.ɵɵtext(201);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(202, ProfileEditComponent_div_0_mat_error_202_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵtemplate(203, ProfileEditComponent_div_0_mat_error_203_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(204, \"mat-form-field\", 60)(205, \"mat-label\");\n    i0.ɵɵtext(206, \"Session Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(207, \"input\", 63);\n    i0.ɵɵelementStart(208, \"span\", 62);\n    i0.ɵɵtext(209);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(210, ProfileEditComponent_div_0_mat_error_210_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵtemplate(211, ProfileEditComponent_div_0_mat_error_211_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(212, \"mat-form-field\", 64)(213, \"mat-label\");\n    i0.ɵɵtext(214, \"Currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(215, \"mat-select\", 65);\n    i0.ɵɵtemplate(216, ProfileEditComponent_div_0_mat_option_216_Template, 2, 2, \"mat-option\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(217, ProfileEditComponent_div_0_mat_error_217_Template, 2, 0, \"mat-error\", 7);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(218, \"mat-card\", 23)(219, \"mat-card-header\")(220, \"mat-card-title\");\n    i0.ɵɵtext(221, \"Service Offerings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(222, \"mat-card-subtitle\");\n    i0.ɵɵtext(223, \"Define your specific services and packages\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(224, \"mat-card-content\")(225, \"div\", 67);\n    i0.ɵɵtemplate(226, ProfileEditComponent_div_0_div_226_Template, 47, 9, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(227, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function ProfileEditComponent_div_0_Template_button_click_227_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.addServiceOffering());\n    });\n    i0.ɵɵelementStart(228, \"mat-icon\");\n    i0.ɵɵtext(229, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(230, \" Add Service \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(231, \"mat-card\", 23)(232, \"mat-card-header\")(233, \"mat-card-title\");\n    i0.ɵɵtext(234, \"Privacy Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(235, \"mat-card-content\")(236, \"div\", 70)(237, \"mat-slide-toggle\", 71)(238, \"span\", 72);\n    i0.ɵɵtext(239, \"Make profile public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(240, \"p\", 73);\n    i0.ɵɵtext(241, \" When enabled, your profile will be visible to everyone and searchable. When disabled, only you can see your profile. \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(242, \"div\", 74)(243, \"button\", 75);\n    i0.ɵɵtemplate(244, ProfileEditComponent_div_0_mat_icon_244_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtemplate(245, ProfileEditComponent_div_0_mat_icon_245_Template, 2, 0, \"mat-icon\", 7);\n    i0.ɵɵtext(246);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(247, \"button\", 76);\n    i0.ɵɵtext(248, \" Cancel \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_22_0;\n    let tmp_23_0;\n    let tmp_24_0;\n    let tmp_25_0;\n    let tmp_26_0;\n    let tmp_30_0;\n    let tmp_31_0;\n    let tmp_32_0;\n    let tmp_33_0;\n    let tmp_34_0;\n    let tmp_35_0;\n    let tmp_37_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaving);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaving || ctx_r0.profileForm.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isSaving ? \"Saving...\" : \"Save Changes\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.profileForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"src\", ctx_r0.profilePhotoPreview || (ctx_r0.profile == null ? null : ctx_r0.profile.profilePhotoUrl) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"uploading\", ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isUploadingProfilePhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isUploadingProfilePhoto ? \"Uploading...\" : \"Change Profile Photo\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"background-image\", ctx_r0.coverPhotoPreview ? \"url(\" + ctx_r0.coverPhotoPreview + \")\" : ctx_r0.profile && ctx_r0.profile.coverPhotoUrl ? \"url(\" + ctx_r0.profile.coverPhotoUrl + \")\" : \"var(--theme-gradient-primary)\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"uploading\", ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isUploadingCoverPhoto);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isUploadingCoverPhoto ? \"Uploading...\" : \"Change Cover Photo\", \" \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = ctx_r0.profileForm.get(\"firstName\")) == null ? null : tmp_22_0.invalid) && ((tmp_22_0 = ctx_r0.profileForm.get(\"firstName\")) == null ? null : tmp_22_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx_r0.profileForm.get(\"lastName\")) == null ? null : tmp_23_0.invalid) && ((tmp_23_0 = ctx_r0.profileForm.get(\"lastName\")) == null ? null : tmp_23_0.touched));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_24_0 = ctx_r0.profileForm.get(\"headline\")) == null ? null : tmp_24_0.value == null ? null : tmp_24_0.value.length) || 0, \"/220\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_25_0 = ctx_r0.profileForm.get(\"summary\")) == null ? null : tmp_25_0.value == null ? null : tmp_25_0.value.length) || 0, \"/2000\");\n    i0.ɵɵadvance(34);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_26_0 = ctx_r0.profileForm.get(\"contactInfo.email\")) == null ? null : tmp_26_0.invalid) && ((tmp_26_0 = ctx_r0.profileForm.get(\"contactInfo.email\")) == null ? null : tmp_26_0.touched));\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.phoneNumbers.controls);\n    i0.ɵɵadvance(39);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.socialLinks.controls);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.skills.controls);\n    i0.ɵɵadvance(19);\n    i0.ɵɵtextInterpolate(((tmp_30_0 = ctx_r0.profileForm.get(\"consultationRates.currency\")) == null ? null : tmp_30_0.value) || \"BGN\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_31_0 = ctx_r0.profileForm.get(\"consultationRates.hourlyRate\")) == null ? null : tmp_31_0.hasError(\"min\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_32_0 = ctx_r0.profileForm.get(\"consultationRates.hourlyRate\")) == null ? null : tmp_32_0.hasError(\"max\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(((tmp_33_0 = ctx_r0.profileForm.get(\"consultationRates.currency\")) == null ? null : tmp_33_0.value) || \"BGN\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_34_0 = ctx_r0.profileForm.get(\"consultationRates.sessionRate\")) == null ? null : tmp_34_0.hasError(\"min\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_35_0 = ctx_r0.profileForm.get(\"consultationRates.sessionRate\")) == null ? null : tmp_35_0.hasError(\"max\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.currencyOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_37_0 = ctx_r0.profileForm.get(\"consultationRates.currency\")) == null ? null : tmp_37_0.hasError(\"required\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.serviceOfferings.controls);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isSaving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isSaving ? \"Saving...\" : \"Save Changes\", \" \");\n  }\n}\nfunction ProfileEditComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵelement(1, \"mat-spinner\", 121);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading profile...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ProfileEditComponent = /*#__PURE__*/(() => {\n  class ProfileEditComponent {\n    constructor(formBuilder, profileService, authService, router, snackBar) {\n      this.formBuilder = formBuilder;\n      this.profileService = profileService;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.profile = null;\n      this.isLoading = true;\n      this.isSaving = false;\n      this.isUploadingProfilePhoto = false;\n      this.isUploadingCoverPhoto = false;\n      this.profilePhotoPreview = null;\n      this.coverPhotoPreview = null;\n      // Static option arrays to prevent infinite change detection loops\n      this.platformOptions = [{\n        value: 'linkedin',\n        label: 'LinkedIn'\n      }, {\n        value: 'twitter',\n        label: 'Twitter'\n      }, {\n        value: 'github',\n        label: 'GitHub'\n      }, {\n        value: 'behance',\n        label: 'Behance'\n      }, {\n        value: 'dribbble',\n        label: 'Dribbble'\n      }, {\n        value: 'instagram',\n        label: 'Instagram'\n      }, {\n        value: 'facebook',\n        label: 'Facebook'\n      }, {\n        value: 'youtube',\n        label: 'YouTube'\n      }, {\n        value: 'other',\n        label: 'Other'\n      }];\n      this.phoneTypeOptions = [{\n        value: 'mobile',\n        label: 'Mobile'\n      }, {\n        value: 'business',\n        label: 'Business'\n      }, {\n        value: 'home',\n        label: 'Home'\n      }];\n      this.skillCategoryOptions = [{\n        value: 'Astrology',\n        label: 'Astrology'\n      }, {\n        value: 'Crystal Healing',\n        label: 'Crystal Healing'\n      }, {\n        value: 'Palmistry',\n        label: 'Palmistry'\n      }, {\n        value: 'Spiritual Counseling',\n        label: 'Spiritual Counseling'\n      }, {\n        value: 'Numerology',\n        label: 'Numerology'\n      }, {\n        value: 'Tarot Reading',\n        label: 'Tarot Reading'\n      }, {\n        value: 'Energy Healing',\n        label: 'Energy Healing'\n      }, {\n        value: 'Meditation',\n        label: 'Meditation'\n      }, {\n        value: 'Other',\n        label: 'Other'\n      }];\n      this.proficiencyLevelOptions = [{\n        value: 'beginner',\n        label: 'Beginner'\n      }, {\n        value: 'intermediate',\n        label: 'Intermediate'\n      }, {\n        value: 'advanced',\n        label: 'Advanced'\n      }, {\n        value: 'expert',\n        label: 'Expert'\n      }];\n      this.serviceCategoryOptions = [{\n        value: 'Reading',\n        label: 'Reading'\n      }, {\n        value: 'Consultation',\n        label: 'Consultation'\n      }, {\n        value: 'Healing',\n        label: 'Healing'\n      }, {\n        value: 'Workshop',\n        label: 'Workshop'\n      }, {\n        value: 'Course',\n        label: 'Course'\n      }, {\n        value: 'Other',\n        label: 'Other'\n      }];\n      this.currencyOptions = [{\n        value: 'BGN',\n        label: 'BGN (Bulgarian Lev)'\n      }, {\n        value: 'EUR',\n        label: 'EUR (Euro)'\n      }, {\n        value: 'USD',\n        label: 'USD (US Dollar)'\n      }, {\n        value: 'GBP',\n        label: 'GBP (British Pound)'\n      }];\n      this.destroy$ = new Subject();\n      this.profileForm = this.createForm();\n    }\n    ngOnInit() {\n      this.loadProfile();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    createForm() {\n      return this.formBuilder.group({\n        // Basic Information\n        firstName: ['', [Validators.required, Validators.minLength(2)]],\n        lastName: ['', [Validators.required, Validators.minLength(2)]],\n        professionalTitle: [''],\n        headline: ['', Validators.maxLength(220)],\n        summary: ['', Validators.maxLength(2000)],\n        // Location\n        location: this.formBuilder.group({\n          city: [''],\n          state: [''],\n          country: [''],\n          displayLocation: ['']\n        }),\n        // Contact Information\n        contactInfo: this.formBuilder.group({\n          email: ['', Validators.email],\n          isEmailPublic: [false],\n          website: [''],\n          portfolioUrl: [''],\n          phoneNumbers: this.formBuilder.array([]),\n          businessAddress: this.formBuilder.group({\n            street: [''],\n            city: [''],\n            state: [''],\n            postalCode: [''],\n            country: [''],\n            isPublic: [false]\n          })\n        }),\n        // Privacy Settings\n        isPublic: [true],\n        // Social Links\n        socialLinks: this.formBuilder.array([]),\n        // Skills\n        skills: this.formBuilder.array([]),\n        // Consultation Rates\n        consultationRates: this.formBuilder.group({\n          hourlyRate: [null, [Validators.min(0), Validators.max(10000)]],\n          sessionRate: [null, [Validators.min(0), Validators.max(10000)]],\n          currency: ['BGN', Validators.required]\n        }),\n        // Service Offerings\n        serviceOfferings: this.formBuilder.array([])\n      });\n    }\n    loadProfile() {\n      this.profileService.getCurrentUserProfile().pipe(takeUntil(this.destroy$)).subscribe({\n        next: profile => {\n          this.profile = profile;\n          this.populateForm(profile);\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading profile:', error);\n          this.snackBar.open('Error loading profile', 'Close', {\n            duration: 5000\n          });\n          this.router.navigate(['/dashboard']);\n          this.isLoading = false;\n        }\n      });\n    }\n    populateForm(profile) {\n      this.profileForm.patchValue({\n        firstName: profile.firstName,\n        lastName: profile.lastName,\n        professionalTitle: profile.professionalTitle || '',\n        headline: profile.headline || '',\n        summary: profile.summary || '',\n        location: {\n          city: profile.location?.city || '',\n          state: profile.location?.state || '',\n          country: profile.location?.country || '',\n          displayLocation: profile.location?.displayLocation || ''\n        },\n        contactInfo: {\n          email: profile.contactInfo.email || '',\n          isEmailPublic: profile.contactInfo.isEmailPublic,\n          website: profile.contactInfo.website || '',\n          portfolioUrl: profile.contactInfo.portfolioUrl || '',\n          businessAddress: {\n            street: profile.contactInfo.businessAddress?.street || '',\n            city: profile.contactInfo.businessAddress?.city || '',\n            state: profile.contactInfo.businessAddress?.state || '',\n            postalCode: profile.contactInfo.businessAddress?.postalCode || '',\n            country: profile.contactInfo.businessAddress?.country || '',\n            isPublic: profile.contactInfo.businessAddress?.isPublic || false\n          }\n        },\n        isPublic: profile.isPublic,\n        consultationRates: {\n          hourlyRate: profile.consultationRates?.hourlyRate || null,\n          sessionRate: profile.consultationRates?.sessionRate || null,\n          currency: profile.consultationRates?.currency || 'BGN'\n        }\n      });\n      // Populate phone numbers\n      this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);\n      // Populate social links\n      this.setSocialLinks(profile.socialLinks || []);\n      // Populate skills\n      this.setSkills(profile.skills || []);\n      // Populate service offerings\n      this.setServiceOfferings(profile.serviceOfferings || []);\n    }\n    // Phone Numbers Management\n    get phoneNumbers() {\n      return this.profileForm.get('contactInfo.phoneNumbers');\n    }\n    setPhoneNumbers(phones) {\n      const phoneArray = this.phoneNumbers;\n      phoneArray.clear();\n      phones.forEach(phone => {\n        phoneArray.push(this.formBuilder.group({\n          id: [phone.id],\n          number: [phone.number, Validators.required],\n          type: [phone.type, Validators.required],\n          isPublic: [phone.isPublic],\n          isPrimary: [phone.isPrimary]\n        }));\n      });\n    }\n    addPhoneNumber() {\n      const phoneGroup = this.formBuilder.group({\n        id: [null],\n        number: ['', Validators.required],\n        type: ['mobile', Validators.required],\n        isPublic: [false],\n        isPrimary: [false]\n      });\n      this.phoneNumbers.push(phoneGroup);\n    }\n    removePhoneNumber(index) {\n      this.phoneNumbers.removeAt(index);\n    }\n    // Social Links Management\n    get socialLinks() {\n      return this.profileForm.get('socialLinks');\n    }\n    setSocialLinks(links) {\n      const linksArray = this.socialLinks;\n      linksArray.clear();\n      links.forEach(link => {\n        linksArray.push(this.formBuilder.group({\n          id: [link.id],\n          platform: [link.platform, Validators.required],\n          url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],\n          displayName: [link.displayName],\n          isPublic: [link.isPublic]\n        }));\n      });\n    }\n    addSocialLink() {\n      const linkGroup = this.formBuilder.group({\n        id: [null],\n        platform: ['linkedin', Validators.required],\n        url: ['', [Validators.required, Validators.pattern('https?://.+')]],\n        displayName: [''],\n        isPublic: [true]\n      });\n      this.socialLinks.push(linkGroup);\n    }\n    removeSocialLink(index) {\n      this.socialLinks.removeAt(index);\n    }\n    // Skills Management\n    get skills() {\n      return this.profileForm.get('skills');\n    }\n    setSkills(skills) {\n      const skillArray = this.skills;\n      skillArray.clear();\n      skills.forEach(skill => {\n        skillArray.push(this.formBuilder.group({\n          id: [skill.id],\n          name: [skill.name, Validators.required],\n          category: [skill.category || ''],\n          proficiencyLevel: [skill.proficiencyLevel || 'intermediate', Validators.required]\n        }));\n      });\n    }\n    addSkill() {\n      const skillGroup = this.formBuilder.group({\n        id: [null],\n        name: ['', Validators.required],\n        category: [''],\n        proficiencyLevel: ['intermediate', Validators.required]\n      });\n      this.skills.push(skillGroup);\n    }\n    removeSkill(index) {\n      this.skills.removeAt(index);\n    }\n    // Service Offerings Management\n    get serviceOfferings() {\n      return this.profileForm.get('serviceOfferings');\n    }\n    setServiceOfferings(services) {\n      const serviceArray = this.serviceOfferings;\n      serviceArray.clear();\n      services.forEach(service => {\n        serviceArray.push(this.formBuilder.group({\n          id: [service.id],\n          name: [service.name, Validators.required],\n          description: [service.description, Validators.required],\n          price: [service.price, [Validators.required, Validators.min(0)]],\n          currency: [service.currency || 'BGN', Validators.required],\n          duration: [service.duration],\n          category: [service.category || ''],\n          isActive: [service.isActive !== false] // default to true\n        }));\n      });\n    }\n\n    addServiceOffering() {\n      const serviceGroup = this.formBuilder.group({\n        id: [null],\n        name: ['', Validators.required],\n        description: ['', Validators.required],\n        price: [0, [Validators.required, Validators.min(0)]],\n        currency: ['BGN', Validators.required],\n        duration: [60],\n        category: [''],\n        isActive: [true]\n      });\n      this.serviceOfferings.push(serviceGroup);\n    }\n    removeServiceOffering(index) {\n      this.serviceOfferings.removeAt(index);\n    }\n    // Form Submission\n    onSubmit() {\n      if (this.validateForm()) {\n        this.isSaving = true;\n        const formValue = this.profileForm.value;\n        const updateRequest = {\n          firstName: formValue.firstName,\n          lastName: formValue.lastName,\n          professionalTitle: formValue.professionalTitle,\n          headline: formValue.headline,\n          location: formValue.location,\n          contactInfo: formValue.contactInfo,\n          summary: formValue.summary,\n          isPublic: formValue.isPublic,\n          skills: formValue.skills || [],\n          consultationRates: formValue.consultationRates,\n          serviceOfferings: formValue.serviceOfferings || [],\n          profilePhotoUrl: this.profile?.profilePhotoUrl,\n          coverPhotoUrl: this.profile?.coverPhotoUrl\n        };\n        this.profileService.updateProfile(updateRequest).pipe(takeUntil(this.destroy$)).subscribe({\n          next: updatedProfile => {\n            this.isSaving = false;\n            this.snackBar.open('Profile updated successfully!', 'Close', {\n              duration: 3000\n            });\n            this.router.navigate(['/profile', updatedProfile.slug]);\n          },\n          error: error => {\n            this.isSaving = false;\n            console.error('Error updating profile:', error);\n            this.snackBar.open('Error updating profile. Please try again.', 'Close', {\n              duration: 5000\n            });\n          }\n        });\n      }\n      // Validation is now handled in validateForm() method\n    }\n\n    onCancel() {\n      if (this.profile) {\n        this.router.navigate(['/profile', this.profile.slug]);\n      } else {\n        this.router.navigate(['/dashboard']);\n      }\n    }\n    // File Upload Methods\n    onProfilePhotoSelected(event) {\n      const file = event.target.files[0];\n      if (file) {\n        if (this.validateImageFile(file, 'profile')) {\n          this.createImagePreview(file, 'profile');\n          this.uploadProfilePhoto(file);\n        }\n      }\n      // Reset the input to allow selecting the same file again\n      event.target.value = '';\n    }\n    onCoverPhotoSelected(event) {\n      const file = event.target.files[0];\n      if (file) {\n        if (this.validateImageFile(file, 'cover')) {\n          this.createImagePreview(file, 'cover');\n          this.uploadCoverPhoto(file);\n        }\n      }\n      // Reset the input to allow selecting the same file again\n      event.target.value = '';\n    }\n    validateImageFile(file, type) {\n      // Check file type\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n      if (!allowedTypes.includes(file.type)) {\n        this.snackBar.open('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'Close', {\n          duration: 5000\n        });\n        return false;\n      }\n      // Check file size (5MB for profile, 10MB for cover)\n      const maxSize = type === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;\n      if (file.size > maxSize) {\n        const maxSizeMB = maxSize / (1024 * 1024);\n        this.snackBar.open(`File size must be less than ${maxSizeMB}MB`, 'Close', {\n          duration: 5000\n        });\n        return false;\n      }\n      return true;\n    }\n    createImagePreview(file, type) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        if (type === 'profile') {\n          this.profilePhotoPreview = e.target.result;\n        } else {\n          this.coverPhotoPreview = e.target.result;\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n    uploadProfilePhoto(file) {\n      this.isUploadingProfilePhoto = true;\n      // Use optimistic updates for better UX\n      this.profileService.uploadProfilePhotoWithOptimisticUpdate(file).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.isUploadingProfilePhoto = false;\n          // Profile state is already updated by the service\n          this.profilePhotoPreview = null; // Clear preview since we have the actual URL\n          this.snackBar.open('Profile photo updated successfully!', 'Close', {\n            duration: 3000\n          });\n        },\n        error: error => {\n          this.isUploadingProfilePhoto = false;\n          this.profilePhotoPreview = null; // Clear preview on error\n          console.error('Error uploading profile photo:', error);\n          // Enhanced error messaging\n          let errorMessage = error.error?.message || 'Error uploading profile photo. Please try again.';\n          if (errorMessage.includes('размер') || errorMessage.includes('size')) {\n            errorMessage = 'File size too large. Please choose a smaller image (max 5MB).';\n          } else if (errorMessage.includes('тип') || errorMessage.includes('type')) {\n            errorMessage = 'Invalid file type. Please choose a JPEG, PNG, GIF, or WebP image.';\n          }\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n    uploadCoverPhoto(file) {\n      this.isUploadingCoverPhoto = true;\n      // Use optimistic updates for better UX\n      this.profileService.uploadCoverPhotoWithOptimisticUpdate(file).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.isUploadingCoverPhoto = false;\n          // Profile state is already updated by the service\n          this.coverPhotoPreview = null; // Clear preview since we have the actual URL\n          this.snackBar.open('Cover photo updated successfully!', 'Close', {\n            duration: 3000\n          });\n        },\n        error: error => {\n          this.isUploadingCoverPhoto = false;\n          this.coverPhotoPreview = null; // Clear preview on error\n          console.error('Error uploading cover photo:', error);\n          // Enhanced error messaging\n          let errorMessage = error.error?.message || 'Error uploading cover photo. Please try again.';\n          if (errorMessage.includes('размер') || errorMessage.includes('size')) {\n            errorMessage = 'File size too large. Please choose a smaller image (max 10MB).';\n          } else if (errorMessage.includes('тип') || errorMessage.includes('type')) {\n            errorMessage = 'Invalid file type. Please choose a JPEG, PNG, GIF, or WebP image.';\n          }\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n    // Utility Methods\n    markFormGroupTouched() {\n      Object.keys(this.profileForm.controls).forEach(key => {\n        const control = this.profileForm.get(key);\n        control?.markAsTouched();\n        if (control instanceof FormArray) {\n          control.controls.forEach(arrayControl => {\n            if (arrayControl instanceof FormGroup) {\n              Object.keys(arrayControl.controls).forEach(arrayKey => {\n                arrayControl.get(arrayKey)?.markAsTouched();\n              });\n            }\n          });\n        }\n      });\n    }\n    getErrorMessage(fieldName) {\n      const control = this.profileForm.get(fieldName);\n      if (!control || !control.errors) return '';\n      const fieldDisplayName = this.getFieldDisplayName(fieldName);\n      if (control.hasError('required')) {\n        return `${fieldDisplayName} is required`;\n      }\n      if (control.hasError('email')) {\n        return 'Please enter a valid email address';\n      }\n      if (control.hasError('minlength')) {\n        const requiredLength = control.errors['minlength'].requiredLength;\n        return `${fieldDisplayName} must be at least ${requiredLength} characters`;\n      }\n      if (control.hasError('maxlength')) {\n        const maxLength = control.errors['maxlength'].requiredLength;\n        return `${fieldDisplayName} must be no more than ${maxLength} characters`;\n      }\n      if (control.hasError('pattern')) {\n        return 'Please enter a valid URL';\n      }\n      if (control.hasError('min')) {\n        const minValue = control.errors['min'].min;\n        return `${fieldDisplayName} must be at least ${minValue}`;\n      }\n      if (control.hasError('max')) {\n        const maxValue = control.errors['max'].max;\n        return `${fieldDisplayName} cannot exceed ${maxValue}`;\n      }\n      return '';\n    }\n    getFieldDisplayName(fieldName) {\n      const fieldNames = {\n        'firstName': 'First Name',\n        'lastName': 'Last Name',\n        'professionalTitle': 'Professional Title',\n        'headline': 'Headline',\n        'summary': 'Summary',\n        'contactInfo.email': 'Email',\n        'contactInfo.website': 'Website',\n        'contactInfo.portfolioUrl': 'Portfolio URL',\n        'location.city': 'City',\n        'location.state': 'State',\n        'location.country': 'Country',\n        'location.displayLocation': 'Display Location',\n        'consultationRates.hourlyRate': 'Hourly Rate',\n        'consultationRates.sessionRate': 'Session Rate',\n        'consultationRates.currency': 'Currency'\n      };\n      return fieldNames[fieldName] || fieldName;\n    }\n    // Enhanced form validation\n    validateForm() {\n      if (this.profileForm.invalid) {\n        this.markFormGroupTouched();\n        // Find first invalid field and focus on it\n        const firstInvalidField = this.findFirstInvalidField();\n        if (firstInvalidField) {\n          firstInvalidField.focus();\n        }\n        // Show specific error message\n        const errors = this.getFormErrors();\n        if (errors.length > 0) {\n          this.snackBar.open(`Please fix the following errors: ${errors.join(', ')}`, 'Close', {\n            duration: 5000\n          });\n        }\n        return false;\n      }\n      return true;\n    }\n    findFirstInvalidField() {\n      const invalidFields = document.querySelectorAll('.mat-form-field.ng-invalid input, .mat-form-field.ng-invalid textarea, .mat-form-field.ng-invalid mat-select');\n      return invalidFields.length > 0 ? invalidFields[0] : null;\n    }\n    getFormErrors() {\n      const errors = [];\n      // Check main form fields\n      Object.keys(this.profileForm.controls).forEach(key => {\n        const control = this.profileForm.get(key);\n        if (control && control.invalid && control.touched) {\n          const errorMessage = this.getErrorMessage(key);\n          if (errorMessage) {\n            errors.push(errorMessage);\n          }\n        }\n      });\n      // Check nested form groups\n      const contactInfo = this.profileForm.get('contactInfo');\n      if (contactInfo && contactInfo.invalid) {\n        Object.keys(contactInfo.controls).forEach(key => {\n          const control = contactInfo.get(key);\n          if (control && control.invalid && control.touched) {\n            const errorMessage = this.getErrorMessage(`contactInfo.${key}`);\n            if (errorMessage) {\n              errors.push(errorMessage);\n            }\n          }\n        });\n      }\n      return errors.slice(0, 3); // Limit to first 3 errors to avoid overwhelming the user\n    }\n\n    static {\n      this.ɵfac = function ProfileEditComponent_Factory(t) {\n        return new (t || ProfileEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProfileService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileEditComponent,\n        selectors: [[\"app-profile-edit\"]],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"profile-edit-container\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"profile-edit-container\"], [1, \"edit-header\"], [1, \"header-actions\"], [\"mat-stroked-button\", \"\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [4, \"ngIf\"], [1, \"profile-form\", 3, \"formGroup\"], [1, \"photo-section\"], [1, \"photo-uploads\"], [1, \"profile-photo-upload\"], [1, \"photo-preview\"], [\"alt\", \"Profile Photo\", 1, \"profile-photo\", 3, \"src\"], [1, \"photo-overlay\"], [\"diameter\", \"24\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/jpeg,image/jpg,image/png,image/gif,image/webp\", 2, \"display\", \"none\", 3, \"change\"], [\"profilePhotoInput\", \"\"], [1, \"upload-hint\"], [1, \"cover-photo-upload\"], [1, \"cover-preview\"], [1, \"cover-photo\"], [\"coverPhotoInput\", \"\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"required\", \"\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"professionalTitle\", \"placeholder\", \"e.g., Senior Software Engineer, UX Designer\"], [\"matInput\", \"\", \"formControlName\", \"headline\", \"rows\", \"2\", \"placeholder\", \"A brief, compelling description of what you do\", \"maxlength\", \"220\"], [\"align\", \"end\"], [\"matInput\", \"\", \"formControlName\", \"summary\", \"rows\", \"6\", \"placeholder\", \"Describe your expertise, experience, and what makes you unique\", \"maxlength\", \"2000\"], [\"formGroupName\", \"location\", 1, \"form-section\"], [\"matInput\", \"\", \"formControlName\", \"city\"], [\"matInput\", \"\", \"formControlName\", \"state\"], [\"matInput\", \"\", \"formControlName\", \"country\"], [\"matInput\", \"\", \"formControlName\", \"displayLocation\", \"placeholder\", \"e.g., San Francisco, CA\"], [\"formGroupName\", \"contactInfo\", 1, \"form-section\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\"], [1, \"checkbox-field\"], [\"formControlName\", \"isEmailPublic\"], [\"matInput\", \"\", \"formControlName\", \"website\", \"placeholder\", \"https://yourwebsite.com\"], [\"matInput\", \"\", \"formControlName\", \"portfolioUrl\", \"placeholder\", \"https://portfolio.com\"], [1, \"phone-numbers-section\"], [\"formArrayName\", \"phoneNumbers\"], [\"class\", \"phone-number-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"formGroupName\", \"businessAddress\", 1, \"business-address-section\"], [\"matInput\", \"\", \"formControlName\", \"street\"], [\"appearance\", \"outline\", 1, \"third-width\"], [\"matInput\", \"\", \"formControlName\", \"postalCode\"], [\"formControlName\", \"isPublic\"], [\"formArrayName\", \"socialLinks\"], [\"class\", \"social-link-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"formArrayName\", \"skills\"], [\"class\", \"skill-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"add-skill-btn\", 3, \"click\"], [\"formGroupName\", \"consultationRates\", 1, \"rates-section\"], [1, \"rate-fields\"], [\"appearance\", \"outline\", 1, \"rate-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"hourlyRate\", \"placeholder\", \"0.00\", \"min\", \"0\", \"max\", \"10000\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"sessionRate\", \"placeholder\", \"0.00\", \"min\", \"0\", \"max\", \"10000\"], [\"appearance\", \"outline\", 1, \"currency-field\"], [\"formControlName\", \"currency\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formArrayName\", \"serviceOfferings\"], [\"class\", \"service-item\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"add-service-btn\", 3, \"click\"], [1, \"privacy-controls\"], [\"formControlName\", \"isPublic\", \"color\", \"primary\"], [1, \"toggle-label\"], [1, \"toggle-description\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", \"routerLink\", \"/profile\"], [\"diameter\", \"20\"], [\"diameter\", \"24\"], [\"diameter\", \"16\"], [1, \"phone-number-item\", 3, \"formGroupName\"], [\"appearance\", \"outline\", 1, \"phone-input\"], [\"matInput\", \"\", \"formControlName\", \"number\", \"placeholder\", \"+****************\"], [\"appearance\", \"outline\", 1, \"phone-type\"], [\"formControlName\", \"type\"], [1, \"phone-controls\"], [\"formControlName\", \"isPrimary\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [3, \"value\"], [1, \"social-link-item\", 3, \"formGroupName\"], [\"appearance\", \"outline\", 1, \"platform-select\"], [\"formControlName\", \"platform\"], [\"appearance\", \"outline\", 1, \"url-input\"], [\"matInput\", \"\", \"formControlName\", \"url\", \"placeholder\", \"https://linkedin.com/in/yourname\"], [1, \"link-controls\"], [1, \"skill-item\", 3, \"formGroupName\"], [1, \"skill-fields\"], [\"appearance\", \"outline\", 1, \"skill-name\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"e.g., Tarot Reading\"], [\"appearance\", \"outline\", 1, \"skill-category\"], [\"formControlName\", \"category\"], [\"value\", \"\"], [\"appearance\", \"outline\", 1, \"skill-proficiency\"], [\"formControlName\", \"proficiencyLevel\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 1, \"remove-skill\", 3, \"click\"], [1, \"service-item\", 3, \"formGroupName\"], [1, \"service-header\"], [\"formControlName\", \"isActive\", \"color\", \"primary\"], [1, \"service-fields\"], [\"appearance\", \"outline\", 1, \"service-name\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"e.g., Personal Tarot Reading\"], [\"appearance\", \"outline\", 1, \"service-category\"], [\"appearance\", \"outline\", 1, \"service-description\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"Describe what this service includes...\"], [1, \"service-pricing\"], [\"appearance\", \"outline\", 1, \"price-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"price\", \"placeholder\", \"0.00\", \"min\", \"0\"], [\"appearance\", \"outline\", 1, \"duration-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"duration\", \"placeholder\", \"60\", \"min\", \"1\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 1, \"remove-service\", 3, \"click\"], [1, \"loading-container\"], [\"diameter\", \"50\"]],\n        template: function ProfileEditComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ProfileEditComponent_div_0_Template, 249, 46, \"div\", 0);\n            i0.ɵɵtemplate(1, ProfileEditComponent_div_1_Template, 4, 0, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i4.RouterLink, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatFormField, i10.MatLabel, i10.MatHint, i10.MatError, i10.MatSuffix, i11.MatInput, i12.MatSelect, i13.MatOption, i14.MatCheckbox, i15.MatProgressSpinner, i16.MatSlideToggle],\n        styles: [\".profile-edit-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px;background-color:var(--theme-background)}.edit-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;padding:20px 0}.edit-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin:0;color:var(--theme-primary);font-size:2rem}.header-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.profile-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.form-section[_ngcontent-%COMP%]{margin-bottom:0}.form-section[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]{padding-bottom:16px}.form-section[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{color:var(--theme-primary);font-size:1.3rem}.form-row[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:flex-start;margin-bottom:16px}.full-width[_ngcontent-%COMP%]{width:100%}.half-width[_ngcontent-%COMP%], .third-width[_ngcontent-%COMP%]{flex:1;min-width:0}.checkbox-field[_ngcontent-%COMP%]{display:flex;align-items:center;min-height:56px}.photo-section[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%]{padding:24px}.photo-uploads[_ngcontent-%COMP%]{display:flex;gap:32px;align-items:flex-start}.profile-photo-upload[_ngcontent-%COMP%], .cover-photo-upload[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px}.photo-preview[_ngcontent-%COMP%]{position:relative;cursor:pointer;border-radius:12px;overflow:hidden;transition:transform .3s ease}.photo-preview[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.profile-photo[_ngcontent-%COMP%]{width:150px;height:150px;border-radius:50%;object-fit:cover;border:4px solid var(--theme-surface);box-shadow:0 4px 16px #0000001a}.cover-preview[_ngcontent-%COMP%]{position:relative;width:300px;height:120px;border-radius:12px;overflow:hidden}.cover-photo[_ngcontent-%COMP%]{width:100%;height:100%;background-size:cover;background-position:center;background-repeat:no-repeat}.photo-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;opacity:0;transition:opacity .3s ease}.photo-overlay.uploading[_ngcontent-%COMP%]{opacity:1;background:rgba(0,0,0,.7)}.photo-preview[_ngcontent-%COMP%]:hover   .photo-overlay[_ngcontent-%COMP%]{opacity:1}.upload-hint[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-top:8px;text-align:center}.photo-overlay[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#fff;font-size:32px;width:32px;height:32px}.phone-numbers-section[_ngcontent-%COMP%]{margin-top:24px}.phone-numbers-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 16px;color:var(--theme-text-primary);font-size:1.1rem}.phone-number-item[_ngcontent-%COMP%]{border:1px solid rgba(0,0,0,.1);border-radius:8px;padding:16px;margin-bottom:12px;background-color:#00000005}.phone-input[_ngcontent-%COMP%]{flex:2}.phone-type[_ngcontent-%COMP%]{flex:1;min-width:120px}.phone-controls[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;align-items:flex-start;min-width:120px}.business-address-section[_ngcontent-%COMP%]{margin-top:24px}.business-address-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 16px;color:var(--theme-text-primary);font-size:1.1rem}.social-link-item[_ngcontent-%COMP%]{border:1px solid rgba(0,0,0,.1);border-radius:8px;padding:16px;margin-bottom:12px;background-color:#00000005}.platform-select[_ngcontent-%COMP%]{flex:1;min-width:140px}.url-input[_ngcontent-%COMP%]{flex:2}.link-controls[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;align-items:flex-start;min-width:100px}.privacy-controls[_ngcontent-%COMP%]{padding:16px}.toggle-label[_ngcontent-%COMP%]{font-weight:500;color:var(--theme-text-primary)}.toggle-description[_ngcontent-%COMP%]{margin:8px 0 0;color:var(--theme-text-secondary);font-size:.9rem;line-height:1.4}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:50vh;gap:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--theme-text-secondary)}.mat-form-field.ng-invalid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%]{color:var(--theme-error)}.mat-error[_ngcontent-%COMP%]{font-size:.8rem}@media (max-width: 768px){.profile-edit-container[_ngcontent-%COMP%]{padding:16px}.edit-header[_ngcontent-%COMP%]{flex-direction:column;gap:16px;text-align:center}.header-actions[_ngcontent-%COMP%]{width:100%;justify-content:center}.form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.half-width[_ngcontent-%COMP%], .third-width[_ngcontent-%COMP%]{width:100%}.photo-uploads[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:24px}.cover-preview[_ngcontent-%COMP%]{width:100%;max-width:300px}.phone-controls[_ngcontent-%COMP%], .link-controls[_ngcontent-%COMP%]{flex-direction:row;align-items:center;justify-content:space-between;width:100%}}@media (max-width: 480px){.edit-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.profile-photo[_ngcontent-%COMP%]{width:120px;height:120px}.cover-preview[_ngcontent-%COMP%]{height:100px}.phone-number-item[_ngcontent-%COMP%], .social-link-item[_ngcontent-%COMP%]{padding:12px}}.form-section[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .3s ease-out}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.form-section[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #0000001a;transition:box-shadow .3s ease}.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%]{color:var(--theme-primary)}button[mat-stroked-button][_ngcontent-%COMP%]{border-color:var(--theme-primary);color:var(--theme-primary)}button[mat-stroked-button][_ngcontent-%COMP%]:hover{background-color:#673ab70a}.mat-chip[_ngcontent-%COMP%]{background-color:var(--theme-primary);color:#fff}.mat-progress-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%]{stroke:var(--theme-primary)}.skill-item[_ngcontent-%COMP%]{margin-bottom:16px;padding:16px;border:1px solid #e0e0e0;border-radius:8px;background:#fafafa}.skill-fields[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:flex-start;flex-wrap:wrap}.skill-name[_ngcontent-%COMP%]{flex:2;min-width:200px}.skill-category[_ngcontent-%COMP%], .skill-proficiency[_ngcontent-%COMP%]{flex:1;min-width:150px}.remove-skill[_ngcontent-%COMP%]{margin-top:8px}.add-skill-btn[_ngcontent-%COMP%]{margin-top:16px}.rates-section[_ngcontent-%COMP%]{padding:16px 0}.rate-fields[_ngcontent-%COMP%]{display:flex;gap:16px;flex-wrap:wrap}.rate-field[_ngcontent-%COMP%]{flex:1;min-width:200px}.currency-field[_ngcontent-%COMP%]{flex:0 0 200px}.service-item[_ngcontent-%COMP%]{margin-bottom:24px;padding:20px;border:1px solid #e0e0e0;border-radius:12px;background:#fafafa;position:relative}.service-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;padding-bottom:12px;border-bottom:1px solid #e0e0e0}.service-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:var(--theme-primary);font-weight:500}.service-fields[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.service-name[_ngcontent-%COMP%], .service-description[_ngcontent-%COMP%]{width:100%}.service-pricing[_ngcontent-%COMP%]{display:flex;gap:16px;flex-wrap:wrap}.price-field[_ngcontent-%COMP%], .duration-field[_ngcontent-%COMP%]{flex:1;min-width:150px}.remove-service[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px}.add-service-btn[_ngcontent-%COMP%]{margin-top:16px}\"]\n      });\n    }\n  }\n  return ProfileEditComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}