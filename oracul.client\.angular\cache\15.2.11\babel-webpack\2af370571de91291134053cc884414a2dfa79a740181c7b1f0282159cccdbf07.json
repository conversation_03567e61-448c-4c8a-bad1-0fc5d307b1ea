{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../core/i18n/translation.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/table\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"../material-demo.component\";\nfunction DashboardComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"div\", 11);\n    i0.ɵɵelement(4, \"mat-spinner\", 12);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\")(8, \"em\");\n    i0.ɵɵtext(9, \"\\u041C\\u043E\\u043B\\u044F, \\u043E\\u0431\\u043D\\u043E\\u0432\\u0435\\u0442\\u0435 \\u0441\\u043B\\u0435\\u0434 \\u043A\\u0430\\u0442\\u043E ASP.NET backend-\\u044A\\u0442 \\u0435 \\u0441\\u0442\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430\\u043D.\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.t.common.loading, \"...\");\n  }\n}\nfunction DashboardComponent_mat_card_17_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"calendar_today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u0414\\u0430\\u0442\\u0430 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const forecast_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, forecast_r12.date, \"short\"));\n  }\n}\nfunction DashboardComponent_mat_card_17_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"thermostat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u0422\\u0435\\u043C\\u043F. (\\u00B0C) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", forecast_r13.temperatureC, \"\\u00B0\");\n  }\n}\nfunction DashboardComponent_mat_card_17_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"thermostat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u0422\\u0435\\u043C\\u043F. (\\u00B0F) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", forecast_r14.temperatureF, \"\\u00B0\");\n  }\n}\nfunction DashboardComponent_mat_card_17_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u0420\\u0435\\u0437\\u044E\\u043C\\u0435 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(forecast_r15.summary);\n  }\n}\nfunction DashboardComponent_mat_card_17_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 28);\n  }\n}\nfunction DashboardComponent_mat_card_17_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 29);\n  }\n}\nfunction DashboardComponent_mat_card_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"table_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u0414\\u0430\\u043D\\u043D\\u0438 \\u0437\\u0430 \\u043F\\u0440\\u043E\\u0433\\u043D\\u043E\\u0437\\u0430\\u0442\\u0430 \\u0437\\u0430 \\u0432\\u0440\\u0435\\u043C\\u0435\\u0442\\u043E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 14)(8, \"table\", 15);\n    i0.ɵɵelementContainerStart(9, 16);\n    i0.ɵɵtemplate(10, DashboardComponent_mat_card_17_th_10_Template, 4, 0, \"th\", 17);\n    i0.ɵɵtemplate(11, DashboardComponent_mat_card_17_td_11_Template, 3, 4, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(12, 19);\n    i0.ɵɵtemplate(13, DashboardComponent_mat_card_17_th_13_Template, 4, 0, \"th\", 17);\n    i0.ɵɵtemplate(14, DashboardComponent_mat_card_17_td_14_Template, 3, 1, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(15, 20);\n    i0.ɵɵtemplate(16, DashboardComponent_mat_card_17_th_16_Template, 4, 0, \"th\", 17);\n    i0.ɵɵtemplate(17, DashboardComponent_mat_card_17_td_17_Template, 3, 1, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18, 21);\n    i0.ɵɵtemplate(19, DashboardComponent_mat_card_17_th_19_Template, 4, 0, \"th\", 17);\n    i0.ɵɵtemplate(20, DashboardComponent_mat_card_17_td_20_Template, 3, 1, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(21, DashboardComponent_mat_card_17_tr_21_Template, 1, 0, \"tr\", 22);\n    i0.ɵɵtemplate(22, DashboardComponent_mat_card_17_tr_22_Template, 1, 0, \"tr\", 23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.forecasts);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.displayedColumns);\n  }\n}\nexport class DashboardComponent {\n  constructor(http, t) {\n    this.http = http;\n    this.t = t;\n    this.forecasts = [];\n    this.displayedColumns = ['date', 'temperatureC', 'temperatureF', 'summary'];\n  }\n  ngOnInit() {\n    this.getForecasts();\n  }\n  getForecasts() {\n    this.http.get(`${environment.apiUrl.replace('/api', '')}/weatherforecast`).subscribe(result => {\n      this.forecasts = result;\n    }, error => {\n      console.error(error);\n    });\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.TranslationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 48,\n      vars: 12,\n      consts: [[1, \"dashboard-container\"], [1, \"header-card\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"data-card\", 4, \"ngIf\"], [1, \"data-card\", \"profile-demo-card\"], [1, \"profile-features\"], [1, \"feature-tag\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/profile-demo\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/profile/luna-starweaver\"], [1, \"loading-container\"], [1, \"loading-content\"], [\"diameter\", \"50\"], [1, \"data-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"weather-table\", 3, \"dataSource\"], [\"matColumnDef\", \"date\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"temperatureC\"], [\"matColumnDef\", \"temperatureF\"], [\"matColumnDef\", \"summary\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"temperature\"], [1, \"summary\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"wb_sunny\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"p\");\n          i0.ɵɵtext(11, \"\\u0422\\u043E\\u0437\\u0438 \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442 \\u0434\\u0435\\u043C\\u043E\\u043D\\u0441\\u0442\\u0440\\u0438\\u0440\\u0430 \\u0438\\u0437\\u0432\\u043B\\u0438\\u0447\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0434\\u0430\\u043D\\u043D\\u0438 \\u043E\\u0442 \\u0441\\u044A\\u0440\\u0432\\u044A\\u0440\\u0430 \\u0441 \\u043F\\u043E\\u043C\\u043E\\u0449\\u0442\\u0430 \\u043D\\u0430 Angular Material \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u0438.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_12_listener() {\n            return ctx.getForecasts();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" \\u041E\\u0431\\u043D\\u043E\\u0432\\u0438 \\u0434\\u0430\\u043D\\u043D\\u0438\\u0442\\u0435 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(16, DashboardComponent_div_16_Template, 10, 1, \"div\", 3);\n          i0.ɵɵtemplate(17, DashboardComponent_mat_card_17_Template, 23, 3, \"mat-card\", 4);\n          i0.ɵɵelementStart(18, \"mat-card\", 5)(19, \"mat-card-header\")(20, \"mat-card-title\")(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-card-subtitle\");\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"mat-card-content\")(27, \"p\");\n          i0.ɵɵtext(28, \"\\u0418\\u0437\\u0441\\u043B\\u0435\\u0434\\u0432\\u0430\\u0439\\u0442\\u0435 \\u0446\\u044F\\u043B\\u043E\\u0441\\u0442\\u043D\\u0430\\u0442\\u0430 \\u043F\\u0440\\u043E\\u0444\\u0435\\u0441\\u0438\\u043E\\u043D\\u0430\\u043B\\u043D\\u0430 \\u043F\\u0440\\u043E\\u0444\\u0438\\u043B\\u043D\\u0430 \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430 \\u0441 \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D \\u0438 \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u0438, \\u0432\\u0434\\u044A\\u0445\\u043D\\u043E\\u0432\\u0435\\u043D\\u0438 \\u043E\\u0442 LinkedIn.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 6)(30, \"span\", 7);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"span\", 7);\n          i0.ɵɵtext(33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 7);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"span\", 7);\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"mat-card-actions\")(39, \"button\", 8)(40, \"mat-icon\");\n          i0.ɵɵtext(41, \"launch\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 9)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(47, \"app-material-demo\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.title, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.weatherForecast, \" \");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", !ctx.forecasts);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.forecasts);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.profileSystem, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.profileSystemDescription, \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.t.dashboard.readingPortfolio);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.dashboard.astrologicalSkills);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.dashboard.spiritualJourney);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.t.dashboard.cosmicAnalytics);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.tryProfileDemo, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.viewSampleProfile, \" \");\n        }\n      },\n      dependencies: [i3.NgIf, i4.RouterLink, i5.MatButton, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow, i8.MatIcon, i9.MatProgressSpinner, i10.MaterialDemoComponent, i3.DatePipe],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\r\\n  padding: 20px;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 20px;\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.header-card[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  margin-top: 8px;\\r\\n}\\r\\n\\r\\n.loading-container[_ngcontent-%COMP%] {\\r\\n  margin: 20px 0;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n  padding: 40px 20px;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%]   .mat-spinner[_ngcontent-%COMP%] {\\r\\n  margin: 0 auto 20px;\\r\\n}\\r\\n\\r\\n.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 10px 0;\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%] {\\r\\n  margin: 20px 0;\\r\\n}\\r\\n\\r\\n.data-card[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.table-container[_ngcontent-%COMP%] {\\r\\n  overflow-x: auto;\\r\\n  margin-top: 16px;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  min-width: 600px;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\r\\n  background-color: #f5f5f5;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n  font-size: 18px;\\r\\n  vertical-align: middle;\\r\\n}\\r\\n\\r\\n.temperature[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: #ff9800;\\r\\n}\\r\\n\\r\\n.summary[_ngcontent-%COMP%] {\\r\\n  font-style: italic;\\r\\n  color: #666;\\r\\n}\\r\\n\\r\\n\\r\\n.profile-demo-card[_ngcontent-%COMP%] {\\r\\n  background: linear-gradient(135deg, rgba(103, 58, 183, 0.05) 0%, rgba(255, 193, 7, 0.05) 100%);\\r\\n  border-left: 4px solid var(--theme-primary);\\r\\n}\\r\\n\\r\\n.profile-features[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 8px;\\r\\n  margin: 16px 0;\\r\\n}\\r\\n\\r\\n.feature-tag[_ngcontent-%COMP%] {\\r\\n  background-color: var(--theme-primary);\\r\\n  color: white;\\r\\n  padding: 4px 12px;\\r\\n  border-radius: 16px;\\r\\n  font-size: 0.8rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.profile-demo-card[_ngcontent-%COMP%]   .mat-card-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 12px;\\r\\n  flex-wrap: wrap;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .dashboard-container[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n\\r\\n  .weather-table[_ngcontent-%COMP%] {\\r\\n    min-width: 500px;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,WAAW,QAAQ,gCAAgC;;;;;;;;;;;;;;ICoB1DC,+BAAkD;IAI1CA,kCAAyC;IACzCA,yBAAG;IAAAA,YAAyB;IAAAA,iBAAI;IAChCA,yBAAG;IAAIA,wOAAwD;IAAAA,iBAAK;;;;IADjEA,eAAyB;IAAzBA,yDAAyB;;;;;IAoB1BA,8BAAsC;IAC1BA,8BAAc;IAAAA,iBAAW;IACnCA,0CACF;IAAAA,iBAAK;;;;;IACLA,8BAAwC;IAAAA,YAAkC;;IAAAA,iBAAK;;;;IAAvCA,eAAkC;IAAlCA,sEAAkC;;;;;IAK1EA,8BAAsC;IAC1BA,0BAAU;IAAAA,iBAAW;IAC/BA,qDACF;IAAAA,iBAAK;;;;;IACLA,8BAAwC;IACZA,YAA4B;IAAAA,iBAAO;;;;IAAnCA,eAA4B;IAA5BA,8DAA4B;;;;;IAMxDA,8BAAsC;IAC1BA,0BAAU;IAAAA,iBAAW;IAC/BA,qDACF;IAAAA,iBAAK;;;;;IACLA,8BAAwC;IACZA,YAA4B;IAAAA,iBAAO;;;;IAAnCA,eAA4B;IAA5BA,8DAA4B;;;;;IAMxDA,8BAAsC;IAC1BA,2BAAW;IAAAA,iBAAW;IAChCA,sDACF;IAAAA,iBAAK;;;;;IACLA,8BAAwC;IAChBA,YAAsB;IAAAA,iBAAO;;;;IAA7BA,eAAsB;IAAtBA,0CAAsB;;;;;IAIhDA,yBAA4D;;;;;IAC5DA,yBAAkE;;;;;IArD1EA,oCAA8C;IAG9BA,2BAAW;IAAAA,iBAAW;IAChCA,kLACF;IAAAA,iBAAiB;IAEnBA,wCAAkB;IAIZA,iCAAkC;IAChCA,gFAGK;IACLA,gFAA+E;IACjFA,0BAAe;IAGfA,kCAA0C;IACxCA,gFAGK;IACLA,gFAEK;IACPA,0BAAe;IAGfA,kCAA0C;IACxCA,gFAGK;IACLA,gFAEK;IACPA,0BAAe;IAGfA,kCAAqC;IACnCA,gFAGK;IACLA,gFAEK;IACPA,0BAAe;IAEfA,gFAA4D;IAC5DA,gFAAkE;IACpEA,iBAAQ;;;;IA7CSA,eAAwB;IAAxBA,6CAAwB;IA2CnBA,gBAAiC;IAAjCA,yDAAiC;IACpBA,eAA0B;IAA1BA,0DAA0B;;;ADxErE,OAAM,MAAOC,kBAAkB;EAI7BC,YAAoBC,IAAgB,EAASC,CAAqB;IAA9C,SAAI,GAAJD,IAAI;IAAqB,MAAC,GAADC,CAAC;IAHvC,cAAS,GAAsB,EAAE;IACjC,qBAAgB,GAAa,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,CAAC;EAElB;EAErEC,QAAQ;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAY;IACV,IAAI,CAACH,IAAI,CAACI,GAAG,CAAoB,GAAGR,WAAW,CAACS,MAAM,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAACC,SAAS,CACpGC,MAAM,IAAI;MACT,IAAI,CAACC,SAAS,GAAGD,MAAM;IACzB,CAAC,EACAE,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,CACF;EACH;;;uBAnBWZ,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAc;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UChB/BnB,8BAAiC;UAKfA,wBAAQ;UAAAA,iBAAW;UAC7BA,YACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,YACF;UAAAA,iBAAoB;UAEtBA,wCAAkB;UACbA,4cAAkG;UAAAA,iBAAI;UACzGA,kCAAmE;UAAzBA;YAAA,OAASoB,kBAAc;UAAA,EAAC;UAChEpB,iCAAU;UAAAA,wBAAO;UAAAA,iBAAW;UAC5BA,kGACF;UAAAA,iBAAS;UAKbA,sEAUM;UAGNA,gFAyDW;UAGXA,oCAA8C;UAG9BA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAiB;UACjBA,0CAAmB;UACjBA,aACF;UAAAA,iBAAoB;UAEtBA,yCAAkB;UACbA,8eAAiG;UAAAA,iBAAI;UACxGA,+BAA8B;UACFA,aAAkC;UAAAA,iBAAO;UACnEA,gCAA0B;UAAAA,aAAoC;UAAAA,iBAAO;UACrEA,gCAA0B;UAAAA,aAAkC;UAAAA,iBAAO;UACnEA,gCAA0B;UAAAA,aAAiC;UAAAA,iBAAO;UAGtEA,yCAAkB;UAEJA,uBAAM;UAAAA,iBAAW;UAC3BA,aACF;UAAAA,iBAAS;UACTA,kCAAiE;UACrDA,2BAAU;UAAAA,iBAAW;UAC/BA,aACF;UAAAA,iBAAS;UAKbA,qCAAuC;UACzCA,iBAAM;;;UA1HEA,eACF;UADEA,sDACF;UAEEA,eACF;UADEA,gEACF;UAYEA,eAAgB;UAAhBA,qCAAgB;UAaXA,eAAe;UAAfA,oCAAe;UAgEpBA,eACF;UADEA,8DACF;UAEEA,eACF;UADEA,yEACF;UAK4BA,eAAkC;UAAlCA,sDAAkC;UAClCA,eAAoC;UAApCA,wDAAoC;UACpCA,eAAkC;UAAlCA,sDAAkC;UAClCA,eAAiC;UAAjCA,qDAAiC;UAM3DA,eACF;UADEA,+DACF;UAGEA,eACF;UADEA,kEACF", "names": ["environment", "i0", "DashboardComponent", "constructor", "http", "t", "ngOnInit", "getForecasts", "get", "apiUrl", "replace", "subscribe", "result", "forecasts", "error", "console", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { TranslationService } from '../core/i18n/translation.service';\r\nimport { environment } from '../../environments/environment';\r\n\r\ninterface WeatherForecast {\r\n  date: string;\r\n  temperatureC: number;\r\n  temperatureF: number;\r\n  summary: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrls: ['./dashboard.component.css']\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  public forecasts: WeatherForecast[] = [];\r\n  public displayedColumns: string[] = ['date', 'temperatureC', 'temperatureF', 'summary'];\r\n\r\n  constructor(private http: HttpClient, public t: TranslationService) {}\r\n\r\n  ngOnInit() {\r\n    this.getForecasts();\r\n  }\r\n\r\n  getForecasts() {\r\n    this.http.get<WeatherForecast[]>(`${environment.apiUrl.replace('/api', '')}/weatherforecast`).subscribe(\r\n      (result) => {\r\n        this.forecasts = result;\r\n      },\r\n      (error) => {\r\n        console.error(error);\r\n      }\r\n    );\r\n  }\r\n}\r\n", "<!-- Dashboard Content -->\r\n<div class=\"dashboard-container\">\r\n  <!-- Header Card -->\r\n  <mat-card class=\"header-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>wb_sunny</mat-icon>\r\n        {{ t.dashboard.title }}\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        {{ t.dashboard.weatherForecast }}\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <p>Този компонент демонстрира извличане на данни от сървъра с помощта на Angular Material компоненти.</p>\r\n      <button mat-raised-button color=\"primary\" (click)=\"getForecasts()\">\r\n        <mat-icon>refresh</mat-icon>\r\n        Обнови данните\r\n      </button>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <!-- Loading Spinner -->\r\n  <div *ngIf=\"!forecasts\" class=\"loading-container\">\r\n    <mat-card>\r\n      <mat-card-content>\r\n        <div class=\"loading-content\">\r\n          <mat-spinner diameter=\"50\"></mat-spinner>\r\n          <p>{{ t.common.loading }}...</p>\r\n          <p><em>Моля, обновете след като ASP.NET backend-ът е стартиран.</em></p>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n\r\n  <!-- Weather Data Table -->\r\n  <mat-card *ngIf=\"forecasts\" class=\"data-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>table_chart</mat-icon>\r\n        Данни за прогнозата за времето\r\n      </mat-card-title>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <div class=\"table-container\">\r\n        <table mat-table [dataSource]=\"forecasts\" class=\"weather-table\">\r\n          <!-- Date Column -->\r\n          <ng-container matColumnDef=\"date\">\r\n            <th mat-header-cell *matHeaderCellDef>\r\n              <mat-icon>calendar_today</mat-icon>\r\n              Дата\r\n            </th>\r\n            <td mat-cell *matCellDef=\"let forecast\">{{ forecast.date | date:'short' }}</td>\r\n          </ng-container>\r\n\r\n          <!-- Temperature C Column -->\r\n          <ng-container matColumnDef=\"temperatureC\">\r\n            <th mat-header-cell *matHeaderCellDef>\r\n              <mat-icon>thermostat</mat-icon>\r\n              Темп. (°C)\r\n            </th>\r\n            <td mat-cell *matCellDef=\"let forecast\">\r\n              <span class=\"temperature\">{{ forecast.temperatureC }}°</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Temperature F Column -->\r\n          <ng-container matColumnDef=\"temperatureF\">\r\n            <th mat-header-cell *matHeaderCellDef>\r\n              <mat-icon>thermostat</mat-icon>\r\n              Темп. (°F)\r\n            </th>\r\n            <td mat-cell *matCellDef=\"let forecast\">\r\n              <span class=\"temperature\">{{ forecast.temperatureF }}°</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Summary Column -->\r\n          <ng-container matColumnDef=\"summary\">\r\n            <th mat-header-cell *matHeaderCellDef>\r\n              <mat-icon>description</mat-icon>\r\n              Резюме\r\n            </th>\r\n            <td mat-cell *matCellDef=\"let forecast\">\r\n              <span class=\"summary\">{{ forecast.summary }}</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\r\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\r\n        </table>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <!-- Profile System Demo -->\r\n  <mat-card class=\"data-card profile-demo-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>person</mat-icon>\r\n        {{ t.dashboard.profileSystem }}\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        {{ t.dashboard.profileSystemDescription }}\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <p>Изследвайте цялостната професионална профилна система с дизайн и функции, вдъхновени от LinkedIn.</p>\r\n      <div class=\"profile-features\">\r\n        <span class=\"feature-tag\">{{ t.dashboard.readingPortfolio }}</span>\r\n        <span class=\"feature-tag\">{{ t.dashboard.astrologicalSkills }}</span>\r\n        <span class=\"feature-tag\">{{ t.dashboard.spiritualJourney }}</span>\r\n        <span class=\"feature-tag\">{{ t.dashboard.cosmicAnalytics }}</span>\r\n      </div>\r\n    </mat-card-content>\r\n    <mat-card-actions>\r\n      <button mat-raised-button color=\"primary\" routerLink=\"/profile-demo\">\r\n        <mat-icon>launch</mat-icon>\r\n        {{ t.dashboard.tryProfileDemo }}\r\n      </button>\r\n      <button mat-stroked-button routerLink=\"/profile/luna-starweaver\">\r\n        <mat-icon>visibility</mat-icon>\r\n        {{ t.dashboard.viewSampleProfile }}\r\n      </button>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n\r\n  <!-- Material Design Demo -->\r\n  <app-material-demo></app-material-demo>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}