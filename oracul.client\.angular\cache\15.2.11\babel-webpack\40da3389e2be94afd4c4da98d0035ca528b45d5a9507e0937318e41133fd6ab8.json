{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\nimport { map, tap, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ProfileService = /*#__PURE__*/(() => {\n  class ProfileService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = `${environment.apiUrl}/profile`;\n      this.currentProfileSubject = new BehaviorSubject(null);\n      this.currentProfile$ = this.currentProfileSubject.asObservable();\n    }\n    /**\r\n     * Get HTTP headers with authentication token\r\n     */\n    getHttpHeaders() {\n      const token = localStorage.getItem('authToken');\n      let headers = new HttpHeaders({\n        'Content-Type': 'application/json'\n      });\n      if (token) {\n        headers = headers.set('Authorization', `Bearer ${token}`);\n      }\n      return headers;\n    }\n    // Profile CRUD Operations\n    getProfile(identifier) {\n      // Check if identifier is a slug or ID\n      const url = isNaN(Number(identifier)) ? `${this.API_URL}/slug/${identifier}` : `${this.API_URL}/${identifier}`;\n      return this.http.get(url).pipe(map(response => response.data), tap(profile => {\n        if (profile) {\n          this.currentProfileSubject.next(profile);\n          // Record profile view\n          this.recordProfileView({\n            profileId: profile.id,\n            referrer: document.referrer,\n            userAgent: navigator.userAgent\n          }).subscribe();\n        }\n      }), catchError(this.handleError));\n    }\n    getCurrentUserProfile() {\n      return this.http.get(`${this.API_URL}/me`, {\n        headers: this.getHttpHeaders()\n      }).pipe(map(response => response.data), tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n    }\n    updateProfile(updates) {\n      const updateRequest = {\n        firstName: updates.firstName,\n        lastName: updates.lastName,\n        professionalTitle: updates.professionalTitle,\n        headline: updates.headline,\n        summary: updates.summary,\n        location: updates.location ? {\n          city: updates.location.city,\n          state: updates.location.state,\n          country: updates.location.country,\n          displayLocation: updates.location.displayLocation\n        } : undefined,\n        contactInfo: updates.contactInfo ? {\n          email: updates.contactInfo.email,\n          isEmailPublic: updates.contactInfo.isEmailPublic,\n          website: updates.contactInfo.website,\n          portfolioUrl: updates.contactInfo.portfolioUrl,\n          businessAddress: updates.contactInfo.businessAddress ? {\n            street: updates.contactInfo.businessAddress.street,\n            city: updates.contactInfo.businessAddress.city,\n            state: updates.contactInfo.businessAddress.state,\n            postalCode: updates.contactInfo.businessAddress.postalCode,\n            country: updates.contactInfo.businessAddress.country,\n            isPublic: updates.contactInfo.businessAddress.isPublic\n          } : undefined\n        } : undefined\n      };\n      return this.http.put(`${this.API_URL}`, updateRequest, {\n        headers: this.getHttpHeaders()\n      }).pipe(map(response => response.data), tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n    }\n    createProfile(profileData) {\n      const createRequest = {\n        username: profileData.username,\n        firstName: profileData.firstName,\n        lastName: profileData.lastName,\n        professionalTitle: profileData.professionalTitle,\n        headline: profileData.headline,\n        isPublic: profileData.isPublic ?? true\n      };\n      return this.http.post(`${this.API_URL}`, createRequest, {\n        headers: this.getHttpHeaders()\n      }).pipe(map(response => response.data), tap(profile => this.currentProfileSubject.next(profile)), catchError(this.handleError));\n    }\n    deleteProfile() {\n      return this.http.delete(`${this.API_URL}`, {\n        headers: this.getHttpHeaders()\n      }).pipe(map(() => void 0), tap(() => this.currentProfileSubject.next(null)), catchError(this.handleError));\n    }\n    // Profile Search\n    searchProfiles(filters, page = 1, limit = 20) {\n      const searchRequest = {\n        location: filters.location,\n        skills: filters.skills || [],\n        sortBy: filters.sortBy || 'relevance',\n        page: page,\n        pageSize: limit\n      };\n      return this.http.post(`${this.API_URL}/search`, searchRequest, {\n        headers: this.getHttpHeaders()\n      }).pipe(map(response => response.data), catchError(this.handleError));\n    }\n    // Search profiles by term\n    searchProfilesByTerm(searchTerm, page = 1, pageSize = 20) {\n      const searchRequest = {\n        searchTerm: searchTerm,\n        skills: [],\n        sortBy: 'relevance',\n        page: page,\n        pageSize: pageSize\n      };\n      return this.http.post(`${this.API_URL}/search`, searchRequest, {\n        headers: this.getHttpHeaders()\n      }).pipe(map(response => response.data), catchError(this.handleError));\n    }\n    // Get public profiles\n    getPublicProfiles(page = 1, pageSize = 20) {\n      return this.http.get(`${this.API_URL}/public?page=${page}&pageSize=${pageSize}`).pipe(map(response => response.data), catchError(this.handleError));\n    }\n    // Skills Management (TODO: Implement in backend)\n    addSkill(skill) {\n      // For now, return a mock skill until backend implements this\n      const newSkill = {\n        ...skill,\n        id: Date.now(),\n        endorsements: 0,\n        isEndorsedByCurrentUser: false\n      };\n      return new Observable(observer => {\n        observer.next(newSkill);\n        observer.complete();\n      });\n    }\n    updateSkill(skillId, updates) {\n      // TODO: Implement in backend\n      return new Observable(observer => {\n        observer.next({\n          ...updates,\n          id: skillId\n        });\n        observer.complete();\n      });\n    }\n    deleteSkill(skillId) {\n      // TODO: Implement in backend\n      return new Observable(observer => {\n        observer.next();\n        observer.complete();\n      });\n    }\n    endorseSkill(request) {\n      // TODO: Implement in backend\n      return new Observable(observer => {\n        observer.next();\n        observer.complete();\n      });\n    }\n    // Experience Management\n    addExperience(experience) {\n      return this.http.post(`${this.API_URL}/me/experiences`, experience).pipe(catchError(this.handleError));\n    }\n    updateExperience(experienceId, updates) {\n      return this.http.put(`${this.API_URL}/me/experiences/${experienceId}`, updates).pipe(catchError(this.handleError));\n    }\n    deleteExperience(experienceId) {\n      return this.http.delete(`${this.API_URL}/me/experiences/${experienceId}`).pipe(catchError(this.handleError));\n    }\n    // Portfolio Management\n    addPortfolioItem(item) {\n      return this.http.post(`${this.API_URL}/me/portfolio`, item).pipe(catchError(this.handleError));\n    }\n    updatePortfolioItem(itemId, updates) {\n      return this.http.put(`${this.API_URL}/me/portfolio/${itemId}`, updates).pipe(catchError(this.handleError));\n    }\n    deletePortfolioItem(itemId) {\n      return this.http.delete(`${this.API_URL}/me/portfolio/${itemId}`).pipe(catchError(this.handleError));\n    }\n    // Achievements & Certifications\n    addAchievement(achievement) {\n      return this.http.post(`${this.API_URL}/me/achievements`, achievement).pipe(catchError(this.handleError));\n    }\n    addCertification(certification) {\n      return this.http.post(`${this.API_URL}/me/certifications`, certification).pipe(catchError(this.handleError));\n    }\n    // Blog Posts\n    getBlogPosts(profileId) {\n      // Blog posts are included in the profile data from the backend\n      // Return empty array for now, as they're part of the main profile\n      return new Observable(observer => {\n        observer.next([]);\n        observer.complete();\n      });\n    }\n    // Analytics\n    getProfileAnalytics() {\n      // For now, return mock analytics since backend doesn't have this endpoint yet\n      const mockAnalytics = {\n        profileViews: 0,\n        uniqueVisitors: 0,\n        viewsThisMonth: 0,\n        viewsThisWeek: 0,\n        topReferrers: [],\n        skillEndorsements: 0,\n        blogPostViews: 0,\n        contactButtonClicks: 0\n      };\n      return new Observable(observer => {\n        observer.next(mockAnalytics);\n        observer.complete();\n      });\n    }\n    recordProfileView(request) {\n      return this.http.post(`${this.API_URL}/view`, request, {\n        headers: this.getHttpHeaders()\n      }).pipe(map(() => void 0), catchError(this.handleError));\n    }\n    // File Upload\n    uploadProfilePhoto(file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      const token = localStorage.getItem('authToken');\n      let headers = new HttpHeaders();\n      if (token) {\n        headers = headers.set('Authorization', `Bearer ${token}`);\n      }\n      // Store original profile state for potential rollback\n      const originalProfile = this.currentProfileSubject.value;\n      return this.http.post(`${this.API_URL}/upload/profile-photo`, formData, {\n        headers: headers\n      }).pipe(map(response => ({\n        url: response.data\n      })), tap(result => {\n        // Update local profile state with new photo URL\n        const currentProfile = this.currentProfileSubject.value;\n        if (currentProfile) {\n          const updatedProfile = {\n            ...currentProfile,\n            profilePhotoUrl: result.url\n          };\n          this.currentProfileSubject.next(updatedProfile);\n        }\n      }), catchError(error => {\n        // Rollback to original profile state on error\n        if (originalProfile) {\n          this.currentProfileSubject.next(originalProfile);\n        }\n        return this.handleError(error);\n      }));\n    }\n    uploadCoverPhoto(file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      const token = localStorage.getItem('authToken');\n      let headers = new HttpHeaders();\n      if (token) {\n        headers = headers.set('Authorization', `Bearer ${token}`);\n      }\n      // Store original profile state for potential rollback\n      const originalProfile = this.currentProfileSubject.value;\n      return this.http.post(`${this.API_URL}/upload/cover-photo`, formData, {\n        headers: headers\n      }).pipe(map(response => ({\n        url: response.data\n      })), tap(result => {\n        // Update local profile state with new cover photo URL\n        const currentProfile = this.currentProfileSubject.value;\n        if (currentProfile) {\n          const updatedProfile = {\n            ...currentProfile,\n            coverPhotoUrl: result.url\n          };\n          this.currentProfileSubject.next(updatedProfile);\n        }\n      }), catchError(error => {\n        // Rollback to original profile state on error\n        if (originalProfile) {\n          this.currentProfileSubject.next(originalProfile);\n        }\n        return this.handleError(error);\n      }));\n    }\n    /**\r\n     * Upload profile photo with optimistic updates\r\n     * Shows immediate feedback while upload is in progress\r\n     */\n    uploadProfilePhotoWithOptimisticUpdate(file) {\n      // Create temporary URL for immediate display\n      const tempUrl = URL.createObjectURL(file);\n      const originalProfile = this.currentProfileSubject.value;\n      // Optimistically update the UI\n      if (originalProfile) {\n        const optimisticProfile = {\n          ...originalProfile,\n          profilePhotoUrl: tempUrl\n        };\n        this.currentProfileSubject.next(optimisticProfile);\n      }\n      return this.uploadProfilePhoto(file).pipe(tap(result => {\n        // Clean up temporary URL\n        URL.revokeObjectURL(tempUrl);\n        // The actual URL update is handled by uploadProfilePhoto\n      }), catchError(error => {\n        // Clean up temporary URL and rollback\n        URL.revokeObjectURL(tempUrl);\n        if (originalProfile) {\n          this.currentProfileSubject.next(originalProfile);\n        }\n        return throwError(() => error);\n      }));\n    }\n    /**\r\n     * Upload cover photo with optimistic updates\r\n     * Shows immediate feedback while upload is in progress\r\n     */\n    uploadCoverPhotoWithOptimisticUpdate(file) {\n      // Create temporary URL for immediate display\n      const tempUrl = URL.createObjectURL(file);\n      const originalProfile = this.currentProfileSubject.value;\n      // Optimistically update the UI\n      if (originalProfile) {\n        const optimisticProfile = {\n          ...originalProfile,\n          coverPhotoUrl: tempUrl\n        };\n        this.currentProfileSubject.next(optimisticProfile);\n      }\n      return this.uploadCoverPhoto(file).pipe(tap(result => {\n        // Clean up temporary URL\n        URL.revokeObjectURL(tempUrl);\n        // The actual URL update is handled by uploadCoverPhoto\n      }), catchError(error => {\n        // Clean up temporary URL and rollback\n        URL.revokeObjectURL(tempUrl);\n        if (originalProfile) {\n          this.currentProfileSubject.next(originalProfile);\n        }\n        return throwError(() => error);\n      }));\n    }\n    // Utility Methods\n    generateProfileSlug(firstName, lastName) {\n      // Generate slug client-side for now\n      const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`.replace(/[^a-z0-9-]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');\n      return new Observable(observer => {\n        observer.next({\n          slug\n        });\n        observer.complete();\n      });\n    }\n    checkSlugAvailability(slug) {\n      // For now, assume all slugs are available\n      // TODO: Implement in backend\n      return new Observable(observer => {\n        observer.next({\n          available: true\n        });\n        observer.complete();\n      });\n    }\n    // Social Sharing\n    getProfileShareData(profileId) {\n      // Generate share data from current profile\n      const currentProfile = this.currentProfileSubject.value;\n      if (currentProfile) {\n        const shareData = {\n          title: `${currentProfile.firstName} ${currentProfile.lastName} - ${currentProfile.professionalTitle}`,\n          description: currentProfile.summary || currentProfile.headline || 'Professional profile',\n          imageUrl: currentProfile.profilePhotoUrl || '',\n          url: `${window.location.origin}/profile/${currentProfile.slug}`\n        };\n        return new Observable(observer => {\n          observer.next(shareData);\n          observer.complete();\n        });\n      }\n      return throwError(() => new Error('Profile not found'));\n    }\n    handleError(error) {\n      console.error('ProfileService error:', error);\n      let errorMessage = 'Възникна неочаквана грешка';\n      if (error.error && error.error.message) {\n        // Backend returned an error message\n        errorMessage = error.error.message;\n      } else if (error.status === 0) {\n        errorMessage = 'Няма връзка със сървъра';\n      } else if (error.status === 401) {\n        errorMessage = 'Не сте упълномощени за тази операция';\n      } else if (error.status === 403) {\n        errorMessage = 'Нямате права за тази операция';\n      } else if (error.status === 404) {\n        errorMessage = 'Профилът не е намерен';\n      } else if (error.status >= 500) {\n        errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\n      }\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function ProfileService_Factory(t) {\n        return new (t || ProfileService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProfileService,\n        factory: ProfileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProfileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}