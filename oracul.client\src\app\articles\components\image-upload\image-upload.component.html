<div class="image-upload-container">
  <!-- Current Image Display -->
  <div *ngIf="currentImageUrl && !isUploading" class="current-image">
    <img [src]="getImageUrl()" [alt]="placeholder" class="uploaded-image">
    <div class="image-overlay">
      <button mat-icon-button color="warn" (click)="removeImage()" class="remove-button">
        <mat-icon>delete</mat-icon>
      </button>
    </div>
  </div>

  <!-- Upload Area -->
  <div *ngIf="!currentImageUrl || isUploading" 
       class="upload-area"
       [class.drag-over]="dragOver"
       [class.uploading]="isUploading"
       (dragover)="onDragOver($event)"
       (dragleave)="onDragLeave($event)"
       (drop)="onDrop($event)">
    
    <!-- Loading State -->
    <div *ngIf="isUploading" class="upload-loading">
      <mat-progress-spinner mode="indeterminate" diameter="40"></mat-progress-spinner>
      <p>Качване на снимката...</p>
    </div>

    <!-- Upload Prompt -->
    <div *ngIf="!isUploading" class="upload-prompt">
      <mat-icon class="upload-icon">cloud_upload</mat-icon>
      <p class="upload-text">{{ placeholder }}</p>
      <p class="upload-hint">Плъзнете и пуснете файл тук или</p>
      
      <input type="file" 
             [accept]="accept" 
             (change)="onFileSelected($event)"
             class="file-input"
             #fileInput>
      
      <button mat-raised-button color="primary" (click)="fileInput.click()">
        <mat-icon>add_photo_alternate</mat-icon>
        Изберете файл
      </button>
      
      <p class="file-requirements">
        Поддържани формати: JPG, PNG, GIF, WebP<br>
        Максимален размер: 5MB
      </p>
    </div>
  </div>
</div>
