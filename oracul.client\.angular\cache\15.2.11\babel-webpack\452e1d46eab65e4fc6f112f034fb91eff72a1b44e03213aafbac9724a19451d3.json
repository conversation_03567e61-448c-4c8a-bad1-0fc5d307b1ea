{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../shared/services/article.service\";\nimport * as i2 from \"../auth/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../core/i18n/translation.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nfunction TestArticlePreviewComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function TestArticlePreviewComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleAuth());\n    });\n    i0.ɵɵtext(1, \" Login for Testing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TestArticlePreviewComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function TestArticlePreviewComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.logout());\n    });\n    i0.ɵɵtext(1, \" Logout for Testing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TestArticlePreviewComponent_mat_card_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 10);\n    i0.ɵɵlistener(\"click\", function TestArticlePreviewComponent_mat_card_16_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const article_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.viewArticle(article_r7));\n    });\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\")(6, \"div\", 12)(7, \"span\", 13);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 14);\n    i0.ɵɵtext(10, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 15);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"mat-card-content\")(14, \"p\", 16);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-chip\", 17);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-card-actions\")(19, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TestArticlePreviewComponent_mat_card_16_Template_button_click_19_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const article_r7 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      ctx_r10.viewArticle(article_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const article_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", article_r7.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", article_r7.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r7.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(article_r7.author);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", article_r7.readTime, \" \", ctx_r2.t.home.minRead, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r7.excerpt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(article_r7.category);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.home.readArticle, \" \");\n  }\n}\nexport class TestArticlePreviewComponent {\n  constructor(articleService, authService, router, t) {\n    this.articleService = articleService;\n    this.authService = authService;\n    this.router = router;\n    this.t = t;\n    this.testArticles = [];\n    this.isAuthenticated = false;\n  }\n  ngOnInit() {\n    // Subscribe to authentication state\n    this.authService.isAuthenticated$.subscribe(isAuth => {\n      this.isAuthenticated = isAuth;\n    });\n    // Load test articles\n    this.loadTestArticles();\n  }\n  loadTestArticles() {\n    // Load articles from database instead of using mock data\n    this.articleService.getFeaturedArticles(6).subscribe({\n      next: articles => {\n        this.testArticles = articles;\n      },\n      error: error => {\n        console.error('Error loading articles from database:', error);\n        // Only show error message, don't fall back to mock data\n        this.testArticles = [];\n      }\n    });\n  }\n  viewArticle(article) {\n    this.router.navigate(['/articles', article.slug]);\n  }\n  toggleAuth() {\n    // For testing purposes, navigate to login\n    this.router.navigate(['/login']);\n  }\n  logout() {\n    this.authService.logout().subscribe(() => {\n      console.log('Logged out for testing');\n    });\n  }\n  static {\n    this.ɵfac = function TestArticlePreviewComponent_Factory(t) {\n      return new (t || TestArticlePreviewComponent)(i0.ɵɵdirectiveInject(i1.ArticleService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.TranslationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TestArticlePreviewComponent,\n      selectors: [[\"app-test-article-preview\"]],\n      decls: 37,\n      vars: 4,\n      consts: [[1, \"test-container\"], [1, \"auth-status\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\", 4, \"ngIf\"], [1, \"articles-section\"], [1, \"articles-grid\"], [\"class\", \"article-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"test-instructions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"article-card\", 3, \"click\"], [\"mat-card-image\", \"\", 1, \"article-image\", 3, \"src\", \"alt\"], [1, \"article-meta\"], [1, \"author\"], [1, \"separator\"], [1, \"read-time\"], [1, \"article-excerpt\"], [1, \"category-chip\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function TestArticlePreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"Article Preview Feature Test\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"h2\");\n          i0.ɵɵtext(5, \"Authentication Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\");\n          i0.ɵɵtext(7, \"User is: \");\n          i0.ɵɵelementStart(8, \"strong\");\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(10, TestArticlePreviewComponent_button_10_Template, 2, 0, \"button\", 2);\n          i0.ɵɵtemplate(11, TestArticlePreviewComponent_button_11_Template, 2, 0, \"button\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 4)(13, \"h2\");\n          i0.ɵɵtext(14, \"Test Articles\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 5);\n          i0.ɵɵtemplate(16, TestArticlePreviewComponent_mat_card_16_Template, 23, 9, \"mat-card\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"h2\");\n          i0.ɵɵtext(19, \"Test Instructions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"ol\")(21, \"li\")(22, \"strong\");\n          i0.ɵɵtext(23, \"Anonymous User Test:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24, \" Make sure you're logged out, then click on any article to see the preview with blur effect and authentication prompt.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"li\")(26, \"strong\");\n          i0.ɵɵtext(27, \"Authenticated User Test:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \" Login using the button above, then click on any article to see the full content without restrictions.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"li\")(30, \"strong\");\n          i0.ɵɵtext(31, \"Navigation Test:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" Verify that clicking articles navigates to /articles/[slug] route.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\")(34, \"strong\");\n          i0.ɵɵtext(35, \"Authentication Prompt Test:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36, \" As anonymous user, verify the authentication prompt appears with proper Bulgarian text and working login/register buttons.\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.isAuthenticated ? \"Authenticated\" : \"Anonymous\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.testArticles);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.MatButton, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardImage, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatIcon, i9.MatChip],\n      styles: [\".test-container[_ngcontent-%COMP%] {\\n      max-width: 1200px;\\n      margin: 0 auto;\\n      padding: 2rem;\\n    }\\n\\n    .auth-status[_ngcontent-%COMP%] {\\n      background: var(--theme-accent-light);\\n      padding: 1rem;\\n      border-radius: 8px;\\n      margin-bottom: 2rem;\\n      text-align: center;\\n    }\\n\\n    .articles-grid[_ngcontent-%COMP%] {\\n      display: grid;\\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n      gap: 1.5rem;\\n      margin-bottom: 2rem;\\n    }\\n\\n    .article-card[_ngcontent-%COMP%] {\\n      cursor: pointer;\\n      transition: transform 0.2s ease, box-shadow 0.2s ease;\\n    }\\n\\n    .article-card[_ngcontent-%COMP%]:hover {\\n      transform: translateY(-4px);\\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n    }\\n\\n    .article-image[_ngcontent-%COMP%] {\\n      height: 200px;\\n      object-fit: cover;\\n    }\\n\\n    .article-meta[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 0.5rem;\\n      color: var(--theme-text-secondary);\\n      font-size: 0.9rem;\\n    }\\n\\n    .separator[_ngcontent-%COMP%] {\\n      color: var(--theme-text-disabled);\\n    }\\n\\n    .article-excerpt[_ngcontent-%COMP%] {\\n      margin-bottom: 1rem;\\n      line-height: 1.5;\\n    }\\n\\n    .category-chip[_ngcontent-%COMP%] {\\n      background: var(--theme-accent);\\n      color: var(--theme-text-primary);\\n    }\\n\\n    .test-instructions[_ngcontent-%COMP%] {\\n      background: var(--theme-surface);\\n      padding: 1.5rem;\\n      border-radius: 8px;\\n      border-left: 4px solid var(--theme-primary);\\n    }\\n\\n    .test-instructions[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n      margin: 1rem 0;\\n    }\\n\\n    .test-instructions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n      margin-bottom: 0.75rem;\\n      line-height: 1.5;\\n    }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;IAgBQA,iCAA0F;IAAhDA;MAAAA;MAAA;MAAA,OAASA,kCAAY;IAAA,EAAC;IAC9DA,mCACF;IAAAA,iBAAS;;;;;;IACTA,iCAAkF;IAA3CA;MAAAA;MAAA;MAAA,OAASA,8BAAQ;IAAA,EAAC;IACvDA,oCACF;IAAAA,iBAAS;;;;;;IAMPA,oCAAmG;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,6CAAoB;IAAA,EAAC;IAChGA,0BAAyF;IAEzFA,uCAAiB;IACCA,YAAmB;IAAAA,iBAAiB;IACpDA,yCAAmB;IAEMA,YAAoB;IAAAA,iBAAO;IAChDA,gCAAwB;IAAAA,uBAAC;IAAAA,iBAAO;IAChCA,iCAAwB;IAAAA,aAA2C;IAAAA,iBAAO;IAKhFA,yCAAkB;IACWA,aAAqB;IAAAA,iBAAI;IACpDA,qCAAgC;IAAAA,aAAsB;IAAAA,iBAAW;IAGnEA,yCAAkB;IACmBA;MAAA;MAAA;MAAA;MAASC,+BAAoB;MAAA,OAAED,uCAAwB;IAAA,EAAC;IACzFA,iCAAU;IAAAA,2BAAU;IAAAA,iBAAW;IAC/BA,aACF;IAAAA,iBAAS;;;;;IAtBSA,eAAwB;IAAxBA,2DAAwB;IAG1BA,eAAmB;IAAnBA,sCAAmB;IAGVA,eAAoB;IAApBA,uCAAoB;IAEjBA,eAA2C;IAA3CA,8EAA2C;IAM5CA,eAAqB;IAArBA,wCAAqB;IAChBA,eAAsB;IAAtBA,yCAAsB;IAMpDA,eACF;IADEA,0DACF;;;AA6Fd,OAAM,MAAOE,2BAA2B;EAItCC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAc,EACfC,CAAqB;IAHpB,mBAAc,GAAdH,cAAc;IACd,gBAAW,GAAXC,WAAW;IACX,WAAM,GAANC,MAAM;IACP,MAAC,GAADC,CAAC;IAPV,iBAAY,GAAqB,EAAE;IACnC,oBAAe,GAAG,KAAK;EAOpB;EAEHC,QAAQ;IACN;IACA,IAAI,CAACH,WAAW,CAACI,gBAAgB,CAACC,SAAS,CAACC,MAAM,IAAG;MACnD,IAAI,CAACC,eAAe,GAAGD,MAAM;IAC/B,CAAC,CAAC;IAEF;IACA,IAAI,CAACE,gBAAgB,EAAE;EACzB;EAEQA,gBAAgB;IACtB;IACA,IAAI,CAACT,cAAc,CAACU,mBAAmB,CAAC,CAAC,CAAC,CAACJ,SAAS,CAAC;MACnDK,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACC,YAAY,GAAGD,QAAQ;MAC9B,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACA,IAAI,CAACD,YAAY,GAAG,EAAE;MACxB;KACD,CAAC;EACJ;EAEAG,WAAW,CAACC,OAAuB;IACjC,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,WAAW,EAAED,OAAO,CAACE,IAAI,CAAC,CAAC;EACnD;EAEAC,UAAU;IACR;IACA,IAAI,CAAClB,MAAM,CAACgB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAG,MAAM;IACJ,IAAI,CAACpB,WAAW,CAACoB,MAAM,EAAE,CAACf,SAAS,CAAC,MAAK;MACvCS,OAAO,CAACO,GAAG,CAAC,wBAAwB,CAAC;IACvC,CAAC,CAAC;EACJ;;;uBAhDWxB,2BAA2B;IAAA;EAAA;;;YAA3BA,2BAA2B;MAAAyB;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UArIpC/B,8BAA4B;UACtBA,4CAA4B;UAAAA,iBAAK;UAErCA,8BAAyB;UACnBA,qCAAqB;UAAAA,iBAAK;UAC9BA,yBAAG;UAAAA,yBAAS;UAAAA,8BAAQ;UAAAA,YAAqD;UAAAA,iBAAS;UAClFA,oFAES;UACTA,oFAES;UACXA,iBAAM;UAENA,+BAA8B;UACxBA,8BAAa;UAAAA,iBAAK;UACtBA,+BAA2B;UACzBA,yFAyBW;UACbA,iBAAM;UAGRA,+BAA+B;UACzBA,kCAAiB;UAAAA,iBAAK;UAC1BA,2BAAI;UACUA,qCAAoB;UAAAA,iBAAS;UAACA,uIAAqH;UAAAA,iBAAK;UACpKA,2BAAI;UAAQA,yCAAwB;UAAAA,iBAAS;UAACA,uHAAqG;UAAAA,iBAAK;UACxJA,2BAAI;UAAQA,iCAAgB;UAAAA,iBAAS;UAACA,oFAAkE;UAAAA,iBAAK;UAC7GA,2BAAI;UAAQA,4CAA2B;UAAAA,iBAAS;UAACA,4IAA0H;UAAAA,iBAAK;;;UA/C9JA,eAAqD;UAArDA,yEAAqD;UACPA,eAAsB;UAAtBA,2CAAsB;UAG7BA,eAAqB;UAArBA,0CAAqB;UAQ3BA,eAAe;UAAfA,0CAAe", "names": ["i0", "ctx_r10", "TestArticlePreviewComponent", "constructor", "articleService", "authService", "router", "t", "ngOnInit", "isAuthenticated$", "subscribe", "isAuth", "isAuthenticated", "loadTestArticles", "getFeaturedArticles", "next", "articles", "testArticles", "error", "console", "viewArticle", "article", "navigate", "slug", "to<PERSON><PERSON><PERSON>", "logout", "log", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\test-article-preview\\test-article-preview.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { ArticleService } from '../shared/services/article.service';\r\nimport { AuthService } from '../auth/services/auth.service';\r\nimport { TranslationService } from '../core/i18n/translation.service';\r\nimport { ArticlePreview } from '../shared/models/article.models';\r\n\r\n@Component({\r\n  selector: 'app-test-article-preview',\r\n  template: `\r\n    <div class=\"test-container\">\r\n      <h1>Article Preview Feature Test</h1>\r\n\r\n      <div class=\"auth-status\">\r\n        <h2>Authentication Status</h2>\r\n        <p>User is: <strong>{{ isAuthenticated ? 'Authenticated' : 'Anonymous' }}</strong></p>\r\n        <button mat-raised-button color=\"primary\" (click)=\"toggleAuth()\" *ngIf=\"!isAuthenticated\">\r\n          Login for Testing\r\n        </button>\r\n        <button mat-raised-button color=\"warn\" (click)=\"logout()\" *ngIf=\"isAuthenticated\">\r\n          Logout for Testing\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"articles-section\">\r\n        <h2>Test Articles</h2>\r\n        <div class=\"articles-grid\">\r\n          <mat-card class=\"article-card\" *ngFor=\"let article of testArticles\" (click)=\"viewArticle(article)\">\r\n            <img mat-card-image [src]=\"article.imageUrl\" [alt]=\"article.title\" class=\"article-image\">\r\n\r\n            <mat-card-header>\r\n              <mat-card-title>{{ article.title }}</mat-card-title>\r\n              <mat-card-subtitle>\r\n                <div class=\"article-meta\">\r\n                  <span class=\"author\">{{ article.author }}</span>\r\n                  <span class=\"separator\">•</span>\r\n                  <span class=\"read-time\">{{ article.readTime }} {{ t.home.minRead }}</span>\r\n                </div>\r\n              </mat-card-subtitle>\r\n            </mat-card-header>\r\n\r\n            <mat-card-content>\r\n              <p class=\"article-excerpt\">{{ article.excerpt }}</p>\r\n              <mat-chip class=\"category-chip\">{{ article.category }}</mat-chip>\r\n            </mat-card-content>\r\n\r\n            <mat-card-actions>\r\n              <button mat-button color=\"primary\" (click)=\"viewArticle(article); $event.stopPropagation()\">\r\n                <mat-icon>visibility</mat-icon>\r\n                {{ t.home.readArticle }}\r\n              </button>\r\n            </mat-card-actions>\r\n          </mat-card>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"test-instructions\">\r\n        <h2>Test Instructions</h2>\r\n        <ol>\r\n          <li><strong>Anonymous User Test:</strong> Make sure you're logged out, then click on any article to see the preview with blur effect and authentication prompt.</li>\r\n          <li><strong>Authenticated User Test:</strong> Login using the button above, then click on any article to see the full content without restrictions.</li>\r\n          <li><strong>Navigation Test:</strong> Verify that clicking articles navigates to /articles/[slug] route.</li>\r\n          <li><strong>Authentication Prompt Test:</strong> As anonymous user, verify the authentication prompt appears with proper Bulgarian text and working login/register buttons.</li>\r\n        </ol>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .test-container {\r\n      max-width: 1200px;\r\n      margin: 0 auto;\r\n      padding: 2rem;\r\n    }\r\n\r\n    .auth-status {\r\n      background: var(--theme-accent-light);\r\n      padding: 1rem;\r\n      border-radius: 8px;\r\n      margin-bottom: 2rem;\r\n      text-align: center;\r\n    }\r\n\r\n    .articles-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n      gap: 1.5rem;\r\n      margin-bottom: 2rem;\r\n    }\r\n\r\n    .article-card {\r\n      cursor: pointer;\r\n      transition: transform 0.2s ease, box-shadow 0.2s ease;\r\n    }\r\n\r\n    .article-card:hover {\r\n      transform: translateY(-4px);\r\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    .article-image {\r\n      height: 200px;\r\n      object-fit: cover;\r\n    }\r\n\r\n    .article-meta {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n      color: var(--theme-text-secondary);\r\n      font-size: 0.9rem;\r\n    }\r\n\r\n    .separator {\r\n      color: var(--theme-text-disabled);\r\n    }\r\n\r\n    .article-excerpt {\r\n      margin-bottom: 1rem;\r\n      line-height: 1.5;\r\n    }\r\n\r\n    .category-chip {\r\n      background: var(--theme-accent);\r\n      color: var(--theme-text-primary);\r\n    }\r\n\r\n    .test-instructions {\r\n      background: var(--theme-surface);\r\n      padding: 1.5rem;\r\n      border-radius: 8px;\r\n      border-left: 4px solid var(--theme-primary);\r\n    }\r\n\r\n    .test-instructions ol {\r\n      margin: 1rem 0;\r\n    }\r\n\r\n    .test-instructions li {\r\n      margin-bottom: 0.75rem;\r\n      line-height: 1.5;\r\n    }\r\n  `]\r\n})\r\nexport class TestArticlePreviewComponent implements OnInit {\r\n  testArticles: ArticlePreview[] = [];\r\n  isAuthenticated = false;\r\n\r\n  constructor(\r\n    private articleService: ArticleService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    public t: TranslationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Subscribe to authentication state\r\n    this.authService.isAuthenticated$.subscribe(isAuth => {\r\n      this.isAuthenticated = isAuth;\r\n    });\r\n\r\n    // Load test articles\r\n    this.loadTestArticles();\r\n  }\r\n\r\n  private loadTestArticles(): void {\r\n    // Load articles from database instead of using mock data\r\n    this.articleService.getFeaturedArticles(6).subscribe({\r\n      next: (articles) => {\r\n        this.testArticles = articles;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading articles from database:', error);\r\n        // Only show error message, don't fall back to mock data\r\n        this.testArticles = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  viewArticle(article: ArticlePreview): void {\r\n    this.router.navigate(['/articles', article.slug]);\r\n  }\r\n\r\n  toggleAuth(): void {\r\n    // For testing purposes, navigate to login\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  logout(): void {\r\n    this.authService.logout().subscribe(() => {\r\n      console.log('Logged out for testing');\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}