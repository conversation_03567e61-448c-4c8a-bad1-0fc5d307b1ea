{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/oauth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/progress-bar\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/checkbox\";\nimport * as i17 from \"@angular/material/divider\";\nimport * as i18 from \"@angular/material/chips\";\nimport * as i19 from \"@angular/material/datepicker\";\nfunction RegisterComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u041F\\u0440\\u0438\\u0441\\u044A\\u0435\\u0434\\u0438\\u043D\\u0435\\u0442\\u0435 \\u0441\\u0435 \\u043A\\u044A\\u043C \\u041E\\u0440\\u0430\\u043A\\u0443\\u043B \\u0438 \\u0437\\u0430\\u043F\\u043E\\u0447\\u043D\\u0435\\u0442\\u0435 \\u0441\\u0432\\u043E\\u0435\\u0442\\u043E \\u043F\\u044A\\u0442\\u0443\\u0432\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"mat-progress-bar\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \\u0421\\u0442\\u044A\\u043F\\u043A\\u0430 \", ctx_r1.currentStep, \" \\u043E\\u0442 \", ctx_r1.totalSteps, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r1.currentStep / ctx_r1.totalSteps * 100);\n  }\n}\nfunction RegisterComponent_div_11_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0417\\u0430 \\u0445\\u043E\\u0440\\u0430, \\u043A\\u043E\\u0438\\u0442\\u043E \\u0442\\u044A\\u0440\\u0441\\u044F\\u0442 \\u0434\\u0443\\u0445\\u043E\\u0432\\u043D\\u043E \\u0432\\u043E\\u0434\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_11_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0417\\u0430 \\u043F\\u0440\\u0430\\u043A\\u0442\\u0438\\u043A\\u0443\\u0432\\u0430\\u0449\\u0438 \\u0430\\u0441\\u0442\\u0440\\u043E\\u043B\\u043E\\u0437\\u0438, \\u0442\\u0430\\u0440\\u043E \\u0447\\u0438\\u0442\\u0446\\u0438 \\u0438 \\u0434\\u0443\\u0445\\u043E\\u0432\\u043D\\u0438 \\u043A\\u043E\\u043D\\u0441\\u0443\\u043B\\u0442\\u0430\\u043D\\u0442\\u0438\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0442\\u0438\\u043F \\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 13)(4, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_div_11_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.setRegistrationType(\"general\"));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"\\u041E\\u0431\\u0438\\u043A\\u043D\\u043E\\u0432\\u0435\\u043D \\u043F\\u043E\\u0442\\u0440\\u0435\\u0431\\u0438\\u0442\\u0435\\u043B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_div_11_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.setRegistrationType(\"oracle\"));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"\\u041E\\u0440\\u0430\\u043A\\u0443\\u043B\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"p\", 16);\n    i0.ɵɵtemplate(15, RegisterComponent_div_11_span_15_Template, 2, 0, \"span\", 4);\n    i0.ɵɵtemplate(16, RegisterComponent_div_11_span_16_Template, 2, 0, \"span\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.registrationType === \"general\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.isOracleRegistration());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.registrationType === \"general\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isOracleRegistration());\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getFirstNameErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getLastNameErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.getEmailErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getPasswordErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getConfirmPasswordErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_div_45_mat_error_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0422\\u0440\\u044F\\u0431\\u0432\\u0430 \\u0434\\u0430 \\u043F\\u0440\\u0438\\u0435\\u043C\\u0435\\u0442\\u0435 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0442\\u0430 \\u0438 \\u043F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u0432\\u0435\\u0440\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E\\u0441\\u0442 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_1_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-checkbox\", 37);\n    i0.ɵɵtext(2, \" \\u0421\\u044A\\u0433\\u043B\\u0430\\u0441\\u044F\\u0432\\u0430\\u043C \\u0441\\u0435 \\u0441 \");\n    i0.ɵɵelementStart(3, \"a\", 38);\n    i0.ɵɵtext(4, \"\\u0423\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u043B\\u0437\\u0432\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u0438 \");\n    i0.ɵɵelementStart(6, \"a\", 38);\n    i0.ɵɵtext(7, \"\\u041F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u0432\\u0435\\u0440\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E\\u0441\\u0442\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, RegisterComponent_form_12_div_1_div_45_mat_error_8_Template, 2, 0, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r24.registerForm.get(\"acceptTerms\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r24.registerForm.get(\"acceptTerms\")) == null ? null : tmp_0_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"mat-form-field\", 25)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"\\u041F\\u044A\\u0440\\u0432\\u043E \\u0438\\u043C\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 26);\n    i0.ɵɵelementStart(6, \"mat-icon\", 27);\n    i0.ɵɵtext(7, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, RegisterComponent_form_12_div_1_mat_error_8_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 25)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"\\u0424\\u0430\\u043C\\u0438\\u043B\\u043D\\u043E \\u0438\\u043C\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 28);\n    i0.ɵɵelementStart(13, \"mat-icon\", 27);\n    i0.ɵɵtext(14, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, RegisterComponent_form_12_div_1_mat_error_15_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-form-field\", 29)(17, \"mat-label\");\n    i0.ɵɵtext(18, \"\\u0418\\u043C\\u0435\\u0439\\u043B \\u0430\\u0434\\u0440\\u0435\\u0441\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 30);\n    i0.ɵɵelementStart(20, \"mat-icon\", 27);\n    i0.ɵɵtext(21, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, RegisterComponent_form_12_div_1_mat_error_22_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"mat-form-field\", 29)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"\\u0422\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0435\\u043D \\u043D\\u043E\\u043C\\u0435\\u0440 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 31);\n    i0.ɵɵelementStart(27, \"mat-icon\", 27);\n    i0.ɵɵtext(28, \"phone\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"mat-form-field\", 29)(30, \"mat-label\");\n    i0.ɵɵtext(31, \"\\u041F\\u0430\\u0440\\u043E\\u043B\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 32);\n    i0.ɵɵelementStart(33, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_1_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.hidePassword = !ctx_r26.hidePassword);\n    });\n    i0.ɵɵelementStart(34, \"mat-icon\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, RegisterComponent_form_12_div_1_mat_error_36_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"mat-form-field\", 29)(38, \"mat-label\");\n    i0.ɵɵtext(39, \"\\u041F\\u043E\\u0442\\u0432\\u044A\\u0440\\u0434\\u0435\\u0442\\u0435 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\\u0442\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(40, \"input\", 34);\n    i0.ɵɵelementStart(41, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_1_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.hideConfirmPassword = !ctx_r28.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(42, \"mat-icon\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(44, RegisterComponent_form_12_div_1_mat_error_44_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, RegisterComponent_form_12_div_1_div_45_Template, 9, 1, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_7_0;\n    let tmp_12_0;\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r9.registerForm.get(\"firstName\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r9.registerForm.get(\"firstName\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r9.registerForm.get(\"lastName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r9.registerForm.get(\"lastName\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r9.registerForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r9.registerForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"type\", ctx_r9.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-label\", \"\\u0421\\u043A\\u0440\\u0438\\u0439 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\")(\"aria-pressed\", ctx_r9.hidePassword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r9.registerForm.get(\"password\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r9.registerForm.get(\"password\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r9.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-label\", \"\\u0421\\u043A\\u0440\\u0438\\u0439 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\")(\"aria-pressed\", ctx_r9.hideConfirmPassword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r9.registerForm.get(\"confirmPassword\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r9.registerForm.get(\"confirmPassword\")) == null ? null : tmp_12_0.touched) || ctx_r9.registerForm.hasError(\"passwordMismatch\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.registrationType === \"general\");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r29.getFieldErrorMessage(\"professionalTitle\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const spec_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", spec_r35);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", spec_r35, \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.getFieldErrorMessage(\"primarySpecialization\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.getFieldErrorMessage(\"headline\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.getFieldErrorMessage(\"summary\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r34.getFieldErrorMessage(\"yearsOfExperience\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u041F\\u0440\\u043E\\u0444\\u0435\\u0441\\u0438\\u043E\\u043D\\u0430\\u043B\\u043D\\u0430 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 29)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"\\u041F\\u0440\\u043E\\u0444\\u0435\\u0441\\u0438\\u043E\\u043D\\u0430\\u043B\\u043D\\u0430 \\u0442\\u0438\\u0442\\u043B\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 39);\n    i0.ɵɵelementStart(7, \"mat-icon\", 27);\n    i0.ɵɵtext(8, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, RegisterComponent_form_12_div_2_mat_error_9_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-form-field\", 29)(11, \"mat-label\");\n    i0.ɵɵtext(12, \"\\u041E\\u0441\\u043D\\u043E\\u0432\\u043D\\u0430 \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-select\", 40);\n    i0.ɵɵtemplate(14, RegisterComponent_form_12_div_2_mat_option_14_Template, 2, 2, \"mat-option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, RegisterComponent_form_12_div_2_mat_error_15_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-form-field\", 29)(17, \"mat-label\");\n    i0.ɵɵtext(18, \"\\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u0438\\u0435 (\\u043A\\u0440\\u0430\\u0442\\u043A\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 42);\n    i0.ɵɵelementStart(20, \"mat-icon\", 27);\n    i0.ɵɵtext(21, \"title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-hint\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, RegisterComponent_form_12_div_2_mat_error_24_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"mat-form-field\", 29)(26, \"mat-label\");\n    i0.ɵɵtext(27, \"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"textarea\", 43);\n    i0.ɵɵtext(29, \"            \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"mat-hint\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, RegisterComponent_form_12_div_2_mat_error_32_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 29)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"\\u0413\\u043E\\u0434\\u0438\\u043D\\u0438 \\u043E\\u043F\\u0438\\u0442\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"input\", 44);\n    i0.ɵɵelementStart(37, \"mat-icon\", 27);\n    i0.ɵɵtext(38, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, RegisterComponent_form_12_div_2_mat_error_39_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r10.registerForm.get(\"professionalTitle\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r10.registerForm.get(\"professionalTitle\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.getSpecializationOptions());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r10.registerForm.get(\"primarySpecialization\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r10.registerForm.get(\"primarySpecialization\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_3_0 = ctx_r10.registerForm.get(\"headline\")) == null ? null : tmp_3_0.value == null ? null : tmp_3_0.value.length) || 0, \"/220\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r10.registerForm.get(\"headline\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r10.registerForm.get(\"headline\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_5_0 = ctx_r10.registerForm.get(\"summary\")) == null ? null : tmp_5_0.value == null ? null : tmp_5_0.value.length) || 0, \"/2000\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r10.registerForm.get(\"summary\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r10.registerForm.get(\"summary\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r10.registerForm.get(\"yearsOfExperience\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r10.registerForm.get(\"yearsOfExperience\")) == null ? null : tmp_7_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_3_mat_error_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r36.getFieldErrorMessage(\"city\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_3_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r37.getFieldErrorMessage(\"country\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u041C\\u0435\\u0441\\u0442\\u043E\\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46)(4, \"mat-form-field\", 25)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"\\u0413\\u0440\\u0430\\u0434\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 47);\n    i0.ɵɵelementStart(8, \"mat-icon\", 27);\n    i0.ɵɵtext(9, \"location_city\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, RegisterComponent_form_12_div_3_mat_error_10_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 25)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"\\u041E\\u0431\\u043B\\u0430\\u0441\\u0442/\\u0420\\u0435\\u0433\\u0438\\u043E\\u043D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 48);\n    i0.ɵɵelementStart(15, \"mat-icon\", 27);\n    i0.ɵɵtext(16, \"map\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 29)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"\\u0414\\u044A\\u0440\\u0436\\u0430\\u0432\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 49);\n    i0.ɵɵelementStart(21, \"mat-icon\", 27);\n    i0.ɵɵtext(22, \"public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, RegisterComponent_form_12_div_3_mat_error_23_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-form-field\", 29)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"\\u041F\\u043E\\u043A\\u0430\\u0437\\u0432\\u0430\\u043D\\u043E \\u043C\\u0435\\u0441\\u0442\\u043E\\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 50);\n    i0.ɵɵelementStart(28, \"mat-icon\", 27);\n    i0.ɵɵtext(29, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r11.registerForm.get(\"city\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r11.registerForm.get(\"city\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r11.registerForm.get(\"country\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r11.registerForm.get(\"country\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r39.getFieldErrorMessage(\"birthDate\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.getFieldErrorMessage(\"birthLocation\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sign_r49 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", sign_r49);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", sign_r49, \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r50 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r50);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r50, \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r43.getFieldErrorMessage(\"oracleTypes\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_option_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lang_r51 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", lang_r51);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", lang_r51, \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r45.getFieldErrorMessage(\"languagesSpoken\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_div_55_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-chip\", 70);\n    i0.ɵɵlistener(\"removed\", function RegisterComponent_form_12_div_4_div_55_mat_chip_2_Template_mat_chip_removed_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const i_r54 = restoredCtx.index;\n      const ctx_r55 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r55.removeSkill(i_r54));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"mat-icon\", 71);\n    i0.ɵɵtext(3, \"cancel\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const skill_r53 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r53, \" \");\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction RegisterComponent_form_12_div_4_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"mat-chip-set\");\n    i0.ɵɵtemplate(2, RegisterComponent_form_12_div_4_div_55_mat_chip_2_Template, 4, 1, \"mat-chip\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ((tmp_0_0 = ctx_r46.registerForm.get(\"skills\")) == null ? null : tmp_0_0.value) || i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r48.getFieldErrorMessage(\"skills\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u041E\\u0440\\u0430\\u043A\\u0443\\u043B\\u0441\\u043A\\u0430 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 51)(4, \"mat-form-field\", 25)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"\\u0414\\u0430\\u0442\\u0430 \\u043D\\u0430 \\u0440\\u0430\\u0436\\u0434\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 52);\n    i0.ɵɵelementStart(8, \"mat-datepicker-toggle\", 53)(9, \"mat-icon\", 54);\n    i0.ɵɵtext(10, \"cake\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(11, \"mat-datepicker\", null, 55);\n    i0.ɵɵtemplate(13, RegisterComponent_form_12_div_4_mat_error_13_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 25)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"\\u0427\\u0430\\u0441 \\u043D\\u0430 \\u0440\\u0430\\u0436\\u0434\\u0430\\u043D\\u0435 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 56);\n    i0.ɵɵelement(18, \"input\", 57);\n    i0.ɵɵelementStart(19, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_4_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.toggleTimeFormat());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"mat-hint\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"mat-form-field\", 29)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"\\u041C\\u044F\\u0441\\u0442\\u043E \\u043D\\u0430 \\u0440\\u0430\\u0436\\u0434\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 59);\n    i0.ɵɵelementStart(28, \"mat-icon\", 27);\n    i0.ɵɵtext(29, \"place\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, RegisterComponent_form_12_div_4_mat_error_30_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"mat-form-field\", 29)(32, \"mat-label\");\n    i0.ɵɵtext(33, \"\\u0410\\u0441\\u0442\\u0440\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u043D\\u0430 \\u0437\\u043E\\u0434\\u0438\\u044F (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"mat-select\", 60);\n    i0.ɵɵtemplate(35, RegisterComponent_form_12_div_4_mat_option_35_Template, 2, 2, \"mat-option\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"mat-form-field\", 29)(37, \"mat-label\");\n    i0.ɵɵtext(38, \"\\u041C\\u0435\\u0442\\u043E\\u0434\\u0438 \\u043D\\u0430 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"mat-select\", 61);\n    i0.ɵɵtemplate(40, RegisterComponent_form_12_div_4_mat_option_40_Template, 2, 2, \"mat-option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"mat-hint\");\n    i0.ɵɵtext(42, \"\\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0432\\u0441\\u0438\\u0447\\u043A\\u0438 \\u043C\\u0435\\u0442\\u043E\\u0434\\u0438, \\u043A\\u043E\\u0438\\u0442\\u043E \\u0438\\u0437\\u043F\\u043E\\u043B\\u0437\\u0432\\u0430\\u0442\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, RegisterComponent_form_12_div_4_mat_error_43_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"mat-form-field\", 29)(45, \"mat-label\");\n    i0.ɵɵtext(46, \"\\u0415\\u0437\\u0438\\u0446\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"mat-select\", 62);\n    i0.ɵɵtemplate(48, RegisterComponent_form_12_div_4_mat_option_48_Template, 2, 2, \"mat-option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"mat-hint\");\n    i0.ɵɵtext(50, \"\\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0435\\u0437\\u0438\\u0446\\u0438\\u0442\\u0435, \\u043D\\u0430 \\u043A\\u043E\\u0438\\u0442\\u043E \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0442\\u0435 \\u043A\\u043E\\u043D\\u0441\\u0443\\u043B\\u0442\\u0430\\u0446\\u0438\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(51, RegisterComponent_form_12_div_4_mat_error_51_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 63)(53, \"h4\");\n    i0.ɵɵtext(54, \"\\u0423\\u043C\\u0435\\u043D\\u0438\\u044F \\u0438 \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(55, RegisterComponent_form_12_div_4_div_55_Template, 3, 2, \"div\", 64);\n    i0.ɵɵelementStart(56, \"mat-form-field\", 29)(57, \"mat-label\");\n    i0.ɵɵtext(58, \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0435\\u0442\\u0435 \\u0443\\u043C\\u0435\\u043D\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"input\", 65, 66);\n    i0.ɵɵlistener(\"keydown.enter\", function RegisterComponent_form_12_div_4_Template_input_keydown_enter_59_listener($event) {\n      i0.ɵɵrestoreView(_r58);\n      const _r47 = i0.ɵɵreference(60);\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.addSkill($event, _r47));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_4_Template_button_click_61_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const _r47 = i0.ɵɵreference(60);\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.addSkillFromInput(_r47));\n    });\n    i0.ɵɵelementStart(62, \"mat-icon\");\n    i0.ɵɵtext(63, \"add\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"mat-hint\");\n    i0.ɵɵtext(65, \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0435\\u0442\\u0435 \\u043F\\u043E\\u043D\\u0435 3 \\u0443\\u043C\\u0435\\u043D\\u0438\\u044F \\u0441\\u0432\\u044A\\u0440\\u0437\\u0430\\u043D\\u0438 \\u0441 \\u0432\\u0430\\u0448\\u0430\\u0442\\u0430 \\u043F\\u0440\\u0430\\u043A\\u0442\\u0438\\u043A\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(66, RegisterComponent_form_12_div_4_mat_error_66_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r38 = i0.ɵɵreference(12);\n    const _r47 = i0.ɵɵreference(60);\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    let tmp_2_0;\n    let tmp_7_0;\n    let tmp_10_0;\n    let tmp_12_0;\n    let tmp_13_0;\n    let tmp_15_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"matDatepicker\", _r38);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r38);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r12.registerForm.get(\"birthDate\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r12.registerForm.get(\"birthDate\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"step\", ctx_r12.timeFormat24h ? 60 : 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", ctx_r12.timeFormat24h ? \"\\u041F\\u0440\\u0435\\u0432\\u043A\\u043B\\u044E\\u0447\\u0438 \\u043D\\u0430 12-\\u0447\\u0430\\u0441\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\" : \"\\u041F\\u0440\\u0435\\u0432\\u043A\\u043B\\u044E\\u0447\\u0438 \\u043D\\u0430 24-\\u0447\\u0430\\u0441\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r12.timeFormat24h ? \"schedule\" : \"access_time\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r12.timeFormat24h ? \"24-\\u0447\\u0430\\u0441\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\" : \"12-\\u0447\\u0430\\u0441\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r12.registerForm.get(\"birthLocation\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r12.registerForm.get(\"birthLocation\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getAstrologicalSigns());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getOracleTypeOptions());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r12.registerForm.get(\"oracleTypes\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r12.registerForm.get(\"oracleTypes\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getLanguageOptions());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r12.registerForm.get(\"languagesSpoken\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r12.registerForm.get(\"languagesSpoken\")) == null ? null : tmp_12_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r12.registerForm.get(\"skills\")) == null ? null : tmp_13_0.value == null ? null : tmp_13_0.value.length) > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", !_r47.value.trim());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx_r12.registerForm.get(\"skills\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r12.registerForm.get(\"skills\")) == null ? null : tmp_15_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u041A\\u043E\\u043D\\u0442\\u0430\\u043A\\u0442\\u0438 \\u0438 \\u0431\\u0438\\u0437\\u043D\\u0435\\u0441 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 29)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"\\u0423\\u0435\\u0431\\u0441\\u0430\\u0439\\u0442 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 72);\n    i0.ɵɵelementStart(7, \"mat-icon\", 27);\n    i0.ɵɵtext(8, \"language\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 29)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"\\u041F\\u043E\\u0440\\u0442\\u0444\\u043E\\u043B\\u0438\\u043E URL (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 73);\n    i0.ɵɵelementStart(13, \"mat-icon\", 27);\n    i0.ɵɵtext(14, \"work\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"h4\");\n    i0.ɵɵtext(16, \"\\u0411\\u0438\\u0437\\u043D\\u0435\\u0441 \\u0430\\u0434\\u0440\\u0435\\u0441 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 29)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"\\u0423\\u043B\\u0438\\u0446\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 74);\n    i0.ɵɵelementStart(21, \"mat-icon\", 27);\n    i0.ɵɵtext(22, \"home\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 75)(24, \"mat-form-field\", 25)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"\\u0413\\u0440\\u0430\\u0434\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 76);\n    i0.ɵɵelementStart(28, \"mat-icon\", 27);\n    i0.ɵɵtext(29, \"location_city\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"mat-form-field\", 25)(31, \"mat-label\");\n    i0.ɵɵtext(32, \"\\u041F\\u043E\\u0449\\u0435\\u043D\\u0441\\u043A\\u0438 \\u043A\\u043E\\u0434\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"input\", 77);\n    i0.ɵɵelementStart(34, \"mat-icon\", 27);\n    i0.ɵɵtext(35, \"markunread_mailbox\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"mat-checkbox\", 78);\n    i0.ɵɵtext(37, \" \\u041F\\u043E\\u043A\\u0430\\u0437\\u0432\\u0430\\u0439 \\u0431\\u0438\\u0437\\u043D\\u0435\\u0441 \\u0430\\u0434\\u0440\\u0435\\u0441\\u0430 \\u043F\\u0443\\u0431\\u043B\\u0438\\u0447\\u043D\\u043E \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterComponent_form_12_div_6_mat_error_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0422\\u0440\\u044F\\u0431\\u0432\\u0430 \\u0434\\u0430 \\u043F\\u0440\\u0438\\u0435\\u043C\\u0435\\u0442\\u0435 \\u0432\\u0441\\u0438\\u0447\\u043A\\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F \\u0438 \\u043F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0438 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u0422\\u0430\\u0440\\u0438\\u0444\\u0438 \\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79)(4, \"mat-form-field\", 25)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"\\u0427\\u0430\\u0441\\u043E\\u0432\\u0430 \\u0442\\u0430\\u0440\\u0438\\u0444\\u0430 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 80);\n    i0.ɵɵelementStart(8, \"mat-icon\", 27);\n    i0.ɵɵtext(9, \"schedule\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"mat-form-field\", 25)(11, \"mat-label\");\n    i0.ɵɵtext(12, \"\\u0422\\u0430\\u0440\\u0438\\u0444\\u0430 \\u0437\\u0430 \\u0441\\u0435\\u0441\\u0438\\u044F (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 81);\n    i0.ɵɵelementStart(14, \"mat-icon\", 27);\n    i0.ɵɵtext(15, \"event\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"mat-form-field\", 29)(17, \"mat-label\");\n    i0.ɵɵtext(18, \"\\u0412\\u0430\\u043B\\u0443\\u0442\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-select\", 82)(20, \"mat-option\", 83);\n    i0.ɵɵtext(21, \"BGN (\\u0411\\u044A\\u043B\\u0433\\u0430\\u0440\\u0441\\u043A\\u0438 \\u043B\\u0435\\u0432)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-option\", 84);\n    i0.ɵɵtext(23, \"EUR (\\u0415\\u0432\\u0440\\u043E)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-option\", 85);\n    i0.ɵɵtext(25, \"USD (\\u0414\\u043E\\u043B\\u0430\\u0440)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 36)(27, \"mat-checkbox\", 37);\n    i0.ɵɵtext(28, \" \\u0421\\u044A\\u0433\\u043B\\u0430\\u0441\\u044F\\u0432\\u0430\\u043C \\u0441\\u0435 \\u0441 \");\n    i0.ɵɵelementStart(29, \"a\", 38);\n    i0.ɵɵtext(30, \"\\u0423\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u043B\\u0437\\u0432\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \", \");\n    i0.ɵɵelementStart(32, \"a\", 38);\n    i0.ɵɵtext(33, \"\\u041F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u0432\\u0435\\u0440\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E\\u0441\\u0442\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" \\u0438 \");\n    i0.ɵɵelementStart(35, \"a\", 38);\n    i0.ɵɵtext(36, \"\\u0423\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0442\\u0430 \\u0437\\u0430 \\u043E\\u0440\\u0430\\u043A\\u0443\\u043B\\u0438\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, RegisterComponent_form_12_div_6_mat_error_37_Template, 2, 0, \"mat-error\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(37);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r14.registerForm.get(\"acceptTerms\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r14.registerForm.get(\"acceptTerms\")) == null ? null : tmp_0_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_7_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_7_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r66.previousStep());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u041D\\u0430\\u0437\\u0430\\u0434 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r62 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r62.isLoading);\n  }\n}\nfunction RegisterComponent_form_12_div_7_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_7_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"arrow_forward\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_7_mat_icon_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"person_add\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtemplate(1, RegisterComponent_form_12_div_7_button_1_Template, 4, 1, \"button\", 87);\n    i0.ɵɵelement(2, \"div\", 88);\n    i0.ɵɵelementStart(3, \"button\", 89);\n    i0.ɵɵtemplate(4, RegisterComponent_form_12_div_7_mat_icon_4_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtemplate(5, RegisterComponent_form_12_div_7_mat_icon_5_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtemplate(6, RegisterComponent_form_12_div_7_mat_icon_6_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.currentStep > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r15.isLoading || !ctx_r15.isCurrentStepValid());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.isLoading && ctx_r15.currentStep < ctx_r15.totalSteps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.isLoading && ctx_r15.currentStep === ctx_r15.totalSteps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.isLoading ? \"\\u041E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0430...\" : ctx_r15.currentStep < ctx_r15.totalSteps ? \"\\u041D\\u0430\\u043F\\u0440\\u0435\\u0434\" : \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0439 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442\", \" \");\n  }\n}\nfunction RegisterComponent_form_12_button_8_mat_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_button_8_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"person_add\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵtemplate(1, RegisterComponent_form_12_button_8_mat_icon_1_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtemplate(2, RegisterComponent_form_12_button_8_mat_icon_2_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r16.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.isLoading ? \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442...\" : \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0439 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442\", \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"mat-divider\");\n    i0.ɵɵelementStart(2, \"span\", 94);\n    i0.ɵɵtext(3, \"\\u0438\\u043B\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"mat-divider\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.signUpWithGoogle());\n    });\n    i0.ɵɵelement(2, \"img\", 97);\n    i0.ɵɵtext(3, \" \\u0420\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u044F \\u0441 Google \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r72.signUpWithFacebook());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\", 99);\n    i0.ɵɵtext(6, \"facebook\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" \\u0420\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u044F \\u0441 Facebook \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isLoading);\n  }\n}\nfunction RegisterComponent_form_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 17);\n    i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_form_12_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.onSubmit());\n    });\n    i0.ɵɵtemplate(1, RegisterComponent_form_12_div_1_Template, 46, 14, \"div\", 18);\n    i0.ɵɵtemplate(2, RegisterComponent_form_12_div_2_Template, 40, 8, \"div\", 18);\n    i0.ɵɵtemplate(3, RegisterComponent_form_12_div_3_Template, 30, 2, \"div\", 18);\n    i0.ɵɵtemplate(4, RegisterComponent_form_12_div_4_Template, 67, 16, \"div\", 18);\n    i0.ɵɵtemplate(5, RegisterComponent_form_12_div_5_Template, 38, 0, \"div\", 18);\n    i0.ɵɵtemplate(6, RegisterComponent_form_12_div_6_Template, 38, 1, \"div\", 18);\n    i0.ɵɵtemplate(7, RegisterComponent_form_12_div_7_Template, 8, 6, \"div\", 19);\n    i0.ɵɵtemplate(8, RegisterComponent_form_12_button_8_Template, 4, 4, \"button\", 20);\n    i0.ɵɵtemplate(9, RegisterComponent_form_12_div_9_Template, 5, 0, \"div\", 21);\n    i0.ɵɵtemplate(10, RegisterComponent_form_12_div_10_Template, 8, 2, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.registerForm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 2 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 3 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 4 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 5 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 6 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.registrationType === \"general\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.registrationType === \"general\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.registrationType === \"general\");\n  }\n}\nexport class RegisterComponent {\n  // Helper method for template\n  isOracleRegistration() {\n    return this.registrationType === 'oracle';\n  }\n  constructor(formBuilder, authService, oauthService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.oauthService = oauthService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.returnUrl = '/';\n    // Registration type\n    this.registrationType = 'general';\n    // Time format preference\n    this.timeFormat24h = true;\n    this.currentStep = 1;\n    this.totalSteps = 1;\n    this.registerForm = this.createGeneralForm();\n  }\n  ngOnInit() {\n    // Get return url from route parameters or default to '/'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n    // Redirect if already logged in\n    this.authService.isAuthenticated$.subscribe(isAuth => {\n      if (isAuth) {\n        // If user is already authenticated, redirect to profile page instead of home\n        this.router.navigate(['/profile/edit']);\n      }\n    });\n  }\n  // Form creation methods\n  createGeneralForm() {\n    return this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phoneNumber: [''],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  createOracleForm() {\n    return this.formBuilder.group({\n      // Basic Information (Step 1)\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phoneNumber: [''],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', [Validators.required]],\n      // Professional Information (Step 2)\n      professionalTitle: ['', [Validators.required]],\n      headline: ['', [Validators.required, Validators.maxLength(220)]],\n      summary: ['', [Validators.required, Validators.maxLength(2000)]],\n      primarySpecialization: ['', [Validators.required]],\n      yearsOfExperience: [0, [Validators.required, Validators.min(0)]],\n      // Location Information (Step 3)\n      city: ['', [Validators.required]],\n      state: [''],\n      country: ['', [Validators.required]],\n      displayLocation: [''],\n      // Oracle-Specific Information (Step 4)\n      birthDate: ['', [Validators.required]],\n      birthTime: [''],\n      birthLocation: ['', [Validators.required]],\n      astrologicalSign: [''],\n      oracleTypes: [[], [Validators.required]],\n      languagesSpoken: [['Български'], [Validators.required]],\n      skills: [[], [Validators.required]],\n      // Contact & Business Information (Step 5)\n      website: [''],\n      portfolioUrl: [''],\n      businessStreet: [''],\n      businessCity: [''],\n      businessState: [''],\n      businessPostalCode: [''],\n      businessCountry: [''],\n      isBusinessAddressPublic: [false],\n      // Consultation Rates (Step 6)\n      hourlyRate: [0],\n      sessionRate: [0],\n      currency: ['BGN'],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  // Registration type switching\n  setRegistrationType(type) {\n    this.registrationType = type;\n    if (type === 'oracle') {\n      this.registerForm = this.createOracleForm();\n      this.totalSteps = 6;\n      this.currentStep = 1;\n    } else {\n      this.registerForm = this.createGeneralForm();\n      this.totalSteps = 1;\n      this.currentStep = 1;\n    }\n  }\n  // Step navigation for oracle registration\n  nextStep() {\n    if (this.currentStep < this.totalSteps && this.isCurrentStepValid()) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  isCurrentStepValid() {\n    if (this.registrationType === 'general') {\n      return this.registerForm.valid;\n    }\n    // Validate current step for oracle registration\n    const step1Fields = ['firstName', 'lastName', 'email', 'password', 'confirmPassword'];\n    const step2Fields = ['professionalTitle', 'headline', 'summary', 'primarySpecialization', 'yearsOfExperience'];\n    const step3Fields = ['city', 'country'];\n    const step4Fields = ['birthDate', 'birthLocation', 'oracleTypes', 'languagesSpoken', 'skills'];\n    const step5Fields = []; // All optional\n    const step6Fields = ['currency', 'acceptTerms'];\n    let fieldsToValidate = [];\n    switch (this.currentStep) {\n      case 1:\n        fieldsToValidate = step1Fields;\n        break;\n      case 2:\n        fieldsToValidate = step2Fields;\n        break;\n      case 3:\n        fieldsToValidate = step3Fields;\n        break;\n      case 4:\n        fieldsToValidate = step4Fields;\n        break;\n      case 5:\n        fieldsToValidate = step5Fields;\n        break;\n      case 6:\n        fieldsToValidate = step6Fields;\n        break;\n    }\n    return fieldsToValidate.every(field => {\n      const control = this.registerForm.get(field);\n      return control ? control.valid : true;\n    });\n  }\n  // Get step title in Bulgarian\n  getStepTitle() {\n    if (this.registrationType === 'general') {\n      return 'Създаване на акаунт';\n    }\n    switch (this.currentStep) {\n      case 1:\n        return 'Основна информация';\n      case 2:\n        return 'Професионална информация';\n      case 3:\n        return 'Местоположение';\n      case 4:\n        return 'Оракулска информация';\n      case 5:\n        return 'Контакти и бизнес';\n      case 6:\n        return 'Тарифи и условия';\n      default:\n        return 'Регистрация';\n    }\n  }\n  // Helper methods for oracle registration\n  getSpecializationOptions() {\n    return ['Астрология', 'Таро', 'Кристални лечения', 'Нумерология', 'Хиромантия', 'Рунология', 'Медиумизъм', 'Енергийно лечение', 'Аура четене', 'Духовно консултиране'];\n  }\n  getOracleTypeOptions() {\n    return ['Таро карти', 'Астрологични карти', 'Кристали', 'Руни', 'Нумерология', 'Хиромантия', 'Медитация', 'Енергийна работа', 'Аура четене', 'Духовно водачество'];\n  }\n  getLanguageOptions() {\n    return ['Български', 'Английски', 'Руски', 'Немски', 'Френски', 'Испански', 'Италиански', 'Турски'];\n  }\n  getAstrologicalSigns() {\n    return ['Овен', 'Телец', 'Близнаци', 'Рак', 'Лъв', 'Дева', 'Везни', 'Скорпион', 'Стрелец', 'Козирог', 'Водолей', 'Риби'];\n  }\n  // Time format toggle\n  toggleTimeFormat() {\n    this.timeFormat24h = !this.timeFormat24h;\n  }\n  // Skill management methods\n  addSkill(event, input) {\n    event.preventDefault();\n    const value = input.value.trim();\n    if (value) {\n      const currentSkills = this.registerForm.get('skills')?.value || [];\n      if (!currentSkills.includes(value)) {\n        const updatedSkills = [...currentSkills, value];\n        this.registerForm.get('skills')?.setValue(updatedSkills);\n        input.value = '';\n      }\n    }\n  }\n  addSkillFromInput(input) {\n    const value = input.value.trim();\n    if (value) {\n      const currentSkills = this.registerForm.get('skills')?.value || [];\n      if (!currentSkills.includes(value)) {\n        const updatedSkills = [...currentSkills, value];\n        this.registerForm.get('skills')?.setValue(updatedSkills);\n        input.value = '';\n      }\n    }\n  }\n  removeSkill(index) {\n    const currentSkills = this.registerForm.get('skills')?.value || [];\n    currentSkills.splice(index, 1);\n    this.registerForm.get('skills')?.setValue(currentSkills);\n  }\n  passwordMatchValidator(control) {\n    const password = control.get('password');\n    const confirmPassword = control.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      return {\n        'passwordMismatch': true\n      };\n    }\n    return null;\n  }\n  onSubmit() {\n    if (this.registrationType === 'oracle' && this.currentStep < this.totalSteps) {\n      // For oracle registration, move to next step if not on final step\n      this.nextStep();\n      return;\n    }\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      if (this.registrationType === 'general') {\n        this.submitGeneralRegistration();\n      } else {\n        this.submitOracleRegistration();\n      }\n    } else {\n      this.markFormGroupTouched();\n      this.snackBar.open('Моля, попълнете всички задължителни полета', 'Затвори', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n    }\n  }\n  submitGeneralRegistration() {\n    const registerRequest = {\n      firstName: this.registerForm.value.firstName,\n      lastName: this.registerForm.value.lastName,\n      email: this.registerForm.value.email,\n      phoneNumber: this.registerForm.value.phoneNumber || undefined,\n      password: this.registerForm.value.password,\n      confirmPassword: this.registerForm.value.confirmPassword,\n      acceptTerms: this.registerForm.value.acceptTerms\n    };\n    this.authService.register(registerRequest).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.success) {\n          this.snackBar.open('Регистрацията е успешна! Добре дошли в Оракул!', 'Затвори', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          // Auto-authenticate and redirect to profile page\n          console.log('Registration successful, user is now authenticated');\n          console.log('User info:', response.user);\n          // Redirect to profile edit page for completing profile\n          this.router.navigate(['/profile/edit']);\n        } else {\n          this.snackBar.open(response.message || 'Регистрацията неуспешна', 'Затвори', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open(error || 'Регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  submitOracleRegistration() {\n    const formValue = this.registerForm.value;\n    const oracleRequest = {\n      // Basic information\n      firstName: formValue.firstName,\n      lastName: formValue.lastName,\n      email: formValue.email,\n      phoneNumber: formValue.phoneNumber || undefined,\n      password: formValue.password,\n      confirmPassword: formValue.confirmPassword,\n      acceptTerms: formValue.acceptTerms,\n      // Professional information\n      professionalTitle: formValue.professionalTitle,\n      headline: formValue.headline,\n      summary: formValue.summary,\n      primarySpecialization: formValue.primarySpecialization,\n      yearsOfExperience: formValue.yearsOfExperience,\n      // Location information\n      city: formValue.city,\n      state: formValue.state,\n      country: formValue.country,\n      displayLocation: formValue.displayLocation || `${formValue.city}, ${formValue.country}`,\n      // Oracle-specific information\n      birthDate: formValue.birthDate,\n      birthTime: formValue.birthTime,\n      birthLocation: formValue.birthLocation,\n      astrologicalSign: formValue.astrologicalSign,\n      oracleTypes: formValue.oracleTypes,\n      languagesSpoken: formValue.languagesSpoken,\n      skills: formValue.skills,\n      // Contact & business information\n      website: formValue.website,\n      portfolioUrl: formValue.portfolioUrl,\n      businessAddress: {\n        street: formValue.businessStreet,\n        city: formValue.businessCity,\n        state: formValue.businessState,\n        postalCode: formValue.businessPostalCode,\n        country: formValue.businessCountry,\n        isPublic: formValue.isBusinessAddressPublic\n      },\n      // Consultation rates\n      consultationRates: {\n        hourlyRate: formValue.hourlyRate,\n        sessionRate: formValue.sessionRate,\n        currency: formValue.currency\n      }\n    };\n    // TODO: Implement oracle registration API call\n    // For now, use the general registration and then create profile\n    this.authService.register(oracleRequest).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.success) {\n          this.snackBar.open('Регистрацията като оракул е успешна! Добре дошли в Оракул!', 'Затвори', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          // Auto-authenticate and redirect to profile page\n          console.log('Oracle registration successful, user is now authenticated');\n          console.log('User info:', response.user);\n          // Redirect to profile edit page for completing/reviewing profile\n          this.router.navigate(['/profile/edit']);\n        } else {\n          this.snackBar.open(response.message || 'Регистрацията неуспешна', 'Затвори', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open(error || 'Регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  markFormGroupTouched() {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFirstNameErrorMessage() {\n    const control = this.registerForm.get('firstName');\n    if (control?.hasError('required')) {\n      return 'Първото име е задължително';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Първото име трябва да бъде поне 2 символа';\n    }\n    return '';\n  }\n  getLastNameErrorMessage() {\n    const control = this.registerForm.get('lastName');\n    if (control?.hasError('required')) {\n      return 'Фамилното име е задължително';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Фамилното име трябва да бъде поне 2 символа';\n    }\n    return '';\n  }\n  getEmailErrorMessage() {\n    const control = this.registerForm.get('email');\n    if (control?.hasError('required')) {\n      return 'Имейлът е задължителен';\n    }\n    if (control?.hasError('email')) {\n      return 'Моля, въведете валиден имейл адрес';\n    }\n    return '';\n  }\n  getPasswordErrorMessage() {\n    const control = this.registerForm.get('password');\n    if (control?.hasError('required')) {\n      return 'Паролата е задължителна';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Паролата трябва да бъде поне 6 символа';\n    }\n    return '';\n  }\n  getConfirmPasswordErrorMessage() {\n    const control = this.registerForm.get('confirmPassword');\n    if (control?.hasError('required')) {\n      return 'Моля, потвърдете паролата си';\n    }\n    if (this.registerForm.hasError('passwordMismatch')) {\n      return 'Паролите не съвпадат';\n    }\n    return '';\n  }\n  // Additional error message methods for oracle fields\n  getFieldErrorMessage(fieldName) {\n    const control = this.registerForm.get(fieldName);\n    if (!control) return '';\n    if (control.hasError('required')) {\n      return 'Това поле е задължително';\n    }\n    if (control.hasError('minlength')) {\n      return `Минимум ${control.errors?.['minlength'].requiredLength} символа`;\n    }\n    if (control.hasError('maxlength')) {\n      return `Максимум ${control.errors?.['maxlength'].requiredLength} символа`;\n    }\n    if (control.hasError('min')) {\n      return `Минимална стойност: ${control.errors?.['min'].min}`;\n    }\n    if (control.hasError('email')) {\n      return 'Моля, въведете валиден имейл адрес';\n    }\n    return '';\n  }\n  navigateToLogin() {\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: this.returnUrl\n      }\n    });\n  }\n  signUpWithGoogle() {\n    this.isLoading = true;\n    this.oauthService.signInWithGooglePopup().subscribe({\n      next: oauthUser => {\n        this.handleOAuthSignUp(oauthUser);\n      },\n      error: () => {\n        this.isLoading = false;\n        this.snackBar.open('Регистрацията с Google неуспешна. Моля, опитайте отново.', 'Затвори', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  signUpWithFacebook() {\n    this.isLoading = true;\n    this.oauthService.signInWithFacebook().subscribe({\n      next: oauthUser => {\n        this.handleOAuthSignUp(oauthUser);\n      },\n      error: () => {\n        this.isLoading = false;\n        this.snackBar.open('Регистрацията с Facebook неуспешна. Моля, опитайте отново.', 'Затвори', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  handleOAuthSignUp(oauthUser) {\n    const oauthRequest = {\n      provider: oauthUser.provider,\n      accessToken: oauthUser.accessToken,\n      email: oauthUser.email,\n      firstName: oauthUser.firstName,\n      lastName: oauthUser.lastName,\n      profilePictureUrl: oauthUser.profilePictureUrl\n    };\n    this.authService.loginWithOAuth(oauthRequest).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.success) {\n          const providerName = oauthUser.provider === 'google' ? 'Google' : 'Facebook';\n          this.snackBar.open(`Добре дошли в Оракул! Акаунтът е създаден с ${providerName}`, 'Затвори', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          // Auto-authenticate and redirect to profile page\n          console.log(`OAuth registration successful with ${providerName}, user is now authenticated`);\n          console.log('User info:', response.user);\n          // Redirect to profile edit page for completing profile\n          this.router.navigate(['/profile/edit']);\n        } else {\n          this.snackBar.open(response.message || 'OAuth регистрацията неуспешна', 'Затвори', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.snackBar.open(error || 'OAuth регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 22,\n      vars: 5,\n      consts: [[1, \"register-container\"], [1, \"register-card\"], [1, \"register-header\"], [1, \"register-icon\"], [4, \"ngIf\"], [\"class\", \"registration-type-selection\", 4, \"ngIf\"], [\"class\", \"register-form\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"register-actions\"], [1, \"login-section\"], [1, \"login-text\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"full-width\", 3, \"click\"], [\"mode\", \"determinate\", 1, \"step-progress\", 3, \"value\"], [1, \"registration-type-selection\"], [1, \"type-buttons-compact\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"type-button-compact\", \"general-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"type-button-compact\", \"oracle-button\", 3, \"click\"], [1, \"type-description\"], [1, \"register-form\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"form-step\", 4, \"ngIf\"], [\"class\", \"navigation-buttons\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", \"class\", \"full-width register-button\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"divider-container\", 4, \"ngIf\"], [\"class\", \"oauth-buttons\", 4, \"ngIf\"], [1, \"form-step\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u043F\\u044A\\u0440\\u0432\\u043E \\u0438\\u043C\\u0435\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0444\\u0430\\u043C\\u0438\\u043B\\u043D\\u043E \\u0438\\u043C\\u0435\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0438\\u044F \\u0438\\u043C\\u0435\\u0439\\u043B\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0435\\u043D \\u043D\\u043E\\u043C\\u0435\\u0440\", \"autocomplete\", \"tel\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0439\\u0442\\u0435 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"\\u041F\\u043E\\u0442\\u0432\\u044A\\u0440\\u0434\\u0435\\u0442\\u0435 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\\u0442\\u0430\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"class\", \"terms-section\", 4, \"ngIf\"], [1, \"terms-section\"], [\"formControlName\", \"acceptTerms\", \"color\", \"primary\"], [\"href\", \"#\", \"target\", \"_blank\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"professionalTitle\", \"placeholder\", \"\\u043D\\u0430\\u043F\\u0440. \\u0410\\u0441\\u0442\\u0440\\u043E\\u043B\\u043E\\u0433, \\u0422\\u0430\\u0440\\u043E \\u0447\\u0438\\u0442\\u0435\\u0446, \\u041A\\u0440\\u0438\\u0441\\u0442\\u0430\\u043B\\u0435\\u043D \\u043B\\u0435\\u0447\\u0438\\u0442\\u0435\\u043B\"], [\"formControlName\", \"primarySpecialization\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"headline\", \"placeholder\", \"\\u041A\\u0440\\u0430\\u0442\\u043A\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0432\\u0430\\u0448\\u0438\\u0442\\u0435 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0438\", \"maxlength\", \"220\"], [\"matInput\", \"\", \"formControlName\", \"summary\", \"placeholder\", \"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0432\\u0430\\u0448\\u0438\\u044F \\u043E\\u043F\\u0438\\u0442 \\u0438 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0438\", \"rows\", \"4\", \"maxlength\", \"2000\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"yearsOfExperience\", \"placeholder\", \"0\", \"min\", \"0\", \"max\", \"50\"], [3, \"value\"], [1, \"location-row\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"city\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0433\\u0440\\u0430\\u0434\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"state\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u043E\\u0431\\u043B\\u0430\\u0441\\u0442\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"country\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0434\\u044A\\u0440\\u0436\\u0430\\u0432\\u0430\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"displayLocation\", \"placeholder\", \"\\u041A\\u0430\\u043A \\u0434\\u0430 \\u0441\\u0435 \\u043F\\u043E\\u043A\\u0430\\u0437\\u0432\\u0430 \\u043C\\u0435\\u0441\\u0442\\u043E\\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435\\u0442\\u043E \\u0432\\u0438 \\u043F\\u0443\\u0431\\u043B\\u0438\\u0447\\u043D\\u043E\"], [1, \"birth-info-row\"], [\"matInput\", \"\", \"formControlName\", \"birthDate\", \"placeholder\", \"\\u0434\\u0434.\\u043C\\u043C.\\u0433\\u0433\\u0433\\u0433\", \"type\", \"text\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"matDatepickerToggleIcon\", \"\"], [\"birthDatePicker\", \"\"], [1, \"time-input-container\"], [\"matInput\", \"\", \"type\", \"time\", \"formControlName\", \"birthTime\", 3, \"step\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matSuffix\", \"\", 3, \"title\", \"click\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"birthLocation\", \"placeholder\", \"\\u0413\\u0440\\u0430\\u0434 \\u0438 \\u0434\\u044A\\u0440\\u0436\\u0430\\u0432\\u0430 \\u043D\\u0430 \\u0440\\u0430\\u0436\\u0434\\u0430\\u043D\\u0435\"], [\"formControlName\", \"astrologicalSign\"], [\"formControlName\", \"oracleTypes\", \"multiple\", \"\"], [\"formControlName\", \"languagesSpoken\", \"multiple\", \"\"], [1, \"skills-section\"], [\"class\", \"skills-chips\", 4, \"ngIf\"], [\"matInput\", \"\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0443\\u043C\\u0435\\u043D\\u0438\\u0435 \\u0438 \\u043D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 Enter \\u0438\\u043B\\u0438 \\u043A\\u043B\\u0438\\u043A\\u043D\\u0435\\u0442\\u0435 +\", 3, \"keydown.enter\"], [\"skillInput\", \"\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matSuffix\", \"\", \"title\", \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438 \\u0443\\u043C\\u0435\\u043D\\u0438\\u0435\", 3, \"disabled\", \"click\"], [1, \"skills-chips\"], [\"removable\", \"true\", 3, \"removed\", 4, \"ngFor\", \"ngForOf\"], [\"removable\", \"true\", 3, \"removed\"], [\"matChipRemove\", \"\"], [\"matInput\", \"\", \"type\", \"url\", \"formControlName\", \"website\", \"placeholder\", \"https://\\u0432\\u0430\\u0448\\u0438\\u044F-\\u0441\\u0430\\u0439\\u0442.com\"], [\"matInput\", \"\", \"type\", \"url\", \"formControlName\", \"portfolioUrl\", \"placeholder\", \"https://\\u043F\\u043E\\u0440\\u0442\\u0444\\u043E\\u043B\\u0438\\u043E.com\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"businessStreet\", \"placeholder\", \"\\u0423\\u043B\\u0438\\u0446\\u0430 \\u0438 \\u043D\\u043E\\u043C\\u0435\\u0440\"], [1, \"business-address-row\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"businessCity\", \"placeholder\", \"\\u0413\\u0440\\u0430\\u0434\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"businessPostalCode\", \"placeholder\", \"\\u041F\\u043E\\u0449\\u0435\\u043D\\u0441\\u043A\\u0438 \\u043A\\u043E\\u0434\"], [\"formControlName\", \"isBusinessAddressPublic\", \"color\", \"primary\"], [1, \"rates-row\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"hourlyRate\", \"placeholder\", \"0\", \"min\", \"0\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"sessionRate\", \"placeholder\", \"0\", \"min\", \"0\"], [\"formControlName\", \"currency\"], [\"value\", \"BGN\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [1, \"navigation-buttons\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"spacer\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"register-button\", 3, \"disabled\"], [1, \"divider-container\"], [1, \"divider-text\"], [1, \"oauth-buttons\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"google-button\", 3, \"disabled\", \"click\"], [\"src\", \"https://developers.google.com/identity/images/g-logo.png\", \"alt\", \"Google\", 1, \"oauth-icon\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"facebook-button\", 3, \"disabled\", \"click\"], [1, \"oauth-icon\", \"facebook-icon\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\", 2)(3, \"mat-card-title\")(4, \"mat-icon\", 3);\n          i0.ɵɵtext(5, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtemplate(8, RegisterComponent_span_8_Template, 2, 0, \"span\", 4);\n          i0.ɵɵtemplate(9, RegisterComponent_span_9_Template, 3, 3, \"span\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"mat-card-content\");\n          i0.ɵɵtemplate(11, RegisterComponent_div_11_Template, 17, 6, \"div\", 5);\n          i0.ɵɵtemplate(12, RegisterComponent_form_12_Template, 11, 11, \"form\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-card-actions\", 7);\n          i0.ɵɵelement(14, \"mat-divider\");\n          i0.ɵɵelementStart(15, \"div\", 8)(16, \"p\", 9);\n          i0.ɵɵtext(17, \"\\u0412\\u0435\\u0447\\u0435 \\u0438\\u043C\\u0430\\u0442\\u0435 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_18_listener() {\n            return ctx.navigateToLogin();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \" \\u0412\\u043B\\u0435\\u0437\\u0442\\u0435 \\u0432 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442\\u0430 \\u0441\\u0438 \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStepTitle(), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.registrationType === \"general\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOracleRegistration());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOracleRegistration() || ctx.registrationType === \"general\");\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatIcon, i10.MatProgressSpinner, i11.MatProgressBar, i12.MatFormField, i12.MatLabel, i12.MatHint, i12.MatError, i12.MatSuffix, i13.MatInput, i14.MatSelect, i15.MatOption, i16.MatCheckbox, i17.MatDivider, i18.MatChip, i18.MatChipRemove, i18.MatChipSet, i19.MatDatepicker, i19.MatDatepickerInput, i19.MatDatepickerToggle, i19.MatDatepickerToggleIcon],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  min-height: 100vh;\\r\\n  padding: 20px;\\r\\n  background: var(--theme-gradient-auth);\\r\\n}\\r\\n\\r\\n.register-card[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  max-width: 500px;\\r\\n  padding: 0;\\r\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\r\\n  border-radius: 16px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.register-header[_ngcontent-%COMP%] {\\r\\n  background: var(--theme-gradient-auth);\\r\\n  color: white;\\r\\n  padding: 24px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.register-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 8px;\\r\\n  margin-bottom: 8px;\\r\\n  font-size: 24px;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.register-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 28px;\\r\\n  width: 28px;\\r\\n  height: 28px;\\r\\n}\\r\\n\\r\\n.register-header[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.8);\\r\\n  font-size: 14px;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n.register-form[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.name-row[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.full-width[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.half-width[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n.terms-section[_ngcontent-%COMP%] {\\r\\n  margin: 8px 0;\\r\\n}\\r\\n\\r\\n.terms-section[_ngcontent-%COMP%]   .mat-checkbox[_ngcontent-%COMP%] {\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.terms-section[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\r\\n  color: #673ab7;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.terms-section[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\r\\n  text-decoration: underline;\\r\\n}\\r\\n\\r\\n.register-button[_ngcontent-%COMP%] {\\r\\n  height: 48px;\\r\\n  font-size: 16px;\\r\\n  font-weight: 500;\\r\\n  margin-top: 8px;\\r\\n}\\r\\n\\r\\n.register-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n.register-actions[_ngcontent-%COMP%] {\\r\\n  padding: 0 24px 24px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.login-section[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.login-text[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.mat-form-field[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 8px;\\r\\n}\\r\\n\\r\\n.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\r\\n  border-radius: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-error[_ngcontent-%COMP%] {\\r\\n  font-size: 12px;\\r\\n  margin-top: 4px;\\r\\n}\\r\\n\\r\\n\\r\\n.mat-spinner[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.divider-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  margin: 24px 0 16px;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.divider-text[_ngcontent-%COMP%] {\\r\\n  color: rgba(0, 0, 0, 0.6);\\r\\n  font-size: 14px;\\r\\n  white-space: nowrap;\\r\\n}\\r\\n\\r\\n.oauth-buttons[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.oauth-button[_ngcontent-%COMP%] {\\r\\n  height: 48px;\\r\\n  font-size: 14px;\\r\\n  font-weight: 500;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  gap: 12px;\\r\\n  border-radius: 8px;\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n\\r\\n.oauth-icon[_ngcontent-%COMP%] {\\r\\n  width: 20px;\\r\\n  height: 20px;\\r\\n}\\r\\n\\r\\n.google-button[_ngcontent-%COMP%] {\\r\\n  border-color: #dadce0;\\r\\n  color: #3c4043;\\r\\n  background-color: #fff;\\r\\n}\\r\\n\\r\\n.google-button[_ngcontent-%COMP%]:hover:not([disabled]) {\\r\\n  background-color: #f8f9fa;\\r\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.facebook-button[_ngcontent-%COMP%] {\\r\\n  border-color: #1877f2;\\r\\n  color: #1877f2;\\r\\n  background-color: #fff;\\r\\n}\\r\\n\\r\\n.facebook-button[_ngcontent-%COMP%]:hover:not([disabled]) {\\r\\n  background-color: #f0f2f5;\\r\\n}\\r\\n\\r\\n.facebook-icon[_ngcontent-%COMP%] {\\r\\n  color: #1877f2;\\r\\n  font-size: 20px;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 600px) {\\r\\n  .register-container[_ngcontent-%COMP%] {\\r\\n    padding: 16px;\\r\\n  }\\r\\n\\r\\n  .register-card[_ngcontent-%COMP%] {\\r\\n    max-width: 100%;\\r\\n  }\\r\\n\\r\\n  .register-form[_ngcontent-%COMP%] {\\r\\n    padding: 20px;\\r\\n  }\\r\\n\\r\\n  .register-actions[_ngcontent-%COMP%] {\\r\\n    padding: 0 20px 20px;\\r\\n  }\\r\\n\\r\\n  .name-row[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 0;\\r\\n  }\\r\\n\\r\\n  .half-width[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.registration-type-selection[_ngcontent-%COMP%] {\\r\\n  padding: 24px;\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.registration-type-selection[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n  color: var(--theme-text-primary);\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.type-buttons-compact[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 12px;\\r\\n  justify-content: center;\\r\\n  margin-bottom: 12px;\\r\\n}\\r\\n\\r\\n.type-button-compact[_ngcontent-%COMP%] {\\r\\n  height: 40px;\\r\\n  padding: 0 16px;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  border-radius: 20px;\\r\\n  transition: all 0.3s ease;\\r\\n  font-size: 14px;\\r\\n  font-weight: 500;\\r\\n  min-width: 140px;\\r\\n}\\r\\n\\r\\n.type-button-compact.selected[_ngcontent-%COMP%] {\\r\\n  background-color: var(--theme-primary);\\r\\n  color: white;\\r\\n  border-color: var(--theme-primary);\\r\\n}\\r\\n\\r\\n.type-button-compact[_ngcontent-%COMP%]:not(.selected) {\\r\\n  border-color: var(--theme-primary);\\r\\n  color: var(--theme-primary);\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.type-button-compact[_ngcontent-%COMP%]:not(.selected):hover {\\r\\n  background-color: var(--theme-primary-light);\\r\\n}\\r\\n\\r\\n.type-button-compact[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 18px;\\r\\n  width: 18px;\\r\\n  height: 18px;\\r\\n}\\r\\n\\r\\n.type-description[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  font-size: 13px;\\r\\n  color: var(--theme-text-secondary);\\r\\n  opacity: 0.8;\\r\\n  min-height: 20px;\\r\\n}\\r\\n\\r\\n\\r\\n.type-buttons[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.type-button[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  max-width: 200px;\\r\\n  height: auto;\\r\\n  padding: 20px 16px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n  border-radius: 12px;\\r\\n  transition: all 0.3s ease;\\r\\n  border: 2px solid transparent;\\r\\n}\\r\\n\\r\\n.type-button.selected[_ngcontent-%COMP%] {\\r\\n  border-color: var(--theme-primary);\\r\\n  background-color: var(--theme-primary-light);\\r\\n}\\r\\n\\r\\n.type-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 32px;\\r\\n  width: 32px;\\r\\n  height: 32px;\\r\\n}\\r\\n\\r\\n.button-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 8px 0;\\r\\n  font-size: 16px;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n.button-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  font-size: 12px;\\r\\n  opacity: 0.8;\\r\\n  text-align: center;\\r\\n  line-height: 1.3;\\r\\n}\\r\\n\\r\\n\\r\\n.step-progress[_ngcontent-%COMP%] {\\r\\n  margin-top: 8px;\\r\\n  height: 4px;\\r\\n  border-radius: 2px;\\r\\n}\\r\\n\\r\\n\\r\\n.form-step[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.form-step[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 16px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n  font-weight: 500;\\r\\n  font-size: 18px;\\r\\n}\\r\\n\\r\\n.form-step[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 16px 0 8px 0;\\r\\n  color: var(--theme-text-secondary);\\r\\n  font-weight: 500;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n\\r\\n.location-row[_ngcontent-%COMP%], .birth-info-row[_ngcontent-%COMP%], .business-address-row[_ngcontent-%COMP%], .rates-row[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n\\r\\n.time-input-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.time-input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n.time-input-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\r\\n  margin-left: 8px;\\r\\n}\\r\\n\\r\\n\\r\\n.navigation-buttons[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  margin-top: 24px;\\r\\n  padding-top: 16px;\\r\\n  border-top: 1px solid var(--theme-divider);\\r\\n}\\r\\n\\r\\n.spacer[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n\\r\\n.skills-section[_ngcontent-%COMP%] {\\r\\n  margin: 16px 0;\\r\\n}\\r\\n\\r\\n.skills-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 12px 0;\\r\\n  color: var(--theme-text-primary);\\r\\n  font-weight: 500;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.skills-chips[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 12px;\\r\\n}\\r\\n\\r\\n.mat-chip-set[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-wrap: wrap;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.mat-chip[_ngcontent-%COMP%] {\\r\\n  background-color: var(--theme-primary-light);\\r\\n  color: var(--theme-primary);\\r\\n  border-radius: 16px;\\r\\n  font-size: 13px;\\r\\n}\\r\\n\\r\\n.mat-chip[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 16px;\\r\\n  width: 16px;\\r\\n  height: 16px;\\r\\n}\\r\\n\\r\\n\\r\\n  .success-snackbar {\\r\\n  background-color: #4caf50;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n  .error-snackbar {\\r\\n  background-color: #f44336;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 600px) {\\r\\n  .type-buttons-compact[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: center;\\r\\n  }\\r\\n\\r\\n  .type-button-compact[_ngcontent-%COMP%] {\\r\\n    min-width: 200px;\\r\\n  }\\r\\n\\r\\n  .type-buttons[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n  }\\r\\n\\r\\n  .type-button[_ngcontent-%COMP%] {\\r\\n    max-width: 100%;\\r\\n  }\\r\\n\\r\\n  .location-row[_ngcontent-%COMP%], .birth-info-row[_ngcontent-%COMP%], .business-address-row[_ngcontent-%COMP%], .rates-row[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 0;\\r\\n  }\\r\\n\\r\\n  .navigation-buttons[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    gap: 12px;\\r\\n  }\\r\\n\\r\\n  .spacer[_ngcontent-%COMP%] {\\r\\n    display: none;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXV0aC9yZWdpc3Rlci9yZWdpc3Rlci5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBYTtFQUNiLHVCQUF1QjtFQUN2QixtQkFBbUI7RUFDbkIsaUJBQWlCO0VBQ2pCLGFBQWE7RUFDYixzQ0FBc0M7QUFDeEM7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsZ0JBQWdCO0VBQ2hCLFVBQVU7RUFDVix5Q0FBeUM7RUFDekMsbUJBQW1CO0VBQ25CLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLHNDQUFzQztFQUN0QyxZQUFZO0VBQ1osYUFBYTtFQUNiLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLFFBQVE7RUFDUixrQkFBa0I7RUFDbEIsZUFBZTtFQUNmLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGVBQWU7RUFDZixXQUFXO0VBQ1gsWUFBWTtBQUNkOztBQUVBO0VBQ0UsK0JBQStCO0VBQy9CLGVBQWU7RUFDZixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsU0FBUztBQUNYOztBQUVBO0VBQ0UsV0FBVztBQUNiOztBQUVBO0VBQ0UsT0FBTztBQUNUOztBQUVBO0VBQ0UsYUFBYTtBQUNmOztBQUVBO0VBQ0UsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSwwQkFBMEI7QUFDNUI7O0FBRUE7RUFDRSxZQUFZO0VBQ1osZUFBZTtFQUNmLGdCQUFnQjtFQUNoQixlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0Usb0JBQW9CO0VBQ3BCLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsU0FBUztBQUNYOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsU0FBUztBQUNYOztBQUVBO0VBQ0UsU0FBUztFQUNULHlCQUF5QjtFQUN6QixlQUFlO0FBQ2pCOztBQUVBO0VBQ0Usa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0Usa0JBQWtCO0FBQ3BCOztBQUVBLGtCQUFrQjtBQUNsQjtFQUNFLGVBQWU7RUFDZixlQUFlO0FBQ2pCOztBQUVBLDRCQUE0QjtBQUM1QjtFQUNFLGlCQUFpQjtBQUNuQjs7QUFFQSwwQkFBMEI7QUFDMUI7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLG1CQUFtQjtFQUNuQixTQUFTO0FBQ1g7O0FBRUE7RUFDRSx5QkFBeUI7RUFDekIsZUFBZTtFQUNmLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsU0FBUztBQUNYOztBQUVBO0VBQ0UsWUFBWTtFQUNaLGVBQWU7RUFDZixnQkFBZ0I7RUFDaEIsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsU0FBUztFQUNULGtCQUFrQjtFQUNsQix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtBQUNkOztBQUVBO0VBQ0UscUJBQXFCO0VBQ3JCLGNBQWM7RUFDZCxzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSx5QkFBeUI7RUFDekIsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0UscUJBQXFCO0VBQ3JCLGNBQWM7RUFDZCxzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSx5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsZUFBZTtBQUNqQjs7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRTtJQUNFLGFBQWE7RUFDZjs7RUFFQTtJQUNFLGVBQWU7RUFDakI7O0VBRUE7SUFDRSxhQUFhO0VBQ2Y7O0VBRUE7SUFDRSxvQkFBb0I7RUFDdEI7O0VBRUE7SUFDRSxzQkFBc0I7SUFDdEIsTUFBTTtFQUNSOztFQUVBO0lBQ0UsV0FBVztFQUNiO0FBQ0Y7O0FBRUEsZ0NBQWdDO0FBQ2hDO0VBQ0UsYUFBYTtFQUNiLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixnQ0FBZ0M7RUFDaEMsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLFNBQVM7RUFDVCx1QkFBdUI7RUFDdkIsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsWUFBWTtFQUNaLGVBQWU7RUFDZixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFFBQVE7RUFDUixtQkFBbUI7RUFDbkIseUJBQXlCO0VBQ3pCLGVBQWU7RUFDZixnQkFBZ0I7RUFDaEIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0Usc0NBQXNDO0VBQ3RDLFlBQVk7RUFDWixrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRSxrQ0FBa0M7RUFDbEMsMkJBQTJCO0VBQzNCLDZCQUE2QjtBQUMvQjs7QUFFQTtFQUNFLDRDQUE0QztBQUM5Qzs7QUFFQTtFQUNFLGVBQWU7RUFDZixXQUFXO0VBQ1gsWUFBWTtBQUNkOztBQUVBO0VBQ0UsU0FBUztFQUNULGVBQWU7RUFDZixrQ0FBa0M7RUFDbEMsWUFBWTtFQUNaLGdCQUFnQjtBQUNsQjs7QUFFQSw2Q0FBNkM7QUFDN0M7RUFDRSxhQUFhO0VBQ2IsU0FBUztFQUNULHVCQUF1QjtBQUN6Qjs7QUFFQTtFQUNFLE9BQU87RUFDUCxnQkFBZ0I7RUFDaEIsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQixhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQixTQUFTO0VBQ1QsbUJBQW1CO0VBQ25CLHlCQUF5QjtFQUN6Qiw2QkFBNkI7QUFDL0I7O0FBRUE7RUFDRSxrQ0FBa0M7RUFDbEMsNENBQTRDO0FBQzlDOztBQUVBO0VBQ0UsZUFBZTtFQUNmLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsZUFBZTtFQUNmLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFNBQVM7RUFDVCxlQUFlO0VBQ2YsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUEsa0JBQWtCO0FBQ2xCO0VBQ0UsZUFBZTtFQUNmLFdBQVc7RUFDWCxrQkFBa0I7QUFDcEI7O0FBRUEsZUFBZTtBQUNmO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsZ0NBQWdDO0VBQ2hDLGdCQUFnQjtFQUNoQixlQUFlO0FBQ2pCOztBQUVBO0VBQ0Usb0JBQW9CO0VBQ3BCLGtDQUFrQztFQUNsQyxnQkFBZ0I7RUFDaEIsZUFBZTtBQUNqQjs7QUFFQSwyQkFBMkI7QUFDM0I7Ozs7RUFJRSxhQUFhO0VBQ2IsU0FBUztBQUNYOztBQUVBLHlCQUF5QjtBQUN6QjtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsV0FBVztBQUNiOztBQUVBO0VBQ0UsT0FBTztBQUNUOztBQUVBO0VBQ0UsZ0JBQWdCO0FBQ2xCOztBQUVBLHVCQUF1QjtBQUN2QjtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsU0FBUztFQUNULGdCQUFnQjtFQUNoQixpQkFBaUI7RUFDakIsMENBQTBDO0FBQzVDOztBQUVBO0VBQ0UsT0FBTztBQUNUOztBQUVBLDJCQUEyQjtBQUMzQjtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsZ0NBQWdDO0VBQ2hDLGdCQUFnQjtFQUNoQixlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLGVBQWU7RUFDZixRQUFRO0FBQ1Y7O0FBRUE7RUFDRSw0Q0FBNEM7RUFDNUMsMkJBQTJCO0VBQzNCLG1CQUFtQjtFQUNuQixlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsZUFBZTtFQUNmLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7O0FBRUEsMkJBQTJCO0FBQzNCO0VBQ0UseUJBQXlCO0VBQ3pCLFlBQVk7QUFDZDs7QUFFQTtFQUNFLHlCQUF5QjtFQUN6QixZQUFZO0FBQ2Q7O0FBRUEsOENBQThDO0FBQzlDO0VBQ0U7SUFDRSxzQkFBc0I7SUFDdEIsbUJBQW1CO0VBQ3JCOztFQUVBO0lBQ0UsZ0JBQWdCO0VBQ2xCOztFQUVBO0lBQ0Usc0JBQXNCO0VBQ3hCOztFQUVBO0lBQ0UsZUFBZTtFQUNqQjs7RUFFQTs7OztJQUlFLHNCQUFzQjtJQUN0QixNQUFNO0VBQ1I7O0VBRUE7SUFDRSxzQkFBc0I7SUFDdEIsU0FBUztFQUNYOztFQUVBO0lBQ0UsYUFBYTtFQUNmO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIucmVnaXN0ZXItY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgcGFkZGluZzogMjBweDtcclxuICBiYWNrZ3JvdW5kOiB2YXIoLS10aGVtZS1ncmFkaWVudC1hdXRoKTtcclxufVxyXG5cclxuLnJlZ2lzdGVyLWNhcmQge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG1heC13aWR0aDogNTAwcHg7XHJcbiAgcGFkZGluZzogMDtcclxuICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICBib3JkZXItcmFkaXVzOiAxNnB4O1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuXHJcbi5yZWdpc3Rlci1oZWFkZXIge1xyXG4gIGJhY2tncm91bmQ6IHZhcigtLXRoZW1lLWdyYWRpZW50LWF1dGgpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxuICBwYWRkaW5nOiAyNHB4O1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxufVxyXG5cclxuLnJlZ2lzdGVyLWhlYWRlciAubWF0LWNhcmQtdGl0bGUge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBnYXA6IDhweDtcclxuICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgZm9udC1zaXplOiAyNHB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbn1cclxuXHJcbi5yZWdpc3Rlci1pY29uIHtcclxuICBmb250LXNpemU6IDI4cHg7XHJcbiAgd2lkdGg6IDI4cHg7XHJcbiAgaGVpZ2h0OiAyOHB4O1xyXG59XHJcblxyXG4ucmVnaXN0ZXItaGVhZGVyIC5tYXQtY2FyZC1zdWJ0aXRsZSB7XHJcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgbWFyZ2luOiAwO1xyXG59XHJcblxyXG4ucmVnaXN0ZXItZm9ybSB7XHJcbiAgcGFkZGluZzogMjRweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgZ2FwOiAxNnB4O1xyXG59XHJcblxyXG4ubmFtZS1yb3cge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZ2FwOiAxNnB4O1xyXG59XHJcblxyXG4uZnVsbC13aWR0aCB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbi5oYWxmLXdpZHRoIHtcclxuICBmbGV4OiAxO1xyXG59XHJcblxyXG4udGVybXMtc2VjdGlvbiB7XHJcbiAgbWFyZ2luOiA4cHggMDtcclxufVxyXG5cclxuLnRlcm1zLXNlY3Rpb24gLm1hdC1jaGVja2JveCB7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG59XHJcblxyXG4udGVybXMtc2VjdGlvbiBhIHtcclxuICBjb2xvcjogIzY3M2FiNztcclxuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbn1cclxuXHJcbi50ZXJtcy1zZWN0aW9uIGE6aG92ZXIge1xyXG4gIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG59XHJcblxyXG4ucmVnaXN0ZXItYnV0dG9uIHtcclxuICBoZWlnaHQ6IDQ4cHg7XHJcbiAgZm9udC1zaXplOiAxNnB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgbWFyZ2luLXRvcDogOHB4O1xyXG59XHJcblxyXG4ucmVnaXN0ZXItYnV0dG9uIC5tYXQtaWNvbiB7XHJcbiAgbWFyZ2luLXJpZ2h0OiA4cHg7XHJcbn1cclxuXHJcbi5yZWdpc3Rlci1hY3Rpb25zIHtcclxuICBwYWRkaW5nOiAwIDI0cHggMjRweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgZ2FwOiAxNnB4O1xyXG59XHJcblxyXG4ubG9naW4tc2VjdGlvbiB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiAxMnB4O1xyXG59XHJcblxyXG4ubG9naW4tdGV4dCB7XHJcbiAgbWFyZ2luOiAwO1xyXG4gIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNik7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG59XHJcblxyXG4ubWF0LWZvcm0tZmllbGQge1xyXG4gIG1hcmdpbi1ib3R0b206IDhweDtcclxufVxyXG5cclxuLm1hdC1mb3JtLWZpZWxkLWFwcGVhcmFuY2Utb3V0bGluZSAubWF0LWZvcm0tZmllbGQtb3V0bGluZSB7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG59XHJcblxyXG4vKiBFcnJvciBzdHlsaW5nICovXHJcbi5tYXQtZXJyb3Ige1xyXG4gIGZvbnQtc2l6ZTogMTJweDtcclxuICBtYXJnaW4tdG9wOiA0cHg7XHJcbn1cclxuXHJcbi8qIExvYWRpbmcgc3Bpbm5lciBzdHlsaW5nICovXHJcbi5tYXQtc3Bpbm5lciB7XHJcbiAgbWFyZ2luLXJpZ2h0OiA4cHg7XHJcbn1cclxuXHJcbi8qIE9BdXRoIEJ1dHRvbnMgU3R5bGluZyAqL1xyXG4uZGl2aWRlci1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBtYXJnaW46IDI0cHggMCAxNnB4O1xyXG4gIGdhcDogMTZweDtcclxufVxyXG5cclxuLmRpdmlkZXItdGV4dCB7XHJcbiAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMC42KTtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxufVxyXG5cclxuLm9hdXRoLWJ1dHRvbnMge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBnYXA6IDEycHg7XHJcbn1cclxuXHJcbi5vYXV0aC1idXR0b24ge1xyXG4gIGhlaWdodDogNDhweDtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgZ2FwOiAxMnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG59XHJcblxyXG4ub2F1dGgtaWNvbiB7XHJcbiAgd2lkdGg6IDIwcHg7XHJcbiAgaGVpZ2h0OiAyMHB4O1xyXG59XHJcblxyXG4uZ29vZ2xlLWJ1dHRvbiB7XHJcbiAgYm9yZGVyLWNvbG9yOiAjZGFkY2UwO1xyXG4gIGNvbG9yOiAjM2M0MDQzO1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XHJcbn1cclxuXHJcbi5nb29nbGUtYnV0dG9uOmhvdmVyOm5vdChbZGlzYWJsZWRdKSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICBib3gtc2hhZG93OiAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcblxyXG4uZmFjZWJvb2stYnV0dG9uIHtcclxuICBib3JkZXItY29sb3I6ICMxODc3ZjI7XHJcbiAgY29sb3I6ICMxODc3ZjI7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxufVxyXG5cclxuLmZhY2Vib29rLWJ1dHRvbjpob3Zlcjpub3QoW2Rpc2FibGVkXSkge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmMGYyZjU7XHJcbn1cclxuXHJcbi5mYWNlYm9vay1pY29uIHtcclxuICBjb2xvcjogIzE4NzdmMjtcclxuICBmb250LXNpemU6IDIwcHg7XHJcbn1cclxuXHJcbi8qIFJlc3BvbnNpdmUgZGVzaWduICovXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA2MDBweCkge1xyXG4gIC5yZWdpc3Rlci1jb250YWluZXIge1xyXG4gICAgcGFkZGluZzogMTZweDtcclxuICB9XHJcblxyXG4gIC5yZWdpc3Rlci1jYXJkIHtcclxuICAgIG1heC13aWR0aDogMTAwJTtcclxuICB9XHJcblxyXG4gIC5yZWdpc3Rlci1mb3JtIHtcclxuICAgIHBhZGRpbmc6IDIwcHg7XHJcbiAgfVxyXG5cclxuICAucmVnaXN0ZXItYWN0aW9ucyB7XHJcbiAgICBwYWRkaW5nOiAwIDIwcHggMjBweDtcclxuICB9XHJcblxyXG4gIC5uYW1lLXJvdyB7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgZ2FwOiAwO1xyXG4gIH1cclxuXHJcbiAgLmhhbGYtd2lkdGgge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBSZWdpc3RyYXRpb24gVHlwZSBTZWxlY3Rpb24gKi9cclxuLnJlZ2lzdHJhdGlvbi10eXBlLXNlbGVjdGlvbiB7XHJcbiAgcGFkZGluZzogMjRweDtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbn1cclxuXHJcbi5yZWdpc3RyYXRpb24tdHlwZS1zZWxlY3Rpb24gaDMge1xyXG4gIG1hcmdpbi1ib3R0b206IDE2cHg7XHJcbiAgY29sb3I6IHZhcigtLXRoZW1lLXRleHQtcHJpbWFyeSk7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxufVxyXG5cclxuLnR5cGUtYnV0dG9ucy1jb21wYWN0IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGdhcDogMTJweDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBtYXJnaW4tYm90dG9tOiAxMnB4O1xyXG59XHJcblxyXG4udHlwZS1idXR0b24tY29tcGFjdCB7XHJcbiAgaGVpZ2h0OiA0MHB4O1xyXG4gIHBhZGRpbmc6IDAgMTZweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiA4cHg7XHJcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIG1pbi13aWR0aDogMTQwcHg7XHJcbn1cclxuXHJcbi50eXBlLWJ1dHRvbi1jb21wYWN0LnNlbGVjdGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS10aGVtZS1wcmltYXJ5KTtcclxuICBjb2xvcjogd2hpdGU7XHJcbiAgYm9yZGVyLWNvbG9yOiB2YXIoLS10aGVtZS1wcmltYXJ5KTtcclxufVxyXG5cclxuLnR5cGUtYnV0dG9uLWNvbXBhY3Q6bm90KC5zZWxlY3RlZCkge1xyXG4gIGJvcmRlci1jb2xvcjogdmFyKC0tdGhlbWUtcHJpbWFyeSk7XHJcbiAgY29sb3I6IHZhcigtLXRoZW1lLXByaW1hcnkpO1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xyXG59XHJcblxyXG4udHlwZS1idXR0b24tY29tcGFjdDpub3QoLnNlbGVjdGVkKTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tdGhlbWUtcHJpbWFyeS1saWdodCk7XHJcbn1cclxuXHJcbi50eXBlLWJ1dHRvbi1jb21wYWN0IC5tYXQtaWNvbiB7XHJcbiAgZm9udC1zaXplOiAxOHB4O1xyXG4gIHdpZHRoOiAxOHB4O1xyXG4gIGhlaWdodDogMThweDtcclxufVxyXG5cclxuLnR5cGUtZGVzY3JpcHRpb24ge1xyXG4gIG1hcmdpbjogMDtcclxuICBmb250LXNpemU6IDEzcHg7XHJcbiAgY29sb3I6IHZhcigtLXRoZW1lLXRleHQtc2Vjb25kYXJ5KTtcclxuICBvcGFjaXR5OiAwLjg7XHJcbiAgbWluLWhlaWdodDogMjBweDtcclxufVxyXG5cclxuLyogTGVnYWN5IHN0eWxlcyBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eSAqL1xyXG4udHlwZS1idXR0b25zIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGdhcDogMTZweDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG5cclxuLnR5cGUtYnV0dG9uIHtcclxuICBmbGV4OiAxO1xyXG4gIG1heC13aWR0aDogMjAwcHg7XHJcbiAgaGVpZ2h0OiBhdXRvO1xyXG4gIHBhZGRpbmc6IDIwcHggMTZweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDEycHg7XHJcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIGJvcmRlcjogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xyXG59XHJcblxyXG4udHlwZS1idXR0b24uc2VsZWN0ZWQge1xyXG4gIGJvcmRlci1jb2xvcjogdmFyKC0tdGhlbWUtcHJpbWFyeSk7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tdGhlbWUtcHJpbWFyeS1saWdodCk7XHJcbn1cclxuXHJcbi50eXBlLWJ1dHRvbiAubWF0LWljb24ge1xyXG4gIGZvbnQtc2l6ZTogMzJweDtcclxuICB3aWR0aDogMzJweDtcclxuICBoZWlnaHQ6IDMycHg7XHJcbn1cclxuXHJcbi5idXR0b24tY29udGVudCBoNCB7XHJcbiAgbWFyZ2luOiAwIDAgOHB4IDA7XHJcbiAgZm9udC1zaXplOiAxNnB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbn1cclxuXHJcbi5idXR0b24tY29udGVudCBwIHtcclxuICBtYXJnaW46IDA7XHJcbiAgZm9udC1zaXplOiAxMnB4O1xyXG4gIG9wYWNpdHk6IDAuODtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgbGluZS1oZWlnaHQ6IDEuMztcclxufVxyXG5cclxuLyogU3RlcCBQcm9ncmVzcyAqL1xyXG4uc3RlcC1wcm9ncmVzcyB7XHJcbiAgbWFyZ2luLXRvcDogOHB4O1xyXG4gIGhlaWdodDogNHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDJweDtcclxufVxyXG5cclxuLyogRm9ybSBTdGVwcyAqL1xyXG4uZm9ybS1zdGVwIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgZ2FwOiAxNnB4O1xyXG59XHJcblxyXG4uZm9ybS1zdGVwIGgzIHtcclxuICBtYXJnaW46IDAgMCAxNnB4IDA7XHJcbiAgY29sb3I6IHZhcigtLXRoZW1lLXRleHQtcHJpbWFyeSk7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICBmb250LXNpemU6IDE4cHg7XHJcbn1cclxuXHJcbi5mb3JtLXN0ZXAgaDQge1xyXG4gIG1hcmdpbjogMTZweCAwIDhweCAwO1xyXG4gIGNvbG9yOiB2YXIoLS10aGVtZS10ZXh0LXNlY29uZGFyeSk7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbn1cclxuXHJcbi8qIEFkZGl0aW9uYWwgcm93IGxheW91dHMgKi9cclxuLmxvY2F0aW9uLXJvdyxcclxuLmJpcnRoLWluZm8tcm93LFxyXG4uYnVzaW5lc3MtYWRkcmVzcy1yb3csXHJcbi5yYXRlcy1yb3cge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZ2FwOiAxNnB4O1xyXG59XHJcblxyXG4vKiBUaW1lIGlucHV0IGNvbnRhaW5lciAqL1xyXG4udGltZS1pbnB1dC1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxuLnRpbWUtaW5wdXQtY29udGFpbmVyIGlucHV0IHtcclxuICBmbGV4OiAxO1xyXG59XHJcblxyXG4udGltZS1pbnB1dC1jb250YWluZXIgYnV0dG9uIHtcclxuICBtYXJnaW4tbGVmdDogOHB4O1xyXG59XHJcblxyXG4vKiBOYXZpZ2F0aW9uIEJ1dHRvbnMgKi9cclxuLm5hdmlnYXRpb24tYnV0dG9ucyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogMTZweDtcclxuICBtYXJnaW4tdG9wOiAyNHB4O1xyXG4gIHBhZGRpbmctdG9wOiAxNnB4O1xyXG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCB2YXIoLS10aGVtZS1kaXZpZGVyKTtcclxufVxyXG5cclxuLnNwYWNlciB7XHJcbiAgZmxleDogMTtcclxufVxyXG5cclxuLyogU2tpbGxzIFNlY3Rpb24gU3R5bGluZyAqL1xyXG4uc2tpbGxzLXNlY3Rpb24ge1xyXG4gIG1hcmdpbjogMTZweCAwO1xyXG59XHJcblxyXG4uc2tpbGxzLXNlY3Rpb24gaDQge1xyXG4gIG1hcmdpbjogMCAwIDEycHggMDtcclxuICBjb2xvcjogdmFyKC0tdGhlbWUtdGV4dC1wcmltYXJ5KTtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxufVxyXG5cclxuLnNraWxscy1jaGlwcyB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMTJweDtcclxufVxyXG5cclxuLm1hdC1jaGlwLXNldCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgZ2FwOiA4cHg7XHJcbn1cclxuXHJcbi5tYXQtY2hpcCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tdGhlbWUtcHJpbWFyeS1saWdodCk7XHJcbiAgY29sb3I6IHZhcigtLXRoZW1lLXByaW1hcnkpO1xyXG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XHJcbiAgZm9udC1zaXplOiAxM3B4O1xyXG59XHJcblxyXG4ubWF0LWNoaXAgLm1hdC1pY29uIHtcclxuICBmb250LXNpemU6IDE2cHg7XHJcbiAgd2lkdGg6IDE2cHg7XHJcbiAgaGVpZ2h0OiAxNnB4O1xyXG59XHJcblxyXG4vKiBDdXN0b20gc25hY2tiYXIgc3R5bGVzICovXHJcbjo6bmctZGVlcCAuc3VjY2Vzcy1zbmFja2JhciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzRjYWY1MDtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuZXJyb3Itc25hY2tiYXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmNDQzMzY7XHJcbiAgY29sb3I6IHdoaXRlO1xyXG59XHJcblxyXG4vKiBSZXNwb25zaXZlIGRlc2lnbiBmb3Igb3JhY2xlIHJlZ2lzdHJhdGlvbiAqL1xyXG5AbWVkaWEgKG1heC13aWR0aDogNjAwcHgpIHtcclxuICAudHlwZS1idXR0b25zLWNvbXBhY3Qge1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgfVxyXG5cclxuICAudHlwZS1idXR0b24tY29tcGFjdCB7XHJcbiAgICBtaW4td2lkdGg6IDIwMHB4O1xyXG4gIH1cclxuXHJcbiAgLnR5cGUtYnV0dG9ucyB7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIH1cclxuXHJcbiAgLnR5cGUtYnV0dG9uIHtcclxuICAgIG1heC13aWR0aDogMTAwJTtcclxuICB9XHJcblxyXG4gIC5sb2NhdGlvbi1yb3csXHJcbiAgLmJpcnRoLWluZm8tcm93LFxyXG4gIC5idXNpbmVzcy1hZGRyZXNzLXJvdyxcclxuICAucmF0ZXMtcm93IHtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBnYXA6IDA7XHJcbiAgfVxyXG5cclxuICAubmF2aWdhdGlvbi1idXR0b25zIHtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBnYXA6IDEycHg7XHJcbiAgfVxyXG5cclxuICAuc3BhY2VyIHtcclxuICAgIGRpc3BsYXk6IG5vbmU7XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAiCA,UAAU,QAAyB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;ICO5EC,4BAA6C;IAAAA,iTAAsD;IAAAA,iBAAO;;;;;IAC1GA,4BAAqC;IACnCA,YACA;IAAAA,uCAImB;IACrBA,iBAAO;;;;IANLA,eACA;IADAA,6HACA;IAEEA,eAA0C;IAA1CA,oEAA0C;;;;;IAiC5CA,4BAA6C;IAAAA,kOAAwC;IAAAA,iBAAO;;;;;IAC5FA,4BAAqC;IAAAA,0UAA2D;IAAAA,iBAAO;;;;;;IAzB3GA,+BAAmE;IAC7DA,sJAAwB;IAAAA,iBAAK;IACjCA,+BAAkC;IAM9BA;MAAAA;MAAA;MAAA,OAASA,0CAAoB,SAAS,CAAC;IAAA,EAAC;IACxCA,gCAAU;IAAAA,sBAAM;IAAAA,iBAAW;IAC3BA,4BAAM;IAAAA,mIAAoB;IAAAA,iBAAO;IAGnCA,kCAK0C;IAAxCA;MAAAA;MAAA;MAAA,OAASA,0CAAoB,QAAQ,CAAC;IAAA,EAAC;IACvCA,iCAAU;IAAAA,6BAAY;IAAAA,iBAAW;IACjCA,6BAAM;IAAAA,qDAAM;IAAAA,iBAAO;IAGvBA,8BAA4B;IAC1BA,6EAA4F;IAC5FA,6EAAuG;IACzGA,iBAAI;;;;IAnBAA,eAAiD;IAAjDA,iEAAiD;IAUjDA,eAAyC;IAAzCA,yDAAyC;IAOpCA,eAAoC;IAApCA,4DAAoC;IACpCA,eAA4B;IAA5BA,oDAA4B;;;;;IAoB/BA,iCAAoG;IAClGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,mEACF;;;;;IAYAA,iCAAkG;IAChGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,kEACF;;;;;IAcFA,iCAA4F;IAC1FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,+DACF;;;;;IAiCAA,iCAAkG;IAChGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,kEACF;;;;;IAqBAA,iCAA+J;IAC7JA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,yEACF;;;;;IAQAA,iCAAwG;IACtGA,2UACF;IAAAA,iBAAY;;;;;IANdA,+BAAkE;IAE9DA,kGAAgB;IAAAA,6BAA4B;IAAAA,oIAAqB;IAAAA,iBAAI;IAACA,wBAAE;IAAAA,6BAA4B;IAAAA,wKAA2B;IAAAA,iBAAI;IAErIA,mGAEY;IACdA,iBAAM;;;;;IAHQA,eAA0F;IAA1FA,uMAA0F;;;;;;IA9G1GA,+BAAiD;IAIhCA,iEAAS;IAAAA,iBAAY;IAChCA,4BAK4B;IAC5BA,oCAAoB;IAAAA,sBAAM;IAAAA,iBAAW;IACrCA,4FAEY;IACdA,iBAAiB;IAEjBA,0CAAwD;IAC3CA,8EAAW;IAAAA,iBAAY;IAClCA,6BAK6B;IAC7BA,qCAAoB;IAAAA,uBAAM;IAAAA,iBAAW;IACrCA,8FAEY;IACdA,iBAAiB;IAInBA,2CAAwD;IAC3CA,8EAAW;IAAAA,iBAAY;IAClCA,6BAKuB;IACvBA,qCAAoB;IAAAA,sBAAK;IAAAA,iBAAW;IACpCA,8FAEY;IACdA,iBAAiB;IAGjBA,2CAAwD;IAC3CA,oJAA0B;IAAAA,iBAAY;IACjDA,6BAKqB;IACrBA,qCAAoB;IAAAA,sBAAK;IAAAA,iBAAW;IAItCA,2CAAwD;IAC3CA,qDAAM;IAAAA,iBAAY;IAC7BA,6BAK8B;IAC9BA,mCAMqC;IAFnCA;MAAAA;MAAA;MAAA;IAAA,EAAsC;IAGtCA,iCAAU;IAAAA,aAAoD;IAAAA,iBAAW;IAE3EA,8FAEY;IACdA,iBAAiB;IAGjBA,2CAAwD;IAC3CA,8HAAmB;IAAAA,iBAAY;IAC1CA,6BAK8B;IAC9BA,mCAM4C;IAF1CA;MAAAA;MAAA;MAAA;IAAA,EAAoD;IAGpDA,iCAAU;IAAAA,aAA2D;IAAAA,iBAAW;IAElFA,8FAEY;IACdA,iBAAiB;IAGjBA,mFAOM;IACRA,iBAAM;;;;;;;;;IAtGYA,eAAsF;IAAtFA,iMAAsF;IActFA,eAAoF;IAApFA,+LAAoF;IAgBtFA,eAA8E;IAA9EA,yLAA8E;IAsBxFA,gBAA2C;IAA3CA,gEAA2C;IAS3CA,eAAkC;IAAlCA,mGAAkC;IAExBA,eAAoD;IAApDA,2EAAoD;IAEpDA,eAAoF;IAApFA,+LAAoF;IAU9FA,eAAkD;IAAlDA,uEAAkD;IASlDA,eAAkC;IAAlCA,mGAAkC;IAExBA,eAA2D;IAA3DA,kFAA2D;IAE3DA,eAAiJ;IAAjJA,qQAAiJ;IAMnIA,eAAoC;IAApCA,4DAAoC;;;;;IAsB9DA,iCAAoH;IAClHA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,kFACF;;;;;IAMEA,sCAA2E;IACzEA,YACF;IAAAA,iBAAa;;;;IAF+CA,gCAAc;IACxEA,eACF;IADEA,yCACF;;;;;IAEFA,iCAA4H;IAC1HA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,sFACF;;;;;IAaAA,iCAAkG;IAChGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,yEACF;;;;;IAaAA,iCAAgG;IAC9FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,wEACF;;;;;IAaAA,iCAAoH;IAClHA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,kFACF;;;;;IAtEJA,+BAA2E;IACrEA,2JAAwB;IAAAA,iBAAK;IAEjCA,0CAAwD;IAC3CA,6HAAmB;IAAAA,iBAAY;IAC1CA,4BAI8D;IAC9DA,oCAAoB;IAAAA,oBAAI;IAAAA,iBAAW;IACnCA,4FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,0IAAqB;IAAAA,iBAAY;IAC5CA,uCAAoD;IAClDA,iGAEa;IACfA,iBAAa;IACbA,8FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,yJAA0B;IAAAA,iBAAY;IACjDA,6BAKkB;IAClBA,qCAAoB;IAAAA,sBAAK;IAAAA,iBAAW;IACpCA,iCAAU;IAAAA,aAA0D;IAAAA,iBAAW;IAC/EA,8FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,kHAAiB;IAAAA,iBAAY;IACxCA,qCAKmB;IACnBA;IAAAA,iBAAW;IACXA,iCAAU;IAAAA,aAA0D;IAAAA,iBAAW;IAC/EA,8FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,8EAAW;IAAAA,iBAAY;IAClCA,6BAMW;IACXA,qCAAoB;IAAAA,yBAAQ;IAAAA,iBAAW;IACvCA,8FAEY;IACdA,iBAAiB;;;;;;;;;;;IA5DHA,eAAsG;IAAtGA,mNAAsG;IAQnFA,eAA6B;IAA7BA,4DAA6B;IAIhDA,eAA8G;IAA9GA,2NAA8G;IAchHA,eAA0D;IAA1DA,+JAA0D;IACxDA,eAAoF;IAApFA,iMAAoF;IActFA,eAA0D;IAA1DA,+JAA0D;IACxDA,eAAkF;IAAlFA,+LAAkF;IAelFA,eAAsG;IAAtGA,mNAAsG;;;;;IAmBhHA,iCAA0F;IACxFA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,qEACF;;;;;IAsBFA,iCAAgG;IAC9FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,wEACF;;;;;IAtCJA,+BAA2E;IACrEA,oGAAc;IAAAA,iBAAK;IAEvBA,+BAA0B;IAEXA,wCAAI;IAAAA,iBAAY;IAC3BA,4BAI8B;IAC9BA,oCAAoB;IAAAA,6BAAa;IAAAA,iBAAW;IAC5CA,8FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,0FAAa;IAAAA,iBAAY;IACpCA,6BAIgC;IAChCA,qCAAoB;IAAAA,oBAAG;IAAAA,iBAAW;IAItCA,2CAAwD;IAC3CA,2DAAO;IAAAA,iBAAY;IAC9BA,6BAIiC;IACjCA,qCAAoB;IAAAA,uBAAM;IAAAA,iBAAW;IACrCA,8FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,0MAAmC;IAAAA,iBAAY;IAC1DA,6BAI+D;IAC/DA,qCAAoB;IAAAA,2BAAU;IAAAA,iBAAW;;;;;;IApC3BA,gBAA4E;IAA5EA,yLAA4E;IAwB9EA,gBAAkF;IAAlFA,+LAAkF;;;;;IAiC5FA,iCAAoG;IAClGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,0EACF;;;;;IAgCFA,iCAA4G;IAC1GA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,8EACF;;;;;IAMEA,sCAAuE;IACrEA,YACF;IAAAA,iBAAa;;;;IAF2CA,gCAAc;IACpEA,eACF;IADEA,yCACF;;;;;IAOAA,sCAAuE;IACrEA,YACF;IAAAA,iBAAa;;;;IAF2CA,gCAAc;IACpEA,eACF;IADEA,yCACF;;;;;IAGFA,iCAAwG;IACtGA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,4EACF;;;;;IAMEA,sCAAqE;IACnEA,YACF;IAAAA,iBAAa;;;;IAFyCA,gCAAc;IAClEA,eACF;IADEA,yCACF;;;;;IAGFA,iCAAgH;IAC9GA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,gFACF;;;;;;IAQIA,oCAGmB;IADjBA;MAAA;MAAA;MAAA;MAAA,OAAWA,yCAAc;IAAA,EAAC;IAE1BA,YACA;IAAAA,oCAAwB;IAAAA,sBAAM;IAAAA,iBAAW;;;;IADzCA,eACA;IADAA,0CACA;;;;;;;;IAPNA,+BAAgF;IAE5EA,kGAMW;IACbA,iBAAe;;;;;IANOA,eAA4C;IAA5CA,uIAA4C;;;;;IA0BpEA,iCAA8F;IAC5FA,YACF;IAAAA,iBAAY;;;;IADVA,eACF;IADEA,uEACF;;;;;;IA5HJA,+BAA2E;IACrEA,mIAAoB;IAAAA,iBAAK;IAE7BA,+BAA4B;IAEbA,gGAAe;IAAAA,iBAAY;IACtCA,4BAKc;IACdA,iDAAyD;IACrBA,qBAAI;IAAAA,iBAAW;IAEnDA,4CAAkD;IAClDA,8FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,yIAAyB;IAAAA,iBAAY;IAChDA,gCAAkC;IAChCA,6BAImC;IACnCA,mCAK4F;IAD1FA;MAAAA;MAAA;MAAA,OAASA,yCAAkB;IAAA,EAAC;IAE5BA,iCAAU;IAAAA,aAAgD;IAAAA,iBAAW;IAGzEA,iCAAU;IAAAA,aAA2D;IAAAA,iBAAW;IAIpFA,2CAAwD;IAC3CA,uGAAgB;IAAAA,iBAAY;IACvCA,6BAI0C;IAC1CA,qCAAoB;IAAAA,sBAAK;IAAAA,iBAAW;IACpCA,8FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,sKAA6B;IAAAA,iBAAY;IACpDA,uCAA+C;IAC7CA,iGAEa;IACfA,iBAAa;IAGfA,2CAAwD;IAC3CA,uGAAgB;IAAAA,iBAAY;IACvCA,uCAAmD;IACjDA,iGAEa;IACfA,iBAAa;IACbA,iCAAU;IAAAA,wOAAwC;IAAAA,iBAAW;IAC7DA,8FAEY;IACdA,iBAAiB;IAEjBA,2CAAwD;IAC3CA,+CAAK;IAAAA,iBAAY;IAC5BA,uCAAuD;IACrDA,iGAEa;IACfA,iBAAa;IACbA,iCAAU;IAAAA,yRAAiD;IAAAA,iBAAW;IACtEA,8FAEY;IACdA,iBAAiB;IAGjBA,gCAA4B;IACtBA,2IAAsB;IAAAA,iBAAK;IAC/BA,mFAUM;IACNA,2CAAwD;IAC3CA,sGAAe;IAAAA,iBAAY;IACtCA,sCAIuD;IAArDA;MAAAA;MAAA;MAAA;MAAA,OAAiBA,6CAAkC;IAAA,EAAC;IAJtDA,iBAIuD;IACvDA,mCAMwB;IAFtBA;MAAAA;MAAA;MAAA;MAAA,OAASA,8CAA6B;IAAA,EAAC;IAGvCA,iCAAU;IAAAA,oBAAG;IAAAA,iBAAW;IAG5BA,iCAAU;IAAAA,+QAAiD;IAAAA,iBAAW;IACtEA,8FAEY;IACdA,iBAAM;;;;;;;;;;;;IArHAA,eAAiC;IAAjCA,oCAAiC;IAIFA,eAAuB;IAAvBA,0BAAuB;IAI5CA,eAAsF;IAAtFA,mMAAsF;IAY9FA,eAAgC;IAAhCA,sDAAgC;IAMhCA,eAAyF;IAAzFA,2UAAyF;IAC/EA,eAAgD;IAAhDA,wEAAgD;IAGpDA,eAA2D;IAA3DA,iMAA2D;IAY3DA,eAA8F;IAA9FA,2MAA8F;IAQ3EA,eAAyB;IAAzBA,wDAAyB;IASzBA,eAAyB;IAAzBA,wDAAyB;IAK5CA,eAA0F;IAA1FA,2MAA0F;IAQvEA,eAAuB;IAAvBA,sDAAuB;IAK1CA,eAAkG;IAAlGA,mNAAkG;IAQnFA,eAAmD;IAAnDA,mJAAmD;IAuB1EA,eAAqC;IAArCA,6CAAqC;IAM7BA,eAAgF;IAAhFA,iMAAgF;;;;;IAOhGA,+BAA2E;IACrEA,yKAA4B;IAAAA,iBAAK;IAErCA,0CAAwD;IAC3CA,wGAAkB;IAAAA,iBAAY;IACzCA,4BAIuC;IACvCA,oCAAoB;IAAAA,wBAAQ;IAAAA,iBAAW;IAGzCA,0CAAwD;IAC3CA,yHAAwB;IAAAA,iBAAY;IAC/CA,6BAIsC;IACtCA,qCAAoB;IAAAA,qBAAI;IAAAA,iBAAW;IAGrCA,2BAAI;IAAAA,kIAAuB;IAAAA,iBAAK;IAChCA,2CAAwD;IAC3CA,+CAAK;IAAAA,iBAAY;IAC5BA,6BAI8B;IAC9BA,qCAAoB;IAAAA,qBAAI;IAAAA,iBAAW;IAGrCA,gCAAkC;IAEnBA,yCAAI;IAAAA,iBAAY;IAC3BA,6BAIqB;IACrBA,qCAAoB;IAAAA,8BAAa;IAAAA,iBAAW;IAG9CA,2CAAwD;IAC3CA,oFAAY;IAAAA,iBAAY;IACnCA,6BAI6B;IAC7BA,qCAAoB;IAAAA,mCAAkB;IAAAA,iBAAW;IAIrDA,yCAAwE;IACtEA,8LACF;IAAAA,iBAAe;;;;;IA+CbA,iCAAwG;IACtGA,4PACF;IAAAA,iBAAY;;;;;IA7ChBA,+BAA2E;IACrEA,sGAAgB;IAAAA,iBAAK;IAEzBA,+BAAuB;IAERA,uIAAwB;IAAAA,iBAAY;IAC/CA,4BAKU;IACVA,oCAAoB;IAAAA,wBAAQ;IAAAA,iBAAW;IAGzCA,2CAAwD;IAC3CA,+IAA0B;IAAAA,iBAAY;IACjDA,6BAKU;IACVA,qCAAoB;IAAAA,sBAAK;IAAAA,iBAAW;IAIxCA,2CAAwD;IAC3CA,qDAAM;IAAAA,iBAAY;IAC7BA,uCAAuC;IACbA,gGAAmB;IAAAA,iBAAa;IACxDA,uCAAwB;IAAAA,+CAAU;IAAAA,iBAAa;IAC/CA,uCAAwB;IAAAA,qDAAW;IAAAA,iBAAa;IAKpDA,gCAA2B;IAEvBA,mGAAgB;IAAAA,8BAA4B;IAAAA,qIAAqB;IAAAA,iBAAI;IAAAA,mBACrE;IAAAA,8BAA4B;IAAAA,yKAA2B;IAAAA,iBAAI;IAACA,yBAC5D;IAAAA,8BAA4B;IAAAA,+HAAoB;IAAAA,iBAAI;IAEtDA,8FAEY;IACdA,iBAAM;;;;;IAHQA,gBAA0F;IAA1FA,uMAA0F;;;;;;IAQxGA,kCAKyB;IADvBA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IAExBA,gCAAU;IAAAA,0BAAU;IAAAA,iBAAW;IAC/BA,gDACF;IAAAA,iBAAS;;;;IAHPA,4CAAsB;;;;;IAYtBA,gCAA4B;IAC1BA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAAyD;IAAAA,6BAAa;IAAAA,iBAAW;;;;;IACjFA,gCAA2D;IAAAA,0BAAU;IAAAA,iBAAW;;;;;IAtBpFA,+BAA+D;IAC7DA,uFAQS;IAETA,0BAA0B;IAE1BA,kCAIkD;IAChDA,0FAEW;IACXA,0FAAiF;IACjFA,0FAAgF;IAChFA,YACF;IAAAA,iBAAS;;;;IApBNA,eAAqB;IAArBA,8CAAqB;IAatBA,eAA+C;IAA/CA,6EAA+C;IACpCA,eAAe;IAAfA,wCAAe;IAGfA,eAA4C;IAA5CA,qFAA4C;IAC5CA,eAA8C;IAA9CA,uFAA8C;IACzDA,eACF;IADEA,kRACF;;;;;IAWAA,gCAA4B;IAC1BA,kCAAyC;IAC3CA,iBAAW;;;;;IACXA,gCAA6B;IAAAA,0BAAU;IAAAA,iBAAW;;;;;IAVpDA,kCAMyB;IACvBA,6FAEW;IACXA,6FAAkD;IAClDA,YACF;IAAAA,iBAAS;;;;IANPA,4CAAsB;IACXA,eAAe;IAAfA,wCAAe;IAGfA,eAAgB;IAAhBA,yCAAgB;IAC3BA,eACF;IADEA,gPACF;;;;;IAGAA,+BAAsE;IACpEA,8BAA2B;IAC3BA,gCAA2B;IAAAA,kCAAG;IAAAA,iBAAO;IACrCA,8BAA2B;IAC7BA,iBAAM;;;;;;IAGNA,+BAAkE;IAO9DA;MAAAA;MAAA;MAAA,OAASA,yCAAkB;IAAA,EAAC;IAC5BA,0BAAoG;IACpGA,kGACF;IAAAA,iBAAS;IAGTA,kCAKiC;IAA/BA;MAAAA;MAAA;MAAA,OAASA,2CAAoB;IAAA,EAAC;IAC9BA,oCAA2C;IAAAA,wBAAQ;IAAAA,iBAAW;IAC9DA,oGACF;IAAAA,iBAAS;;;;IAfPA,eAAsB;IAAtBA,4CAAsB;IAWtBA,eAAsB;IAAtBA,4CAAsB;;;;;;IA7iB5BA,gCACuE;IADtCA;MAAAA;MAAA;MAAA,OAAYA,iCAAU;IAAA,EAAC;IAItDA,6EAkHM;IAGNA,4EAwEM;IAGNA,4EAkDM;IAGNA,6EA8HM;IAGNA,4EA2DM;IAGNA,4EA+CM;IAGNA,2EAyBM;IAGNA,iFAYS;IAGTA,2EAIM;IAGNA,6EAsBM;IACRA,iBAAO;;;;IAnjBDA,+CAA0B;IAIxBA,eAAuB;IAAvBA,+CAAuB;IAqHvBA,eAAiD;IAAjDA,gFAAiD;IA2EjDA,eAAiD;IAAjDA,gFAAiD;IAqDjDA,eAAiD;IAAjDA,gFAAiD;IAiIjDA,eAAiD;IAAjDA,gFAAiD;IA8DjDA,eAAiD;IAAjDA,gFAAiD;IAkDtBA,eAA4B;IAA5BA,oDAA4B;IA6B1DA,eAAoC;IAApCA,4DAAoC;IAcPA,eAAoC;IAApCA,4DAAoC;IAOxCA,eAAoC;IAApCA,4DAAoC;;;ADlkBxE,OAAM,MAAOC,iBAAiB;EAa5B;EACAC,oBAAoB;IAClB,OAAO,IAAI,CAACC,gBAAgB,KAAK,QAAQ;EAC3C;EAIAC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,YAA0B,EAC1BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IALrB,gBAAW,GAAXL,WAAW;IACX,gBAAW,GAAXC,WAAW;IACX,iBAAY,GAAZC,YAAY;IACZ,WAAM,GAANC,MAAM;IACN,UAAK,GAALC,KAAK;IACL,aAAQ,GAARC,QAAQ;IAxBlB,cAAS,GAAG,KAAK;IACjB,iBAAY,GAAG,IAAI;IACnB,wBAAmB,GAAG,IAAI;IAC1B,cAAS,GAAG,GAAG;IAEf;IACA,qBAAgB,GAAyB,SAAS;IAElD;IACA,kBAAa,GAAG,IAAI;IAMpB,gBAAW,GAAG,CAAC;IACf,eAAU,GAAG,CAAC;IAUZ,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,iBAAiB,EAAE;EAC9C;EAEAC,QAAQ;IACN;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,GAAG;IAEpE;IACA,IAAI,CAACV,WAAW,CAACW,gBAAgB,CAACC,SAAS,CAACC,MAAM,IAAG;MACnD,IAAIA,MAAM,EAAE;QACV;QACA,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACQR,iBAAiB;IACvB,OAAO,IAAI,CAACP,WAAW,CAACgB,KAAK,CAAC;MAC5BC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAAC2B,KAAK,CAAC,CAAC;MACpDC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DK,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAC5CO,WAAW,EAAE,CAAC,KAAK,EAAE,CAAC/B,UAAU,CAACgC,YAAY,CAAC;KAC/C,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEQC,gBAAgB;IACtB,OAAO,IAAI,CAAC7B,WAAW,CAACgB,KAAK,CAAC;MAC5B;MACAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAAC2B,KAAK,CAAC,CAAC;MACpDC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DK,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAE5C;MACAY,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAC9Ca,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAACsC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAChEC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAACsC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;MAChEE,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAClDiB,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAACzC,UAAU,CAACwB,QAAQ,EAAExB,UAAU,CAAC0C,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAEhE;MACAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC3C,UAAU,CAACwB,QAAQ,CAAC,CAAC;MACjCoB,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAACwB,QAAQ,CAAC,CAAC;MACpCsB,eAAe,EAAE,CAAC,EAAE,CAAC;MAErB;MACAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC/C,UAAU,CAACwB,QAAQ,CAAC,CAAC;MACtCwB,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACjD,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAC1C0B,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACnD,UAAU,CAACwB,QAAQ,CAAC,CAAC;MACxC4B,eAAe,EAAE,CAAC,CAAC,WAAW,CAAC,EAAE,CAACpD,UAAU,CAACwB,QAAQ,CAAC,CAAC;MACvD6B,MAAM,EAAE,CAAC,EAAE,EAAE,CAACrD,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAEnC;MACA8B,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,uBAAuB,EAAE,CAAC,KAAK,CAAC;MAEhC;MACAC,UAAU,EAAE,CAAC,CAAC,CAAC;MACfC,WAAW,EAAE,CAAC,CAAC,CAAC;MAChBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MAEjBjC,WAAW,EAAE,CAAC,KAAK,EAAE,CAAC/B,UAAU,CAACgC,YAAY,CAAC;KAC/C,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEA;EACA+B,mBAAmB,CAACC,IAA0B;IAC5C,IAAI,CAAC9D,gBAAgB,GAAG8D,IAAI;IAC5B,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAI,CAACtD,YAAY,GAAG,IAAI,CAACuB,gBAAgB,EAAE;MAC3C,IAAI,CAACgC,UAAU,GAAG,CAAC;MACnB,IAAI,CAACC,WAAW,GAAG,CAAC;KACrB,MAAM;MACL,IAAI,CAACxD,YAAY,GAAG,IAAI,CAACC,iBAAiB,EAAE;MAC5C,IAAI,CAACsD,UAAU,GAAG,CAAC;MACnB,IAAI,CAACC,WAAW,GAAG,CAAC;;EAExB;EAEA;EACAC,QAAQ;IACN,IAAI,IAAI,CAACD,WAAW,GAAG,IAAI,CAACD,UAAU,IAAI,IAAI,CAACG,kBAAkB,EAAE,EAAE;MACnE,IAAI,CAACF,WAAW,EAAE;;EAEtB;EAEAG,YAAY;IACV,IAAI,IAAI,CAACH,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAE,kBAAkB;IAChB,IAAI,IAAI,CAAClE,gBAAgB,KAAK,SAAS,EAAE;MACvC,OAAO,IAAI,CAACQ,YAAY,CAAC4D,KAAK;;IAGhC;IACA,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,iBAAiB,CAAC;IACrF,MAAMC,WAAW,GAAG,CAAC,mBAAmB,EAAE,UAAU,EAAE,SAAS,EAAE,uBAAuB,EAAE,mBAAmB,CAAC;IAC9G,MAAMC,WAAW,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC;IACvC,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,iBAAiB,EAAE,QAAQ,CAAC;IAC9F,MAAMC,WAAW,GAAa,EAAE,CAAC,CAAC;IAClC,MAAMC,WAAW,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC;IAE/C,IAAIC,gBAAgB,GAAa,EAAE;IACnC,QAAQ,IAAI,CAACX,WAAW;MACtB,KAAK,CAAC;QAAEW,gBAAgB,GAAGN,WAAW;QAAE;MACxC,KAAK,CAAC;QAAEM,gBAAgB,GAAGL,WAAW;QAAE;MACxC,KAAK,CAAC;QAAEK,gBAAgB,GAAGJ,WAAW;QAAE;MACxC,KAAK,CAAC;QAAEI,gBAAgB,GAAGH,WAAW;QAAE;MACxC,KAAK,CAAC;QAAEG,gBAAgB,GAAGF,WAAW;QAAE;MACxC,KAAK,CAAC;QAAEE,gBAAgB,GAAGD,WAAW;QAAE;IAAM;IAGhD,OAAOC,gBAAgB,CAACC,KAAK,CAACC,KAAK,IAAG;MACpC,MAAMC,OAAO,GAAG,IAAI,CAACtE,YAAY,CAACuE,GAAG,CAACF,KAAK,CAAC;MAC5C,OAAOC,OAAO,GAAGA,OAAO,CAACV,KAAK,GAAG,IAAI;IACvC,CAAC,CAAC;EACJ;EAEA;EACAY,YAAY;IACV,IAAI,IAAI,CAAChF,gBAAgB,KAAK,SAAS,EAAE;MACvC,OAAO,qBAAqB;;IAG9B,QAAQ,IAAI,CAACgE,WAAW;MACtB,KAAK,CAAC;QAAE,OAAO,oBAAoB;MACnC,KAAK,CAAC;QAAE,OAAO,0BAA0B;MACzC,KAAK,CAAC;QAAE,OAAO,gBAAgB;MAC/B,KAAK,CAAC;QAAE,OAAO,sBAAsB;MACrC,KAAK,CAAC;QAAE,OAAO,mBAAmB;MAClC,KAAK,CAAC;QAAE,OAAO,kBAAkB;MACjC;QAAS,OAAO,aAAa;IAAC;EAElC;EAEA;EACAiB,wBAAwB;IACtB,OAAO,CACL,YAAY,EACZ,MAAM,EACN,mBAAmB,EACnB,aAAa,EACb,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,mBAAmB,EACnB,aAAa,EACb,sBAAsB,CACvB;EACH;EAEAC,oBAAoB;IAClB,OAAO,CACL,YAAY,EACZ,oBAAoB,EACpB,UAAU,EACV,MAAM,EACN,aAAa,EACb,YAAY,EACZ,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,oBAAoB,CACrB;EACH;EAEAC,kBAAkB;IAChB,OAAO,CACL,WAAW,EACX,WAAW,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACT,UAAU,EACV,YAAY,EACZ,QAAQ,CACT;EACH;EAEAC,oBAAoB;IAClB,OAAO,CACL,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EACjD,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAC7D;EACH;EAEA;EACAC,gBAAgB;IACd,IAAI,CAACC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEA;EACAC,QAAQ,CAACC,KAAoB,EAAEC,KAAuB;IACpDD,KAAK,CAACE,cAAc,EAAE;IACtB,MAAMC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACC,IAAI,EAAE;IAEhC,IAAID,KAAK,EAAE;MACT,MAAME,aAAa,GAAG,IAAI,CAACrF,YAAY,CAACuE,GAAG,CAAC,QAAQ,CAAC,EAAEY,KAAK,IAAI,EAAE;MAClE,IAAI,CAACE,aAAa,CAACC,QAAQ,CAACH,KAAK,CAAC,EAAE;QAClC,MAAMI,aAAa,GAAG,CAAC,GAAGF,aAAa,EAAEF,KAAK,CAAC;QAC/C,IAAI,CAACnF,YAAY,CAACuE,GAAG,CAAC,QAAQ,CAAC,EAAEiB,QAAQ,CAACD,aAAa,CAAC;QACxDN,KAAK,CAACE,KAAK,GAAG,EAAE;;;EAGtB;EAEAM,iBAAiB,CAACR,KAAuB;IACvC,MAAME,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACC,IAAI,EAAE;IAEhC,IAAID,KAAK,EAAE;MACT,MAAME,aAAa,GAAG,IAAI,CAACrF,YAAY,CAACuE,GAAG,CAAC,QAAQ,CAAC,EAAEY,KAAK,IAAI,EAAE;MAClE,IAAI,CAACE,aAAa,CAACC,QAAQ,CAACH,KAAK,CAAC,EAAE;QAClC,MAAMI,aAAa,GAAG,CAAC,GAAGF,aAAa,EAAEF,KAAK,CAAC;QAC/C,IAAI,CAACnF,YAAY,CAACuE,GAAG,CAAC,QAAQ,CAAC,EAAEiB,QAAQ,CAACD,aAAa,CAAC;QACxDN,KAAK,CAACE,KAAK,GAAG,EAAE;;;EAGtB;EAEAO,WAAW,CAACC,KAAa;IACvB,MAAMN,aAAa,GAAG,IAAI,CAACrF,YAAY,CAACuE,GAAG,CAAC,QAAQ,CAAC,EAAEY,KAAK,IAAI,EAAE;IAClEE,aAAa,CAACO,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC3F,YAAY,CAACuE,GAAG,CAAC,QAAQ,CAAC,EAAEiB,QAAQ,CAACH,aAAa,CAAC;EAC1D;EAEA/D,sBAAsB,CAACgD,OAAwB;IAC7C,MAAMrD,QAAQ,GAAGqD,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACxC,MAAMrD,eAAe,GAAGoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAEtD,IAAItD,QAAQ,IAAIC,eAAe,IAAID,QAAQ,CAACkE,KAAK,KAAKjE,eAAe,CAACiE,KAAK,EAAE;MAC3E,OAAO;QAAE,kBAAkB,EAAE;MAAI,CAAE;;IAErC,OAAO,IAAI;EACb;EAEAU,QAAQ;IACN,IAAI,IAAI,CAACrG,gBAAgB,KAAK,QAAQ,IAAI,IAAI,CAACgE,WAAW,GAAG,IAAI,CAACD,UAAU,EAAE;MAC5E;MACA,IAAI,CAACE,QAAQ,EAAE;MACf;;IAGF,IAAI,IAAI,CAACzD,YAAY,CAAC4D,KAAK,EAAE;MAC3B,IAAI,CAACkC,SAAS,GAAG,IAAI;MAErB,IAAI,IAAI,CAACtG,gBAAgB,KAAK,SAAS,EAAE;QACvC,IAAI,CAACuG,yBAAyB,EAAE;OACjC,MAAM;QACL,IAAI,CAACC,wBAAwB,EAAE;;KAElC,MAAM;MACL,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,CAAClG,QAAQ,CAACmG,IAAI,CAAC,4CAA4C,EAAE,SAAS,EAAE;QAC1EC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;;EAEN;EAEQL,yBAAyB;IAC/B,MAAMM,eAAe,GAAoB;MACvC1F,SAAS,EAAE,IAAI,CAACX,YAAY,CAACmF,KAAK,CAACxE,SAAS;MAC5CG,QAAQ,EAAE,IAAI,CAACd,YAAY,CAACmF,KAAK,CAACrE,QAAQ;MAC1CC,KAAK,EAAE,IAAI,CAACf,YAAY,CAACmF,KAAK,CAACpE,KAAK;MACpCC,WAAW,EAAE,IAAI,CAAChB,YAAY,CAACmF,KAAK,CAACnE,WAAW,IAAIsF,SAAS;MAC7DrF,QAAQ,EAAE,IAAI,CAACjB,YAAY,CAACmF,KAAK,CAAClE,QAAQ;MAC1CC,eAAe,EAAE,IAAI,CAAClB,YAAY,CAACmF,KAAK,CAACjE,eAAe;MACxDC,WAAW,EAAE,IAAI,CAACnB,YAAY,CAACmF,KAAK,CAAChE;KACtC;IAED,IAAI,CAACxB,WAAW,CAAC4G,QAAQ,CAACF,eAAe,CAAC,CAAC9F,SAAS,CAAC;MACnDiG,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAIW,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC3G,QAAQ,CAACmG,IAAI,CAAC,gDAAgD,EAAE,SAAS,EAAE;YAC9EC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UAEF;UACAO,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjED,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEH,QAAQ,CAACI,IAAI,CAAC;UAExC;UACA,IAAI,CAAChH,MAAM,CAACY,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;SACxC,MAAM;UACL,IAAI,CAACV,QAAQ,CAACmG,IAAI,CAACO,QAAQ,CAACK,OAAO,IAAI,yBAAyB,EAAE,SAAS,EAAE;YAC3EX,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;;MAEN,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC/F,QAAQ,CAACmG,IAAI,CAACa,KAAK,IAAI,iDAAiD,EAAE,SAAS,EAAE;UACxFZ,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEQJ,wBAAwB;IAC9B,MAAMgB,SAAS,GAAG,IAAI,CAAChH,YAAY,CAACmF,KAAK;IACzC,MAAM8B,aAAa,GAA0B;MAC3C;MACAtG,SAAS,EAAEqG,SAAS,CAACrG,SAAS;MAC9BG,QAAQ,EAAEkG,SAAS,CAAClG,QAAQ;MAC5BC,KAAK,EAAEiG,SAAS,CAACjG,KAAK;MACtBC,WAAW,EAAEgG,SAAS,CAAChG,WAAW,IAAIsF,SAAS;MAC/CrF,QAAQ,EAAE+F,SAAS,CAAC/F,QAAQ;MAC5BC,eAAe,EAAE8F,SAAS,CAAC9F,eAAe;MAC1CC,WAAW,EAAE6F,SAAS,CAAC7F,WAAW;MAElC;MACAK,iBAAiB,EAAEwF,SAAS,CAACxF,iBAAiB;MAC9CC,QAAQ,EAAEuF,SAAS,CAACvF,QAAQ;MAC5BE,OAAO,EAAEqF,SAAS,CAACrF,OAAO;MAC1BC,qBAAqB,EAAEoF,SAAS,CAACpF,qBAAqB;MACtDC,iBAAiB,EAAEmF,SAAS,CAACnF,iBAAiB;MAE9C;MACAE,IAAI,EAAEiF,SAAS,CAACjF,IAAI;MACpBC,KAAK,EAAEgF,SAAS,CAAChF,KAAK;MACtBC,OAAO,EAAE+E,SAAS,CAAC/E,OAAO;MAC1BC,eAAe,EAAE8E,SAAS,CAAC9E,eAAe,IAAI,GAAG8E,SAAS,CAACjF,IAAI,KAAKiF,SAAS,CAAC/E,OAAO,EAAE;MAEvF;MACAE,SAAS,EAAE6E,SAAS,CAAC7E,SAAS;MAC9BC,SAAS,EAAE4E,SAAS,CAAC5E,SAAS;MAC9BC,aAAa,EAAE2E,SAAS,CAAC3E,aAAa;MACtCC,gBAAgB,EAAE0E,SAAS,CAAC1E,gBAAgB;MAC5CC,WAAW,EAAEyE,SAAS,CAACzE,WAAW;MAClCC,eAAe,EAAEwE,SAAS,CAACxE,eAAe;MAC1CC,MAAM,EAAEuE,SAAS,CAACvE,MAAM;MAExB;MACAC,OAAO,EAAEsE,SAAS,CAACtE,OAAO;MAC1BC,YAAY,EAAEqE,SAAS,CAACrE,YAAY;MACpCuE,eAAe,EAAE;QACfC,MAAM,EAAEH,SAAS,CAACpE,cAAc;QAChCb,IAAI,EAAEiF,SAAS,CAACnE,YAAY;QAC5Bb,KAAK,EAAEgF,SAAS,CAAClE,aAAa;QAC9BsE,UAAU,EAAEJ,SAAS,CAACjE,kBAAkB;QACxCd,OAAO,EAAE+E,SAAS,CAAChE,eAAe;QAClCqE,QAAQ,EAAEL,SAAS,CAAC/D;OACrB;MAED;MACAqE,iBAAiB,EAAE;QACjBpE,UAAU,EAAE8D,SAAS,CAAC9D,UAAU;QAChCC,WAAW,EAAE6D,SAAS,CAAC7D,WAAW;QAClCC,QAAQ,EAAE4D,SAAS,CAAC5D;;KAEvB;IAED;IACA;IACA,IAAI,CAACzD,WAAW,CAAC4G,QAAQ,CAACU,aAAa,CAAC,CAAC1G,SAAS,CAAC;MACjDiG,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAIW,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAC3G,QAAQ,CAACmG,IAAI,CAAC,4DAA4D,EAAE,SAAS,EAAE;YAC1FC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UAEF;UACAO,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;UACxED,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEH,QAAQ,CAACI,IAAI,CAAC;UAExC;UACA,IAAI,CAAChH,MAAM,CAACY,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;SACxC,MAAM;UACL,IAAI,CAACV,QAAQ,CAACmG,IAAI,CAACO,QAAQ,CAACK,OAAO,IAAI,yBAAyB,EAAE,SAAS,EAAE;YAC3EX,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;;MAEN,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC/F,QAAQ,CAACmG,IAAI,CAACa,KAAK,IAAI,iDAAiD,EAAE,SAAS,EAAE;UACxFZ,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEQH,oBAAoB;IAC1BsB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxH,YAAY,CAACyH,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACpD,MAAMrD,OAAO,GAAG,IAAI,CAACtE,YAAY,CAACuE,GAAG,CAACoD,GAAG,CAAC;MAC1CrD,OAAO,EAAEsD,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,wBAAwB;IACtB,MAAMvD,OAAO,GAAG,IAAI,CAACtE,YAAY,CAACuE,GAAG,CAAC,WAAW,CAAC;IAClD,IAAID,OAAO,EAAEwD,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,4BAA4B;;IAErC,IAAIxD,OAAO,EAAEwD,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,2CAA2C;;IAEpD,OAAO,EAAE;EACX;EAEAC,uBAAuB;IACrB,MAAMzD,OAAO,GAAG,IAAI,CAACtE,YAAY,CAACuE,GAAG,CAAC,UAAU,CAAC;IACjD,IAAID,OAAO,EAAEwD,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,8BAA8B;;IAEvC,IAAIxD,OAAO,EAAEwD,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,6CAA6C;;IAEtD,OAAO,EAAE;EACX;EAEAE,oBAAoB;IAClB,MAAM1D,OAAO,GAAG,IAAI,CAACtE,YAAY,CAACuE,GAAG,CAAC,OAAO,CAAC;IAC9C,IAAID,OAAO,EAAEwD,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,wBAAwB;;IAEjC,IAAIxD,OAAO,EAAEwD,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,OAAO,oCAAoC;;IAE7C,OAAO,EAAE;EACX;EAEAG,uBAAuB;IACrB,MAAM3D,OAAO,GAAG,IAAI,CAACtE,YAAY,CAACuE,GAAG,CAAC,UAAU,CAAC;IACjD,IAAID,OAAO,EAAEwD,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,yBAAyB;;IAElC,IAAIxD,OAAO,EAAEwD,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,wCAAwC;;IAEjD,OAAO,EAAE;EACX;EAEAI,8BAA8B;IAC5B,MAAM5D,OAAO,GAAG,IAAI,CAACtE,YAAY,CAACuE,GAAG,CAAC,iBAAiB,CAAC;IACxD,IAAID,OAAO,EAAEwD,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,8BAA8B;;IAEvC,IAAI,IAAI,CAAC9H,YAAY,CAAC8H,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAClD,OAAO,sBAAsB;;IAE/B,OAAO,EAAE;EACX;EAEA;EACAK,oBAAoB,CAACC,SAAiB;IACpC,MAAM9D,OAAO,GAAG,IAAI,CAACtE,YAAY,CAACuE,GAAG,CAAC6D,SAAS,CAAC;IAChD,IAAI,CAAC9D,OAAO,EAAE,OAAO,EAAE;IAEvB,IAAIA,OAAO,CAACwD,QAAQ,CAAC,UAAU,CAAC,EAAE;MAChC,OAAO,0BAA0B;;IAEnC,IAAIxD,OAAO,CAACwD,QAAQ,CAAC,WAAW,CAAC,EAAE;MACjC,OAAO,WAAWxD,OAAO,CAAC+D,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc,UAAU;;IAE1E,IAAIhE,OAAO,CAACwD,QAAQ,CAAC,WAAW,CAAC,EAAE;MACjC,OAAO,YAAYxD,OAAO,CAAC+D,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc,UAAU;;IAE3E,IAAIhE,OAAO,CAACwD,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC3B,OAAO,uBAAuBxD,OAAO,CAAC+D,MAAM,GAAG,KAAK,CAAC,CAACvG,GAAG,EAAE;;IAE7D,IAAIwC,OAAO,CAACwD,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7B,OAAO,oCAAoC;;IAE7C,OAAO,EAAE;EACX;EAEAS,eAAe;IACb,IAAI,CAAC1I,MAAM,CAACY,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAAEJ,WAAW,EAAE;QAAEF,SAAS,EAAE,IAAI,CAACA;MAAS;IAAE,CAAE,CAAC;EAClF;EAEAqI,gBAAgB;IACd,IAAI,CAAC1C,SAAS,GAAG,IAAI;IAErB,IAAI,CAAClG,YAAY,CAAC6I,qBAAqB,EAAE,CAAClI,SAAS,CAAC;MAClDiG,IAAI,EAAGkC,SAAoB,IAAI;QAC7B,IAAI,CAACC,iBAAiB,CAACD,SAAS,CAAC;MACnC,CAAC;MACD3B,KAAK,EAAE,MAAK;QACV,IAAI,CAACjB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC/F,QAAQ,CAACmG,IAAI,CAAC,0DAA0D,EAAE,SAAS,EAAE;UACxFC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEAwC,kBAAkB;IAChB,IAAI,CAAC9C,SAAS,GAAG,IAAI;IAErB,IAAI,CAAClG,YAAY,CAACiJ,kBAAkB,EAAE,CAACtI,SAAS,CAAC;MAC/CiG,IAAI,EAAGkC,SAAoB,IAAI;QAC7B,IAAI,CAACC,iBAAiB,CAACD,SAAS,CAAC;MACnC,CAAC;MACD3B,KAAK,EAAE,MAAK;QACV,IAAI,CAACjB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC/F,QAAQ,CAACmG,IAAI,CAAC,4DAA4D,EAAE,SAAS,EAAE;UAC1FC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEQuC,iBAAiB,CAACD,SAAoB;IAC5C,MAAMI,YAAY,GAAsB;MACtCC,QAAQ,EAAEL,SAAS,CAACK,QAAQ;MAC5BC,WAAW,EAAEN,SAAS,CAACM,WAAW;MAClCjI,KAAK,EAAE2H,SAAS,CAAC3H,KAAK;MACtBJ,SAAS,EAAE+H,SAAS,CAAC/H,SAAS;MAC9BG,QAAQ,EAAE4H,SAAS,CAAC5H,QAAQ;MAC5BmI,iBAAiB,EAAEP,SAAS,CAACO;KAC9B;IAED,IAAI,CAACtJ,WAAW,CAACuJ,cAAc,CAACJ,YAAY,CAAC,CAACvI,SAAS,CAAC;MACtDiG,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAIW,QAAQ,CAACC,OAAO,EAAE;UACpB,MAAMyC,YAAY,GAAGT,SAAS,CAACK,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,UAAU;UAC5E,IAAI,CAAChJ,QAAQ,CAACmG,IAAI,CAAC,+CAA+CiD,YAAY,EAAE,EAAE,SAAS,EAAE;YAC3FhD,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UAEF;UACAO,OAAO,CAACC,GAAG,CAAC,sCAAsCuC,YAAY,6BAA6B,CAAC;UAC5FxC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEH,QAAQ,CAACI,IAAI,CAAC;UAExC;UACA,IAAI,CAAChH,MAAM,CAACY,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;SACxC,MAAM;UACL,IAAI,CAACV,QAAQ,CAACmG,IAAI,CAACO,QAAQ,CAACK,OAAO,IAAI,+BAA+B,EAAE,SAAS,EAAE;YACjFX,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;;MAEN,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC/F,QAAQ,CAACmG,IAAI,CAACa,KAAK,IAAI,uDAAuD,EAAE,SAAS,EAAE;UAC9FZ,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;;;uBAxlBW9G,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAA8J;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCb9BnK,8BAAgC;UAIQA,0BAAU;UAAAA,iBAAW;UACrDA,YACF;UAAAA,iBAAiB;UACjBA,yCAAmB;UACjBA,oEAA0G;UAC1GA,oEAOO;UACTA,iBAAoB;UAGtBA,yCAAkB;UAEhBA,qEA2BM;UAENA,wEAmjBO;UACTA,iBAAmB;UAEnBA,4CAA2C;UACzCA,+BAA2B;UAE3BA,+BAA2B;UACHA,8GAAkB;UAAAA,iBAAI;UAC5CA,mCAKqB;UADnBA;YAAA,OAASoK,qBAAiB;UAAA,EAAC;UAE3BpK,iCAAU;UAAAA,sBAAK;UAAAA,iBAAW;UAC1BA,sHACF;UAAAA,iBAAS;;;UAjnBTA,eACF;UADEA,mDACF;UAESA,eAAoC;UAApCA,yDAAoC;UACpCA,eAA4B;UAA5BA,iDAA4B;UAaKA,eAAuB;UAAvBA,4CAAuB;UA8B1DA,eAA8D;UAA9DA,uFAA8D", "names": ["Validators", "i0", "RegisterComponent", "isOracleRegistration", "registrationType", "constructor", "formBuilder", "authService", "oauthService", "router", "route", "snackBar", "registerForm", "createGeneralForm", "ngOnInit", "returnUrl", "snapshot", "queryParams", "isAuthenticated$", "subscribe", "isAuth", "navigate", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "email", "phoneNumber", "password", "confirmPassword", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "createOracleForm", "professional<PERSON>itle", "headline", "max<PERSON><PERSON><PERSON>", "summary", "primarySpecialization", "yearsOfExperience", "min", "city", "state", "country", "displayLocation", "birthDate", "birthTime", "birthLocation", "astrologicalSign", "oracleTypes", "languagesSpoken", "skills", "website", "portfolioUrl", "businessStreet", "businessCity", "businessState", "businessPostalCode", "businessCountry", "isBusinessAddressPublic", "hourlyRate", "sessionRate", "currency", "setRegistrationType", "type", "totalSteps", "currentStep", "nextStep", "isCurrentStepValid", "previousStep", "valid", "step1Fields", "step2Fields", "step3Fields", "step4Fields", "step5Fields", "step6Fields", "fieldsToValidate", "every", "field", "control", "get", "getStepTitle", "getSpecializationOptions", "getOracleTypeOptions", "getLanguageOptions", "getAstrologicalSigns", "toggleTimeFormat", "timeFormat24h", "addSkill", "event", "input", "preventDefault", "value", "trim", "currentSkills", "includes", "updatedSkills", "setValue", "addSkillFromInput", "removeSkill", "index", "splice", "onSubmit", "isLoading", "submitGeneralRegistration", "submitOracleRegistration", "markFormGroupTouched", "open", "duration", "panelClass", "registerRequest", "undefined", "register", "next", "response", "success", "console", "log", "user", "message", "error", "formValue", "oracleRequest", "businessAddress", "street", "postalCode", "isPublic", "consultationRates", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "getFirstNameErrorMessage", "<PERSON><PERSON><PERSON><PERSON>", "getLastNameErrorMessage", "getEmailErrorMessage", "getPasswordErrorMessage", "getConfirmPasswordErrorMessage", "getFieldErrorMessage", "fieldName", "errors", "<PERSON><PERSON><PERSON><PERSON>", "navigateToLogin", "signUpWithGoogle", "signInWithGooglePopup", "<PERSON><PERSON><PERSON><PERSON>ser", "handleOAuthSignUp", "signUpWithFacebook", "signInWithFacebook", "oauthRequest", "provider", "accessToken", "profilePictureUrl", "loginWithOAuth", "providerName", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\register\\register.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../services/auth.service';\r\nimport { OAuthService, OAuthUser } from '../services/oauth.service';\r\nimport { RegisterRequest, OracleRegisterRequest, OAuthLoginRequest } from '../models/auth.models';\r\n\r\n@Component({\r\n  selector: 'app-register',\r\n  templateUrl: './register.component.html',\r\n  styleUrls: ['./register.component.css']\r\n})\r\nexport class RegisterComponent implements OnInit {\r\n  registerForm: FormGroup;\r\n  isLoading = false;\r\n  hidePassword = true;\r\n  hideConfirmPassword = true;\r\n  returnUrl = '/';\r\n\r\n  // Registration type\r\n  registrationType: 'general' | 'oracle' = 'general';\r\n\r\n  // Time format preference\r\n  timeFormat24h = true;\r\n\r\n  // Helper method for template\r\n  isOracleRegistration(): boolean {\r\n    return this.registrationType === 'oracle';\r\n  }\r\n  currentStep = 1;\r\n  totalSteps = 1;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private authService: AuthService,\r\n    private oauthService: OAuthService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.registerForm = this.createGeneralForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Get return url from route parameters or default to '/'\r\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\r\n\r\n    // Redirect if already logged in\r\n    this.authService.isAuthenticated$.subscribe(isAuth => {\r\n      if (isAuth) {\r\n        // If user is already authenticated, redirect to profile page instead of home\r\n        this.router.navigate(['/profile/edit']);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Form creation methods\r\n  private createGeneralForm(): FormGroup {\r\n    return this.formBuilder.group({\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      phoneNumber: [''],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      confirmPassword: ['', [Validators.required]],\r\n      acceptTerms: [false, [Validators.requiredTrue]]\r\n    }, { validators: this.passwordMatchValidator });\r\n  }\r\n\r\n  private createOracleForm(): FormGroup {\r\n    return this.formBuilder.group({\r\n      // Basic Information (Step 1)\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      phoneNumber: [''],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      confirmPassword: ['', [Validators.required]],\r\n\r\n      // Professional Information (Step 2)\r\n      professionalTitle: ['', [Validators.required]],\r\n      headline: ['', [Validators.required, Validators.maxLength(220)]],\r\n      summary: ['', [Validators.required, Validators.maxLength(2000)]],\r\n      primarySpecialization: ['', [Validators.required]],\r\n      yearsOfExperience: [0, [Validators.required, Validators.min(0)]],\r\n\r\n      // Location Information (Step 3)\r\n      city: ['', [Validators.required]],\r\n      state: [''],\r\n      country: ['', [Validators.required]],\r\n      displayLocation: [''],\r\n\r\n      // Oracle-Specific Information (Step 4)\r\n      birthDate: ['', [Validators.required]],\r\n      birthTime: [''],\r\n      birthLocation: ['', [Validators.required]],\r\n      astrologicalSign: [''],\r\n      oracleTypes: [[], [Validators.required]],\r\n      languagesSpoken: [['Български'], [Validators.required]],\r\n      skills: [[], [Validators.required]],\r\n\r\n      // Contact & Business Information (Step 5)\r\n      website: [''],\r\n      portfolioUrl: [''],\r\n      businessStreet: [''],\r\n      businessCity: [''],\r\n      businessState: [''],\r\n      businessPostalCode: [''],\r\n      businessCountry: [''],\r\n      isBusinessAddressPublic: [false],\r\n\r\n      // Consultation Rates (Step 6)\r\n      hourlyRate: [0],\r\n      sessionRate: [0],\r\n      currency: ['BGN'],\r\n\r\n      acceptTerms: [false, [Validators.requiredTrue]]\r\n    }, { validators: this.passwordMatchValidator });\r\n  }\r\n\r\n  // Registration type switching\r\n  setRegistrationType(type: 'general' | 'oracle'): void {\r\n    this.registrationType = type;\r\n    if (type === 'oracle') {\r\n      this.registerForm = this.createOracleForm();\r\n      this.totalSteps = 6;\r\n      this.currentStep = 1;\r\n    } else {\r\n      this.registerForm = this.createGeneralForm();\r\n      this.totalSteps = 1;\r\n      this.currentStep = 1;\r\n    }\r\n  }\r\n\r\n  // Step navigation for oracle registration\r\n  nextStep(): void {\r\n    if (this.currentStep < this.totalSteps && this.isCurrentStepValid()) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep(): void {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  isCurrentStepValid(): boolean {\r\n    if (this.registrationType === 'general') {\r\n      return this.registerForm.valid;\r\n    }\r\n\r\n    // Validate current step for oracle registration\r\n    const step1Fields = ['firstName', 'lastName', 'email', 'password', 'confirmPassword'];\r\n    const step2Fields = ['professionalTitle', 'headline', 'summary', 'primarySpecialization', 'yearsOfExperience'];\r\n    const step3Fields = ['city', 'country'];\r\n    const step4Fields = ['birthDate', 'birthLocation', 'oracleTypes', 'languagesSpoken', 'skills'];\r\n    const step5Fields: string[] = []; // All optional\r\n    const step6Fields = ['currency', 'acceptTerms'];\r\n\r\n    let fieldsToValidate: string[] = [];\r\n    switch (this.currentStep) {\r\n      case 1: fieldsToValidate = step1Fields; break;\r\n      case 2: fieldsToValidate = step2Fields; break;\r\n      case 3: fieldsToValidate = step3Fields; break;\r\n      case 4: fieldsToValidate = step4Fields; break;\r\n      case 5: fieldsToValidate = step5Fields; break;\r\n      case 6: fieldsToValidate = step6Fields; break;\r\n    }\r\n\r\n    return fieldsToValidate.every(field => {\r\n      const control = this.registerForm.get(field);\r\n      return control ? control.valid : true;\r\n    });\r\n  }\r\n\r\n  // Get step title in Bulgarian\r\n  getStepTitle(): string {\r\n    if (this.registrationType === 'general') {\r\n      return 'Създаване на акаунт';\r\n    }\r\n\r\n    switch (this.currentStep) {\r\n      case 1: return 'Основна информация';\r\n      case 2: return 'Професионална информация';\r\n      case 3: return 'Местоположение';\r\n      case 4: return 'Оракулска информация';\r\n      case 5: return 'Контакти и бизнес';\r\n      case 6: return 'Тарифи и условия';\r\n      default: return 'Регистрация';\r\n    }\r\n  }\r\n\r\n  // Helper methods for oracle registration\r\n  getSpecializationOptions(): string[] {\r\n    return [\r\n      'Астрология',\r\n      'Таро',\r\n      'Кристални лечения',\r\n      'Нумерология',\r\n      'Хиромантия',\r\n      'Рунология',\r\n      'Медиумизъм',\r\n      'Енергийно лечение',\r\n      'Аура четене',\r\n      'Духовно консултиране'\r\n    ];\r\n  }\r\n\r\n  getOracleTypeOptions(): string[] {\r\n    return [\r\n      'Таро карти',\r\n      'Астрологични карти',\r\n      'Кристали',\r\n      'Руни',\r\n      'Нумерология',\r\n      'Хиромантия',\r\n      'Медитация',\r\n      'Енергийна работа',\r\n      'Аура четене',\r\n      'Духовно водачество'\r\n    ];\r\n  }\r\n\r\n  getLanguageOptions(): string[] {\r\n    return [\r\n      'Български',\r\n      'Английски',\r\n      'Руски',\r\n      'Немски',\r\n      'Френски',\r\n      'Испански',\r\n      'Италиански',\r\n      'Турски'\r\n    ];\r\n  }\r\n\r\n  getAstrologicalSigns(): string[] {\r\n    return [\r\n      'Овен', 'Телец', 'Близнаци', 'Рак', 'Лъв', 'Дева',\r\n      'Везни', 'Скорпион', 'Стрелец', 'Козирог', 'Водолей', 'Риби'\r\n    ];\r\n  }\r\n\r\n  // Time format toggle\r\n  toggleTimeFormat(): void {\r\n    this.timeFormat24h = !this.timeFormat24h;\r\n  }\r\n\r\n  // Skill management methods\r\n  addSkill(event: KeyboardEvent, input: HTMLInputElement): void {\r\n    event.preventDefault();\r\n    const value = input.value.trim();\r\n\r\n    if (value) {\r\n      const currentSkills = this.registerForm.get('skills')?.value || [];\r\n      if (!currentSkills.includes(value)) {\r\n        const updatedSkills = [...currentSkills, value];\r\n        this.registerForm.get('skills')?.setValue(updatedSkills);\r\n        input.value = '';\r\n      }\r\n    }\r\n  }\r\n\r\n  addSkillFromInput(input: HTMLInputElement): void {\r\n    const value = input.value.trim();\r\n\r\n    if (value) {\r\n      const currentSkills = this.registerForm.get('skills')?.value || [];\r\n      if (!currentSkills.includes(value)) {\r\n        const updatedSkills = [...currentSkills, value];\r\n        this.registerForm.get('skills')?.setValue(updatedSkills);\r\n        input.value = '';\r\n      }\r\n    }\r\n  }\r\n\r\n  removeSkill(index: number): void {\r\n    const currentSkills = this.registerForm.get('skills')?.value || [];\r\n    currentSkills.splice(index, 1);\r\n    this.registerForm.get('skills')?.setValue(currentSkills);\r\n  }\r\n\r\n  passwordMatchValidator(control: AbstractControl): { [key: string]: boolean } | null {\r\n    const password = control.get('password');\r\n    const confirmPassword = control.get('confirmPassword');\r\n\r\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\r\n      return { 'passwordMismatch': true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.registrationType === 'oracle' && this.currentStep < this.totalSteps) {\r\n      // For oracle registration, move to next step if not on final step\r\n      this.nextStep();\r\n      return;\r\n    }\r\n\r\n    if (this.registerForm.valid) {\r\n      this.isLoading = true;\r\n\r\n      if (this.registrationType === 'general') {\r\n        this.submitGeneralRegistration();\r\n      } else {\r\n        this.submitOracleRegistration();\r\n      }\r\n    } else {\r\n      this.markFormGroupTouched();\r\n      this.snackBar.open('Моля, попълнете всички задължителни полета', 'Затвори', {\r\n        duration: 3000,\r\n        panelClass: ['error-snackbar']\r\n      });\r\n    }\r\n  }\r\n\r\n  private submitGeneralRegistration(): void {\r\n    const registerRequest: RegisterRequest = {\r\n      firstName: this.registerForm.value.firstName,\r\n      lastName: this.registerForm.value.lastName,\r\n      email: this.registerForm.value.email,\r\n      phoneNumber: this.registerForm.value.phoneNumber || undefined,\r\n      password: this.registerForm.value.password,\r\n      confirmPassword: this.registerForm.value.confirmPassword,\r\n      acceptTerms: this.registerForm.value.acceptTerms\r\n    };\r\n\r\n    this.authService.register(registerRequest).subscribe({\r\n      next: (response) => {\r\n        this.isLoading = false;\r\n        if (response.success) {\r\n          this.snackBar.open('Регистрацията е успешна! Добре дошли в Оракул!', 'Затвори', {\r\n            duration: 5000,\r\n            panelClass: ['success-snackbar']\r\n          });\r\n\r\n          // Auto-authenticate and redirect to profile page\r\n          console.log('Registration successful, user is now authenticated');\r\n          console.log('User info:', response.user);\r\n\r\n          // Redirect to profile edit page for completing profile\r\n          this.router.navigate(['/profile/edit']);\r\n        } else {\r\n          this.snackBar.open(response.message || 'Регистрацията неуспешна', 'Затвори', {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open(error || 'Регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private submitOracleRegistration(): void {\r\n    const formValue = this.registerForm.value;\r\n    const oracleRequest: OracleRegisterRequest = {\r\n      // Basic information\r\n      firstName: formValue.firstName,\r\n      lastName: formValue.lastName,\r\n      email: formValue.email,\r\n      phoneNumber: formValue.phoneNumber || undefined,\r\n      password: formValue.password,\r\n      confirmPassword: formValue.confirmPassword,\r\n      acceptTerms: formValue.acceptTerms,\r\n\r\n      // Professional information\r\n      professionalTitle: formValue.professionalTitle,\r\n      headline: formValue.headline,\r\n      summary: formValue.summary,\r\n      primarySpecialization: formValue.primarySpecialization,\r\n      yearsOfExperience: formValue.yearsOfExperience,\r\n\r\n      // Location information\r\n      city: formValue.city,\r\n      state: formValue.state,\r\n      country: formValue.country,\r\n      displayLocation: formValue.displayLocation || `${formValue.city}, ${formValue.country}`,\r\n\r\n      // Oracle-specific information\r\n      birthDate: formValue.birthDate,\r\n      birthTime: formValue.birthTime,\r\n      birthLocation: formValue.birthLocation,\r\n      astrologicalSign: formValue.astrologicalSign,\r\n      oracleTypes: formValue.oracleTypes,\r\n      languagesSpoken: formValue.languagesSpoken,\r\n      skills: formValue.skills,\r\n\r\n      // Contact & business information\r\n      website: formValue.website,\r\n      portfolioUrl: formValue.portfolioUrl,\r\n      businessAddress: {\r\n        street: formValue.businessStreet,\r\n        city: formValue.businessCity,\r\n        state: formValue.businessState,\r\n        postalCode: formValue.businessPostalCode,\r\n        country: formValue.businessCountry,\r\n        isPublic: formValue.isBusinessAddressPublic\r\n      },\r\n\r\n      // Consultation rates\r\n      consultationRates: {\r\n        hourlyRate: formValue.hourlyRate,\r\n        sessionRate: formValue.sessionRate,\r\n        currency: formValue.currency\r\n      }\r\n    };\r\n\r\n    // TODO: Implement oracle registration API call\r\n    // For now, use the general registration and then create profile\r\n    this.authService.register(oracleRequest).subscribe({\r\n      next: (response) => {\r\n        this.isLoading = false;\r\n        if (response.success) {\r\n          this.snackBar.open('Регистрацията като оракул е успешна! Добре дошли в Оракул!', 'Затвори', {\r\n            duration: 5000,\r\n            panelClass: ['success-snackbar']\r\n          });\r\n\r\n          // Auto-authenticate and redirect to profile page\r\n          console.log('Oracle registration successful, user is now authenticated');\r\n          console.log('User info:', response.user);\r\n\r\n          // Redirect to profile edit page for completing/reviewing profile\r\n          this.router.navigate(['/profile/edit']);\r\n        } else {\r\n          this.snackBar.open(response.message || 'Регистрацията неуспешна', 'Затвори', {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open(error || 'Регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.registerForm.controls).forEach(key => {\r\n      const control = this.registerForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  getFirstNameErrorMessage(): string {\r\n    const control = this.registerForm.get('firstName');\r\n    if (control?.hasError('required')) {\r\n      return 'Първото име е задължително';\r\n    }\r\n    if (control?.hasError('minlength')) {\r\n      return 'Първото име трябва да бъде поне 2 символа';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getLastNameErrorMessage(): string {\r\n    const control = this.registerForm.get('lastName');\r\n    if (control?.hasError('required')) {\r\n      return 'Фамилното име е задължително';\r\n    }\r\n    if (control?.hasError('minlength')) {\r\n      return 'Фамилното име трябва да бъде поне 2 символа';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getEmailErrorMessage(): string {\r\n    const control = this.registerForm.get('email');\r\n    if (control?.hasError('required')) {\r\n      return 'Имейлът е задължителен';\r\n    }\r\n    if (control?.hasError('email')) {\r\n      return 'Моля, въведете валиден имейл адрес';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getPasswordErrorMessage(): string {\r\n    const control = this.registerForm.get('password');\r\n    if (control?.hasError('required')) {\r\n      return 'Паролата е задължителна';\r\n    }\r\n    if (control?.hasError('minlength')) {\r\n      return 'Паролата трябва да бъде поне 6 символа';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getConfirmPasswordErrorMessage(): string {\r\n    const control = this.registerForm.get('confirmPassword');\r\n    if (control?.hasError('required')) {\r\n      return 'Моля, потвърдете паролата си';\r\n    }\r\n    if (this.registerForm.hasError('passwordMismatch')) {\r\n      return 'Паролите не съвпадат';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Additional error message methods for oracle fields\r\n  getFieldErrorMessage(fieldName: string): string {\r\n    const control = this.registerForm.get(fieldName);\r\n    if (!control) return '';\r\n\r\n    if (control.hasError('required')) {\r\n      return 'Това поле е задължително';\r\n    }\r\n    if (control.hasError('minlength')) {\r\n      return `Минимум ${control.errors?.['minlength'].requiredLength} символа`;\r\n    }\r\n    if (control.hasError('maxlength')) {\r\n      return `Максимум ${control.errors?.['maxlength'].requiredLength} символа`;\r\n    }\r\n    if (control.hasError('min')) {\r\n      return `Минимална стойност: ${control.errors?.['min'].min}`;\r\n    }\r\n    if (control.hasError('email')) {\r\n      return 'Моля, въведете валиден имейл адрес';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  navigateToLogin(): void {\r\n    this.router.navigate(['/login'], { queryParams: { returnUrl: this.returnUrl } });\r\n  }\r\n\r\n  signUpWithGoogle(): void {\r\n    this.isLoading = true;\r\n\r\n    this.oauthService.signInWithGooglePopup().subscribe({\r\n      next: (oauthUser: OAuthUser) => {\r\n        this.handleOAuthSignUp(oauthUser);\r\n      },\r\n      error: () => {\r\n        this.isLoading = false;\r\n        this.snackBar.open('Регистрацията с Google неуспешна. Моля, опитайте отново.', 'Затвори', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  signUpWithFacebook(): void {\r\n    this.isLoading = true;\r\n\r\n    this.oauthService.signInWithFacebook().subscribe({\r\n      next: (oauthUser: OAuthUser) => {\r\n        this.handleOAuthSignUp(oauthUser);\r\n      },\r\n      error: () => {\r\n        this.isLoading = false;\r\n        this.snackBar.open('Регистрацията с Facebook неуспешна. Моля, опитайте отново.', 'Затвори', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleOAuthSignUp(oauthUser: OAuthUser): void {\r\n    const oauthRequest: OAuthLoginRequest = {\r\n      provider: oauthUser.provider,\r\n      accessToken: oauthUser.accessToken,\r\n      email: oauthUser.email,\r\n      firstName: oauthUser.firstName,\r\n      lastName: oauthUser.lastName,\r\n      profilePictureUrl: oauthUser.profilePictureUrl\r\n    };\r\n\r\n    this.authService.loginWithOAuth(oauthRequest).subscribe({\r\n      next: (response) => {\r\n        this.isLoading = false;\r\n        if (response.success) {\r\n          const providerName = oauthUser.provider === 'google' ? 'Google' : 'Facebook';\r\n          this.snackBar.open(`Добре дошли в Оракул! Акаунтът е създаден с ${providerName}`, 'Затвори', {\r\n            duration: 5000,\r\n            panelClass: ['success-snackbar']\r\n          });\r\n\r\n          // Auto-authenticate and redirect to profile page\r\n          console.log(`OAuth registration successful with ${providerName}, user is now authenticated`);\r\n          console.log('User info:', response.user);\r\n\r\n          // Redirect to profile edit page for completing profile\r\n          this.router.navigate(['/profile/edit']);\r\n        } else {\r\n          this.snackBar.open(response.message || 'OAuth регистрацията неуспешна', 'Затвори', {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n        this.snackBar.open(error || 'OAuth регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"register-container\">\r\n  <mat-card class=\"register-card\">\r\n    <mat-card-header class=\"register-header\">\r\n      <mat-card-title>\r\n        <mat-icon class=\"register-icon\">person_add</mat-icon>\r\n        {{ getStepTitle() }}\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        <span *ngIf=\"registrationType === 'general'\">Присъединете се към Оракул и започнете своето пътуване</span>\r\n        <span *ngIf=\"isOracleRegistration()\">\r\n          Стъпка {{ currentStep }} от {{ totalSteps }}\r\n          <mat-progress-bar\r\n            mode=\"determinate\"\r\n            [value]=\"(currentStep / totalSteps) * 100\"\r\n            class=\"step-progress\">\r\n          </mat-progress-bar>\r\n        </span>\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <!-- Registration Type Selection -->\r\n      <div class=\"registration-type-selection\" *ngIf=\"currentStep === 1\">\r\n        <h3>Изберете тип регистрация</h3>\r\n        <div class=\"type-buttons-compact\">\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"type-button-compact general-button\"\r\n            [class.selected]=\"registrationType === 'general'\"\r\n            (click)=\"setRegistrationType('general')\">\r\n            <mat-icon>person</mat-icon>\r\n            <span>Обикновен потребител</span>\r\n          </button>\r\n\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"type-button-compact oracle-button\"\r\n            [class.selected]=\"isOracleRegistration()\"\r\n            (click)=\"setRegistrationType('oracle')\">\r\n            <mat-icon>auto_awesome</mat-icon>\r\n            <span>Оракул</span>\r\n          </button>\r\n        </div>\r\n        <p class=\"type-description\">\r\n          <span *ngIf=\"registrationType === 'general'\">За хора, които търсят духовно водачество</span>\r\n          <span *ngIf=\"isOracleRegistration()\">За практикуващи астролози, таро читци и духовни консултанти</span>\r\n        </p>\r\n      </div>\r\n\r\n      <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"register-form\"\r\n            *ngIf=\"isOracleRegistration() || registrationType === 'general'\">\r\n\r\n        <!-- Step 1: Basic Information (Both types) -->\r\n        <div *ngIf=\"currentStep === 1\" class=\"form-step\">\r\n          <!-- Name Fields Row -->\r\n          <div class=\"name-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Първо име</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"text\"\r\n                formControlName=\"firstName\"\r\n                placeholder=\"Въведете първо име\"\r\n                autocomplete=\"given-name\">\r\n              <mat-icon matSuffix>person</mat-icon>\r\n              <mat-error *ngIf=\"registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched\">\r\n                {{ getFirstNameErrorMessage() }}\r\n              </mat-error>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Фамилно име</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"text\"\r\n                formControlName=\"lastName\"\r\n                placeholder=\"Въведете фамилно име\"\r\n                autocomplete=\"family-name\">\r\n              <mat-icon matSuffix>person</mat-icon>\r\n              <mat-error *ngIf=\"registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched\">\r\n                {{ getLastNameErrorMessage() }}\r\n              </mat-error>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <!-- Email Field -->\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Имейл адрес</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"email\"\r\n              formControlName=\"email\"\r\n              placeholder=\"Въведете вашия имейл\"\r\n              autocomplete=\"email\">\r\n            <mat-icon matSuffix>email</mat-icon>\r\n            <mat-error *ngIf=\"registerForm.get('email')?.invalid && registerForm.get('email')?.touched\">\r\n              {{ getEmailErrorMessage() }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Phone Number Field (Optional) -->\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Телефонен номер (по избор)</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"tel\"\r\n              formControlName=\"phoneNumber\"\r\n              placeholder=\"Въведете телефонен номер\"\r\n              autocomplete=\"tel\">\r\n            <mat-icon matSuffix>phone</mat-icon>\r\n          </mat-form-field>\r\n\r\n          <!-- Password Field -->\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Парола</mat-label>\r\n            <input\r\n              matInput\r\n              [type]=\"hidePassword ? 'password' : 'text'\"\r\n              formControlName=\"password\"\r\n              placeholder=\"Създайте парола\"\r\n              autocomplete=\"new-password\">\r\n            <button\r\n              mat-icon-button\r\n              matSuffix\r\n              type=\"button\"\r\n              (click)=\"hidePassword = !hidePassword\"\r\n              [attr.aria-label]=\"'Скрий парола'\"\r\n              [attr.aria-pressed]=\"hidePassword\">\r\n              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n            </button>\r\n            <mat-error *ngIf=\"registerForm.get('password')?.invalid && registerForm.get('password')?.touched\">\r\n              {{ getPasswordErrorMessage() }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Confirm Password Field -->\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Потвърдете паролата</mat-label>\r\n            <input\r\n              matInput\r\n              [type]=\"hideConfirmPassword ? 'password' : 'text'\"\r\n              formControlName=\"confirmPassword\"\r\n              placeholder=\"Потвърдете паролата\"\r\n              autocomplete=\"new-password\">\r\n            <button\r\n              mat-icon-button\r\n              matSuffix\r\n              type=\"button\"\r\n              (click)=\"hideConfirmPassword = !hideConfirmPassword\"\r\n              [attr.aria-label]=\"'Скрий парола'\"\r\n              [attr.aria-pressed]=\"hideConfirmPassword\">\r\n              <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n            </button>\r\n            <mat-error *ngIf=\"(registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) || registerForm.hasError('passwordMismatch')\">\r\n              {{ getConfirmPasswordErrorMessage() }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Terms and Conditions for General Users -->\r\n          <div class=\"terms-section\" *ngIf=\"registrationType === 'general'\">\r\n            <mat-checkbox formControlName=\"acceptTerms\" color=\"primary\">\r\n              Съгласявам се с <a href=\"#\" target=\"_blank\">Условията за ползване</a> и <a href=\"#\" target=\"_blank\">Политиката за поверителност</a>\r\n            </mat-checkbox>\r\n            <mat-error *ngIf=\"registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched\">\r\n              Трябва да приемете условията и политиката за поверителност\r\n            </mat-error>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 2: Professional Information (Oracle only) -->\r\n        <div *ngIf=\"currentStep === 2 && isOracleRegistration()\" class=\"form-step\">\r\n          <h3>Професионална информация</h3>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Професионална титла</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"text\"\r\n              formControlName=\"professionalTitle\"\r\n              placeholder=\"напр. Астролог, Таро читец, Кристален лечител\">\r\n            <mat-icon matSuffix>work</mat-icon>\r\n            <mat-error *ngIf=\"registerForm.get('professionalTitle')?.invalid && registerForm.get('professionalTitle')?.touched\">\r\n              {{ getFieldErrorMessage('professionalTitle') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Основна специализация</mat-label>\r\n            <mat-select formControlName=\"primarySpecialization\">\r\n              <mat-option *ngFor=\"let spec of getSpecializationOptions()\" [value]=\"spec\">\r\n                {{ spec }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-error *ngIf=\"registerForm.get('primarySpecialization')?.invalid && registerForm.get('primarySpecialization')?.touched\">\r\n              {{ getFieldErrorMessage('primarySpecialization') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Заглавие (кратко описание)</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"text\"\r\n              formControlName=\"headline\"\r\n              placeholder=\"Кратко описание на вашите услуги\"\r\n              maxlength=\"220\">\r\n            <mat-icon matSuffix>title</mat-icon>\r\n            <mat-hint>{{ registerForm.get('headline')?.value?.length || 0 }}/220</mat-hint>\r\n            <mat-error *ngIf=\"registerForm.get('headline')?.invalid && registerForm.get('headline')?.touched\">\r\n              {{ getFieldErrorMessage('headline') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Подробно описание</mat-label>\r\n            <textarea\r\n              matInput\r\n              formControlName=\"summary\"\r\n              placeholder=\"Подробно описание на вашия опит и услуги\"\r\n              rows=\"4\"\r\n              maxlength=\"2000\">\r\n            </textarea>\r\n            <mat-hint>{{ registerForm.get('summary')?.value?.length || 0 }}/2000</mat-hint>\r\n            <mat-error *ngIf=\"registerForm.get('summary')?.invalid && registerForm.get('summary')?.touched\">\r\n              {{ getFieldErrorMessage('summary') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Години опит</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"number\"\r\n              formControlName=\"yearsOfExperience\"\r\n              placeholder=\"0\"\r\n              min=\"0\"\r\n              max=\"50\">\r\n            <mat-icon matSuffix>schedule</mat-icon>\r\n            <mat-error *ngIf=\"registerForm.get('yearsOfExperience')?.invalid && registerForm.get('yearsOfExperience')?.touched\">\r\n              {{ getFieldErrorMessage('yearsOfExperience') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <!-- Step 3: Location Information (Oracle only) -->\r\n        <div *ngIf=\"currentStep === 3 && isOracleRegistration()\" class=\"form-step\">\r\n          <h3>Местоположение</h3>\r\n\r\n          <div class=\"location-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Град</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"text\"\r\n                formControlName=\"city\"\r\n                placeholder=\"Въведете град\">\r\n              <mat-icon matSuffix>location_city</mat-icon>\r\n              <mat-error *ngIf=\"registerForm.get('city')?.invalid && registerForm.get('city')?.touched\">\r\n                {{ getFieldErrorMessage('city') }}\r\n              </mat-error>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Област/Регион</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"text\"\r\n                formControlName=\"state\"\r\n                placeholder=\"Въведете област\">\r\n              <mat-icon matSuffix>map</mat-icon>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Държава</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"text\"\r\n              formControlName=\"country\"\r\n              placeholder=\"Въведете държава\">\r\n            <mat-icon matSuffix>public</mat-icon>\r\n            <mat-error *ngIf=\"registerForm.get('country')?.invalid && registerForm.get('country')?.touched\">\r\n              {{ getFieldErrorMessage('country') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Показвано местоположение (по избор)</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"text\"\r\n              formControlName=\"displayLocation\"\r\n              placeholder=\"Как да се показва местоположението ви публично\">\r\n            <mat-icon matSuffix>visibility</mat-icon>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <!-- Step 4: Oracle-Specific Information -->\r\n        <div *ngIf=\"currentStep === 4 && isOracleRegistration()\" class=\"form-step\">\r\n          <h3>Оракулска информация</h3>\r\n\r\n          <div class=\"birth-info-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Дата на раждане</mat-label>\r\n              <input\r\n                matInput\r\n                [matDatepicker]=\"birthDatePicker\"\r\n                formControlName=\"birthDate\"\r\n                placeholder=\"дд.мм.гггг\"\r\n                type=\"text\">\r\n              <mat-datepicker-toggle matSuffix [for]=\"birthDatePicker\">\r\n                <mat-icon matDatepickerToggleIcon>cake</mat-icon>\r\n              </mat-datepicker-toggle>\r\n              <mat-datepicker #birthDatePicker></mat-datepicker>\r\n              <mat-error *ngIf=\"registerForm.get('birthDate')?.invalid && registerForm.get('birthDate')?.touched\">\r\n                {{ getFieldErrorMessage('birthDate') }}\r\n              </mat-error>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Час на раждане (по избор)</mat-label>\r\n              <div class=\"time-input-container\">\r\n                <input\r\n                  matInput\r\n                  type=\"time\"\r\n                  formControlName=\"birthTime\"\r\n                  [step]=\"timeFormat24h ? 60 : 60\">\r\n                <button\r\n                  mat-icon-button\r\n                  type=\"button\"\r\n                  matSuffix\r\n                  (click)=\"toggleTimeFormat()\"\r\n                  [title]=\"timeFormat24h ? 'Превключи на 12-часов формат' : 'Превключи на 24-часов формат'\">\r\n                  <mat-icon>{{ timeFormat24h ? 'schedule' : 'access_time' }}</mat-icon>\r\n                </button>\r\n              </div>\r\n              <mat-hint>{{ timeFormat24h ? '24-часов формат' : '12-часов формат' }}</mat-hint>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Място на раждане</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"text\"\r\n              formControlName=\"birthLocation\"\r\n              placeholder=\"Град и държава на раждане\">\r\n            <mat-icon matSuffix>place</mat-icon>\r\n            <mat-error *ngIf=\"registerForm.get('birthLocation')?.invalid && registerForm.get('birthLocation')?.touched\">\r\n              {{ getFieldErrorMessage('birthLocation') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Астрологична зодия (по избор)</mat-label>\r\n            <mat-select formControlName=\"astrologicalSign\">\r\n              <mat-option *ngFor=\"let sign of getAstrologicalSigns()\" [value]=\"sign\">\r\n                {{ sign }}\r\n              </mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Методи на работа</mat-label>\r\n            <mat-select formControlName=\"oracleTypes\" multiple>\r\n              <mat-option *ngFor=\"let type of getOracleTypeOptions()\" [value]=\"type\">\r\n                {{ type }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-hint>Изберете всички методи, които използвате</mat-hint>\r\n            <mat-error *ngIf=\"registerForm.get('oracleTypes')?.invalid && registerForm.get('oracleTypes')?.touched\">\r\n              {{ getFieldErrorMessage('oracleTypes') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Езици</mat-label>\r\n            <mat-select formControlName=\"languagesSpoken\" multiple>\r\n              <mat-option *ngFor=\"let lang of getLanguageOptions()\" [value]=\"lang\">\r\n                {{ lang }}\r\n              </mat-option>\r\n            </mat-select>\r\n            <mat-hint>Изберете езиците, на които предлагате консултации</mat-hint>\r\n            <mat-error *ngIf=\"registerForm.get('languagesSpoken')?.invalid && registerForm.get('languagesSpoken')?.touched\">\r\n              {{ getFieldErrorMessage('languagesSpoken') }}\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- Skills Section -->\r\n          <div class=\"skills-section\">\r\n            <h4>Умения и специализации</h4>\r\n            <div class=\"skills-chips\" *ngIf=\"registerForm.get('skills')?.value?.length > 0\">\r\n              <mat-chip-set>\r\n                <mat-chip\r\n                  *ngFor=\"let skill of registerForm.get('skills')?.value || []; let i = index\"\r\n                  (removed)=\"removeSkill(i)\"\r\n                  removable=\"true\">\r\n                  {{ skill }}\r\n                  <mat-icon matChipRemove>cancel</mat-icon>\r\n                </mat-chip>\r\n              </mat-chip-set>\r\n            </div>\r\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n              <mat-label>Добавете умение</mat-label>\r\n              <input\r\n                matInput\r\n                #skillInput\r\n                placeholder=\"Въведете умение и натиснете Enter или кликнете +\"\r\n                (keydown.enter)=\"addSkill($any($event), skillInput)\">\r\n              <button\r\n                mat-icon-button\r\n                type=\"button\"\r\n                matSuffix\r\n                (click)=\"addSkillFromInput(skillInput)\"\r\n                [disabled]=\"!skillInput.value.trim()\"\r\n                title=\"Добави умение\">\r\n                <mat-icon>add</mat-icon>\r\n              </button>\r\n            </mat-form-field>\r\n            <mat-hint>Добавете поне 3 умения свързани с вашата практика</mat-hint>\r\n            <mat-error *ngIf=\"registerForm.get('skills')?.invalid && registerForm.get('skills')?.touched\">\r\n              {{ getFieldErrorMessage('skills') }}\r\n            </mat-error>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 5: Contact & Business Information (Oracle only) -->\r\n        <div *ngIf=\"currentStep === 5 && isOracleRegistration()\" class=\"form-step\">\r\n          <h3>Контакти и бизнес информация</h3>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Уебсайт (по избор)</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"url\"\r\n              formControlName=\"website\"\r\n              placeholder=\"https://вашия-сайт.com\">\r\n            <mat-icon matSuffix>language</mat-icon>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Портфолио URL (по избор)</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"url\"\r\n              formControlName=\"portfolioUrl\"\r\n              placeholder=\"https://портфолио.com\">\r\n            <mat-icon matSuffix>work</mat-icon>\r\n          </mat-form-field>\r\n\r\n          <h4>Бизнес адрес (по избор)</h4>\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Улица</mat-label>\r\n            <input\r\n              matInput\r\n              type=\"text\"\r\n              formControlName=\"businessStreet\"\r\n              placeholder=\"Улица и номер\">\r\n            <mat-icon matSuffix>home</mat-icon>\r\n          </mat-form-field>\r\n\r\n          <div class=\"business-address-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Град</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"text\"\r\n                formControlName=\"businessCity\"\r\n                placeholder=\"Град\">\r\n              <mat-icon matSuffix>location_city</mat-icon>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Пощенски код</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"text\"\r\n                formControlName=\"businessPostalCode\"\r\n                placeholder=\"Пощенски код\">\r\n              <mat-icon matSuffix>markunread_mailbox</mat-icon>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <mat-checkbox formControlName=\"isBusinessAddressPublic\" color=\"primary\">\r\n            Показвай бизнес адреса публично\r\n          </mat-checkbox>\r\n        </div>\r\n\r\n        <!-- Step 6: Consultation Rates & Terms (Oracle only) -->\r\n        <div *ngIf=\"currentStep === 6 && isOracleRegistration()\" class=\"form-step\">\r\n          <h3>Тарифи и условия</h3>\r\n\r\n          <div class=\"rates-row\">\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Часова тарифа (по избор)</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"number\"\r\n                formControlName=\"hourlyRate\"\r\n                placeholder=\"0\"\r\n                min=\"0\">\r\n              <mat-icon matSuffix>schedule</mat-icon>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"half-width\">\r\n              <mat-label>Тарифа за сесия (по избор)</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"number\"\r\n                formControlName=\"sessionRate\"\r\n                placeholder=\"0\"\r\n                min=\"0\">\r\n              <mat-icon matSuffix>event</mat-icon>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Валута</mat-label>\r\n            <mat-select formControlName=\"currency\">\r\n              <mat-option value=\"BGN\">BGN (Български лев)</mat-option>\r\n              <mat-option value=\"EUR\">EUR (Евро)</mat-option>\r\n              <mat-option value=\"USD\">USD (Долар)</mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n\r\n          <!-- Terms and Conditions for Oracle -->\r\n          <div class=\"terms-section\">\r\n            <mat-checkbox formControlName=\"acceptTerms\" color=\"primary\">\r\n              Съгласявам се с <a href=\"#\" target=\"_blank\">Условията за ползване</a>,\r\n              <a href=\"#\" target=\"_blank\">Политиката за поверителност</a> и\r\n              <a href=\"#\" target=\"_blank\">Условията за оракули</a>\r\n            </mat-checkbox>\r\n            <mat-error *ngIf=\"registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched\">\r\n              Трябва да приемете всички условия и политики\r\n            </mat-error>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Navigation Buttons -->\r\n        <div class=\"navigation-buttons\" *ngIf=\"isOracleRegistration()\">\r\n          <button\r\n            mat-button\r\n            type=\"button\"\r\n            *ngIf=\"currentStep > 1\"\r\n            (click)=\"previousStep()\"\r\n            [disabled]=\"isLoading\">\r\n            <mat-icon>arrow_back</mat-icon>\r\n            Назад\r\n          </button>\r\n\r\n          <div class=\"spacer\"></div>\r\n\r\n          <button\r\n            mat-raised-button\r\n            color=\"primary\"\r\n            type=\"submit\"\r\n            [disabled]=\"isLoading || !isCurrentStepValid()\">\r\n            <mat-icon *ngIf=\"isLoading\">\r\n              <mat-spinner diameter=\"20\"></mat-spinner>\r\n            </mat-icon>\r\n            <mat-icon *ngIf=\"!isLoading && currentStep < totalSteps\">arrow_forward</mat-icon>\r\n            <mat-icon *ngIf=\"!isLoading && currentStep === totalSteps\">person_add</mat-icon>\r\n            {{ isLoading ? 'Обработка...' : (currentStep < totalSteps ? 'Напред' : 'Създай акаунт') }}\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Register Button for General Users -->\r\n        <button\r\n          *ngIf=\"registrationType === 'general'\"\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          type=\"submit\"\r\n          class=\"full-width register-button\"\r\n          [disabled]=\"isLoading\">\r\n          <mat-icon *ngIf=\"isLoading\">\r\n            <mat-spinner diameter=\"20\"></mat-spinner>\r\n          </mat-icon>\r\n          <mat-icon *ngIf=\"!isLoading\">person_add</mat-icon>\r\n          {{ isLoading ? 'Създаване на акаунт...' : 'Създай акаунт' }}\r\n        </button>\r\n\r\n        <!-- Divider -->\r\n        <div class=\"divider-container\" *ngIf=\"registrationType === 'general'\">\r\n          <mat-divider></mat-divider>\r\n          <span class=\"divider-text\">или</span>\r\n          <mat-divider></mat-divider>\r\n        </div>\r\n\r\n        <!-- OAuth Buttons -->\r\n        <div class=\"oauth-buttons\" *ngIf=\"registrationType === 'general'\">\r\n          <!-- Google Sign-Up Button -->\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"full-width oauth-button google-button\"\r\n            [disabled]=\"isLoading\"\r\n            (click)=\"signUpWithGoogle()\">\r\n            <img src=\"https://developers.google.com/identity/images/g-logo.png\" alt=\"Google\" class=\"oauth-icon\">\r\n            Регистрация с Google\r\n          </button>\r\n\r\n          <!-- Facebook Sign-Up Button -->\r\n          <button\r\n            mat-stroked-button\r\n            type=\"button\"\r\n            class=\"full-width oauth-button facebook-button\"\r\n            [disabled]=\"isLoading\"\r\n            (click)=\"signUpWithFacebook()\">\r\n            <mat-icon class=\"oauth-icon facebook-icon\">facebook</mat-icon>\r\n            Регистрация с Facebook\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions class=\"register-actions\">\r\n      <mat-divider></mat-divider>\r\n\r\n      <div class=\"login-section\">\r\n        <p class=\"login-text\">Вече имате акаунт?</p>\r\n        <button\r\n          mat-stroked-button\r\n          color=\"primary\"\r\n          type=\"button\"\r\n          (click)=\"navigateToLogin()\"\r\n          class=\"full-width\">\r\n          <mat-icon>login</mat-icon>\r\n          Влезте в акаунта си\r\n        </button>\r\n      </div>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}