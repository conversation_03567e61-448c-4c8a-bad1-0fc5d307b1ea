/**
 * Standard API response format from the backend
 */
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors: string[];
}

/**
 * Profile search request format for the backend
 */
export interface ProfileSearchRequest {
  searchTerm?: string;
  location?: string;
  skills: string[];
  sortBy?: string;
  page: number;
  pageSize: number;
}

/**
 * Profile search result format from the backend
 */
export interface ProfileSearchResult {
  profiles: any[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

/**
 * Profile view request format for the backend
 */
export interface ProfileViewRequest {
  profileId: number;
  viewerUserId?: number;
  referrer?: string;
  userAgent?: string;
}

/**
 * Create profile request format for the backend
 */
export interface CreateProfileRequest {
  username: string;
  firstName: string;
  lastName: string;
  professionalTitle?: string;
  headline?: string;
  isPublic?: boolean;
}

/**
 * Update profile request format for the backend
 */
export interface UpdateProfileRequest {
  username?: string;
  firstName?: string;
  lastName?: string;
  profilePhotoUrl?: string;
  coverPhotoUrl?: string;
  professionalTitle?: string;
  headline?: string;
  summary?: string;
  isPublic?: boolean;
  location?: {
    city?: string;
    state?: string;
    country?: string;
    displayLocation?: string;
  };
  contactInfo?: {
    email?: string;
    isEmailPublic?: boolean;
    website?: string;
    portfolioUrl?: string;
    businessAddress?: {
      street?: string;
      city?: string;
      state?: string;
      postalCode?: string;
      country?: string;
      isPublic?: boolean;
    };
  };
  skills?: Array<{
    id?: number;
    name: string;
    category?: string;
    proficiencyLevel?: string;
  }>;
  consultationRates?: {
    hourlyRate?: number;
    sessionRate?: number;
    currency: string;
  };
  serviceOfferings?: Array<{
    id?: number;
    name: string;
    description: string;
    price: number;
    currency: string;
    duration?: number;
    category?: string;
    isActive: boolean;
  }>;
}
