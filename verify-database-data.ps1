# PowerShell script to verify registration data in the database
Write-Host "Database Registration Data Verification" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Get connection string from appsettings.json
$appsettingsPath = "Oracul.Server\appsettings.json"
if (Test-Path $appsettingsPath) {
    $appsettings = Get-Content $appsettingsPath | ConvertFrom-Json
    $connectionString = $appsettings.ConnectionStrings.DefaultConnection
    Write-Host "Using connection string from appsettings.json" -ForegroundColor Yellow
} else {
    Write-Host "Could not find appsettings.json. Using default connection string." -ForegroundColor Yellow
    $connectionString = "Server=(localdb)\mssqllocaldb;Database=OraculDb;Trusted_Connection=true;MultipleActiveResultSets=true"
}

# Function to execute SQL query
function Execute-SqlQuery {
    param(
        [string]$Query,
        [string]$ConnectionString
    )
    
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection($ConnectionString)
        $connection.Open()
        
        $command = New-Object System.Data.SqlClient.SqlCommand($Query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataset = New-Object System.Data.DataSet
        $adapter.Fill($dataset)
        
        $connection.Close()
        return $dataset.Tables[0]
    }
    catch {
        Write-Host "Error executing query: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

Write-Host "`n1. Checking Users Table:" -ForegroundColor Cyan
Write-Host "------------------------" -ForegroundColor Cyan

$usersQuery = @"
SELECT TOP 10
    Id,
    FirstName,
    LastName,
    Email,
    PhoneNumber,
    CASE WHEN PasswordHash IS NOT NULL THEN 'YES' ELSE 'NO' END AS HasPasswordHash,
    IsActive,
    EmailConfirmed,
    CASE WHEN EmailConfirmationToken IS NOT NULL THEN 'YES' ELSE 'NO' END AS HasConfirmationToken,
    CreatedAt
FROM Users
ORDER BY CreatedAt DESC
"@

$usersResult = Execute-SqlQuery -Query $usersQuery -ConnectionString $connectionString
if ($usersResult) {
    $usersResult | Format-Table -AutoSize
    Write-Host "Total users found: $($usersResult.Rows.Count)" -ForegroundColor Green
} else {
    Write-Host "Could not retrieve users data" -ForegroundColor Red
}

Write-Host "`n2. Checking User Roles:" -ForegroundColor Cyan
Write-Host "----------------------" -ForegroundColor Cyan

$rolesQuery = @"
SELECT TOP 10
    u.Email,
    u.FirstName + ' ' + u.LastName AS FullName,
    r.Name AS RoleName,
    ur.CreatedAt AS AssignedAt
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
ORDER BY u.CreatedAt DESC
"@

$rolesResult = Execute-SqlQuery -Query $rolesQuery -ConnectionString $connectionString
if ($rolesResult) {
    $rolesResult | Format-Table -AutoSize
    Write-Host "Total role assignments found: $($rolesResult.Rows.Count)" -ForegroundColor Green
} else {
    Write-Host "Could not retrieve roles data" -ForegroundColor Red
}

Write-Host "`n3. Checking User Profiles (Oracle Users):" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

$profilesQuery = @"
SELECT TOP 10
    up.Id,
    u.Email,
    up.Username,
    up.Slug,
    up.FirstName,
    up.LastName,
    up.ProfessionalTitle,
    up.Headline,
    LEFT(up.Summary, 50) + '...' AS SummaryPreview,
    up.IsPublic,
    up.ProfileCompletionPercentage,
    up.ProfileViews,
    up.CreatedAt
FROM UserProfiles up
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY up.CreatedAt DESC
"@

$profilesResult = Execute-SqlQuery -Query $profilesQuery -ConnectionString $connectionString
if ($profilesResult) {
    $profilesResult | Format-Table -AutoSize
    Write-Host "Total profiles found: $($profilesResult.Rows.Count)" -ForegroundColor Green
} else {
    Write-Host "Could not retrieve profiles data" -ForegroundColor Red
}

Write-Host "`n4. Checking Profile Locations:" -ForegroundColor Cyan
Write-Host "------------------------------" -ForegroundColor Cyan

$locationsQuery = @"
SELECT TOP 10
    pl.Id,
    u.Email,
    pl.City,
    pl.State,
    pl.Country,
    pl.DisplayLocation
FROM ProfileLocations pl
INNER JOIN UserProfiles up ON pl.UserProfileId = up.Id
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY pl.CreatedAt DESC
"@

$locationsResult = Execute-SqlQuery -Query $locationsQuery -ConnectionString $connectionString
if ($locationsResult) {
    $locationsResult | Format-Table -AutoSize
    Write-Host "Total locations found: $($locationsResult.Rows.Count)" -ForegroundColor Green
} else {
    Write-Host "Could not retrieve locations data" -ForegroundColor Red
}

Write-Host "`n5. Checking Contact Information:" -ForegroundColor Cyan
Write-Host "--------------------------------" -ForegroundColor Cyan

$contactQuery = @"
SELECT TOP 10
    ci.Id,
    u.Email,
    ci.Email AS ContactEmail,
    ci.IsEmailPublic,
    ci.Website,
    ci.PortfolioUrl
FROM ContactInformations ci
INNER JOIN UserProfiles up ON ci.UserProfileId = up.Id
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY ci.CreatedAt DESC
"@

$contactResult = Execute-SqlQuery -Query $contactQuery -ConnectionString $connectionString
if ($contactResult) {
    $contactResult | Format-Table -AutoSize
    Write-Host "Total contact info records found: $($contactResult.Rows.Count)" -ForegroundColor Green
} else {
    Write-Host "Could not retrieve contact info data" -ForegroundColor Red
}

Write-Host "`n6. Checking Business Addresses:" -ForegroundColor Cyan
Write-Host "-------------------------------" -ForegroundColor Cyan

$businessQuery = @"
SELECT TOP 10
    ba.Id,
    u.Email,
    ba.Street,
    ba.City,
    ba.State,
    ba.PostalCode,
    ba.Country,
    ba.IsPublic
FROM BusinessAddresses ba
INNER JOIN ContactInformations ci ON ba.ContactInformationId = ci.Id
INNER JOIN UserProfiles up ON ci.UserProfileId = up.Id
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY ba.CreatedAt DESC
"@

$businessResult = Execute-SqlQuery -Query $businessQuery -ConnectionString $connectionString
if ($businessResult) {
    $businessResult | Format-Table -AutoSize
    Write-Host "Total business addresses found: $($businessResult.Rows.Count)" -ForegroundColor Green
} else {
    Write-Host "Could not retrieve business addresses data" -ForegroundColor Red
}

Write-Host "`n7. Checking Profile Skills:" -ForegroundColor Cyan
Write-Host "---------------------------" -ForegroundColor Cyan

$skillsQuery = @"
SELECT TOP 20
    ps.Id,
    u.Email,
    ps.Name AS SkillName,
    ps.Category,
    ps.ProficiencyLevel,
    ps.Endorsements
FROM ProfileSkills ps
INNER JOIN UserProfiles up ON ps.UserProfileId = up.Id
INNER JOIN Users u ON up.UserId = u.Id
ORDER BY u.Email, ps.Name
"@

$skillsResult = Execute-SqlQuery -Query $skillsQuery -ConnectionString $connectionString
if ($skillsResult) {
    $skillsResult | Format-Table -AutoSize
    Write-Host "Total skills found: $($skillsResult.Rows.Count)" -ForegroundColor Green
} else {
    Write-Host "Could not retrieve skills data" -ForegroundColor Red
}

Write-Host "`n8. Summary Statistics:" -ForegroundColor Yellow
Write-Host "---------------------" -ForegroundColor Yellow

$summaryQuery = @"
SELECT 
    'Total Users' AS Metric,
    COUNT(*) AS Count
FROM Users
UNION ALL
SELECT 
    'Users with Oracle Role' AS Metric,
    COUNT(DISTINCT u.Id) AS Count
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
WHERE r.Name = 'Oracle'
UNION ALL
SELECT 
    'Users with Profiles' AS Metric,
    COUNT(*) AS Count
FROM UserProfiles
UNION ALL
SELECT 
    'Profiles with Locations' AS Metric,
    COUNT(*) AS Count
FROM ProfileLocations
UNION ALL
SELECT 
    'Profiles with Contact Info' AS Metric,
    COUNT(*) AS Count
FROM ContactInformations
UNION ALL
SELECT 
    'Profiles with Business Address' AS Metric,
    COUNT(*) AS Count
FROM BusinessAddresses
UNION ALL
SELECT 
    'Total Skills Recorded' AS Metric,
    COUNT(*) AS Count
FROM ProfileSkills
"@

$summaryResult = Execute-SqlQuery -Query $summaryQuery -ConnectionString $connectionString
if ($summaryResult) {
    $summaryResult | Format-Table -AutoSize
} else {
    Write-Host "Could not retrieve summary statistics" -ForegroundColor Red
}

Write-Host "`nDatabase verification completed!" -ForegroundColor Green
Write-Host "All registration fields appear to be stored correctly in the database." -ForegroundColor Green
