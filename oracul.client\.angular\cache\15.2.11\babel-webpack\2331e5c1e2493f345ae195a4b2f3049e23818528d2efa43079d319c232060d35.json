{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Quill Editor\nimport { QuillModule } from 'ngx-quill';\n// Angular Material Modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDividerModule } from '@angular/material/divider';\n// Components\nimport { ArticleViewComponent } from './article-view/article-view.component';\nimport { ArticleEditorComponent } from './article-editor/article-editor.component';\nimport { ArticleManagementComponent, ConfirmDeleteDialogComponent } from './article-management/article-management.component';\nimport { ImageUploadComponent } from './components/image-upload/image-upload.component';\nimport { ArticlePreviewDialogComponent } from './components/article-preview-dialog/article-preview-dialog.component';\n// Services\nimport { ArticleService } from '../shared/services/article.service';\nconst routes = [{\n  path: 'manage',\n  component: ArticleManagementComponent\n}, {\n  path: 'editor',\n  component: ArticleEditorComponent\n}, {\n  path: 'editor/:id',\n  component: ArticleEditorComponent\n}, {\n  path: ':slug',\n  component: ArticleViewComponent\n}];\nlet ArticlesModule = class ArticlesModule {};\nArticlesModule = __decorate([NgModule({\n  declarations: [ArticleViewComponent, ArticleEditorComponent, ArticleManagementComponent, ConfirmDeleteDialogComponent, ImageUploadComponent, ArticlePreviewDialogComponent],\n  imports: [CommonModule, RouterModule.forChild(routes), ReactiveFormsModule, FormsModule, QuillModule.forRoot(), MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, MatProgressSpinnerModule, MatProgressBarModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatCheckboxModule, MatTableModule, MatPaginatorModule, MatSortModule, MatDialogModule, MatSnackBarModule, MatTabsModule, MatMenuModule, MatTooltipModule, MatDividerModule],\n  providers: [ArticleService]\n})], ArticlesModule);\nexport { ArticlesModule };", "map": {"version": 3, "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,WAAW,QAAQ,WAAW;AAEvC;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,0BAA0B,EAAEC,4BAA4B,QAAQ,mDAAmD;AAC5H,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,6BAA6B,QAAQ,sEAAsE;AAEpH;AACA,SAASC,cAAc,QAAQ,oCAAoC;AAEnE,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEP;CACZ,EACD;EACEM,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAER;CACZ,EACD;EACEO,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAER;CACZ,EACD;EACEO,IAAI,EAAE,OAAO;EACbC,SAAS,EAAET;CACZ,CACF;AAyCM,IAAMU,cAAc,GAApB,MAAMA,cAAc,GAAI;AAAlBA,cAAc,eAvC1BnC,QAAQ,CAAC;EACRoC,YAAY,EAAE,CACZX,oBAAoB,EACpBC,sBAAsB,EACtBC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,oBAAoB,EACpBC,6BAA6B,CAC9B;EACDO,OAAO,EAAE,CACPpC,YAAY,EACZC,YAAY,CAACoC,QAAQ,CAACN,MAAM,CAAC,EAC7B7B,mBAAmB,EACnBC,WAAW,EACXC,WAAW,CAACkC,OAAO,EAAE,EACrBjC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,wBAAwB,EACxBC,oBAAoB,EACpBC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB,CACjB;EACDgB,SAAS,EAAE,CACTT,cAAc;CAEjB,CAAC,GACWI,cAAc,CAAI;SAAlBA,cAAc", "names": ["NgModule", "CommonModule", "RouterModule", "ReactiveFormsModule", "FormsModule", "QuillModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatProgressSpinnerModule", "MatProgressBarModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatCheckboxModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatDialogModule", "MatSnackBarModule", "MatTabsModule", "MatMenuModule", "MatTooltipModule", "MatDividerModule", "ArticleViewComponent", "ArticleEditorComponent", "ArticleManagementComponent", "ConfirmDeleteDialogComponent", "ImageUploadComponent", "ArticlePreviewDialogComponent", "ArticleService", "routes", "path", "component", "ArticlesModule", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "providers"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\articles.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\r\n\r\n// Quill Editor\r\nimport { QuillModule } from 'ngx-quill';\r\n\r\n// Angular Material Modules\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatSortModule } from '@angular/material/sort';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatDividerModule } from '@angular/material/divider';\r\n\r\n// Components\r\nimport { ArticleViewComponent } from './article-view/article-view.component';\r\nimport { ArticleEditorComponent } from './article-editor/article-editor.component';\r\nimport { ArticleManagementComponent, ConfirmDeleteDialogComponent } from './article-management/article-management.component';\r\nimport { ImageUploadComponent } from './components/image-upload/image-upload.component';\r\nimport { ArticlePreviewDialogComponent } from './components/article-preview-dialog/article-preview-dialog.component';\r\n\r\n// Services\r\nimport { ArticleService } from '../shared/services/article.service';\r\n\r\nconst routes = [\r\n  {\r\n    path: 'manage',\r\n    component: ArticleManagementComponent\r\n  },\r\n  {\r\n    path: 'editor',\r\n    component: ArticleEditorComponent\r\n  },\r\n  {\r\n    path: 'editor/:id',\r\n    component: ArticleEditorComponent\r\n  },\r\n  {\r\n    path: ':slug',\r\n    component: ArticleViewComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ArticleViewComponent,\r\n    ArticleEditorComponent,\r\n    ArticleManagementComponent,\r\n    ConfirmDeleteDialogComponent,\r\n    ImageUploadComponent,\r\n    ArticlePreviewDialogComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    QuillModule.forRoot(),\r\n    MatCardModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatChipsModule,\r\n    MatProgressSpinnerModule,\r\n    MatProgressBarModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatCheckboxModule,\r\n    MatTableModule,\r\n    MatPaginatorModule,\r\n    MatSortModule,\r\n    MatDialogModule,\r\n    MatSnackBarModule,\r\n    MatTabsModule,\r\n    MatMenuModule,\r\n    MatTooltipModule,\r\n    MatDividerModule\r\n  ],\r\n  providers: [\r\n    ArticleService\r\n  ]\r\n})\r\nexport class ArticlesModule { }\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}