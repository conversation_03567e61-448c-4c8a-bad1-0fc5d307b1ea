import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FileUploadService } from '../../../shared/services/file-upload.service';

@Component({
  selector: 'app-image-upload',
  templateUrl: './image-upload.component.html',
  styleUrls: ['./image-upload.component.css']
})
export class ImageUploadComponent {
  @Input() currentImageUrl?: string;
  @Input() placeholder: string = 'Качете снимка';
  @Input() accept: string = 'image/*';
  @Output() imageUploaded = new EventEmitter<string>();
  @Output() imageRemoved = new EventEmitter<void>();

  isUploading = false;
  dragOver = false;

  constructor(
    private fileUploadService: FileUploadService,
    private snackBar: MatSnackBar
  ) {}

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.uploadFile(input.files[0]);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;

    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      this.uploadFile(event.dataTransfer.files[0]);
    }
  }

  private uploadFile(file: File): void {
    // Validate file
    const validation = this.fileUploadService.validateImageFile(file);
    if (!validation.valid) {
      this.snackBar.open(validation.error!, 'Затвори', { duration: 3000 });
      return;
    }

    this.isUploading = true;

    this.fileUploadService.uploadImage(file).subscribe({
      next: (response) => {
        const fullUrl = this.fileUploadService.getImageUrl(response.url);
        this.imageUploaded.emit(fullUrl);
        this.snackBar.open('Снимката е качена успешно', 'Затвори', { duration: 3000 });
        this.isUploading = false;
      },
      error: (error) => {
        console.error('Error uploading image:', error);
        this.snackBar.open(error.message || 'Грешка при качването на снимката', 'Затвори', { duration: 3000 });
        this.isUploading = false;
      }
    });
  }

  removeImage(): void {
    this.imageRemoved.emit();
  }

  getImageUrl(): string {
    if (!this.currentImageUrl) return '';
    return this.fileUploadService.getImageUrl(this.currentImageUrl);
  }
}
