using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Oracul.Server.Models;
using Oracul.Server.Services;
using System.Security.Claims;

namespace Oracul.Server.Controllers
{
    /// <summary>
    /// Controller for managing astrology-focused user profiles
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ProfileController : ControllerBase
    {
        private readonly ProfileService _profileService;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ILogger<ProfileController> _logger;

        public ProfileController(
            ProfileService profileService,
            IBlobStorageService blobStorageService,
            ILogger<ProfileController> logger)
        {
            _profileService = profileService;
            _blobStorageService = blobStorageService;
            _logger = logger;
        }

        /// <summary>
        /// Get profile by ID
        /// </summary>
        [HttpGet("{id:int}")]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetProfile(int id)
        {
            var result = await _profileService.GetProfileAsync(id);
            
            if (!result.Success)
            {
                return NotFound(result);
            }

            // Record profile view if it's a different user viewing
            var currentUserId = GetCurrentUserId();
            if (currentUserId != result.Data?.UserId)
            {
                await _profileService.RecordProfileViewAsync(new ProfileViewRequest
                {
                    ProfileId = id,
                    ViewerUserId = currentUserId,
                    Referrer = Request.Headers.Referer.ToString(),
                    UserAgent = Request.Headers.UserAgent.ToString()
                });
            }

            return Ok(result);
        }

        /// <summary>
        /// Get profile by slug
        /// </summary>
        [HttpGet("slug/{slug}")]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetProfileBySlug(string slug)
        {
            var result = await _profileService.GetProfileBySlugAsync(slug);
            
            if (!result.Success)
            {
                return NotFound(result);
            }

            // Record profile view if it's a different user viewing
            var currentUserId = GetCurrentUserId();
            if (currentUserId != result.Data?.UserId)
            {
                await _profileService.RecordProfileViewAsync(new ProfileViewRequest
                {
                    ProfileId = result.Data!.Id,
                    ViewerUserId = currentUserId,
                    Referrer = Request.Headers.Referer.ToString(),
                    UserAgent = Request.Headers.UserAgent.ToString()
                });
            }

            return Ok(result);
        }

        /// <summary>
        /// Get current user's profile
        /// </summary>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetMyProfile()
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Потребителят не е удостоверен"
                });
            }

            var result = await _profileService.GetCurrentUserProfileAsync(userId.Value);
            
            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Create a new profile
        /// </summary>
        [HttpPost]
        [Authorize]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> CreateProfile([FromBody] CreateProfileRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Невалидни данни",
                    Errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()
                });
            }

            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Потребителят не е удостоверен"
                });
            }

            var result = await _profileService.CreateProfileAsync(userId.Value, request);
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return CreatedAtAction(nameof(GetProfile), new { id = result.Data!.Id }, result);
        }

        /// <summary>
        /// Update current user's profile
        /// </summary>
        [HttpPut]
        [Authorize]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> UpdateProfile([FromBody] UpdateProfileRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Невалидни данни",
                    Errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()
                });
            }

            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(new ApiResponse<UserProfileDto>
                {
                    Success = false,
                    Message = "Потребителят не е удостоверен"
                });
            }

            var result = await _profileService.UpdateProfileAsync(userId.Value, request);
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Delete current user's profile
        /// </summary>
        [HttpDelete]
        [Authorize]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteProfile()
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(new ApiResponse<bool>
                {
                    Success = false,
                    Message = "Потребителят не е удостоверен",
                    Data = false
                });
            }

            var result = await _profileService.DeleteProfileAsync(userId.Value);
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Search profiles
        /// </summary>
        [HttpPost("search")]
        public async Task<ActionResult<ApiResponse<ProfileSearchResult>>> SearchProfiles([FromBody] ProfileSearchRequest request)
        {
            var result = await _profileService.SearchProfilesAsync(request);
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get public profiles with pagination
        /// </summary>
        [HttpGet("public")]
        public async Task<ActionResult<ApiResponse<ProfileSearchResult>>> GetPublicProfiles(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 20)
        {
            var request = new ProfileSearchRequest
            {
                Page = page,
                PageSize = Math.Min(pageSize, 50) // Limit page size to 50
            };

            var result = await _profileService.SearchProfilesAsync(request);
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Record profile view
        /// </summary>
        [HttpPost("view")]
        public async Task<ActionResult<ApiResponse<bool>>> RecordProfileView([FromBody] ProfileViewRequest request)
        {
            // Set viewer user ID if authenticated
            var currentUserId = GetCurrentUserId();
            if (currentUserId.HasValue)
            {
                request.ViewerUserId = currentUserId.Value;
            }

            var result = await _profileService.RecordProfileViewAsync(request);
            
            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Upload profile photo
        /// </summary>
        [HttpPost("upload/profile-photo")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<string>>> UploadProfilePhoto(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Няма избран файл",
                        Errors = new List<string> { "Моля, изберете файл за качване" }
                    });
                }

                // Get current user ID
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Потребителят не е удостоверен"
                    });
                }

                // Validate file
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
                var maxSizeBytes = 5 * 1024 * 1024; // 5MB
                var validation = _blobStorageService.ValidateFile(file.FileName, file.ContentType, file.Length, allowedTypes, maxSizeBytes);

                if (!validation.IsValid)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Невалиден файл",
                        Errors = validation.Errors
                    });
                }

                // Upload to blob storage
                using var stream = file.OpenReadStream();
                var containerPath = $"profiles/{userId.Value}";
                var uploadResult = await _blobStorageService.UploadFileAsync(
                    stream,
                    file.FileName,
                    file.ContentType,
                    containerPath);

                if (!uploadResult.Success)
                {
                    return StatusCode(500, new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Възникна грешка при качване на снимката",
                        Errors = new List<string> { uploadResult.ErrorMessage ?? "Unknown error" }
                    });
                }

                // Update profile with new photo URL
                var updateRequest = new UpdateProfileRequest
                {
                    ProfilePhotoUrl = uploadResult.BlobUrl
                };

                var updateResult = await _profileService.UpdateProfileAsync(userId.Value, updateRequest);
                if (!updateResult.Success)
                {
                    // Database update failed - attempt to rollback by deleting the uploaded file
                    _logger.LogWarning("Failed to update profile with new photo URL: {Error}. Attempting rollback.", updateResult.Message);

                    try
                    {
                        await _blobStorageService.DeleteFileAsync(uploadResult.BlobPath);
                        _logger.LogInformation("Successfully rolled back uploaded file: {BlobPath}", uploadResult.BlobPath);
                    }
                    catch (Exception rollbackEx)
                    {
                        _logger.LogError(rollbackEx, "Failed to rollback uploaded file: {BlobPath}", uploadResult.BlobPath);
                    }

                    // Return error since the operation failed
                    return StatusCode(500, new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Възникна грешка при обновяване на профила",
                        Errors = new List<string> { updateResult.Message }
                    });
                }

                return Ok(new ApiResponse<string>
                {
                    Success = true,
                    Message = "Снимката е качена и профилът е обновен успешно",
                    Data = uploadResult.BlobUrl
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при качване на профилна снимка");
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = "Възникна грешка при качване на снимката",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Upload cover photo
        /// </summary>
        [HttpPost("upload/cover-photo")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<string>>> UploadCoverPhoto(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Няма избран файл",
                        Errors = new List<string> { "Моля, изберете файл за качване" }
                    });
                }

                // Get current user ID
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Потребителят не е удостоверен"
                    });
                }

                // Validate file
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
                var maxSizeBytes = 10 * 1024 * 1024; // 10MB for cover photos
                var validation = _blobStorageService.ValidateFile(file.FileName, file.ContentType, file.Length, allowedTypes, maxSizeBytes);

                if (!validation.IsValid)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Невалиден файл",
                        Errors = validation.Errors
                    });
                }

                // Upload to blob storage
                using var stream = file.OpenReadStream();
                var containerPath = $"profiles/{userId.Value}";
                var uploadResult = await _blobStorageService.UploadFileAsync(
                    stream,
                    file.FileName,
                    file.ContentType,
                    containerPath);

                if (!uploadResult.Success)
                {
                    return StatusCode(500, new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Възникна грешка при качване на корицата",
                        Errors = new List<string> { uploadResult.ErrorMessage ?? "Unknown error" }
                    });
                }

                // Update profile with new cover photo URL
                var updateRequest = new UpdateProfileRequest
                {
                    CoverPhotoUrl = uploadResult.BlobUrl
                };

                var updateResult = await _profileService.UpdateProfileAsync(userId.Value, updateRequest);
                if (!updateResult.Success)
                {
                    // Database update failed - attempt to rollback by deleting the uploaded file
                    _logger.LogWarning("Failed to update profile with new cover photo URL: {Error}. Attempting rollback.", updateResult.Message);

                    try
                    {
                        await _blobStorageService.DeleteFileAsync(uploadResult.BlobPath);
                        _logger.LogInformation("Successfully rolled back uploaded file: {BlobPath}", uploadResult.BlobPath);
                    }
                    catch (Exception rollbackEx)
                    {
                        _logger.LogError(rollbackEx, "Failed to rollback uploaded file: {BlobPath}", uploadResult.BlobPath);
                    }

                    // Return error since the operation failed
                    return StatusCode(500, new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Възникна грешка при обновяване на профила",
                        Errors = new List<string> { updateResult.Message }
                    });
                }

                return Ok(new ApiResponse<string>
                {
                    Success = true,
                    Message = "Корицата е качена и профилът е обновен успешно",
                    Data = uploadResult.BlobUrl
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при качване на корица");
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = "Възникна грешка при качване на корицата",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Upload portfolio image
        /// </summary>
        [HttpPost("upload/portfolio-image")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<string>>> UploadPortfolioImage(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Няма избран файл",
                        Errors = new List<string> { "Моля, изберете файл за качване" }
                    });
                }

                // Get current user ID
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Потребителят не е удостоверен"
                    });
                }

                // Validate file
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
                var maxSizeBytes = 10 * 1024 * 1024; // 10MB
                var validation = _blobStorageService.ValidateFile(file.FileName, file.ContentType, file.Length, allowedTypes, maxSizeBytes);

                if (!validation.IsValid)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Невалиден файл",
                        Errors = validation.Errors
                    });
                }

                // Upload to blob storage
                using var stream = file.OpenReadStream();
                var containerPath = $"portfolios/{userId.Value}";
                var uploadResult = await _blobStorageService.UploadFileAsync(
                    stream,
                    file.FileName,
                    file.ContentType,
                    containerPath);

                if (!uploadResult.Success)
                {
                    return StatusCode(500, new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Възникна грешка при качване на снимката",
                        Errors = new List<string> { uploadResult.ErrorMessage ?? "Unknown error" }
                    });
                }

                return Ok(new ApiResponse<string>
                {
                    Success = true,
                    Message = "Снимката е качена успешно",
                    Data = uploadResult.BlobUrl
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при качване на портфолио снимка");
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = "Възникна грешка при качване на снимката",
                    Errors = new List<string> { ex.Message }
                });
            }
        }



        /// <summary>
        /// Upload document file (for certifications, achievements, etc.)
        /// </summary>
        [HttpPost("upload/document")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<string>>> UploadDocument(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Няма избран файл",
                        Errors = new List<string> { "Моля, изберете файл за качване" }
                    });
                }

                // Get current user ID
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Потребителят не е удостоверен"
                    });
                }

                // Validate file
                var allowedTypes = new[] {
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
                };
                var maxSizeBytes = 10 * 1024 * 1024; // 10MB
                var validation = _blobStorageService.ValidateFile(file.FileName, file.ContentType, file.Length, allowedTypes, maxSizeBytes);

                if (!validation.IsValid)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Невалиден файл",
                        Errors = validation.Errors
                    });
                }

                // Upload to blob storage
                using var stream = file.OpenReadStream();
                var containerPath = $"documents/{userId.Value}";
                var uploadResult = await _blobStorageService.UploadFileAsync(
                    stream,
                    file.FileName,
                    file.ContentType,
                    containerPath);

                if (!uploadResult.Success)
                {
                    return StatusCode(500, new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Възникна грешка при качване на документа",
                        Errors = new List<string> { uploadResult.ErrorMessage ?? "Unknown error" }
                    });
                }

                return Ok(new ApiResponse<string>
                {
                    Success = true,
                    Message = "Документът е качен успешно",
                    Data = uploadResult.BlobUrl
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Грешка при качване на документ");
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = "Възникна грешка при качване на документа",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Get current user ID from JWT claims
        /// </summary>
        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            return null;
        }
    }
}
