{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FileUploadService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/fileupload`;\n  }\n  /**\r\n   * Upload an image file\r\n   */\n  uploadImage(file) {\n    const formData = new FormData();\n    formData.append('file', file);\n    return this.http.post(`${this.API_URL}/image`, formData).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Delete an uploaded image\r\n   */\n  deleteImage(fileName) {\n    return this.http.delete(`${this.API_URL}/image?fileName=${encodeURIComponent(fileName)}`).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Validate image file before upload\r\n   */\n  validateImageFile(file) {\n    // Check file type\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: 'Неподдържан формат на файла. Разрешени са: JPG, PNG, GIF, WebP'\n      };\n    }\n    // Check file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    if (file.size > maxSize) {\n      return {\n        valid: false,\n        error: 'Файлът е твърде голям. Максималният размер е 5MB'\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n  /**\r\n   * Get full URL for uploaded image\r\n   */\n  getImageUrl(relativePath) {\n    if (relativePath.startsWith('http')) {\n      return relativePath; // Already a full URL\n    }\n\n    return `${environment.apiUrl.replace('/api', '')}${relativePath}`;\n  }\n  handleError(error) {\n    let errorMessage = 'Възникна неочаквана грешка при качването на файла';\n    if (error.error && error.error.message) {\n      errorMessage = error.error.message;\n    } else if (error.status === 0) {\n      errorMessage = 'Няма връзка със сървъра';\n    } else if (error.status === 401) {\n      errorMessage = 'Необходима е автентификация за качване на файлове';\n    } else if (error.status === 403) {\n      errorMessage = 'Нямате права за качване на файлове';\n    } else if (error.status === 413) {\n      errorMessage = 'Файлът е твърде голям';\n    } else if (error.status >= 500) {\n      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\n    }\n    console.error('File upload service error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function FileUploadService_Factory(t) {\n      return new (t || FileUploadService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FileUploadService,\n      factory: FileUploadService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,mCAAmC;;;AAW/D,OAAM,MAAOC,iBAAiB;EAG5BC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IAFP,YAAO,GAAG,GAAGH,WAAW,CAACI,MAAM,aAAa;EAEtB;EAEvC;;;EAGAC,WAAW,CAACC,IAAU;IACpB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,OAAO,IAAI,CAACH,IAAI,CAACO,IAAI,CAAsB,GAAG,IAAI,CAACC,OAAO,QAAQ,EAAEJ,QAAQ,CAAC,CAC1EK,IAAI,CAACb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAC,WAAW,CAACC,QAAgB;IAC1B,OAAO,IAAI,CAACZ,IAAI,CAACa,MAAM,CAAO,GAAG,IAAI,CAACL,OAAO,mBAAmBM,kBAAkB,CAACF,QAAQ,CAAC,EAAE,CAAC,CAC5FH,IAAI,CAACb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAK,iBAAiB,CAACZ,IAAU;IAC1B;IACA,MAAMa,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IACxF,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACd,IAAI,CAACe,IAAI,CAAC,EAAE;MACrC,OAAO;QACLC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;OACR;;IAGH;IACA,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,IAAIlB,IAAI,CAACmB,IAAI,GAAGD,OAAO,EAAE;MACvB,OAAO;QACLF,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;OACR;;IAGH,OAAO;MAAED,KAAK,EAAE;IAAI,CAAE;EACxB;EAEA;;;EAGAI,WAAW,CAACC,YAAoB;IAC9B,IAAIA,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MACnC,OAAOD,YAAY,CAAC,CAAC;;;IAEvB,OAAO,GAAG3B,WAAW,CAACI,MAAM,CAACyB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAGF,YAAY,EAAE;EACnE;EAEQd,WAAW,CAACU,KAAwB;IAC1C,IAAIO,YAAY,GAAG,mDAAmD;IAEtE,IAAIP,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACQ,OAAO,EAAE;MACtCD,YAAY,GAAGP,KAAK,CAACA,KAAK,CAACQ,OAAO;KACnC,MAAM,IAAIR,KAAK,CAACS,MAAM,KAAK,CAAC,EAAE;MAC7BF,YAAY,GAAG,yBAAyB;KACzC,MAAM,IAAIP,KAAK,CAACS,MAAM,KAAK,GAAG,EAAE;MAC/BF,YAAY,GAAG,mDAAmD;KACnE,MAAM,IAAIP,KAAK,CAACS,MAAM,KAAK,GAAG,EAAE;MAC/BF,YAAY,GAAG,oCAAoC;KACpD,MAAM,IAAIP,KAAK,CAACS,MAAM,KAAK,GAAG,EAAE;MAC/BF,YAAY,GAAG,uBAAuB;KACvC,MAAM,IAAIP,KAAK,CAACS,MAAM,IAAI,GAAG,EAAE;MAC9BF,YAAY,GAAG,iDAAiD;;IAGlEG,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAOzB,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAACJ,YAAY,CAAC,CAAC;EAClD;;;uBA9EW7B,iBAAiB;IAAA;EAAA;;;aAAjBA,iBAAiB;MAAAkC,SAAjBlC,iBAAiB;MAAAmC,YAFhB;IAAM;EAAA", "names": ["throwError", "catchError", "environment", "FileUploadService", "constructor", "http", "apiUrl", "uploadImage", "file", "formData", "FormData", "append", "post", "API_URL", "pipe", "handleError", "deleteImage", "fileName", "delete", "encodeURIComponent", "validateImageFile", "allowedTypes", "includes", "type", "valid", "error", "maxSize", "size", "getImageUrl", "relativePath", "startsWith", "replace", "errorMessage", "message", "status", "console", "Error", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\shared\\services\\file-upload.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { environment } from '../../../environments/environment';\r\n\r\nexport interface ImageUploadResponse {\r\n  url: string;\r\n  fileName: string;\r\n  size: number;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class FileUploadService {\r\n  private readonly API_URL = `${environment.apiUrl}/fileupload`;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  /**\r\n   * Upload an image file\r\n   */\r\n  uploadImage(file: File): Observable<ImageUploadResponse> {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return this.http.post<ImageUploadResponse>(`${this.API_URL}/image`, formData)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Delete an uploaded image\r\n   */\r\n  deleteImage(fileName: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/image?fileName=${encodeURIComponent(fileName)}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Validate image file before upload\r\n   */\r\n  validateImageFile(file: File): { valid: boolean; error?: string } {\r\n    // Check file type\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\r\n    if (!allowedTypes.includes(file.type)) {\r\n      return {\r\n        valid: false,\r\n        error: 'Неподдържан формат на файла. Разрешени са: JPG, PNG, GIF, WebP'\r\n      };\r\n    }\r\n\r\n    // Check file size (max 5MB)\r\n    const maxSize = 5 * 1024 * 1024; // 5MB\r\n    if (file.size > maxSize) {\r\n      return {\r\n        valid: false,\r\n        error: 'Файлът е твърде голям. Максималният размер е 5MB'\r\n      };\r\n    }\r\n\r\n    return { valid: true };\r\n  }\r\n\r\n  /**\r\n   * Get full URL for uploaded image\r\n   */\r\n  getImageUrl(relativePath: string): string {\r\n    if (relativePath.startsWith('http')) {\r\n      return relativePath; // Already a full URL\r\n    }\r\n    return `${environment.apiUrl.replace('/api', '')}${relativePath}`;\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse): Observable<never> {\r\n    let errorMessage = 'Възникна неочаквана грешка при качването на файла';\r\n\r\n    if (error.error && error.error.message) {\r\n      errorMessage = error.error.message;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Няма връзка със сървъра';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Необходима е автентификация за качване на файлове';\r\n    } else if (error.status === 403) {\r\n      errorMessage = 'Нямате права за качване на файлове';\r\n    } else if (error.status === 413) {\r\n      errorMessage = 'Файлът е твърде голям';\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\r\n    }\r\n\r\n    console.error('File upload service error:', error);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}