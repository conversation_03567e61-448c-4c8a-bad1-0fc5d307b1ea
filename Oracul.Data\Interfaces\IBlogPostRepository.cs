using Oracul.Data.Models;

namespace Oracul.Data.Interfaces
{
    /// <summary>
    /// Specific repository interface for BlogPost entity with additional methods
    /// </summary>
    public interface IBlogPostRepository : IRepository<BlogPost>
    {
        // Blog post-specific methods
        Task<BlogPost?> GetBySlugAsync(string slug);
        Task<IEnumerable<BlogPost>> GetFeaturedArticlesAsync(int count);
        Task<IEnumerable<BlogPost>> GetRecentArticlesAsync(int count);
        Task<IEnumerable<BlogPost>> GetPopularArticlesAsync(int count);

        // Search and filtering
        Task<(IEnumerable<BlogPost> Articles, int TotalCount)> SearchArticlesAsync(
            string? searchTerm,
            string? category,
            int? authorId,
            int page,
            int pageSize,
            string sortBy,
            string sortOrder);

        Task<(IEnumerable<BlogPost> Articles, int TotalCount)> GetByCategoryAsync(
            string category, 
            int page, 
            int pageSize);

        Task<(IEnumerable<BlogPost> Articles, int TotalCount)> GetByAuthorAsync(
            int authorId, 
            int page, 
            int pageSize);

        // Categories and tags
        Task<IEnumerable<string>> GetCategoriesAsync();

        // Validation
        Task<bool> IsSlugAvailableAsync(string slug, int? excludeArticleId = null);

        // Analytics
        Task IncrementReadCountAsync(int articleId);
    }
}
