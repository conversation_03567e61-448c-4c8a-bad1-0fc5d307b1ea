{"ast": null, "code": "import { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./token.service\";\nexport class AuthService {\n  constructor(http, router, tokenService) {\n    this.http = http;\n    this.router = router;\n    this.tokenService = tokenService;\n    this.API_URL = `${environment.apiUrl}/auth`;\n    this.TOKEN_KEY = 'access_token';\n    this.REFRESH_TOKEN_KEY = 'refresh_token';\n    this.USER_KEY = 'user_info';\n    this.REMEMBER_ME_KEY = 'remember_me';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.initializeAuth();\n  }\n  initializeAuth() {\n    const token = this.tokenService.getToken();\n    const user = this.tokenService.getStoredUser();\n    if (token && user) {\n      // Check if token is expired\n      if (this.tokenService.isTokenExpired(token)) {\n        // Try to refresh the token\n        this.refreshToken().subscribe({\n          next: () => {\n            this.currentUserSubject.next(user);\n            this.isAuthenticatedSubject.next(true);\n            this.startTokenExpirationCheck();\n          },\n          error: () => {\n            // Refresh failed, clear auth data\n            this.clearAuthData();\n          }\n        });\n      } else {\n        this.currentUserSubject.next(user);\n        this.isAuthenticatedSubject.next(true);\n        this.startTokenExpirationCheck();\n      }\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/login`, credentials).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        // Store remember me preference\n        const rememberMe = credentials.rememberMe || false;\n        this.tokenService.setRememberMe(rememberMe);\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        if (response.refreshToken) {\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        }\n        this.tokenService.setUser(response.user, rememberMe);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  register(userData) {\n    // Determine endpoint based on data type\n    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\n    return this.http.post(`${this.API_URL}/${endpoint}`, userData).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        // For registration, default to remember me = false\n        const rememberMe = false;\n        this.tokenService.setRememberMe(rememberMe);\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        if (response.refreshToken) {\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        }\n        this.tokenService.setUser(response.user, rememberMe);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  isOracleRegistration(userData) {\n    return 'professionalTitle' in userData;\n  }\n  logout() {\n    return this.http.post(`${this.API_URL}/logout`, {}).pipe(tap(() => {\n      this.clearAuthData();\n    }), catchError(() => {\n      // Even if logout fails on server, clear local data\n      this.clearAuthData();\n      return throwError('Logout failed');\n    }));\n  }\n  refreshToken() {\n    const refreshToken = this.tokenService.getRefreshToken();\n    if (!refreshToken) {\n      return throwError('No refresh token available');\n    }\n    return this.http.post(`${this.API_URL}/refresh-token`, {\n      refreshToken\n    }).pipe(tap(response => {\n      if (response.success && response.accessToken) {\n        const rememberMe = this.tokenService.getRememberMe();\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        if (response.refreshToken) {\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        }\n        if (response.user) {\n          this.tokenService.setUser(response.user, rememberMe);\n          this.currentUserSubject.next(response.user);\n        }\n      }\n    }), catchError(error => {\n      this.clearAuthData();\n      return throwError(error);\n    }));\n  }\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/me`).pipe(tap(user => {\n      const rememberMe = this.tokenService.getRememberMe();\n      this.tokenService.setUser(user, rememberMe);\n      this.currentUserSubject.next(user);\n    }), catchError(this.handleError));\n  }\n  changePassword(request) {\n    return this.http.post(`${this.API_URL}/change-password`, request).pipe(catchError(this.handleError));\n  }\n  forgotPassword(request) {\n    return this.http.post(`${this.API_URL}/forgot-password`, request).pipe(catchError(this.handleError));\n  }\n  resetPassword(request) {\n    return this.http.post(`${this.API_URL}/reset-password`, request).pipe(catchError(this.handleError));\n  }\n  loginWithOAuth(request) {\n    return this.http.post(`${this.API_URL}/oauth-login`, request).pipe(tap(response => {\n      if (response.success && response.accessToken && response.user) {\n        // For OAuth, default to remember me = true for better UX\n        const rememberMe = true;\n        this.tokenService.setRememberMe(rememberMe);\n        this.tokenService.setToken(response.accessToken, rememberMe);\n        if (response.refreshToken) {\n          this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n        }\n        this.tokenService.setUser(response.user, rememberMe);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }), catchError(this.handleError));\n  }\n  // Token management - delegated to TokenService\n  getToken() {\n    return this.tokenService.getToken();\n  }\n  checkTokenExpiration() {\n    const token = this.tokenService.getToken();\n    if (token && this.tokenService.isTokenExpired(token)) {\n      // Try to refresh the token\n      this.refreshToken().subscribe({\n        next: () => {\n          // Token refreshed successfully\n        },\n        error: () => {\n          // Refresh failed, logout user\n          this.clearAuthData();\n        }\n      });\n    }\n  }\n  startTokenExpirationCheck() {\n    // Check token expiration every 5 minutes\n    setInterval(() => {\n      this.checkTokenExpiration();\n    }, 5 * 60 * 1000);\n  }\n  clearAuthData() {\n    this.tokenService.clearAllTokens();\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/login']);\n  }\n  isAuthenticated() {\n    return this.tokenService.isAuthenticated();\n  }\n  hasRole(role) {\n    const user = this.currentUserSubject.value;\n    return user?.roles.includes(role) || false;\n  }\n  hasPermission(permission) {\n    const user = this.currentUserSubject.value;\n    return user?.permissions.includes(permission) || false;\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    return throwError(errorMessage);\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.TokenService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAAcC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAGrD,SAASC,WAAW,QAAQ,mCAAmC;;;;;AAiB/D,OAAM,MAAOC,WAAW;EAatBC,YACUC,IAAgB,EAChBC,MAAc,EACdC,YAA0B;IAF1B,SAAI,GAAJF,IAAI;IACJ,WAAM,GAANC,MAAM;IACN,iBAAY,GAAZC,YAAY;IAfL,YAAO,GAAG,GAAGL,WAAW,CAACM,MAAM,OAAO;IACtC,cAAS,GAAG,cAAc;IAC1B,sBAAiB,GAAG,eAAe;IACnC,aAAQ,GAAG,WAAW;IACtB,oBAAe,GAAG,aAAa;IAExC,uBAAkB,GAAG,IAAIV,eAAe,CAAkB,IAAI,CAAC;IAChE,iBAAY,GAAG,IAAI,CAACW,kBAAkB,CAACC,YAAY,EAAE;IAEpD,2BAAsB,GAAG,IAAIZ,eAAe,CAAU,KAAK,CAAC;IAC7D,qBAAgB,GAAG,IAAI,CAACa,sBAAsB,CAACD,YAAY,EAAE;IAOlE,IAAI,CAACE,cAAc,EAAE;EACvB;EAEQA,cAAc;IACpB,MAAMC,KAAK,GAAG,IAAI,CAACN,YAAY,CAACO,QAAQ,EAAE;IAC1C,MAAMC,IAAI,GAAG,IAAI,CAACR,YAAY,CAACS,aAAa,EAAE;IAE9C,IAAIH,KAAK,IAAIE,IAAI,EAAE;MACjB;MACA,IAAI,IAAI,CAACR,YAAY,CAACU,cAAc,CAACJ,KAAK,CAAC,EAAE;QAC3C;QACA,IAAI,CAACK,YAAY,EAAE,CAACC,SAAS,CAAC;UAC5BC,IAAI,EAAE,MAAK;YACT,IAAI,CAACX,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;YAClC,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;YACtC,IAAI,CAACC,yBAAyB,EAAE;UAClC,CAAC;UACDC,KAAK,EAAE,MAAK;YACV;YACA,IAAI,CAACC,aAAa,EAAE;UACtB;SACD,CAAC;OACH,MAAM;QACL,IAAI,CAACd,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;QAClC,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,CAACC,yBAAyB,EAAE;;;EAGtC;EAEAG,KAAK,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,QAAQ,EAAEF,WAAW,CAAC,CACtEG,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACd,IAAI,EAAE;QAC7D;QACA,MAAMiB,UAAU,GAAGP,WAAW,CAACO,UAAU,IAAI,KAAK;QAClD,IAAI,CAACzB,YAAY,CAAC0B,aAAa,CAACD,UAAU,CAAC;QAE3C,IAAI,CAACzB,YAAY,CAAC2B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAC5D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UACzB,IAAI,CAACX,YAAY,CAAC4B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;;QAEtE,IAAI,CAACzB,YAAY,CAAC6B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;QACpD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFpB,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAC7B;EACL;EAEAC,QAAQ,CAACC,QAAiD;IACxD;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,oBAAoB,CAACF,QAAQ,CAAC,GAAG,iBAAiB,GAAG,UAAU;IAErF,OAAO,IAAI,CAAClC,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,IAAIa,QAAQ,EAAE,EAAED,QAAQ,CAAC,CACzEX,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACd,IAAI,EAAE;QAC7D;QACA,MAAMiB,UAAU,GAAG,KAAK;QACxB,IAAI,CAACzB,YAAY,CAAC0B,aAAa,CAACD,UAAU,CAAC;QAE3C,IAAI,CAACzB,YAAY,CAAC2B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAC5D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UACzB,IAAI,CAACX,YAAY,CAAC4B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;;QAEtE,IAAI,CAACzB,YAAY,CAAC6B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;QACpD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFpB,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAC7B;EACL;EAEQI,oBAAoB,CAACF,QAAiD;IAC5E,OAAO,mBAAmB,IAAIA,QAAQ;EACxC;EAEAG,MAAM;IACJ,OAAO,IAAI,CAACrC,IAAI,CAACqB,IAAI,CAAC,GAAG,IAAI,CAACC,OAAO,SAAS,EAAE,EAAE,CAAC,CAChDC,IAAI,CACH3B,GAAG,CAAC,MAAK;MACP,IAAI,CAACsB,aAAa,EAAE;IACtB,CAAC,CAAC,EACFvB,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACuB,aAAa,EAAE;MACpB,OAAOxB,UAAU,CAAC,eAAe,CAAC;IACpC,CAAC,CAAC,CACH;EACL;EAEAmB,YAAY;IACV,MAAMA,YAAY,GAAG,IAAI,CAACX,YAAY,CAACoC,eAAe,EAAE;IACxD,IAAI,CAACzB,YAAY,EAAE;MACjB,OAAOnB,UAAU,CAAC,4BAA4B,CAAC;;IAGjD,OAAO,IAAI,CAACM,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,gBAAgB,EAAE;MAAET;IAAY,CAAE,CAAC,CACnFU,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;QAC5C,MAAMC,UAAU,GAAG,IAAI,CAACzB,YAAY,CAACqC,aAAa,EAAE;QACpD,IAAI,CAACrC,YAAY,CAAC2B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAC5D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UACzB,IAAI,CAACX,YAAY,CAAC4B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;;QAEtE,IAAIH,QAAQ,CAACd,IAAI,EAAE;UACjB,IAAI,CAACR,YAAY,CAAC6B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;UACpD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;;;IAGjD,CAAC,CAAC,EACFf,UAAU,CAACsB,KAAK,IAAG;MACjB,IAAI,CAACC,aAAa,EAAE;MACpB,OAAOxB,UAAU,CAACuB,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACL;EAEAuB,cAAc;IACZ,OAAO,IAAI,CAACxC,IAAI,CAACyC,GAAG,CAAW,GAAG,IAAI,CAACnB,OAAO,KAAK,CAAC,CACjDC,IAAI,CACH3B,GAAG,CAACc,IAAI,IAAG;MACT,MAAMiB,UAAU,GAAG,IAAI,CAACzB,YAAY,CAACqC,aAAa,EAAE;MACpD,IAAI,CAACrC,YAAY,CAAC6B,OAAO,CAACrB,IAAI,EAAEiB,UAAU,CAAC;MAC3C,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACL,IAAI,CAAC;IACpC,CAAC,CAAC,EACFf,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAC7B;EACL;EAEAU,cAAc,CAACC,OAA8B;IAC3C,OAAO,IAAI,CAAC3C,IAAI,CAACqB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,kBAAkB,EAAEqB,OAAO,CAAC,CAChFpB,IAAI,CAAC5B,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAAC;EACvC;EAEAY,cAAc,CAACD,OAA8B;IAC3C,OAAO,IAAI,CAAC3C,IAAI,CAACqB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,kBAAkB,EAAEqB,OAAO,CAAC,CAChFpB,IAAI,CAAC5B,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAAC;EACvC;EAEAa,aAAa,CAACF,OAA6B;IACzC,OAAO,IAAI,CAAC3C,IAAI,CAACqB,IAAI,CAAmB,GAAG,IAAI,CAACC,OAAO,iBAAiB,EAAEqB,OAAO,CAAC,CAC/EpB,IAAI,CAAC5B,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAAC;EACvC;EAEAc,cAAc,CAACH,OAA0B;IACvC,OAAO,IAAI,CAAC3C,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACC,OAAO,cAAc,EAAEqB,OAAO,CAAC,CACxEpB,IAAI,CACH3B,GAAG,CAAC4B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACd,IAAI,EAAE;QAC7D;QACA,MAAMiB,UAAU,GAAG,IAAI;QACvB,IAAI,CAACzB,YAAY,CAAC0B,aAAa,CAACD,UAAU,CAAC;QAE3C,IAAI,CAACzB,YAAY,CAAC2B,QAAQ,CAACL,QAAQ,CAACE,WAAW,EAAEC,UAAU,CAAC;QAC5D,IAAIH,QAAQ,CAACX,YAAY,EAAE;UACzB,IAAI,CAACX,YAAY,CAAC4B,eAAe,CAACN,QAAQ,CAACX,YAAY,EAAEc,UAAU,CAAC;;QAEtE,IAAI,CAACzB,YAAY,CAAC6B,OAAO,CAACP,QAAQ,CAACd,IAAI,EAAEiB,UAAU,CAAC;QACpD,IAAI,CAACvB,kBAAkB,CAACW,IAAI,CAACS,QAAQ,CAACd,IAAI,CAAC;QAC3C,IAAI,CAACJ,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,EACFpB,UAAU,CAAC,IAAI,CAACqC,WAAW,CAAC,CAC7B;EACL;EAEA;EACAvB,QAAQ;IACN,OAAO,IAAI,CAACP,YAAY,CAACO,QAAQ,EAAE;EACrC;EAEAsC,oBAAoB;IAClB,MAAMvC,KAAK,GAAG,IAAI,CAACN,YAAY,CAACO,QAAQ,EAAE;IAC1C,IAAID,KAAK,IAAI,IAAI,CAACN,YAAY,CAACU,cAAc,CAACJ,KAAK,CAAC,EAAE;MACpD;MACA,IAAI,CAACK,YAAY,EAAE,CAACC,SAAS,CAAC;QAC5BC,IAAI,EAAE,MAAK;UACT;QAAA,CACD;QACDE,KAAK,EAAE,MAAK;UACV;UACA,IAAI,CAACC,aAAa,EAAE;QACtB;OACD,CAAC;;EAEN;EAEAF,yBAAyB;IACvB;IACAgC,WAAW,CAAC,MAAK;MACf,IAAI,CAACD,oBAAoB,EAAE;IAC7B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;EACnB;EAEQ7B,aAAa;IACnB,IAAI,CAAChB,YAAY,CAAC+C,cAAc,EAAE;IAClC,IAAI,CAAC7C,kBAAkB,CAACW,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACT,sBAAsB,CAACS,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACd,MAAM,CAACiD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,eAAe;IACb,OAAO,IAAI,CAACjD,YAAY,CAACiD,eAAe,EAAE;EAC5C;EAIAC,OAAO,CAACC,IAAY;IAClB,MAAM3C,IAAI,GAAG,IAAI,CAACN,kBAAkB,CAACkD,KAAK;IAC1C,OAAO5C,IAAI,EAAE6C,KAAK,CAACC,QAAQ,CAACH,IAAI,CAAC,IAAI,KAAK;EAC5C;EAEAI,aAAa,CAACC,UAAkB;IAC9B,MAAMhD,IAAI,GAAG,IAAI,CAACN,kBAAkB,CAACkD,KAAK;IAC1C,OAAO5C,IAAI,EAAEiD,WAAW,CAACH,QAAQ,CAACE,UAAU,CAAC,IAAI,KAAK;EACxD;EAEQ1B,WAAW,CAACf,KAAU;IAC5B,IAAI2C,YAAY,GAAG,mBAAmB;IAEtC,IAAI3C,KAAK,CAACA,KAAK,EAAE4C,OAAO,EAAE;MACxBD,YAAY,GAAG3C,KAAK,CAACA,KAAK,CAAC4C,OAAO;KACnC,MAAM,IAAI5C,KAAK,CAAC4C,OAAO,EAAE;MACxBD,YAAY,GAAG3C,KAAK,CAAC4C,OAAO;;IAG9B,OAAOnE,UAAU,CAACkE,YAAY,CAAC;EACjC;;;uBA3PW9D,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAgE,SAAXhE,WAAW;MAAAiE,YAFV;IAAM;EAAA", "names": ["BehaviorSubject", "throwError", "catchError", "tap", "environment", "AuthService", "constructor", "http", "router", "tokenService", "apiUrl", "currentUserSubject", "asObservable", "isAuthenticatedSubject", "initializeAuth", "token", "getToken", "user", "getStoredUser", "isTokenExpired", "refreshToken", "subscribe", "next", "startTokenExpirationCheck", "error", "clearAuthData", "login", "credentials", "post", "API_URL", "pipe", "response", "success", "accessToken", "rememberMe", "setRememberMe", "setToken", "setRefreshToken", "setUser", "handleError", "register", "userData", "endpoint", "isOracleRegistration", "logout", "getRefreshToken", "getRememberMe", "getCurrentUser", "get", "changePassword", "request", "forgotPassword", "resetPassword", "loginWithOAuth", "checkTokenExpiration", "setInterval", "clearAllTokens", "navigate", "isAuthenticated", "hasRole", "role", "value", "roles", "includes", "hasPermission", "permission", "permissions", "errorMessage", "message", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\auth\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\r\nimport { map, catchError, tap } from 'rxjs/operators';\r\nimport { Router } from '@angular/router';\r\nimport { TokenService } from './token.service';\r\nimport { environment } from '../../../environments/environment';\r\nimport {\r\n  LoginRequest,\r\n  RegisterRequest,\r\n  OracleRegisterRequest,\r\n  AuthResponse,\r\n  UserInfo,\r\n  ChangePasswordRequest,\r\n  ForgotPasswordRequest,\r\n  ResetPasswordRequest,\r\n  ApiResponse,\r\n  OAuthLoginRequest\r\n} from '../models/auth.models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private readonly API_URL = `${environment.apiUrl}/auth`;\r\n  private readonly TOKEN_KEY = 'access_token';\r\n  private readonly REFRESH_TOKEN_KEY = 'refresh_token';\r\n  private readonly USER_KEY = 'user_info';\r\n  private readonly REMEMBER_ME_KEY = 'remember_me';\r\n\r\n  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n\r\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\r\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private router: Router,\r\n    private tokenService: TokenService\r\n  ) {\r\n    this.initializeAuth();\r\n  }\r\n\r\n  private initializeAuth(): void {\r\n    const token = this.tokenService.getToken();\r\n    const user = this.tokenService.getStoredUser();\r\n\r\n    if (token && user) {\r\n      // Check if token is expired\r\n      if (this.tokenService.isTokenExpired(token)) {\r\n        // Try to refresh the token\r\n        this.refreshToken().subscribe({\r\n          next: () => {\r\n            this.currentUserSubject.next(user);\r\n            this.isAuthenticatedSubject.next(true);\r\n            this.startTokenExpirationCheck();\r\n          },\r\n          error: () => {\r\n            // Refresh failed, clear auth data\r\n            this.clearAuthData();\r\n          }\r\n        });\r\n      } else {\r\n        this.currentUserSubject.next(user);\r\n        this.isAuthenticatedSubject.next(true);\r\n        this.startTokenExpirationCheck();\r\n      }\r\n    }\r\n  }\r\n\r\n  login(credentials: LoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/login`, credentials)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            // Store remember me preference\r\n            const rememberMe = credentials.rememberMe || false;\r\n            this.tokenService.setRememberMe(rememberMe);\r\n\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            this.tokenService.setUser(response.user, rememberMe);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  register(userData: RegisterRequest | OracleRegisterRequest): Observable<AuthResponse> {\r\n    // Determine endpoint based on data type\r\n    const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\r\n\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/${endpoint}`, userData)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            // For registration, default to remember me = false\r\n            const rememberMe = false;\r\n            this.tokenService.setRememberMe(rememberMe);\r\n\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            this.tokenService.setUser(response.user, rememberMe);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  private isOracleRegistration(userData: RegisterRequest | OracleRegisterRequest): userData is OracleRegisterRequest {\r\n    return 'professionalTitle' in userData;\r\n  }\r\n\r\n  logout(): Observable<any> {\r\n    return this.http.post(`${this.API_URL}/logout`, {})\r\n      .pipe(\r\n        tap(() => {\r\n          this.clearAuthData();\r\n        }),\r\n        catchError(() => {\r\n          // Even if logout fails on server, clear local data\r\n          this.clearAuthData();\r\n          return throwError('Logout failed');\r\n        })\r\n      );\r\n  }\r\n\r\n  refreshToken(): Observable<AuthResponse> {\r\n    const refreshToken = this.tokenService.getRefreshToken();\r\n    if (!refreshToken) {\r\n      return throwError('No refresh token available');\r\n    }\r\n\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/refresh-token`, { refreshToken })\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken) {\r\n            const rememberMe = this.tokenService.getRememberMe();\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            if (response.user) {\r\n              this.tokenService.setUser(response.user, rememberMe);\r\n              this.currentUserSubject.next(response.user);\r\n            }\r\n          }\r\n        }),\r\n        catchError(error => {\r\n          this.clearAuthData();\r\n          return throwError(error);\r\n        })\r\n      );\r\n  }\r\n\r\n  getCurrentUser(): Observable<UserInfo> {\r\n    return this.http.get<UserInfo>(`${this.API_URL}/me`)\r\n      .pipe(\r\n        tap(user => {\r\n          const rememberMe = this.tokenService.getRememberMe();\r\n          this.tokenService.setUser(user, rememberMe);\r\n          this.currentUserSubject.next(user);\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  changePassword(request: ChangePasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/change-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  forgotPassword(request: ForgotPasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/forgot-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  resetPassword(request: ResetPasswordRequest): Observable<ApiResponse<any>> {\r\n    return this.http.post<ApiResponse<any>>(`${this.API_URL}/reset-password`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  loginWithOAuth(request: OAuthLoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.API_URL}/oauth-login`, request)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.accessToken && response.user) {\r\n            // For OAuth, default to remember me = true for better UX\r\n            const rememberMe = true;\r\n            this.tokenService.setRememberMe(rememberMe);\r\n\r\n            this.tokenService.setToken(response.accessToken, rememberMe);\r\n            if (response.refreshToken) {\r\n              this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\r\n            }\r\n            this.tokenService.setUser(response.user, rememberMe);\r\n            this.currentUserSubject.next(response.user);\r\n            this.isAuthenticatedSubject.next(true);\r\n          }\r\n        }),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Token management - delegated to TokenService\r\n  getToken(): string | null {\r\n    return this.tokenService.getToken();\r\n  }\r\n\r\n  checkTokenExpiration(): void {\r\n    const token = this.tokenService.getToken();\r\n    if (token && this.tokenService.isTokenExpired(token)) {\r\n      // Try to refresh the token\r\n      this.refreshToken().subscribe({\r\n        next: () => {\r\n          // Token refreshed successfully\r\n        },\r\n        error: () => {\r\n          // Refresh failed, logout user\r\n          this.clearAuthData();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  startTokenExpirationCheck(): void {\r\n    // Check token expiration every 5 minutes\r\n    setInterval(() => {\r\n      this.checkTokenExpiration();\r\n    }, 5 * 60 * 1000);\r\n  }\r\n\r\n  private clearAuthData(): void {\r\n    this.tokenService.clearAllTokens();\r\n    this.currentUserSubject.next(null);\r\n    this.isAuthenticatedSubject.next(false);\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    return this.tokenService.isAuthenticated();\r\n  }\r\n\r\n\r\n\r\n  hasRole(role: string): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user?.roles.includes(role) || false;\r\n  }\r\n\r\n  hasPermission(permission: string): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user?.permissions.includes(permission) || false;\r\n  }\r\n\r\n  private handleError(error: any): Observable<never> {\r\n    let errorMessage = 'An error occurred';\r\n\r\n    if (error.error?.message) {\r\n      errorMessage = error.error.message;\r\n    } else if (error.message) {\r\n      errorMessage = error.message;\r\n    }\r\n\r\n    return throwError(errorMessage);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}