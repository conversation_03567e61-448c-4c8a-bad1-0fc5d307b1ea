{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ArticleService = /*#__PURE__*/(() => {\n  class ArticleService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = `${environment.apiUrl}/article`;\n    }\n    /**\r\n     * Get featured articles for home page\r\n     */\n    getFeaturedArticles(count = 6) {\n      const params = new HttpParams().set('count', count.toString());\n      return this.http.get(`${this.API_URL}/featured`, {\n        params\n      }).pipe(map(response => response.articles), catchError(error => {\n        console.error('Error loading featured articles from database:', error);\n        throw error;\n      }));\n    }\n    /**\r\n     * Get article by slug - returns full content for authenticated users,\r\n     * limited content for anonymous users\r\n     */\n    getArticleBySlug(slug) {\n      return this.http.get(`${this.API_URL}/slug/${slug}`).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Get article preview by slug - always returns limited content\r\n     */\n    getArticlePreview(slug) {\n      return this.http.get(`${this.API_URL}/preview/${slug}`).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Search articles with filters\r\n     */\n    searchArticles(request) {\n      return this.http.post(`${this.API_URL}/search`, request).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Get articles by category\r\n     */\n    getArticlesByCategory(category, page = 1, pageSize = 10) {\n      const params = new HttpParams().set('category', category).set('page', page.toString()).set('pageSize', pageSize.toString());\n      return this.http.get(`${this.API_URL}/category`, {\n        params\n      }).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Get articles by author\r\n     */\n    getArticlesByAuthor(authorId, page = 1, pageSize = 10) {\n      const params = new HttpParams().set('authorId', authorId.toString()).set('page', page.toString()).set('pageSize', pageSize.toString());\n      return this.http.get(`${this.API_URL}/author`, {\n        params\n      }).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Record article view for analytics\r\n     */\n    recordArticleView(articleId) {\n      return this.http.post(`${this.API_URL}/${articleId}/view`, {}).pipe(catchError(this.handleError));\n    }\n    // Article Management Methods\n    /**\r\n     * Get user's articles for management\r\n     */\n    getMyArticles(page = 1, pageSize = 10, status) {\n      let params = new HttpParams().set('page', page.toString()).set('pageSize', pageSize.toString());\n      if (status) {\n        params = params.set('status', status);\n      }\n      return this.http.get(`${this.API_URL}/my-articles`, {\n        params\n      }).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Get article by ID for editing\r\n     */\n    getArticleForEdit(articleId) {\n      return this.http.get(`${this.API_URL}/${articleId}`).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Create a new article\r\n     */\n    createArticle(request) {\n      return this.http.post(`${this.API_URL}`, request).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Update an existing article\r\n     */\n    updateArticle(articleId, request) {\n      return this.http.put(`${this.API_URL}/${articleId}`, request).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Delete an article\r\n     */\n    deleteArticle(articleId) {\n      return this.http.delete(`${this.API_URL}/${articleId}`).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Publish an article\r\n     */\n    publishArticle(articleId, publishAt) {\n      const request = publishAt ? {\n        publishAt\n      } : {};\n      return this.http.post(`${this.API_URL}/${articleId}/publish`, request).pipe(catchError(this.handleError));\n    }\n    /**\r\n     * Unpublish an article (set to draft)\r\n     */\n    unpublishArticle(articleId) {\n      return this.http.post(`${this.API_URL}/${articleId}/unpublish`, {}).pipe(catchError(this.handleError));\n    }\n    handleError(error) {\n      let errorMessage = 'Възникна неочаквана грешка при зареждането на статиите';\n      if (error.error && error.error.message) {\n        errorMessage = error.error.message;\n      } else if (error.status === 0) {\n        errorMessage = 'Няма връзка със сървъра';\n      } else if (error.status === 404) {\n        errorMessage = 'Статията не е намерена';\n      } else if (error.status === 401) {\n        errorMessage = 'Необходима е автентификация за достъп до пълното съдържание';\n      } else if (error.status === 403) {\n        errorMessage = 'Нямате права за достъп до това съдържание';\n      } else if (error.status >= 500) {\n        errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\n      }\n      console.error('Article service error:', error);\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function ArticleService_Factory(t) {\n        return new (t || ArticleService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ArticleService,\n        factory: ArticleService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ArticleService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}