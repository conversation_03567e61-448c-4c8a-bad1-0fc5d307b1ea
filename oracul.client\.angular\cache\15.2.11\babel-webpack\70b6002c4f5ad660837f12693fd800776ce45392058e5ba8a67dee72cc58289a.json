{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../profile/services/profile.service\";\nimport * as i2 from \"../shared/services/article.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../core/i18n/translation.service\";\nimport * as i5 from \"../shared/services/avatar.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../shared/directives/lazy-load-image.directive\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/chips\";\nfunction HomeComponent_div_31_mat_card_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r7.name, \" \");\n  }\n}\nfunction HomeComponent_div_31_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_31_mat_card_1_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const profile_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.viewProfile(profile_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵelement(2, \"img\", 35);\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"h3\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 38);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 39)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 40)(14, \"p\", 41);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 42)(17, \"div\", 43);\n    i0.ɵɵtemplate(18, HomeComponent_div_31_mat_card_1_span_18_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 45)(20, \"div\", 46)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 46)(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"thumb_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"mat-card-actions\")(31, \"button\", 47)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 48)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const profile_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"appLazyLoadImage\", profile_r5.profilePhotoUrl)(\"fallbackFirstName\", profile_r5.firstName)(\"fallbackLastName\", profile_r5.lastName)(\"alt\", profile_r5.firstName + \" \" + profile_r5.lastName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", profile_r5.firstName, \" \", profile_r5.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(profile_r5.professionalTitle);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(profile_r5.location == null ? null : profile_r5.location.displayLocation);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(profile_r5.headline);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", profile_r5.skills.slice(0, 3));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", profile_r5.profileViews, \" \", ctx_r4.t.home.views, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.getTotalEndorsements(profile_r5), \" \", ctx_r4.t.home.endorsements, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.t.home.viewProfile, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.t.common.contact, \" \");\n  }\n}\nfunction HomeComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, HomeComponent_div_31_mat_card_1_Template, 39, 16, \"mat-card\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredProfiles);\n  }\n}\nfunction HomeComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.t.common.loading, \"...\");\n  }\n}\nfunction HomeComponent_mat_card_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 51);\n    i0.ɵɵlistener(\"click\", function HomeComponent_mat_card_43_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const article_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.readArticle(article_r10));\n    });\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementStart(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\")(6, \"div\", 53)(7, \"span\", 54);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 55);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"p\", 57);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-chip\", 58);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-actions\")(20, \"button\", 47)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"read_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 59)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 59)(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"bookmark_border\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const article_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", article_r10.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", article_r10.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r10.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"By \", article_r10.author, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, article_r10.publishedAt, \"MMM d, y\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", article_r10.readTime, \" \", ctx_r2.t.home.minRead, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r10.excerpt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(article_r10.category);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.home.readArticle, \" \");\n  }\n}\nfunction HomeComponent_mat_card_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 60);\n    i0.ɵɵlistener(\"click\", function HomeComponent_mat_card_54_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const sign_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.viewHoroscope(sign_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"div\", 61)(3, \"span\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-title\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"p\", 63);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 64)(13, \"div\", 65)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"local_fire_department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 65)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"casino\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 65)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"palette\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"mat-card-actions\")(29, \"button\", 47)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"read_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const sign_r13 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(sign_r13.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sign_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sign_r13.dateRange);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(sign_r13.todayPrediction);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(sign_r13.element);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.t.home.luckyNumber, \": \", sign_r13.luckyNumber, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(sign_r13.luckyColor);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.home.fullReading, \" \");\n  }\n}\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor(profileService, articleService, router, t, avatarService) {\n      this.profileService = profileService;\n      this.articleService = articleService;\n      this.router = router;\n      this.t = t;\n      this.avatarService = avatarService;\n      this.featuredProfiles = [];\n      this.featuredArticles = [];\n      this.horoscopeSigns = [];\n      this.isLoading = true;\n    }\n    ngOnInit() {\n      this.loadHomeData();\n    }\n    loadHomeData() {\n      this.isLoading = true;\n      // Load featured profiles from backend\n      this.profileService.getPublicProfiles(1, 6).subscribe({\n        next: result => {\n          this.featuredProfiles = result.profiles;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading featured profiles:', error);\n          // Fallback to single profile if public profiles fail\n          this.profileService.getProfile('luna-starweaver').subscribe({\n            next: profile => {\n              this.featuredProfiles = [profile];\n              this.isLoading = false;\n            },\n            error: err => {\n              console.error('Error loading profile:', err);\n              this.featuredProfiles = [];\n              this.isLoading = false;\n            }\n          });\n        }\n      });\n      // Load featured articles\n      this.loadFeaturedArticles();\n      // Load horoscope data (still using mock data for now)\n      this.horoscopeSigns = this.getMockHoroscope();\n    }\n    loadFeaturedArticles() {\n      this.articleService.getFeaturedArticles(6).subscribe({\n        next: articles => {\n          this.featuredArticles = articles;\n        },\n        error: error => {\n          console.error('Error loading featured articles from database:', error);\n          // Don't fall back to mock data - show empty state or error message\n          this.featuredArticles = [];\n        }\n      });\n    }\n    getMockHoroscope() {\n      return [{\n        id: 1,\n        name: 'Овен',\n        symbol: '♈',\n        element: 'Огън',\n        dateRange: '21 март - 19 април',\n        todayPrediction: 'Вашата огнена енергия е на върха си днес. Насочете тази страст към творчески проекти и нови начинания.',\n        luckyNumber: 7,\n        luckyColor: 'Червен',\n        compatibility: ['Лъв', 'Стрелец', 'Близнаци']\n      }, {\n        id: 2,\n        name: 'Телец',\n        symbol: '♉',\n        element: 'Земя',\n        dateRange: '20 април - 20 май',\n        todayPrediction: 'Фокусирайте се върху стабилността и комфорта днес. Вашата практична природа ще ви води към мъдри финансови решения.',\n        luckyNumber: 3,\n        luckyColor: 'Зелен',\n        compatibility: ['Дева', 'Козирог', 'Рак']\n      }, {\n        id: 3,\n        name: 'Близнаци',\n        symbol: '♊',\n        element: 'Въздух',\n        dateRange: '21 май - 20 юни',\n        todayPrediction: 'Комуникацията е ключова днес. Вашият ум и чар ще отворят нови врати и ще укрепят отношенията.',\n        luckyNumber: 5,\n        luckyColor: 'Жълт',\n        compatibility: ['Везни', 'Водолей', 'Овен']\n      }, {\n        id: 4,\n        name: 'Рак',\n        symbol: '♋',\n        element: 'Вода',\n        dateRange: '21 юни - 22 юли',\n        todayPrediction: 'Доверете се на интуицията си днес. Вашата емоционална интелигентност ще ви помогне да навигирате сложни ситуации с грация.',\n        luckyNumber: 2,\n        luckyColor: 'Сребърен',\n        compatibility: ['Скорпион', 'Риби', 'Телец']\n      }, {\n        id: 5,\n        name: 'Leo',\n        symbol: '♌',\n        element: 'Fire',\n        dateRange: 'Jul 23 - Aug 22',\n        todayPrediction: 'Your natural leadership shines bright today. Take center stage and inspire others with your confidence.',\n        luckyNumber: 1,\n        luckyColor: 'Gold',\n        compatibility: ['Aries', 'Sagittarius', 'Gemini']\n      }, {\n        id: 6,\n        name: 'Virgo',\n        symbol: '♍',\n        element: 'Earth',\n        dateRange: 'Aug 23 - Sep 22',\n        todayPrediction: 'Attention to detail pays off today. Your analytical skills will help you solve problems others cannot.',\n        luckyNumber: 6,\n        luckyColor: 'Navy Blue',\n        compatibility: ['Taurus', 'Capricorn', 'Cancer']\n      }];\n    }\n    navigateToLogin() {\n      console.log('Navigating to login...');\n      this.router.navigate(['/login']);\n    }\n    navigateToRegister() {\n      console.log('Navigating to register...');\n      this.router.navigate(['/register']);\n    }\n    viewProfile(profile) {\n      this.router.navigate(['/profile', profile.slug]);\n    }\n    readArticle(article) {\n      // Navigate to article view page\n      this.router.navigate(['/articles', article.slug]);\n    }\n    viewHoroscope(sign) {\n      // For now, just navigate to login to view detailed horoscope\n      this.router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: `/horoscope/${sign.name.toLowerCase()}`\n        }\n      });\n    }\n    searchProfiles() {\n      this.router.navigate(['/profiles/search']);\n    }\n    getTotalEndorsements(profile) {\n      return profile.skills.reduce((sum, skill) => sum + skill.endorsements, 0);\n    }\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ProfileService), i0.ɵɵdirectiveInject(i2.ArticleService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.TranslationService), i0.ɵɵdirectiveInject(i5.AvatarService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"app-home\"]],\n        decls: 70,\n        vars: 19,\n        consts: [[\"id\", \"hero\", 1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"cta-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"secondary-button\", 3, \"click\"], [1, \"hero-image\"], [\"src\", \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop\", \"alt\", \"Mystical cosmic background\", 1, \"hero-img\"], [\"id\", \"astrologers\", 1, \"featured-section\"], [1, \"section-container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"section-subtitle\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"view-all-btn\", 3, \"click\"], [\"class\", \"profiles-grid\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"id\", \"articles\", 1, \"articles-section\"], [1, \"articles-grid\"], [\"class\", \"article-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"horoscope\", 1, \"horoscope-section\"], [1, \"horoscope-grid\"], [\"class\", \"horoscope-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"cta-section\"], [1, \"cta-content\"], [1, \"cta-title\"], [1, \"cta-subtitle\"], [1, \"cta-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"cta-primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"cta-secondary\", 3, \"click\"], [1, \"profiles-grid\"], [\"class\", \"profile-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-card\", 3, \"click\"], [1, \"profile-header\"], [\"loadingClass\", \"home-avatar-loading\", \"errorClass\", \"home-avatar-error\", \"loadedClass\", \"home-avatar-loaded\", 1, \"profile-avatar\", 3, \"appLazyLoadImage\", \"fallbackFirstName\", \"fallbackLastName\", \"alt\"], [1, \"profile-info\"], [1, \"profile-name\"], [1, \"profile-title\"], [1, \"profile-location\"], [1, \"profile-content\"], [1, \"profile-headline\"], [1, \"profile-skills\"], [1, \"skills-container\"], [\"class\", \"skill-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"profile-stats\"], [1, \"stat\"], [\"mat-button\", \"\", \"color\", \"primary\"], [\"mat-button\", \"\", \"color\", \"accent\"], [1, \"skill-chip\"], [1, \"loading-container\"], [1, \"article-card\", 3, \"click\"], [\"mat-card-image\", \"\", 1, \"article-image\", 3, \"src\", \"alt\"], [1, \"article-meta\"], [1, \"author\"], [1, \"date\"], [1, \"read-time\"], [1, \"article-excerpt\"], [1, \"category-chip\"], [\"mat-icon-button\", \"\"], [1, \"horoscope-card\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"sign-avatar\"], [1, \"sign-symbol\"], [1, \"prediction\"], [1, \"sign-details\"], [1, \"detail\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 5)(8, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_8_listener() {\n              return ctx.navigateToRegister();\n            });\n            i0.ɵɵelementStart(9, \"mat-icon\");\n            i0.ɵɵtext(10, \"star\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_12_listener() {\n              return ctx.navigateToLogin();\n            });\n            i0.ɵɵelementStart(13, \"mat-icon\");\n            i0.ɵɵtext(14, \"login\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 8);\n            i0.ɵɵelement(17, \"img\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"section\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"h2\", 13)(22, \"mat-icon\");\n            i0.ɵɵtext(23, \"people\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"p\", 14);\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_27_listener() {\n              return ctx.searchProfiles();\n            });\n            i0.ɵɵelementStart(28, \"mat-icon\");\n            i0.ɵɵtext(29, \"search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(31, HomeComponent_div_31_Template, 2, 1, \"div\", 16);\n            i0.ɵɵtemplate(32, HomeComponent_div_32_Template, 4, 1, \"div\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"section\", 18)(34, \"div\", 11)(35, \"div\", 12)(36, \"h2\", 13)(37, \"mat-icon\");\n            i0.ɵɵtext(38, \"article\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"p\", 14);\n            i0.ɵɵtext(41);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"div\", 19);\n            i0.ɵɵtemplate(43, HomeComponent_mat_card_43_Template, 30, 13, \"mat-card\", 20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(44, \"section\", 21)(45, \"div\", 11)(46, \"div\", 12)(47, \"h2\", 13)(48, \"mat-icon\");\n            i0.ɵɵtext(49, \"brightness_7\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(50);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"p\", 14);\n            i0.ɵɵtext(52);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"div\", 22);\n            i0.ɵɵtemplate(54, HomeComponent_mat_card_54_Template, 33, 9, \"mat-card\", 23);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(55, \"section\", 24)(56, \"div\", 25)(57, \"h2\", 26);\n            i0.ɵɵtext(58);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"p\", 27);\n            i0.ɵɵtext(60);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"div\", 28)(62, \"button\", 29);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_62_listener() {\n              return ctx.navigateToRegister();\n            });\n            i0.ɵɵelementStart(63, \"mat-icon\");\n            i0.ɵɵtext(64, \"star\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(65);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"button\", 30);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_66_listener() {\n              return ctx.navigateToLogin();\n            });\n            i0.ɵɵelementStart(67, \"mat-icon\");\n            i0.ɵɵtext(68, \"login\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(69);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.t.home.heroTitle);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.heroSubtitle, \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.startJourney, \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.common.login, \" \");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.featuredAstrologers, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.t.home.featuredAstrologersSubtitle);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.browseAllAstrologers, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.cosmicWisdomArticles, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.t.home.cosmicWisdomSubtitle);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.featuredArticles);\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.dailyHoroscope, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.t.home.dailyHoroscopeSubtitle);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.horoscopeSigns);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.t.home.ctaTitle);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.ctaSubtitle, \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.createFreeAccount, \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.alreadyMember, \" \");\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i7.LazyLoadImageDirective, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardActions, i9.MatCardAvatar, i9.MatCardContent, i9.MatCardHeader, i9.MatCardImage, i9.MatCardSubtitle, i9.MatCardTitle, i10.MatIcon, i11.MatProgressSpinner, i12.MatChip, i6.DatePipe],\n        styles: [\".hero-section[_ngcontent-%COMP%]{background:var(--theme-gradient-primary);color:#fff;padding:80px 20px;min-height:500px;display:flex;align-items:center;justify-content:center}.hero-content[_ngcontent-%COMP%]{max-width:1200px;width:100%;display:grid;grid-template-columns:1fr 1fr;gap:60px;align-items:center}.hero-text[_ngcontent-%COMP%]{max-width:500px}.hero-title[_ngcontent-%COMP%]{font-size:3.5rem;font-weight:700;margin-bottom:20px;line-height:1.2}.hero-subtitle[_ngcontent-%COMP%]{font-size:1.2rem;margin-bottom:40px;opacity:.9;line-height:1.6}.hero-actions[_ngcontent-%COMP%]{display:flex;gap:20px;flex-wrap:wrap}.cta-button[_ngcontent-%COMP%], .secondary-button[_ngcontent-%COMP%]{padding:12px 32px;font-size:1.1rem;font-weight:600;border-radius:8px}.hero-image[_ngcontent-%COMP%]{display:flex;justify-content:center}.hero-img[_ngcontent-%COMP%]{max-width:100%;height:auto;border-radius:16px;box-shadow:0 20px 40px #0000004d}.featured-section[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%], .horoscope-section[_ngcontent-%COMP%]{padding:80px 20px;background:var(--theme-background)}.section-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:60px}.section-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;font-size:2.5rem;font-weight:700;color:var(--theme-text-primary);margin-bottom:16px}.section-subtitle[_ngcontent-%COMP%]{font-size:1.2rem;color:var(--theme-text-secondary);margin-bottom:30px}.view-all-btn[_ngcontent-%COMP%]{margin-top:20px}.profiles-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:30px;margin-bottom:40px}.profile-card[_ngcontent-%COMP%]{cursor:pointer;transition:transform .3s ease,box-shadow .3s ease;border-radius:16px;overflow:hidden}.profile-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 24px #00000026}.profile-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:20px;background:var(--theme-accent-light)}.profile-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;object-fit:cover;border:3px solid var(--theme-accent);transition:opacity .3s ease,filter .3s ease}.profile-avatar.home-avatar-loading[_ngcontent-%COMP%]{opacity:.7;filter:blur(1px)}.profile-avatar.home-avatar-loaded[_ngcontent-%COMP%]{opacity:1;filter:none}.profile-avatar.home-avatar-error[_ngcontent-%COMP%]{opacity:.8;filter:grayscale(20%)}.profile-info[_ngcontent-%COMP%]{flex:1}.profile-name[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;margin:0 0 4px;color:var(--theme-text-primary)}.profile-title[_ngcontent-%COMP%]{font-size:1rem;color:var(--theme-text-secondary);margin:0 0 8px}.profile-location[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-size:.9rem;color:var(--theme-text-secondary)}.profile-content[_ngcontent-%COMP%]{padding:20px}.profile-headline[_ngcontent-%COMP%]{font-size:.95rem;line-height:1.5;color:var(--theme-text-primary);margin-bottom:16px;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}.profile-skills[_ngcontent-%COMP%]{margin-bottom:16px}.skills-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px}.skill-chip[_ngcontent-%COMP%]{background:var(--theme-accent);color:var(--theme-text-primary);padding:4px 12px;border-radius:16px;font-size:.85rem;font-weight:500}.profile-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:16px}.stat[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:.9rem;color:var(--theme-text-secondary)}.stat[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.articles-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:30px}.article-card[_ngcontent-%COMP%]{cursor:pointer;transition:transform .3s ease,box-shadow .3s ease;border-radius:16px;overflow:hidden}.article-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 16px #0000001f}.article-image[_ngcontent-%COMP%]{height:200px;object-fit:cover}.article-meta[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px;font-size:.85rem}.author[_ngcontent-%COMP%]{font-weight:600;color:var(--theme-primary)}.date[_ngcontent-%COMP%], .read-time[_ngcontent-%COMP%]{color:var(--theme-text-secondary)}.article-excerpt[_ngcontent-%COMP%]{font-size:.95rem;line-height:1.6;color:var(--theme-text-primary);margin-bottom:16px;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}.category-chip[_ngcontent-%COMP%]{background:var(--theme-primary);color:#fff;font-size:.8rem}.horoscope-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:24px}.horoscope-card[_ngcontent-%COMP%]{cursor:pointer;transition:transform .3s ease,box-shadow .3s ease;border-radius:16px;border:2px solid var(--theme-accent-light)}.horoscope-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 16px #0000001f;border-color:var(--theme-accent)}.sign-avatar[_ngcontent-%COMP%]{background:var(--theme-gradient-primary);color:#fff;display:flex;align-items:center;justify-content:center;width:50px;height:50px;border-radius:50%}.sign-symbol[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700}.prediction[_ngcontent-%COMP%]{font-size:.95rem;line-height:1.5;color:var(--theme-text-primary);margin-bottom:16px}.sign-details[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:12px}.detail[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:.85rem;color:var(--theme-text-secondary)}.detail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.cta-section[_ngcontent-%COMP%]{background:var(--theme-gradient-secondary);color:#fff;padding:80px 20px;text-align:center}.cta-content[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.cta-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:20px}.cta-subtitle[_ngcontent-%COMP%]{font-size:1.2rem;margin-bottom:40px;opacity:.9;line-height:1.6}.cta-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:20px;flex-wrap:wrap}.cta-primary[_ngcontent-%COMP%], .cta-secondary[_ngcontent-%COMP%]{padding:12px 32px;font-size:1.1rem;font-weight:600;border-radius:8px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:20px;padding:60px 20px;color:var(--theme-text-secondary)}@media (max-width: 768px){.nav-links[_ngcontent-%COMP%], .nav-actions[_ngcontent-%COMP%]{display:none}.mobile-menu-toggle[_ngcontent-%COMP%]{display:flex}.nav-container[_ngcontent-%COMP%]{padding:0 16px}.hero-section[_ngcontent-%COMP%]{padding:120px 20px 80px}.hero-content[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:40px;text-align:center}.hero-title[_ngcontent-%COMP%]{font-size:2.5rem}.profiles-grid[_ngcontent-%COMP%], .articles-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.horoscope-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(250px,1fr))}.section-title[_ngcontent-%COMP%], .cta-title[_ngcontent-%COMP%]{font-size:2rem}.hero-actions[_ngcontent-%COMP%], .cta-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}}\"]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}