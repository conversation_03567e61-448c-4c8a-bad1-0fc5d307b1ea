{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/oauth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/progress-bar\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/checkbox\";\nimport * as i17 from \"@angular/material/divider\";\nimport * as i18 from \"@angular/material/chips\";\nimport * as i19 from \"@angular/material/datepicker\";\nfunction RegisterComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u041F\\u0440\\u0438\\u0441\\u044A\\u0435\\u0434\\u0438\\u043D\\u0435\\u0442\\u0435 \\u0441\\u0435 \\u043A\\u044A\\u043C \\u041E\\u0440\\u0430\\u043A\\u0443\\u043B \\u0438 \\u0437\\u0430\\u043F\\u043E\\u0447\\u043D\\u0435\\u0442\\u0435 \\u0441\\u0432\\u043E\\u0435\\u0442\\u043E \\u043F\\u044A\\u0442\\u0443\\u0432\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"mat-progress-bar\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \\u0421\\u0442\\u044A\\u043F\\u043A\\u0430 \", ctx_r1.currentStep, \" \\u043E\\u0442 \", ctx_r1.totalSteps, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r1.currentStep / ctx_r1.totalSteps * 100);\n  }\n}\nfunction RegisterComponent_div_11_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0417\\u0430 \\u0445\\u043E\\u0440\\u0430, \\u043A\\u043E\\u0438\\u0442\\u043E \\u0442\\u044A\\u0440\\u0441\\u044F\\u0442 \\u0434\\u0443\\u0445\\u043E\\u0432\\u043D\\u043E \\u0432\\u043E\\u0434\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_11_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0417\\u0430 \\u043F\\u0440\\u0430\\u043A\\u0442\\u0438\\u043A\\u0443\\u0432\\u0430\\u0449\\u0438 \\u0430\\u0441\\u0442\\u0440\\u043E\\u043B\\u043E\\u0437\\u0438, \\u0442\\u0430\\u0440\\u043E \\u0447\\u0438\\u0442\\u0446\\u0438 \\u0438 \\u0434\\u0443\\u0445\\u043E\\u0432\\u043D\\u0438 \\u043A\\u043E\\u043D\\u0441\\u0443\\u043B\\u0442\\u0430\\u043D\\u0442\\u0438\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0442\\u0438\\u043F \\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 13)(4, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_div_11_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.setRegistrationType(\"general\"));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"\\u041E\\u0431\\u0438\\u043A\\u043D\\u043E\\u0432\\u0435\\u043D \\u043F\\u043E\\u0442\\u0440\\u0435\\u0431\\u0438\\u0442\\u0435\\u043B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_div_11_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.setRegistrationType(\"oracle\"));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"auto_awesome\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"\\u041E\\u0440\\u0430\\u043A\\u0443\\u043B\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"p\", 16);\n    i0.ɵɵtemplate(15, RegisterComponent_div_11_span_15_Template, 2, 0, \"span\", 4);\n    i0.ɵɵtemplate(16, RegisterComponent_div_11_span_16_Template, 2, 0, \"span\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.registrationType === \"general\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.isOracleRegistration());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.registrationType === \"general\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isOracleRegistration());\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getFirstNameErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getLastNameErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.getEmailErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getPasswordErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_mat_error_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getConfirmPasswordErrorMessage(), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_1_div_45_mat_error_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0422\\u0440\\u044F\\u0431\\u0432\\u0430 \\u0434\\u0430 \\u043F\\u0440\\u0438\\u0435\\u043C\\u0435\\u0442\\u0435 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0442\\u0430 \\u0438 \\u043F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u0432\\u0435\\u0440\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E\\u0441\\u0442 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_1_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-checkbox\", 37);\n    i0.ɵɵtext(2, \" \\u0421\\u044A\\u0433\\u043B\\u0430\\u0441\\u044F\\u0432\\u0430\\u043C \\u0441\\u0435 \\u0441 \");\n    i0.ɵɵelementStart(3, \"a\", 38);\n    i0.ɵɵtext(4, \"\\u0423\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u043B\\u0437\\u0432\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u0438 \");\n    i0.ɵɵelementStart(6, \"a\", 38);\n    i0.ɵɵtext(7, \"\\u041F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u0432\\u0435\\u0440\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E\\u0441\\u0442\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, RegisterComponent_form_12_div_1_div_45_mat_error_8_Template, 2, 0, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r24.registerForm.get(\"acceptTerms\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r24.registerForm.get(\"acceptTerms\")) == null ? null : tmp_0_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"mat-form-field\", 25)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"\\u041F\\u044A\\u0440\\u0432\\u043E \\u0438\\u043C\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 26);\n    i0.ɵɵelementStart(6, \"mat-icon\", 27);\n    i0.ɵɵtext(7, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, RegisterComponent_form_12_div_1_mat_error_8_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 25)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"\\u0424\\u0430\\u043C\\u0438\\u043B\\u043D\\u043E \\u0438\\u043C\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 28);\n    i0.ɵɵelementStart(13, \"mat-icon\", 27);\n    i0.ɵɵtext(14, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, RegisterComponent_form_12_div_1_mat_error_15_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-form-field\", 29)(17, \"mat-label\");\n    i0.ɵɵtext(18, \"\\u0418\\u043C\\u0435\\u0439\\u043B \\u0430\\u0434\\u0440\\u0435\\u0441\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 30);\n    i0.ɵɵelementStart(20, \"mat-icon\", 27);\n    i0.ɵɵtext(21, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, RegisterComponent_form_12_div_1_mat_error_22_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"mat-form-field\", 29)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"\\u0422\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0435\\u043D \\u043D\\u043E\\u043C\\u0435\\u0440 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 31);\n    i0.ɵɵelementStart(27, \"mat-icon\", 27);\n    i0.ɵɵtext(28, \"phone\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"mat-form-field\", 29)(30, \"mat-label\");\n    i0.ɵɵtext(31, \"\\u041F\\u0430\\u0440\\u043E\\u043B\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 32);\n    i0.ɵɵelementStart(33, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_1_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.hidePassword = !ctx_r26.hidePassword);\n    });\n    i0.ɵɵelementStart(34, \"mat-icon\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, RegisterComponent_form_12_div_1_mat_error_36_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"mat-form-field\", 29)(38, \"mat-label\");\n    i0.ɵɵtext(39, \"\\u041F\\u043E\\u0442\\u0432\\u044A\\u0440\\u0434\\u0435\\u0442\\u0435 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\\u0442\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(40, \"input\", 34);\n    i0.ɵɵelementStart(41, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_1_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.hideConfirmPassword = !ctx_r28.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(42, \"mat-icon\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(44, RegisterComponent_form_12_div_1_mat_error_44_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, RegisterComponent_form_12_div_1_div_45_Template, 9, 1, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_7_0;\n    let tmp_12_0;\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r9.registerForm.get(\"firstName\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r9.registerForm.get(\"firstName\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r9.registerForm.get(\"lastName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r9.registerForm.get(\"lastName\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r9.registerForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r9.registerForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"type\", ctx_r9.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-label\", \"\\u0421\\u043A\\u0440\\u0438\\u0439 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\")(\"aria-pressed\", ctx_r9.hidePassword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r9.registerForm.get(\"password\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r9.registerForm.get(\"password\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r9.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-label\", \"\\u0421\\u043A\\u0440\\u0438\\u0439 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\")(\"aria-pressed\", ctx_r9.hideConfirmPassword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r9.registerForm.get(\"confirmPassword\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r9.registerForm.get(\"confirmPassword\")) == null ? null : tmp_12_0.touched) || ctx_r9.registerForm.hasError(\"passwordMismatch\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.registrationType === \"general\");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r29.getFieldErrorMessage(\"professionalTitle\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const spec_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", spec_r35);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", spec_r35, \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.getFieldErrorMessage(\"primarySpecialization\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.getFieldErrorMessage(\"headline\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.getFieldErrorMessage(\"summary\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r34.getFieldErrorMessage(\"yearsOfExperience\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u041F\\u0440\\u043E\\u0444\\u0435\\u0441\\u0438\\u043E\\u043D\\u0430\\u043B\\u043D\\u0430 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 29)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"\\u041F\\u0440\\u043E\\u0444\\u0435\\u0441\\u0438\\u043E\\u043D\\u0430\\u043B\\u043D\\u0430 \\u0442\\u0438\\u0442\\u043B\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 39);\n    i0.ɵɵelementStart(7, \"mat-icon\", 27);\n    i0.ɵɵtext(8, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, RegisterComponent_form_12_div_2_mat_error_9_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-form-field\", 29)(11, \"mat-label\");\n    i0.ɵɵtext(12, \"\\u041E\\u0441\\u043D\\u043E\\u0432\\u043D\\u0430 \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-select\", 40);\n    i0.ɵɵtemplate(14, RegisterComponent_form_12_div_2_mat_option_14_Template, 2, 2, \"mat-option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, RegisterComponent_form_12_div_2_mat_error_15_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-form-field\", 29)(17, \"mat-label\");\n    i0.ɵɵtext(18, \"\\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u0438\\u0435 (\\u043A\\u0440\\u0430\\u0442\\u043A\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 42);\n    i0.ɵɵelementStart(20, \"mat-icon\", 27);\n    i0.ɵɵtext(21, \"title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-hint\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, RegisterComponent_form_12_div_2_mat_error_24_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"mat-form-field\", 29)(26, \"mat-label\");\n    i0.ɵɵtext(27, \"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"textarea\", 43);\n    i0.ɵɵtext(29, \"            \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"mat-hint\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, RegisterComponent_form_12_div_2_mat_error_32_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 29)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"\\u0413\\u043E\\u0434\\u0438\\u043D\\u0438 \\u043E\\u043F\\u0438\\u0442\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"input\", 44);\n    i0.ɵɵelementStart(37, \"mat-icon\", 27);\n    i0.ɵɵtext(38, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, RegisterComponent_form_12_div_2_mat_error_39_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r10.registerForm.get(\"professionalTitle\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r10.registerForm.get(\"professionalTitle\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.getSpecializationOptions());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r10.registerForm.get(\"primarySpecialization\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r10.registerForm.get(\"primarySpecialization\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_3_0 = ctx_r10.registerForm.get(\"headline\")) == null ? null : tmp_3_0.value == null ? null : tmp_3_0.value.length) || 0, \"/220\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r10.registerForm.get(\"headline\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r10.registerForm.get(\"headline\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_5_0 = ctx_r10.registerForm.get(\"summary\")) == null ? null : tmp_5_0.value == null ? null : tmp_5_0.value.length) || 0, \"/2000\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r10.registerForm.get(\"summary\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r10.registerForm.get(\"summary\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r10.registerForm.get(\"yearsOfExperience\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r10.registerForm.get(\"yearsOfExperience\")) == null ? null : tmp_7_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_3_mat_error_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r36.getFieldErrorMessage(\"city\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_3_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r37.getFieldErrorMessage(\"country\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u041C\\u0435\\u0441\\u0442\\u043E\\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46)(4, \"mat-form-field\", 25)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"\\u0413\\u0440\\u0430\\u0434\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 47);\n    i0.ɵɵelementStart(8, \"mat-icon\", 27);\n    i0.ɵɵtext(9, \"location_city\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, RegisterComponent_form_12_div_3_mat_error_10_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 25)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"\\u041E\\u0431\\u043B\\u0430\\u0441\\u0442/\\u0420\\u0435\\u0433\\u0438\\u043E\\u043D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 48);\n    i0.ɵɵelementStart(15, \"mat-icon\", 27);\n    i0.ɵɵtext(16, \"map\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 29)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"\\u0414\\u044A\\u0440\\u0436\\u0430\\u0432\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 49);\n    i0.ɵɵelementStart(21, \"mat-icon\", 27);\n    i0.ɵɵtext(22, \"public\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, RegisterComponent_form_12_div_3_mat_error_23_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-form-field\", 29)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"\\u041F\\u043E\\u043A\\u0430\\u0437\\u0432\\u0430\\u043D\\u043E \\u043C\\u0435\\u0441\\u0442\\u043E\\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 50);\n    i0.ɵɵelementStart(28, \"mat-icon\", 27);\n    i0.ɵɵtext(29, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r11.registerForm.get(\"city\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r11.registerForm.get(\"city\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r11.registerForm.get(\"country\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r11.registerForm.get(\"country\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r39.getFieldErrorMessage(\"birthDate\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.getFieldErrorMessage(\"birthLocation\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sign_r49 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", sign_r49);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", sign_r49, \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r50 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r50);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r50, \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r43.getFieldErrorMessage(\"oracleTypes\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_option_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lang_r51 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", lang_r51);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", lang_r51, \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r45.getFieldErrorMessage(\"languagesSpoken\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_div_55_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-chip\", 70);\n    i0.ɵɵlistener(\"removed\", function RegisterComponent_form_12_div_4_div_55_mat_chip_2_Template_mat_chip_removed_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const i_r54 = restoredCtx.index;\n      const ctx_r55 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r55.removeSkill(i_r54));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"mat-icon\", 71);\n    i0.ɵɵtext(3, \"cancel\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const skill_r53 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r53, \" \");\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction RegisterComponent_form_12_div_4_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"mat-chip-set\");\n    i0.ɵɵtemplate(2, RegisterComponent_form_12_div_4_div_55_mat_chip_2_Template, 4, 1, \"mat-chip\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ((tmp_0_0 = ctx_r46.registerForm.get(\"skills\")) == null ? null : tmp_0_0.value) || i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction RegisterComponent_form_12_div_4_mat_error_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r48.getFieldErrorMessage(\"skills\"), \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u041E\\u0440\\u0430\\u043A\\u0443\\u043B\\u0441\\u043A\\u0430 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 51)(4, \"mat-form-field\", 25)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"\\u0414\\u0430\\u0442\\u0430 \\u043D\\u0430 \\u0440\\u0430\\u0436\\u0434\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 52);\n    i0.ɵɵelementStart(8, \"mat-datepicker-toggle\", 53)(9, \"mat-icon\", 54);\n    i0.ɵɵtext(10, \"cake\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(11, \"mat-datepicker\", null, 55);\n    i0.ɵɵtemplate(13, RegisterComponent_form_12_div_4_mat_error_13_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 25)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"\\u0427\\u0430\\u0441 \\u043D\\u0430 \\u0440\\u0430\\u0436\\u0434\\u0430\\u043D\\u0435 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 56);\n    i0.ɵɵelement(18, \"input\", 57);\n    i0.ɵɵelementStart(19, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_4_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.toggleTimeFormat());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"mat-hint\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"mat-form-field\", 29)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"\\u041C\\u044F\\u0441\\u0442\\u043E \\u043D\\u0430 \\u0440\\u0430\\u0436\\u0434\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 59);\n    i0.ɵɵelementStart(28, \"mat-icon\", 27);\n    i0.ɵɵtext(29, \"place\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, RegisterComponent_form_12_div_4_mat_error_30_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"mat-form-field\", 29)(32, \"mat-label\");\n    i0.ɵɵtext(33, \"\\u0410\\u0441\\u0442\\u0440\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u043D\\u0430 \\u0437\\u043E\\u0434\\u0438\\u044F (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"mat-select\", 60);\n    i0.ɵɵtemplate(35, RegisterComponent_form_12_div_4_mat_option_35_Template, 2, 2, \"mat-option\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"mat-form-field\", 29)(37, \"mat-label\");\n    i0.ɵɵtext(38, \"\\u041C\\u0435\\u0442\\u043E\\u0434\\u0438 \\u043D\\u0430 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"mat-select\", 61);\n    i0.ɵɵtemplate(40, RegisterComponent_form_12_div_4_mat_option_40_Template, 2, 2, \"mat-option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"mat-hint\");\n    i0.ɵɵtext(42, \"\\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0432\\u0441\\u0438\\u0447\\u043A\\u0438 \\u043C\\u0435\\u0442\\u043E\\u0434\\u0438, \\u043A\\u043E\\u0438\\u0442\\u043E \\u0438\\u0437\\u043F\\u043E\\u043B\\u0437\\u0432\\u0430\\u0442\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, RegisterComponent_form_12_div_4_mat_error_43_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"mat-form-field\", 29)(45, \"mat-label\");\n    i0.ɵɵtext(46, \"\\u0415\\u0437\\u0438\\u0446\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"mat-select\", 62);\n    i0.ɵɵtemplate(48, RegisterComponent_form_12_div_4_mat_option_48_Template, 2, 2, \"mat-option\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"mat-hint\");\n    i0.ɵɵtext(50, \"\\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0435\\u0437\\u0438\\u0446\\u0438\\u0442\\u0435, \\u043D\\u0430 \\u043A\\u043E\\u0438\\u0442\\u043E \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0442\\u0435 \\u043A\\u043E\\u043D\\u0441\\u0443\\u043B\\u0442\\u0430\\u0446\\u0438\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(51, RegisterComponent_form_12_div_4_mat_error_51_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 63)(53, \"h4\");\n    i0.ɵɵtext(54, \"\\u0423\\u043C\\u0435\\u043D\\u0438\\u044F \\u0438 \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(55, RegisterComponent_form_12_div_4_div_55_Template, 3, 2, \"div\", 64);\n    i0.ɵɵelementStart(56, \"mat-form-field\", 29)(57, \"mat-label\");\n    i0.ɵɵtext(58, \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0435\\u0442\\u0435 \\u0443\\u043C\\u0435\\u043D\\u0438\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"input\", 65, 66);\n    i0.ɵɵlistener(\"keydown.enter\", function RegisterComponent_form_12_div_4_Template_input_keydown_enter_59_listener($event) {\n      i0.ɵɵrestoreView(_r58);\n      const _r47 = i0.ɵɵreference(60);\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.addSkill($event, _r47));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_4_Template_button_click_61_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const _r47 = i0.ɵɵreference(60);\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.addSkillFromInput(_r47));\n    });\n    i0.ɵɵelementStart(62, \"mat-icon\");\n    i0.ɵɵtext(63, \"add\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"mat-hint\");\n    i0.ɵɵtext(65, \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0435\\u0442\\u0435 \\u043F\\u043E\\u043D\\u0435 3 \\u0443\\u043C\\u0435\\u043D\\u0438\\u044F \\u0441\\u0432\\u044A\\u0440\\u0437\\u0430\\u043D\\u0438 \\u0441 \\u0432\\u0430\\u0448\\u0430\\u0442\\u0430 \\u043F\\u0440\\u0430\\u043A\\u0442\\u0438\\u043A\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(66, RegisterComponent_form_12_div_4_mat_error_66_Template, 2, 1, \"mat-error\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r38 = i0.ɵɵreference(12);\n    const _r47 = i0.ɵɵreference(60);\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    let tmp_2_0;\n    let tmp_7_0;\n    let tmp_10_0;\n    let tmp_12_0;\n    let tmp_13_0;\n    let tmp_15_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"matDatepicker\", _r38);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r38);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r12.registerForm.get(\"birthDate\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r12.registerForm.get(\"birthDate\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"step\", ctx_r12.timeFormat24h ? 60 : 60);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", ctx_r12.timeFormat24h ? \"\\u041F\\u0440\\u0435\\u0432\\u043A\\u043B\\u044E\\u0447\\u0438 \\u043D\\u0430 12-\\u0447\\u0430\\u0441\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\" : \"\\u041F\\u0440\\u0435\\u0432\\u043A\\u043B\\u044E\\u0447\\u0438 \\u043D\\u0430 24-\\u0447\\u0430\\u0441\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r12.timeFormat24h ? \"schedule\" : \"access_time\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r12.timeFormat24h ? \"24-\\u0447\\u0430\\u0441\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\" : \"12-\\u0447\\u0430\\u0441\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r12.registerForm.get(\"birthLocation\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r12.registerForm.get(\"birthLocation\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getAstrologicalSigns());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getOracleTypeOptions());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r12.registerForm.get(\"oracleTypes\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r12.registerForm.get(\"oracleTypes\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getLanguageOptions());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r12.registerForm.get(\"languagesSpoken\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r12.registerForm.get(\"languagesSpoken\")) == null ? null : tmp_12_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r12.registerForm.get(\"skills\")) == null ? null : tmp_13_0.value == null ? null : tmp_13_0.value.length) > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", !_r47.value.trim());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx_r12.registerForm.get(\"skills\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r12.registerForm.get(\"skills\")) == null ? null : tmp_15_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u041A\\u043E\\u043D\\u0442\\u0430\\u043A\\u0442\\u0438 \\u0438 \\u0431\\u0438\\u0437\\u043D\\u0435\\u0441 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 29)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"\\u0423\\u0435\\u0431\\u0441\\u0430\\u0439\\u0442 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 72);\n    i0.ɵɵelementStart(7, \"mat-icon\", 27);\n    i0.ɵɵtext(8, \"language\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 29)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"\\u041F\\u043E\\u0440\\u0442\\u0444\\u043E\\u043B\\u0438\\u043E URL (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 73);\n    i0.ɵɵelementStart(13, \"mat-icon\", 27);\n    i0.ɵɵtext(14, \"work\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"h4\");\n    i0.ɵɵtext(16, \"\\u0411\\u0438\\u0437\\u043D\\u0435\\u0441 \\u0430\\u0434\\u0440\\u0435\\u0441 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 29)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"\\u0423\\u043B\\u0438\\u0446\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 74);\n    i0.ɵɵelementStart(21, \"mat-icon\", 27);\n    i0.ɵɵtext(22, \"home\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 75)(24, \"mat-form-field\", 25)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"\\u0413\\u0440\\u0430\\u0434\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 76);\n    i0.ɵɵelementStart(28, \"mat-icon\", 27);\n    i0.ɵɵtext(29, \"location_city\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"mat-form-field\", 25)(31, \"mat-label\");\n    i0.ɵɵtext(32, \"\\u041F\\u043E\\u0449\\u0435\\u043D\\u0441\\u043A\\u0438 \\u043A\\u043E\\u0434\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"input\", 77);\n    i0.ɵɵelementStart(34, \"mat-icon\", 27);\n    i0.ɵɵtext(35, \"markunread_mailbox\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"mat-checkbox\", 78);\n    i0.ɵɵtext(37, \" \\u041F\\u043E\\u043A\\u0430\\u0437\\u0432\\u0430\\u0439 \\u0431\\u0438\\u0437\\u043D\\u0435\\u0441 \\u0430\\u0434\\u0440\\u0435\\u0441\\u0430 \\u043F\\u0443\\u0431\\u043B\\u0438\\u0447\\u043D\\u043E \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterComponent_form_12_div_6_mat_error_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0422\\u0440\\u044F\\u0431\\u0432\\u0430 \\u0434\\u0430 \\u043F\\u0440\\u0438\\u0435\\u043C\\u0435\\u0442\\u0435 \\u0432\\u0441\\u0438\\u0447\\u043A\\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F \\u0438 \\u043F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0438 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u0422\\u0430\\u0440\\u0438\\u0444\\u0438 \\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79)(4, \"mat-form-field\", 25)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"\\u0427\\u0430\\u0441\\u043E\\u0432\\u0430 \\u0442\\u0430\\u0440\\u0438\\u0444\\u0430 (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 80);\n    i0.ɵɵelementStart(8, \"mat-icon\", 27);\n    i0.ɵɵtext(9, \"schedule\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"mat-form-field\", 25)(11, \"mat-label\");\n    i0.ɵɵtext(12, \"\\u0422\\u0430\\u0440\\u0438\\u0444\\u0430 \\u0437\\u0430 \\u0441\\u0435\\u0441\\u0438\\u044F (\\u043F\\u043E \\u0438\\u0437\\u0431\\u043E\\u0440)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 81);\n    i0.ɵɵelementStart(14, \"mat-icon\", 27);\n    i0.ɵɵtext(15, \"event\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"mat-form-field\", 29)(17, \"mat-label\");\n    i0.ɵɵtext(18, \"\\u0412\\u0430\\u043B\\u0443\\u0442\\u0430\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-select\", 82)(20, \"mat-option\", 83);\n    i0.ɵɵtext(21, \"BGN (\\u0411\\u044A\\u043B\\u0433\\u0430\\u0440\\u0441\\u043A\\u0438 \\u043B\\u0435\\u0432)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-option\", 84);\n    i0.ɵɵtext(23, \"EUR (\\u0415\\u0432\\u0440\\u043E)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-option\", 85);\n    i0.ɵɵtext(25, \"USD (\\u0414\\u043E\\u043B\\u0430\\u0440)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 36)(27, \"mat-checkbox\", 37);\n    i0.ɵɵtext(28, \" \\u0421\\u044A\\u0433\\u043B\\u0430\\u0441\\u044F\\u0432\\u0430\\u043C \\u0441\\u0435 \\u0441 \");\n    i0.ɵɵelementStart(29, \"a\", 38);\n    i0.ɵɵtext(30, \"\\u0423\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u043B\\u0437\\u0432\\u0430\\u043D\\u0435\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \", \");\n    i0.ɵɵelementStart(32, \"a\", 38);\n    i0.ɵɵtext(33, \"\\u041F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\\u0442\\u0430 \\u0437\\u0430 \\u043F\\u043E\\u0432\\u0435\\u0440\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E\\u0441\\u0442\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" \\u0438 \");\n    i0.ɵɵelementStart(35, \"a\", 38);\n    i0.ɵɵtext(36, \"\\u0423\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0442\\u0430 \\u0437\\u0430 \\u043E\\u0440\\u0430\\u043A\\u0443\\u043B\\u0438\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, RegisterComponent_form_12_div_6_mat_error_37_Template, 2, 0, \"mat-error\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(37);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r14.registerForm.get(\"acceptTerms\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r14.registerForm.get(\"acceptTerms\")) == null ? null : tmp_0_0.touched));\n  }\n}\nfunction RegisterComponent_form_12_div_7_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_7_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r66.previousStep());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u041D\\u0430\\u0437\\u0430\\u0434 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r62 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r62.isLoading);\n  }\n}\nfunction RegisterComponent_form_12_div_7_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_7_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"arrow_forward\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_7_mat_icon_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"person_add\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtemplate(1, RegisterComponent_form_12_div_7_button_1_Template, 4, 1, \"button\", 87);\n    i0.ɵɵelement(2, \"div\", 88);\n    i0.ɵɵelementStart(3, \"button\", 89);\n    i0.ɵɵtemplate(4, RegisterComponent_form_12_div_7_mat_icon_4_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtemplate(5, RegisterComponent_form_12_div_7_mat_icon_5_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtemplate(6, RegisterComponent_form_12_div_7_mat_icon_6_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.currentStep > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r15.isLoading || !ctx_r15.isCurrentStepValid());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.isLoading && ctx_r15.currentStep < ctx_r15.totalSteps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.isLoading && ctx_r15.currentStep === ctx_r15.totalSteps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.isLoading ? \"\\u041E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0430...\" : ctx_r15.currentStep < ctx_r15.totalSteps ? \"\\u041D\\u0430\\u043F\\u0440\\u0435\\u0434\" : \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0439 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442\", \" \");\n  }\n}\nfunction RegisterComponent_form_12_button_8_mat_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵelement(1, \"mat-spinner\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_button_8_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"person_add\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵtemplate(1, RegisterComponent_form_12_button_8_mat_icon_1_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtemplate(2, RegisterComponent_form_12_button_8_mat_icon_2_Template, 2, 0, \"mat-icon\", 4);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r16.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.isLoading ? \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442...\" : \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0439 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442\", \" \");\n  }\n}\nfunction RegisterComponent_form_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"mat-divider\");\n    i0.ɵɵelementStart(2, \"span\", 94);\n    i0.ɵɵtext(3, \"\\u0438\\u043B\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"mat-divider\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_form_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.signUpWithGoogle());\n    });\n    i0.ɵɵelement(2, \"img\", 97);\n    i0.ɵɵtext(3, \" \\u0420\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u044F \\u0441 Google \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function RegisterComponent_form_12_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r72.signUpWithFacebook());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\", 99);\n    i0.ɵɵtext(6, \"facebook\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" \\u0420\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u044F \\u0441 Facebook \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isLoading);\n  }\n}\nfunction RegisterComponent_form_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 17);\n    i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_form_12_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.onSubmit());\n    });\n    i0.ɵɵtemplate(1, RegisterComponent_form_12_div_1_Template, 46, 14, \"div\", 18);\n    i0.ɵɵtemplate(2, RegisterComponent_form_12_div_2_Template, 40, 8, \"div\", 18);\n    i0.ɵɵtemplate(3, RegisterComponent_form_12_div_3_Template, 30, 2, \"div\", 18);\n    i0.ɵɵtemplate(4, RegisterComponent_form_12_div_4_Template, 67, 16, \"div\", 18);\n    i0.ɵɵtemplate(5, RegisterComponent_form_12_div_5_Template, 38, 0, \"div\", 18);\n    i0.ɵɵtemplate(6, RegisterComponent_form_12_div_6_Template, 38, 1, \"div\", 18);\n    i0.ɵɵtemplate(7, RegisterComponent_form_12_div_7_Template, 8, 6, \"div\", 19);\n    i0.ɵɵtemplate(8, RegisterComponent_form_12_button_8_Template, 4, 4, \"button\", 20);\n    i0.ɵɵtemplate(9, RegisterComponent_form_12_div_9_Template, 5, 0, \"div\", 21);\n    i0.ɵɵtemplate(10, RegisterComponent_form_12_div_10_Template, 8, 2, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.registerForm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 2 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 3 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 4 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 5 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentStep === 6 && ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isOracleRegistration());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.registrationType === \"general\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.registrationType === \"general\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.registrationType === \"general\");\n  }\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    // Helper method for template\n    isOracleRegistration() {\n      return this.registrationType === 'oracle';\n    }\n    constructor(formBuilder, authService, oauthService, router, route, snackBar) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.oauthService = oauthService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.isLoading = false;\n      this.hidePassword = true;\n      this.hideConfirmPassword = true;\n      this.returnUrl = '/';\n      // Registration type\n      this.registrationType = 'general';\n      // Time format preference\n      this.timeFormat24h = true;\n      this.currentStep = 1;\n      this.totalSteps = 1;\n      this.registerForm = this.createGeneralForm();\n    }\n    ngOnInit() {\n      // Get return url from route parameters or default to '/'\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n      // Redirect if already logged in\n      this.authService.isAuthenticated$.subscribe(isAuth => {\n        if (isAuth) {\n          // If user is already authenticated, redirect to profile page instead of home\n          this.router.navigate(['/profile/edit']);\n        }\n      });\n    }\n    // Form creation methods\n    createGeneralForm() {\n      return this.formBuilder.group({\n        firstName: ['', [Validators.required, Validators.minLength(2)]],\n        lastName: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        phoneNumber: [''],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        confirmPassword: ['', [Validators.required]],\n        acceptTerms: [false, [Validators.requiredTrue]]\n      }, {\n        validators: this.passwordMatchValidator\n      });\n    }\n    createOracleForm() {\n      return this.formBuilder.group({\n        // Basic Information (Step 1)\n        firstName: ['', [Validators.required, Validators.minLength(2)]],\n        lastName: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        phoneNumber: [''],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        confirmPassword: ['', [Validators.required]],\n        // Professional Information (Step 2)\n        professionalTitle: ['', [Validators.required]],\n        headline: ['', [Validators.required, Validators.maxLength(220)]],\n        summary: ['', [Validators.required, Validators.maxLength(2000)]],\n        primarySpecialization: ['', [Validators.required]],\n        yearsOfExperience: [0, [Validators.required, Validators.min(0)]],\n        // Location Information (Step 3)\n        city: ['', [Validators.required]],\n        state: [''],\n        country: ['', [Validators.required]],\n        displayLocation: [''],\n        // Oracle-Specific Information (Step 4)\n        birthDate: ['', [Validators.required]],\n        birthTime: [''],\n        birthLocation: ['', [Validators.required]],\n        astrologicalSign: [''],\n        oracleTypes: [[], [Validators.required]],\n        languagesSpoken: [['Български'], [Validators.required]],\n        skills: [[], [Validators.required]],\n        // Contact & Business Information (Step 5)\n        website: [''],\n        portfolioUrl: [''],\n        businessStreet: [''],\n        businessCity: [''],\n        businessState: [''],\n        businessPostalCode: [''],\n        businessCountry: [''],\n        isBusinessAddressPublic: [false],\n        // Consultation Rates (Step 6)\n        hourlyRate: [0],\n        sessionRate: [0],\n        currency: ['BGN'],\n        acceptTerms: [false, [Validators.requiredTrue]]\n      }, {\n        validators: this.passwordMatchValidator\n      });\n    }\n    // Registration type switching\n    setRegistrationType(type) {\n      this.registrationType = type;\n      if (type === 'oracle') {\n        this.registerForm = this.createOracleForm();\n        this.totalSteps = 6;\n        this.currentStep = 1;\n      } else {\n        this.registerForm = this.createGeneralForm();\n        this.totalSteps = 1;\n        this.currentStep = 1;\n      }\n    }\n    // Step navigation for oracle registration\n    nextStep() {\n      if (this.currentStep < this.totalSteps && this.isCurrentStepValid()) {\n        this.currentStep++;\n      }\n    }\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    }\n    isCurrentStepValid() {\n      if (this.registrationType === 'general') {\n        return this.registerForm.valid;\n      }\n      // Validate current step for oracle registration\n      const step1Fields = ['firstName', 'lastName', 'email', 'password', 'confirmPassword'];\n      const step2Fields = ['professionalTitle', 'headline', 'summary', 'primarySpecialization', 'yearsOfExperience'];\n      const step3Fields = ['city', 'country'];\n      const step4Fields = ['birthDate', 'birthLocation', 'oracleTypes', 'languagesSpoken', 'skills'];\n      const step5Fields = []; // All optional\n      const step6Fields = ['currency', 'acceptTerms'];\n      let fieldsToValidate = [];\n      switch (this.currentStep) {\n        case 1:\n          fieldsToValidate = step1Fields;\n          break;\n        case 2:\n          fieldsToValidate = step2Fields;\n          break;\n        case 3:\n          fieldsToValidate = step3Fields;\n          break;\n        case 4:\n          fieldsToValidate = step4Fields;\n          break;\n        case 5:\n          fieldsToValidate = step5Fields;\n          break;\n        case 6:\n          fieldsToValidate = step6Fields;\n          break;\n      }\n      return fieldsToValidate.every(field => {\n        const control = this.registerForm.get(field);\n        return control ? control.valid : true;\n      });\n    }\n    // Get step title in Bulgarian\n    getStepTitle() {\n      if (this.registrationType === 'general') {\n        return 'Създаване на акаунт';\n      }\n      switch (this.currentStep) {\n        case 1:\n          return 'Основна информация';\n        case 2:\n          return 'Професионална информация';\n        case 3:\n          return 'Местоположение';\n        case 4:\n          return 'Оракулска информация';\n        case 5:\n          return 'Контакти и бизнес';\n        case 6:\n          return 'Тарифи и условия';\n        default:\n          return 'Регистрация';\n      }\n    }\n    // Helper methods for oracle registration\n    getSpecializationOptions() {\n      return ['Астрология', 'Таро', 'Кристални лечения', 'Нумерология', 'Хиромантия', 'Рунология', 'Медиумизъм', 'Енергийно лечение', 'Аура четене', 'Духовно консултиране'];\n    }\n    getOracleTypeOptions() {\n      return ['Таро карти', 'Астрологични карти', 'Кристали', 'Руни', 'Нумерология', 'Хиромантия', 'Медитация', 'Енергийна работа', 'Аура четене', 'Духовно водачество'];\n    }\n    getLanguageOptions() {\n      return ['Български', 'Английски', 'Руски', 'Немски', 'Френски', 'Испански', 'Италиански', 'Турски'];\n    }\n    getAstrologicalSigns() {\n      return ['Овен', 'Телец', 'Близнаци', 'Рак', 'Лъв', 'Дева', 'Везни', 'Скорпион', 'Стрелец', 'Козирог', 'Водолей', 'Риби'];\n    }\n    // Time format toggle\n    toggleTimeFormat() {\n      this.timeFormat24h = !this.timeFormat24h;\n    }\n    // Skill management methods\n    addSkill(event, input) {\n      event.preventDefault();\n      const value = input.value.trim();\n      if (value) {\n        const currentSkills = this.registerForm.get('skills')?.value || [];\n        if (!currentSkills.includes(value)) {\n          const updatedSkills = [...currentSkills, value];\n          this.registerForm.get('skills')?.setValue(updatedSkills);\n          input.value = '';\n        }\n      }\n    }\n    addSkillFromInput(input) {\n      const value = input.value.trim();\n      if (value) {\n        const currentSkills = this.registerForm.get('skills')?.value || [];\n        if (!currentSkills.includes(value)) {\n          const updatedSkills = [...currentSkills, value];\n          this.registerForm.get('skills')?.setValue(updatedSkills);\n          input.value = '';\n        }\n      }\n    }\n    removeSkill(index) {\n      const currentSkills = this.registerForm.get('skills')?.value || [];\n      currentSkills.splice(index, 1);\n      this.registerForm.get('skills')?.setValue(currentSkills);\n    }\n    passwordMatchValidator(control) {\n      const password = control.get('password');\n      const confirmPassword = control.get('confirmPassword');\n      if (password && confirmPassword && password.value !== confirmPassword.value) {\n        return {\n          'passwordMismatch': true\n        };\n      }\n      return null;\n    }\n    onSubmit() {\n      if (this.registrationType === 'oracle' && this.currentStep < this.totalSteps) {\n        // For oracle registration, move to next step if not on final step\n        this.nextStep();\n        return;\n      }\n      if (this.registerForm.valid) {\n        this.isLoading = true;\n        if (this.registrationType === 'general') {\n          this.submitGeneralRegistration();\n        } else {\n          this.submitOracleRegistration();\n        }\n      } else {\n        this.markFormGroupTouched();\n        this.snackBar.open('Моля, попълнете всички задължителни полета', 'Затвори', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    }\n    submitGeneralRegistration() {\n      const registerRequest = {\n        firstName: this.registerForm.value.firstName,\n        lastName: this.registerForm.value.lastName,\n        email: this.registerForm.value.email,\n        phoneNumber: this.registerForm.value.phoneNumber || undefined,\n        password: this.registerForm.value.password,\n        confirmPassword: this.registerForm.value.confirmPassword,\n        acceptTerms: this.registerForm.value.acceptTerms\n      };\n      this.authService.register(registerRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.success) {\n            this.snackBar.open('Регистрацията е успешна! Добре дошли в Оракул!', 'Затвори', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n            // Auto-authenticate and redirect to profile page\n            console.log('Registration successful, user is now authenticated');\n            console.log('User info:', response.user);\n            // Redirect to profile edit page for completing profile\n            this.router.navigate(['/profile/edit']);\n          } else {\n            this.snackBar.open(response.message || 'Регистрацията неуспешна', 'Затвори', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error || 'Регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    submitOracleRegistration() {\n      const formValue = this.registerForm.value;\n      const oracleRequest = {\n        // Basic information\n        firstName: formValue.firstName,\n        lastName: formValue.lastName,\n        email: formValue.email,\n        phoneNumber: formValue.phoneNumber || undefined,\n        password: formValue.password,\n        confirmPassword: formValue.confirmPassword,\n        acceptTerms: formValue.acceptTerms,\n        // Professional information\n        professionalTitle: formValue.professionalTitle,\n        headline: formValue.headline,\n        summary: formValue.summary,\n        primarySpecialization: formValue.primarySpecialization,\n        yearsOfExperience: formValue.yearsOfExperience,\n        // Location information\n        city: formValue.city,\n        state: formValue.state,\n        country: formValue.country,\n        displayLocation: formValue.displayLocation || `${formValue.city}, ${formValue.country}`,\n        // Oracle-specific information\n        birthDate: formValue.birthDate,\n        birthTime: formValue.birthTime,\n        birthLocation: formValue.birthLocation,\n        astrologicalSign: formValue.astrologicalSign,\n        oracleTypes: formValue.oracleTypes,\n        languagesSpoken: formValue.languagesSpoken,\n        skills: formValue.skills,\n        // Contact & business information\n        website: formValue.website,\n        portfolioUrl: formValue.portfolioUrl,\n        businessAddress: {\n          street: formValue.businessStreet,\n          city: formValue.businessCity,\n          state: formValue.businessState,\n          postalCode: formValue.businessPostalCode,\n          country: formValue.businessCountry,\n          isPublic: formValue.isBusinessAddressPublic\n        },\n        // Consultation rates\n        consultationRates: {\n          hourlyRate: formValue.hourlyRate,\n          sessionRate: formValue.sessionRate,\n          currency: formValue.currency\n        }\n      };\n      // TODO: Implement oracle registration API call\n      // For now, use the general registration and then create profile\n      this.authService.register(oracleRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.success) {\n            this.snackBar.open('Регистрацията като оракул е успешна! Добре дошли в Оракул!', 'Затвори', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n            // Auto-authenticate and redirect to profile page\n            console.log('Oracle registration successful, user is now authenticated');\n            console.log('User info:', response.user);\n            // Redirect to profile edit page for completing/reviewing profile\n            this.router.navigate(['/profile/edit']);\n          } else {\n            this.snackBar.open(response.message || 'Регистрацията неуспешна', 'Затвори', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error || 'Регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    markFormGroupTouched() {\n      Object.keys(this.registerForm.controls).forEach(key => {\n        const control = this.registerForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getFirstNameErrorMessage() {\n      const control = this.registerForm.get('firstName');\n      if (control?.hasError('required')) {\n        return 'Първото име е задължително';\n      }\n      if (control?.hasError('minlength')) {\n        return 'Първото име трябва да бъде поне 2 символа';\n      }\n      return '';\n    }\n    getLastNameErrorMessage() {\n      const control = this.registerForm.get('lastName');\n      if (control?.hasError('required')) {\n        return 'Фамилното име е задължително';\n      }\n      if (control?.hasError('minlength')) {\n        return 'Фамилното име трябва да бъде поне 2 символа';\n      }\n      return '';\n    }\n    getEmailErrorMessage() {\n      const control = this.registerForm.get('email');\n      if (control?.hasError('required')) {\n        return 'Имейлът е задължителен';\n      }\n      if (control?.hasError('email')) {\n        return 'Моля, въведете валиден имейл адрес';\n      }\n      return '';\n    }\n    getPasswordErrorMessage() {\n      const control = this.registerForm.get('password');\n      if (control?.hasError('required')) {\n        return 'Паролата е задължителна';\n      }\n      if (control?.hasError('minlength')) {\n        return 'Паролата трябва да бъде поне 6 символа';\n      }\n      return '';\n    }\n    getConfirmPasswordErrorMessage() {\n      const control = this.registerForm.get('confirmPassword');\n      if (control?.hasError('required')) {\n        return 'Моля, потвърдете паролата си';\n      }\n      if (this.registerForm.hasError('passwordMismatch')) {\n        return 'Паролите не съвпадат';\n      }\n      return '';\n    }\n    // Additional error message methods for oracle fields\n    getFieldErrorMessage(fieldName) {\n      const control = this.registerForm.get(fieldName);\n      if (!control) return '';\n      if (control.hasError('required')) {\n        return 'Това поле е задължително';\n      }\n      if (control.hasError('minlength')) {\n        return `Минимум ${control.errors?.['minlength'].requiredLength} символа`;\n      }\n      if (control.hasError('maxlength')) {\n        return `Максимум ${control.errors?.['maxlength'].requiredLength} символа`;\n      }\n      if (control.hasError('min')) {\n        return `Минимална стойност: ${control.errors?.['min'].min}`;\n      }\n      if (control.hasError('email')) {\n        return 'Моля, въведете валиден имейл адрес';\n      }\n      return '';\n    }\n    navigateToLogin() {\n      this.router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: this.returnUrl\n        }\n      });\n    }\n    signUpWithGoogle() {\n      this.isLoading = true;\n      this.oauthService.signInWithGooglePopup().subscribe({\n        next: oauthUser => {\n          this.handleOAuthSignUp(oauthUser);\n        },\n        error: () => {\n          this.isLoading = false;\n          this.snackBar.open('Регистрацията с Google неуспешна. Моля, опитайте отново.', 'Затвори', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    signUpWithFacebook() {\n      this.isLoading = true;\n      this.oauthService.signInWithFacebook().subscribe({\n        next: oauthUser => {\n          this.handleOAuthSignUp(oauthUser);\n        },\n        error: () => {\n          this.isLoading = false;\n          this.snackBar.open('Регистрацията с Facebook неуспешна. Моля, опитайте отново.', 'Затвори', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    handleOAuthSignUp(oauthUser) {\n      const oauthRequest = {\n        provider: oauthUser.provider,\n        accessToken: oauthUser.accessToken,\n        email: oauthUser.email,\n        firstName: oauthUser.firstName,\n        lastName: oauthUser.lastName,\n        profilePictureUrl: oauthUser.profilePictureUrl\n      };\n      this.authService.loginWithOAuth(oauthRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.success) {\n            const providerName = oauthUser.provider === 'google' ? 'Google' : 'Facebook';\n            this.snackBar.open(`Добре дошли в Оракул! Акаунтът е създаден с ${providerName}`, 'Затвори', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n            // Auto-authenticate and redirect to profile page\n            console.log(`OAuth registration successful with ${providerName}, user is now authenticated`);\n            console.log('User info:', response.user);\n            // Redirect to profile edit page for completing profile\n            this.router.navigate(['/profile/edit']);\n          } else {\n            this.snackBar.open(response.message || 'OAuth регистрацията неуспешна', 'Затвори', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.snackBar.open(error || 'OAuth регистрацията неуспешна. Моля, опитайте отново.', 'Затвори', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    static {\n      this.ɵfac = function RegisterComponent_Factory(t) {\n        return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RegisterComponent,\n        selectors: [[\"app-register\"]],\n        decls: 22,\n        vars: 5,\n        consts: [[1, \"register-container\"], [1, \"register-card\"], [1, \"register-header\"], [1, \"register-icon\"], [4, \"ngIf\"], [\"class\", \"registration-type-selection\", 4, \"ngIf\"], [\"class\", \"register-form\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"register-actions\"], [1, \"login-section\"], [1, \"login-text\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"full-width\", 3, \"click\"], [\"mode\", \"determinate\", 1, \"step-progress\", 3, \"value\"], [1, \"registration-type-selection\"], [1, \"type-buttons-compact\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"type-button-compact\", \"general-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"type-button-compact\", \"oracle-button\", 3, \"click\"], [1, \"type-description\"], [1, \"register-form\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"form-step\", 4, \"ngIf\"], [\"class\", \"navigation-buttons\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", \"class\", \"full-width register-button\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"divider-container\", 4, \"ngIf\"], [\"class\", \"oauth-buttons\", 4, \"ngIf\"], [1, \"form-step\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u043F\\u044A\\u0440\\u0432\\u043E \\u0438\\u043C\\u0435\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0444\\u0430\\u043C\\u0438\\u043B\\u043D\\u043E \\u0438\\u043C\\u0435\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0438\\u044F \\u0438\\u043C\\u0435\\u0439\\u043B\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0435\\u043D \\u043D\\u043E\\u043C\\u0435\\u0440\", \"autocomplete\", \"tel\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0439\\u0442\\u0435 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"\\u041F\\u043E\\u0442\\u0432\\u044A\\u0440\\u0434\\u0435\\u0442\\u0435 \\u043F\\u0430\\u0440\\u043E\\u043B\\u0430\\u0442\\u0430\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"class\", \"terms-section\", 4, \"ngIf\"], [1, \"terms-section\"], [\"formControlName\", \"acceptTerms\", \"color\", \"primary\"], [\"href\", \"#\", \"target\", \"_blank\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"professionalTitle\", \"placeholder\", \"\\u043D\\u0430\\u043F\\u0440. \\u0410\\u0441\\u0442\\u0440\\u043E\\u043B\\u043E\\u0433, \\u0422\\u0430\\u0440\\u043E \\u0447\\u0438\\u0442\\u0435\\u0446, \\u041A\\u0440\\u0438\\u0441\\u0442\\u0430\\u043B\\u0435\\u043D \\u043B\\u0435\\u0447\\u0438\\u0442\\u0435\\u043B\"], [\"formControlName\", \"primarySpecialization\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"headline\", \"placeholder\", \"\\u041A\\u0440\\u0430\\u0442\\u043A\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0432\\u0430\\u0448\\u0438\\u0442\\u0435 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0438\", \"maxlength\", \"220\"], [\"matInput\", \"\", \"formControlName\", \"summary\", \"placeholder\", \"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u043E \\u043E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0432\\u0430\\u0448\\u0438\\u044F \\u043E\\u043F\\u0438\\u0442 \\u0438 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0438\", \"rows\", \"4\", \"maxlength\", \"2000\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"yearsOfExperience\", \"placeholder\", \"0\", \"min\", \"0\", \"max\", \"50\"], [3, \"value\"], [1, \"location-row\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"city\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0433\\u0440\\u0430\\u0434\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"state\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u043E\\u0431\\u043B\\u0430\\u0441\\u0442\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"country\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0434\\u044A\\u0440\\u0436\\u0430\\u0432\\u0430\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"displayLocation\", \"placeholder\", \"\\u041A\\u0430\\u043A \\u0434\\u0430 \\u0441\\u0435 \\u043F\\u043E\\u043A\\u0430\\u0437\\u0432\\u0430 \\u043C\\u0435\\u0441\\u0442\\u043E\\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435\\u0442\\u043E \\u0432\\u0438 \\u043F\\u0443\\u0431\\u043B\\u0438\\u0447\\u043D\\u043E\"], [1, \"birth-info-row\"], [\"matInput\", \"\", \"formControlName\", \"birthDate\", \"placeholder\", \"\\u0434\\u0434.\\u043C\\u043C.\\u0433\\u0433\\u0433\\u0433\", \"type\", \"text\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"matDatepickerToggleIcon\", \"\"], [\"birthDatePicker\", \"\"], [1, \"time-input-container\"], [\"matInput\", \"\", \"type\", \"time\", \"formControlName\", \"birthTime\", 3, \"step\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matSuffix\", \"\", 3, \"title\", \"click\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"birthLocation\", \"placeholder\", \"\\u0413\\u0440\\u0430\\u0434 \\u0438 \\u0434\\u044A\\u0440\\u0436\\u0430\\u0432\\u0430 \\u043D\\u0430 \\u0440\\u0430\\u0436\\u0434\\u0430\\u043D\\u0435\"], [\"formControlName\", \"astrologicalSign\"], [\"formControlName\", \"oracleTypes\", \"multiple\", \"\"], [\"formControlName\", \"languagesSpoken\", \"multiple\", \"\"], [1, \"skills-section\"], [\"class\", \"skills-chips\", 4, \"ngIf\"], [\"matInput\", \"\", \"placeholder\", \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0443\\u043C\\u0435\\u043D\\u0438\\u0435 \\u0438 \\u043D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 Enter \\u0438\\u043B\\u0438 \\u043A\\u043B\\u0438\\u043A\\u043D\\u0435\\u0442\\u0435 +\", 3, \"keydown.enter\"], [\"skillInput\", \"\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matSuffix\", \"\", \"title\", \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438 \\u0443\\u043C\\u0435\\u043D\\u0438\\u0435\", 3, \"disabled\", \"click\"], [1, \"skills-chips\"], [\"removable\", \"true\", 3, \"removed\", 4, \"ngFor\", \"ngForOf\"], [\"removable\", \"true\", 3, \"removed\"], [\"matChipRemove\", \"\"], [\"matInput\", \"\", \"type\", \"url\", \"formControlName\", \"website\", \"placeholder\", \"https://\\u0432\\u0430\\u0448\\u0438\\u044F-\\u0441\\u0430\\u0439\\u0442.com\"], [\"matInput\", \"\", \"type\", \"url\", \"formControlName\", \"portfolioUrl\", \"placeholder\", \"https://\\u043F\\u043E\\u0440\\u0442\\u0444\\u043E\\u043B\\u0438\\u043E.com\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"businessStreet\", \"placeholder\", \"\\u0423\\u043B\\u0438\\u0446\\u0430 \\u0438 \\u043D\\u043E\\u043C\\u0435\\u0440\"], [1, \"business-address-row\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"businessCity\", \"placeholder\", \"\\u0413\\u0440\\u0430\\u0434\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"businessPostalCode\", \"placeholder\", \"\\u041F\\u043E\\u0449\\u0435\\u043D\\u0441\\u043A\\u0438 \\u043A\\u043E\\u0434\"], [\"formControlName\", \"isBusinessAddressPublic\", \"color\", \"primary\"], [1, \"rates-row\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"hourlyRate\", \"placeholder\", \"0\", \"min\", \"0\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"sessionRate\", \"placeholder\", \"0\", \"min\", \"0\"], [\"formControlName\", \"currency\"], [\"value\", \"BGN\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [1, \"navigation-buttons\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"spacer\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"register-button\", 3, \"disabled\"], [1, \"divider-container\"], [1, \"divider-text\"], [1, \"oauth-buttons\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"google-button\", 3, \"disabled\", \"click\"], [\"src\", \"https://developers.google.com/identity/images/g-logo.png\", \"alt\", \"Google\", 1, \"oauth-icon\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"full-width\", \"oauth-button\", \"facebook-button\", 3, \"disabled\", \"click\"], [1, \"oauth-icon\", \"facebook-icon\"]],\n        template: function RegisterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\", 2)(3, \"mat-card-title\")(4, \"mat-icon\", 3);\n            i0.ɵɵtext(5, \"person_add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n            i0.ɵɵtemplate(8, RegisterComponent_span_8_Template, 2, 0, \"span\", 4);\n            i0.ɵɵtemplate(9, RegisterComponent_span_9_Template, 3, 3, \"span\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"mat-card-content\");\n            i0.ɵɵtemplate(11, RegisterComponent_div_11_Template, 17, 6, \"div\", 5);\n            i0.ɵɵtemplate(12, RegisterComponent_form_12_Template, 11, 11, \"form\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-actions\", 7);\n            i0.ɵɵelement(14, \"mat-divider\");\n            i0.ɵɵelementStart(15, \"div\", 8)(16, \"p\", 9);\n            i0.ɵɵtext(17, \"\\u0412\\u0435\\u0447\\u0435 \\u0438\\u043C\\u0430\\u0442\\u0435 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_18_listener() {\n              return ctx.navigateToLogin();\n            });\n            i0.ɵɵelementStart(19, \"mat-icon\");\n            i0.ɵɵtext(20, \"login\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(21, \" \\u0412\\u043B\\u0435\\u0437\\u0442\\u0435 \\u0432 \\u0430\\u043A\\u0430\\u0443\\u043D\\u0442\\u0430 \\u0441\\u0438 \");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getStepTitle(), \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.registrationType === \"general\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isOracleRegistration());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isOracleRegistration() || ctx.registrationType === \"general\");\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatIcon, i10.MatProgressSpinner, i11.MatProgressBar, i12.MatFormField, i12.MatLabel, i12.MatHint, i12.MatError, i12.MatSuffix, i13.MatInput, i14.MatSelect, i15.MatOption, i16.MatCheckbox, i17.MatDivider, i18.MatChip, i18.MatChipRemove, i18.MatChipSet, i19.MatDatepicker, i19.MatDatepickerInput, i19.MatDatepickerToggle, i19.MatDatepickerToggleIcon],\n        styles: [\".register-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;padding:20px;background:var(--theme-gradient-auth)}.register-card[_ngcontent-%COMP%]{width:100%;max-width:500px;padding:0;box-shadow:0 8px 32px #0000001a;border-radius:16px;overflow:hidden}.register-header[_ngcontent-%COMP%]{background:var(--theme-gradient-auth);color:#fff;padding:24px;text-align:center}.register-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;margin-bottom:8px;font-size:24px;font-weight:500}.register-icon[_ngcontent-%COMP%]{font-size:28px;width:28px;height:28px}.register-header[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%]{color:#fffc;font-size:14px;margin:0}.register-form[_ngcontent-%COMP%]{padding:24px;display:flex;flex-direction:column;gap:16px}.name-row[_ngcontent-%COMP%]{display:flex;gap:16px}.full-width[_ngcontent-%COMP%]{width:100%}.half-width[_ngcontent-%COMP%]{flex:1}.terms-section[_ngcontent-%COMP%]{margin:8px 0}.terms-section[_ngcontent-%COMP%]   .mat-checkbox[_ngcontent-%COMP%]{font-size:14px}.terms-section[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#673ab7;text-decoration:none}.terms-section[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.register-button[_ngcontent-%COMP%]{height:48px;font-size:16px;font-weight:500;margin-top:8px}.register-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{margin-right:8px}.register-actions[_ngcontent-%COMP%]{padding:0 24px 24px;display:flex;flex-direction:column;gap:16px}.login-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.login-text[_ngcontent-%COMP%]{margin:0;color:#0009;font-size:14px}.mat-form-field[_ngcontent-%COMP%]{margin-bottom:8px}.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%]{border-radius:8px}.mat-error[_ngcontent-%COMP%]{font-size:12px;margin-top:4px}.mat-spinner[_ngcontent-%COMP%]{margin-right:8px}.divider-container[_ngcontent-%COMP%]{display:flex;align-items:center;margin:24px 0 16px;gap:16px}.divider-text[_ngcontent-%COMP%]{color:#0009;font-size:14px;white-space:nowrap}.oauth-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.oauth-button[_ngcontent-%COMP%]{height:48px;font-size:14px;font-weight:500;display:flex;align-items:center;justify-content:center;gap:12px;border-radius:8px;transition:all .3s ease}.oauth-icon[_ngcontent-%COMP%]{width:20px;height:20px}.google-button[_ngcontent-%COMP%]{border-color:#dadce0;color:#3c4043;background-color:#fff}.google-button[_ngcontent-%COMP%]:hover:not([disabled]){background-color:#f8f9fa;box-shadow:0 1px 3px #0000001a}.facebook-button[_ngcontent-%COMP%]{border-color:#1877f2;color:#1877f2;background-color:#fff}.facebook-button[_ngcontent-%COMP%]:hover:not([disabled]){background-color:#f0f2f5}.facebook-icon[_ngcontent-%COMP%]{color:#1877f2;font-size:20px}@media (max-width: 600px){.register-container[_ngcontent-%COMP%]{padding:16px}.register-card[_ngcontent-%COMP%]{max-width:100%}.register-form[_ngcontent-%COMP%]{padding:20px}.register-actions[_ngcontent-%COMP%]{padding:0 20px 20px}.name-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.half-width[_ngcontent-%COMP%]{width:100%}}.registration-type-selection[_ngcontent-%COMP%]{padding:24px;text-align:center}.registration-type-selection[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:16px;color:var(--theme-text-primary);font-weight:500}.type-buttons-compact[_ngcontent-%COMP%]{display:flex;gap:12px;justify-content:center;margin-bottom:12px}.type-button-compact[_ngcontent-%COMP%]{height:40px;padding:0 16px;display:flex;align-items:center;gap:8px;border-radius:20px;transition:all .3s ease;font-size:14px;font-weight:500;min-width:140px}.type-button-compact.selected[_ngcontent-%COMP%]{background-color:var(--theme-primary);color:#fff;border-color:var(--theme-primary)}.type-button-compact[_ngcontent-%COMP%]:not(.selected){border-color:var(--theme-primary);color:var(--theme-primary);background-color:transparent}.type-button-compact[_ngcontent-%COMP%]:not(.selected):hover{background-color:var(--theme-primary-light)}.type-button-compact[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.type-description[_ngcontent-%COMP%]{margin:0;font-size:13px;color:var(--theme-text-secondary);opacity:.8;min-height:20px}.type-buttons[_ngcontent-%COMP%]{display:flex;gap:16px;justify-content:center}.type-button[_ngcontent-%COMP%]{flex:1;max-width:200px;height:auto;padding:20px 16px;display:flex;flex-direction:column;align-items:center;gap:12px;border-radius:12px;transition:all .3s ease;border:2px solid transparent}.type-button.selected[_ngcontent-%COMP%]{border-color:var(--theme-primary);background-color:var(--theme-primary-light)}.type-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:32px;width:32px;height:32px}.button-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;font-size:16px;font-weight:500}.button-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:12px;opacity:.8;text-align:center;line-height:1.3}.step-progress[_ngcontent-%COMP%]{margin-top:8px;height:4px;border-radius:2px}.form-step[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.form-step[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 16px;color:var(--theme-text-primary);font-weight:500;font-size:18px}.form-step[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:16px 0 8px;color:var(--theme-text-secondary);font-weight:500;font-size:14px}.location-row[_ngcontent-%COMP%], .birth-info-row[_ngcontent-%COMP%], .business-address-row[_ngcontent-%COMP%], .rates-row[_ngcontent-%COMP%]{display:flex;gap:16px}.time-input-container[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%}.time-input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1}.time-input-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:8px}.navigation-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;margin-top:24px;padding-top:16px;border-top:1px solid var(--theme-divider)}.spacer[_ngcontent-%COMP%]{flex:1}.skills-section[_ngcontent-%COMP%]{margin:16px 0}.skills-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:var(--theme-text-primary);font-weight:500;font-size:14px}.skills-chips[_ngcontent-%COMP%]{margin-bottom:12px}.mat-chip-set[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px}.mat-chip[_ngcontent-%COMP%]{background-color:var(--theme-primary-light);color:var(--theme-primary);border-radius:16px;font-size:13px}.mat-chip[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}  .success-snackbar{background-color:#4caf50;color:#fff}  .error-snackbar{background-color:#f44336;color:#fff}@media (max-width: 600px){.type-buttons-compact[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.type-button-compact[_ngcontent-%COMP%]{min-width:200px}.type-buttons[_ngcontent-%COMP%]{flex-direction:column}.type-button[_ngcontent-%COMP%]{max-width:100%}.location-row[_ngcontent-%COMP%], .birth-info-row[_ngcontent-%COMP%], .business-address-row[_ngcontent-%COMP%], .rates-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.navigation-buttons[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.spacer[_ngcontent-%COMP%]{display:none}}\"]\n      });\n    }\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}