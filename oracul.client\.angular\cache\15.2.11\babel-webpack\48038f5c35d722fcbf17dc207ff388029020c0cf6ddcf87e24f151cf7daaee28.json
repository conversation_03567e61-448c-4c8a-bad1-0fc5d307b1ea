{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Harmonia/oracul.client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { defaultModules, QUILL_CONFIG_TOKEN } from 'ngx-quill/config';\nexport * from 'ngx-quill/config';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, Inject, EventEmitter, SecurityContext, PLATFORM_ID, Directive, Input, Output, ElementRef, ChangeDetectorRef, Renderer2, NgZone, forwardRef, Component, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, isPlatformServer, CommonModule } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { defer, isObservable, firstValueFrom, Subscription, fromEvent } from 'rxjs';\nimport { shareReplay, mergeMap, debounceTime } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nfunction QuillEditorComponent_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nfunction QuillEditorComponent_ng_container_0_pre_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"pre\", 2);\n  }\n}\nfunction QuillEditorComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, QuillEditorComponent_ng_container_0_div_1_Template, 1, 0, \"div\", 1);\n    i0.ɵɵtemplate(2, QuillEditorComponent_ng_container_0_pre_2_Template, 1, 0, \"pre\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.preserve);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preserve);\n  }\n}\nfunction QuillEditorComponent_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nfunction QuillEditorComponent_ng_container_2_pre_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"pre\", 2);\n  }\n}\nfunction QuillEditorComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, QuillEditorComponent_ng_container_2_div_1_Template, 1, 0, \"div\", 1);\n    i0.ɵɵtemplate(2, QuillEditorComponent_ng_container_2_pre_2_Template, 1, 0, \"pre\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.preserve);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.preserve);\n  }\n}\nconst _c0 = [[[\"\", \"quill-editor-toolbar\", \"\"]]];\nconst _c1 = [\"[quill-editor-toolbar]\"];\nfunction QuillViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 1);\n  }\n}\nfunction QuillViewComponent_pre_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"pre\", 1);\n  }\n}\nconst getFormat = (format, configFormat) => {\n  const passedFormat = format || configFormat;\n  return passedFormat || 'html';\n};\nlet QuillService = /*#__PURE__*/(() => {\n  class QuillService {\n    constructor(injector, config) {\n      var _this = this;\n      this.config = config;\n      this.quill$ = defer( /*#__PURE__*/_asyncToGenerator(function* () {\n        if (!_this.Quill) {\n          // Quill adds events listeners on import https://github.com/quilljs/quill/blob/develop/core/emitter.js#L8\n          // We'd want to use the unpatched `addEventListener` method to have all event callbacks to be run outside of zone.\n          // We don't know yet if the `zone.js` is used or not, just save the value to restore it back further.\n          const maybePatchedAddEventListener = _this.document.addEventListener;\n          // There're 2 types of Angular applications:\n          // 1) zone-full (by default)\n          // 2) zone-less\n          // The developer can avoid importing the `zone.js` package and tells Angular that he/she is responsible for running\n          // the change detection by himself. This is done by \"nooping\" the zone through `CompilerOptions` when bootstrapping\n          // the root module. We fallback to `document.addEventListener` if `__zone_symbol__addEventListener` is not defined,\n          // this means the `zone.js` is not imported.\n          // The `__zone_symbol__addEventListener` is basically a native DOM API, which is not patched by zone.js, thus not even going\n          // through the `zone.js` task lifecycle. You can also access the native DOM API as follows `target[Zone.__symbol__('methodName')]`.\n          // eslint-disable-next-line @typescript-eslint/dot-notation\n          _this.document.addEventListener = _this.document['__zone_symbol__addEventListener'] || _this.document.addEventListener;\n          const quillImport = yield import('quill');\n          _this.document.addEventListener = maybePatchedAddEventListener;\n          _this.Quill = quillImport.default ? quillImport.default : quillImport;\n        }\n        // Only register custom options and modules once\n        _this.config.customOptions?.forEach(customOption => {\n          const newCustomOption = _this.Quill.import(customOption.import);\n          newCustomOption.whitelist = customOption.whitelist;\n          _this.Quill.register(newCustomOption, true, _this.config.suppressGlobalRegisterWarning);\n        });\n        return yield _this.registerCustomModules(_this.Quill, _this.config.customModules, _this.config.suppressGlobalRegisterWarning);\n      })).pipe(shareReplay({\n        bufferSize: 1,\n        refCount: true\n      }));\n      this.document = injector.get(DOCUMENT);\n      if (!this.config) {\n        this.config = {\n          modules: defaultModules\n        };\n      }\n    }\n    getQuill() {\n      return this.quill$;\n    }\n    /**\n     * Marked as internal so it won't be available for `ngx-quill` consumers, this is only\n     * internal method to be used within the library.\n     *\n     * @internal\n     */\n    registerCustomModules(Quill, customModules, suppressGlobalRegisterWarning) {\n      return _asyncToGenerator(function* () {\n        if (Array.isArray(customModules)) {\n          // eslint-disable-next-line prefer-const\n          for (let {\n            implementation,\n            path\n          } of customModules) {\n            // The `implementation` might be an observable that resolves the actual implementation,\n            // e.g. if it should be lazy loaded.\n            if (isObservable(implementation)) {\n              implementation = yield firstValueFrom(implementation);\n            }\n            Quill.register(path, implementation, suppressGlobalRegisterWarning);\n          }\n        }\n        // Return `Quill` constructor so we'll be able to re-use its return value except of using\n        // `map` operators, etc.\n        return Quill;\n      })();\n    }\n  }\n  QuillService.ɵfac = function QuillService_Factory(t) {\n    return new (t || QuillService)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(QUILL_CONFIG_TOKEN, 8));\n  };\n  QuillService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: QuillService,\n    factory: QuillService.ɵfac,\n    providedIn: 'root'\n  });\n  return QuillService;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nlet QuillEditorBase = /*#__PURE__*/(() => {\n  class QuillEditorBase {\n    constructor(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service) {\n      this.elementRef = elementRef;\n      this.cd = cd;\n      this.domSanitizer = domSanitizer;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.zone = zone;\n      this.service = service;\n      this.required = false;\n      this.customToolbarPosition = 'top';\n      this.styles = null;\n      this.strict = true;\n      this.customOptions = [];\n      this.customModules = [];\n      this.preserveWhitespace = false;\n      this.trimOnValidation = false;\n      this.compareValues = false;\n      this.filterNull = false;\n      /*\n      https://github.com/KillerCodeMonkey/ngx-quill/issues/1257 - fix null value set\n             provide default empty value\n      by default null\n             e.g. defaultEmptyValue=\"\" - empty string\n             <quill-editor\n        defaultEmptyValue=\"\"\n        formControlName=\"message\"\n      ></quill-editor>\n      */\n      this.defaultEmptyValue = null;\n      this.onEditorCreated = new EventEmitter();\n      this.onEditorChanged = new EventEmitter();\n      this.onContentChanged = new EventEmitter();\n      this.onSelectionChanged = new EventEmitter();\n      this.onFocus = new EventEmitter();\n      this.onBlur = new EventEmitter();\n      this.disabled = false; // used to store initial value before ViewInit\n      this.preserve = false;\n      this.toolbarPosition = 'top';\n      this.subscription = null;\n      this.quillSubscription = null;\n      this.valueGetter = (quillEditor, editorElement) => {\n        let html = editorElement.querySelector('.ql-editor').innerHTML;\n        if (html === '<p><br></p>' || html === '<div><br></div>') {\n          html = this.defaultEmptyValue;\n        }\n        let modelValue = html;\n        const format = getFormat(this.format, this.service.config.format);\n        if (format === 'text') {\n          modelValue = quillEditor.getText();\n        } else if (format === 'object') {\n          modelValue = quillEditor.getContents();\n        } else if (format === 'json') {\n          try {\n            modelValue = JSON.stringify(quillEditor.getContents());\n          } catch (e) {\n            modelValue = quillEditor.getText();\n          }\n        }\n        return modelValue;\n      };\n      this.valueSetter = (quillEditor, value) => {\n        const format = getFormat(this.format, this.service.config.format);\n        if (format === 'html') {\n          const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : this.service.config.sanitize || false;\n          if (sanitize) {\n            value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n          }\n          return quillEditor.clipboard.convert(value);\n        } else if (format === 'json') {\n          try {\n            return JSON.parse(value);\n          } catch (e) {\n            return [{\n              insert: value\n            }];\n          }\n        }\n        return value;\n      };\n      this.selectionChangeHandler = (range, oldRange, source) => {\n        const shouldTriggerOnModelTouched = !range && !!this.onModelTouched;\n        // only emit changes when there's any listener\n        if (!this.onBlur.observed && !this.onFocus.observed && !this.onSelectionChanged.observed && !shouldTriggerOnModelTouched) {\n          return;\n        }\n        this.zone.run(() => {\n          if (range === null) {\n            this.onBlur.emit({\n              editor: this.quillEditor,\n              source\n            });\n          } else if (oldRange === null) {\n            this.onFocus.emit({\n              editor: this.quillEditor,\n              source\n            });\n          }\n          this.onSelectionChanged.emit({\n            editor: this.quillEditor,\n            oldRange,\n            range,\n            source\n          });\n          if (shouldTriggerOnModelTouched) {\n            this.onModelTouched();\n          }\n          this.cd.markForCheck();\n        });\n      };\n      this.textChangeHandler = (delta, oldDelta, source) => {\n        // only emit changes emitted by user interactions\n        const text = this.quillEditor.getText();\n        const content = this.quillEditor.getContents();\n        let html = this.editorElem.querySelector('.ql-editor').innerHTML;\n        if (html === '<p><br></p>' || html === '<div><br></div>') {\n          html = this.defaultEmptyValue;\n        }\n        const trackChanges = this.trackChanges || this.service.config.trackChanges;\n        const shouldTriggerOnModelChange = (source === 'user' || trackChanges && trackChanges === 'all') && !!this.onModelChange;\n        // only emit changes when there's any listener\n        if (!this.onContentChanged.observed && !shouldTriggerOnModelChange) {\n          return;\n        }\n        this.zone.run(() => {\n          if (shouldTriggerOnModelChange) {\n            this.onModelChange(this.valueGetter(this.quillEditor, this.editorElem));\n          }\n          this.onContentChanged.emit({\n            content,\n            delta,\n            editor: this.quillEditor,\n            html,\n            oldDelta,\n            source,\n            text\n          });\n          this.cd.markForCheck();\n        });\n      };\n      // eslint-disable-next-line max-len\n      this.editorChangeHandler = (event, current, old, source) => {\n        // only emit changes when there's any listener\n        if (!this.onEditorChanged.observed) {\n          return;\n        }\n        // only emit changes emitted by user interactions\n        if (event === 'text-change') {\n          const text = this.quillEditor.getText();\n          const content = this.quillEditor.getContents();\n          let html = this.editorElem.querySelector('.ql-editor').innerHTML;\n          if (html === '<p><br></p>' || html === '<div><br></div>') {\n            html = this.defaultEmptyValue;\n          }\n          this.zone.run(() => {\n            this.onEditorChanged.emit({\n              content,\n              delta: current,\n              editor: this.quillEditor,\n              event,\n              html,\n              oldDelta: old,\n              source,\n              text\n            });\n            this.cd.markForCheck();\n          });\n        } else {\n          this.zone.run(() => {\n            this.onEditorChanged.emit({\n              editor: this.quillEditor,\n              event,\n              oldRange: old,\n              range: current,\n              source\n            });\n            this.cd.markForCheck();\n          });\n        }\n      };\n      this.document = injector.get(DOCUMENT);\n    }\n    static normalizeClassNames(classes) {\n      const classList = classes.trim().split(' ');\n      return classList.reduce((prev, cur) => {\n        const trimmed = cur.trim();\n        if (trimmed) {\n          prev.push(trimmed);\n        }\n        return prev;\n      }, []);\n    }\n    ngOnInit() {\n      this.preserve = this.preserveWhitespace;\n      this.toolbarPosition = this.customToolbarPosition;\n    }\n    ngAfterViewInit() {\n      if (isPlatformServer(this.platformId)) {\n        return;\n      }\n      // The `quill-editor` component might be destroyed before the `quill` chunk is loaded and its code is executed\n      // this will lead to runtime exceptions, since the code will be executed on DOM nodes that don't exist within the tree.\n      this.quillSubscription = this.service.getQuill().pipe(mergeMap(Quill => {\n        const promises = [this.service.registerCustomModules(Quill, this.customModules)];\n        const beforeRender = this.beforeRender ?? this.service.config.beforeRender;\n        if (beforeRender) {\n          promises.push(beforeRender());\n        }\n        return Promise.all(promises).then(() => Quill);\n      })).subscribe(Quill => {\n        this.editorElem = this.elementRef.nativeElement.querySelector('[quill-editor-element]');\n        const toolbarElem = this.elementRef.nativeElement.querySelector('[quill-editor-toolbar]');\n        const modules = Object.assign({}, this.modules || this.service.config.modules);\n        if (toolbarElem) {\n          modules.toolbar = toolbarElem;\n        } else if (modules.toolbar === undefined) {\n          modules.toolbar = defaultModules.toolbar;\n        }\n        let placeholder = this.placeholder !== undefined ? this.placeholder : this.service.config.placeholder;\n        if (placeholder === undefined) {\n          placeholder = 'Insert text here ...';\n        }\n        if (this.styles) {\n          Object.keys(this.styles).forEach(key => {\n            this.renderer.setStyle(this.editorElem, key, this.styles[key]);\n          });\n        }\n        if (this.classes) {\n          this.addClasses(this.classes);\n        }\n        this.customOptions.forEach(customOption => {\n          const newCustomOption = Quill.import(customOption.import);\n          newCustomOption.whitelist = customOption.whitelist;\n          Quill.register(newCustomOption, true);\n        });\n        let bounds = this.bounds && this.bounds === 'self' ? this.editorElem : this.bounds;\n        if (!bounds) {\n          bounds = this.service.config.bounds ? this.service.config.bounds : this.document.body;\n        }\n        let debug = this.debug;\n        if (!debug && debug !== false && this.service.config.debug) {\n          debug = this.service.config.debug;\n        }\n        let readOnly = this.readOnly;\n        if (!readOnly && this.readOnly !== false) {\n          readOnly = this.service.config.readOnly !== undefined ? this.service.config.readOnly : false;\n        }\n        let defaultEmptyValue = this.defaultEmptyValue;\n        if (this.service.config.hasOwnProperty('defaultEmptyValue')) {\n          defaultEmptyValue = this.service.config.defaultEmptyValue;\n        }\n        let scrollingContainer = this.scrollingContainer;\n        if (!scrollingContainer && this.scrollingContainer !== null) {\n          scrollingContainer = this.service.config.scrollingContainer === null || this.service.config.scrollingContainer ? this.service.config.scrollingContainer : null;\n        }\n        let formats = this.formats;\n        if (!formats && formats === undefined) {\n          formats = this.service.config.formats ? [...this.service.config.formats] : this.service.config.formats === null ? null : undefined;\n        }\n        this.zone.runOutsideAngular(() => {\n          this.quillEditor = new Quill(this.editorElem, {\n            bounds,\n            debug: debug,\n            formats: formats,\n            modules,\n            placeholder,\n            readOnly,\n            defaultEmptyValue,\n            scrollingContainer: scrollingContainer,\n            strict: this.strict,\n            theme: this.theme || (this.service.config.theme ? this.service.config.theme : 'snow')\n          });\n          // Set optional link placeholder, Quill has no native API for it so using workaround\n          if (this.linkPlaceholder) {\n            const tooltip = this.quillEditor?.theme?.tooltip;\n            const input = tooltip?.root?.querySelector('input[data-link]');\n            if (input?.dataset) {\n              input.dataset.link = this.linkPlaceholder;\n            }\n          }\n        });\n        if (this.content) {\n          const format = getFormat(this.format, this.service.config.format);\n          if (format === 'text') {\n            this.quillEditor.setText(this.content, 'silent');\n          } else {\n            const newValue = this.valueSetter(this.quillEditor, this.content);\n            this.quillEditor.setContents(newValue, 'silent');\n          }\n          this.quillEditor.getModule('history').clear();\n        }\n        // initialize disabled status based on this.disabled as default value\n        this.setDisabledState();\n        this.addQuillEventListeners();\n        // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n        // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n        if (!this.onEditorCreated.observed && !this.onValidatorChanged) {\n          return;\n        }\n        // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n        // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n        // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n        requestAnimationFrame(() => {\n          if (this.onValidatorChanged) {\n            this.onValidatorChanged();\n          }\n          this.onEditorCreated.emit(this.quillEditor);\n          this.onEditorCreated.complete();\n        });\n      });\n    }\n    ngOnDestroy() {\n      this.dispose();\n      this.quillSubscription?.unsubscribe();\n      this.quillSubscription = null;\n    }\n    ngOnChanges(changes) {\n      if (!this.quillEditor) {\n        return;\n      }\n      /* eslint-disable @typescript-eslint/dot-notation */\n      if (changes.readOnly) {\n        this.quillEditor.enable(!changes.readOnly.currentValue);\n      }\n      if (changes.placeholder) {\n        this.quillEditor.root.dataset.placeholder = changes.placeholder.currentValue;\n      }\n      if (changes.defaultEmptyValue) {\n        this.quillEditor.root.dataset.defaultEmptyValue = changes.defaultEmptyValue.currentValue;\n      }\n      if (changes.styles) {\n        const currentStyling = changes.styles.currentValue;\n        const previousStyling = changes.styles.previousValue;\n        if (previousStyling) {\n          Object.keys(previousStyling).forEach(key => {\n            this.renderer.removeStyle(this.editorElem, key);\n          });\n        }\n        if (currentStyling) {\n          Object.keys(currentStyling).forEach(key => {\n            this.renderer.setStyle(this.editorElem, key, this.styles[key]);\n          });\n        }\n      }\n      if (changes.classes) {\n        const currentClasses = changes.classes.currentValue;\n        const previousClasses = changes.classes.previousValue;\n        if (previousClasses) {\n          this.removeClasses(previousClasses);\n        }\n        if (currentClasses) {\n          this.addClasses(currentClasses);\n        }\n      }\n      // We'd want to re-apply event listeners if the `debounceTime` binding changes to apply the\n      // `debounceTime` operator or vice-versa remove it.\n      if (changes.debounceTime) {\n        this.addQuillEventListeners();\n      }\n      /* eslint-enable @typescript-eslint/dot-notation */\n    }\n\n    addClasses(classList) {\n      QuillEditorBase.normalizeClassNames(classList).forEach(c => {\n        this.renderer.addClass(this.editorElem, c);\n      });\n    }\n    removeClasses(classList) {\n      QuillEditorBase.normalizeClassNames(classList).forEach(c => {\n        this.renderer.removeClass(this.editorElem, c);\n      });\n    }\n    writeValue(currentValue) {\n      // optional fix for https://github.com/angular/angular/issues/14988\n      if (this.filterNull && currentValue === null) {\n        return;\n      }\n      this.content = currentValue;\n      if (!this.quillEditor) {\n        return;\n      }\n      const format = getFormat(this.format, this.service.config.format);\n      const newValue = this.valueSetter(this.quillEditor, currentValue);\n      if (this.compareValues) {\n        const currentEditorValue = this.quillEditor.getContents();\n        if (JSON.stringify(currentEditorValue) === JSON.stringify(newValue)) {\n          return;\n        }\n      }\n      if (currentValue) {\n        if (format === 'text') {\n          this.quillEditor.setText(currentValue);\n        } else {\n          this.quillEditor.setContents(newValue);\n        }\n        return;\n      }\n      this.quillEditor.setText('');\n    }\n    setDisabledState(isDisabled = this.disabled) {\n      // store initial value to set appropriate disabled status after ViewInit\n      this.disabled = isDisabled;\n      if (this.quillEditor) {\n        if (isDisabled) {\n          this.quillEditor.disable();\n          this.renderer.setAttribute(this.elementRef.nativeElement, 'disabled', 'disabled');\n        } else {\n          if (!this.readOnly) {\n            this.quillEditor.enable();\n          }\n          this.renderer.removeAttribute(this.elementRef.nativeElement, 'disabled');\n        }\n      }\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    registerOnValidatorChange(fn) {\n      this.onValidatorChanged = fn;\n    }\n    validate() {\n      if (!this.quillEditor) {\n        return null;\n      }\n      const err = {};\n      let valid = true;\n      const text = this.quillEditor.getText();\n      // trim text if wanted + handle special case that an empty editor contains a new line\n      const textLength = this.trimOnValidation ? text.trim().length : text.length === 1 && text.trim().length === 0 ? 0 : text.length - 1;\n      const deltaOperations = this.quillEditor.getContents().ops;\n      const onlyEmptyOperation = deltaOperations && deltaOperations.length === 1 && ['\\n', ''].includes(deltaOperations[0].insert);\n      if (this.minLength && textLength && textLength < this.minLength) {\n        err.minLengthError = {\n          given: textLength,\n          minLength: this.minLength\n        };\n        valid = false;\n      }\n      if (this.maxLength && textLength > this.maxLength) {\n        err.maxLengthError = {\n          given: textLength,\n          maxLength: this.maxLength\n        };\n        valid = false;\n      }\n      if (this.required && !textLength && onlyEmptyOperation) {\n        err.requiredError = {\n          empty: true\n        };\n        valid = false;\n      }\n      return valid ? null : err;\n    }\n    addQuillEventListeners() {\n      this.dispose();\n      // We have to enter the `<root>` zone when adding event listeners, so `debounceTime` will spawn the\n      // `AsyncAction` there w/o triggering change detections. We still re-enter the Angular's zone through\n      // `zone.run` when we emit an event to the parent component.\n      this.zone.runOutsideAngular(() => {\n        this.subscription = new Subscription();\n        this.subscription.add(\n        // mark model as touched if editor lost focus\n        fromEvent(this.quillEditor, 'selection-change').subscribe(([range, oldRange, source]) => {\n          this.selectionChangeHandler(range, oldRange, source);\n        }));\n        // The `fromEvent` supports passing JQuery-style event targets, the editor has `on` and `off` methods which\n        // will be invoked upon subscription and teardown.\n        let textChange$ = fromEvent(this.quillEditor, 'text-change');\n        let editorChange$ = fromEvent(this.quillEditor, 'editor-change');\n        if (typeof this.debounceTime === 'number') {\n          textChange$ = textChange$.pipe(debounceTime(this.debounceTime));\n          editorChange$ = editorChange$.pipe(debounceTime(this.debounceTime));\n        }\n        this.subscription.add(\n        // update model if text changes\n        textChange$.subscribe(([delta, oldDelta, source]) => {\n          this.textChangeHandler(delta, oldDelta, source);\n        }));\n        this.subscription.add(\n        // triggered if selection or text changed\n        editorChange$.subscribe(([event, current, old, source]) => {\n          this.editorChangeHandler(event, current, old, source);\n        }));\n      });\n    }\n    dispose() {\n      if (this.subscription !== null) {\n        this.subscription.unsubscribe();\n        this.subscription = null;\n      }\n    }\n  }\n  QuillEditorBase.ɵfac = function QuillEditorBase_Factory(t) {\n    return new (t || QuillEditorBase)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(QuillService));\n  };\n  QuillEditorBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: QuillEditorBase,\n    inputs: {\n      format: \"format\",\n      theme: \"theme\",\n      modules: \"modules\",\n      debug: \"debug\",\n      readOnly: \"readOnly\",\n      placeholder: \"placeholder\",\n      maxLength: \"maxLength\",\n      minLength: \"minLength\",\n      required: \"required\",\n      formats: \"formats\",\n      customToolbarPosition: \"customToolbarPosition\",\n      sanitize: \"sanitize\",\n      beforeRender: \"beforeRender\",\n      styles: \"styles\",\n      strict: \"strict\",\n      scrollingContainer: \"scrollingContainer\",\n      bounds: \"bounds\",\n      customOptions: \"customOptions\",\n      customModules: \"customModules\",\n      trackChanges: \"trackChanges\",\n      preserveWhitespace: \"preserveWhitespace\",\n      classes: \"classes\",\n      trimOnValidation: \"trimOnValidation\",\n      linkPlaceholder: \"linkPlaceholder\",\n      compareValues: \"compareValues\",\n      filterNull: \"filterNull\",\n      debounceTime: \"debounceTime\",\n      defaultEmptyValue: \"defaultEmptyValue\",\n      valueGetter: \"valueGetter\",\n      valueSetter: \"valueSetter\"\n    },\n    outputs: {\n      onEditorCreated: \"onEditorCreated\",\n      onEditorChanged: \"onEditorChanged\",\n      onContentChanged: \"onContentChanged\",\n      onSelectionChanged: \"onSelectionChanged\",\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return QuillEditorBase;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet QuillEditorComponent = /*#__PURE__*/(() => {\n  class QuillEditorComponent extends QuillEditorBase {\n    constructor(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service) {\n      super(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service);\n    }\n  }\n  QuillEditorComponent.ɵfac = function QuillEditorComponent_Factory(t) {\n    return new (t || QuillEditorComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(ElementRef), i0.ɵɵdirectiveInject(ChangeDetectorRef), i0.ɵɵdirectiveInject(DomSanitizer), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(Renderer2), i0.ɵɵdirectiveInject(NgZone), i0.ɵɵdirectiveInject(QuillService));\n  };\n  QuillEditorComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: QuillEditorComponent,\n    selectors: [[\"quill-editor\"]],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([{\n      multi: true,\n      provide: NG_VALUE_ACCESSOR,\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      useExisting: forwardRef(() => QuillEditorComponent)\n    }, {\n      multi: true,\n      provide: NG_VALIDATORS,\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      useExisting: forwardRef(() => QuillEditorComponent)\n    }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c1,\n    decls: 3,\n    vars: 2,\n    consts: [[4, \"ngIf\"], [\"quill-editor-element\", \"\", 4, \"ngIf\"], [\"quill-editor-element\", \"\"]],\n    template: function QuillEditorComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵtemplate(0, QuillEditorComponent_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵtemplate(2, QuillEditorComponent_ng_container_2_Template, 3, 2, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.toolbarPosition !== \"top\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.toolbarPosition === \"top\");\n      }\n    },\n    dependencies: [CommonModule, i3.NgIf],\n    styles: [\"[_nghost-%COMP%]{display:inline-block}\"]\n  });\n  return QuillEditorComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet QuillViewHTMLComponent = /*#__PURE__*/(() => {\n  class QuillViewHTMLComponent {\n    constructor(sanitizer, service) {\n      this.sanitizer = sanitizer;\n      this.service = service;\n      this.content = '';\n      this.innerHTML = '';\n      this.themeClass = 'ql-snow';\n    }\n    ngOnChanges(changes) {\n      if (changes.theme) {\n        const theme = changes.theme.currentValue || (this.service.config.theme ? this.service.config.theme : 'snow');\n        this.themeClass = `ql-${theme} ngx-quill-view-html`;\n      } else if (!this.theme) {\n        const theme = this.service.config.theme ? this.service.config.theme : 'snow';\n        this.themeClass = `ql-${theme} ngx-quill-view-html`;\n      }\n      if (changes.content) {\n        const content = changes.content.currentValue;\n        const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : this.service.config.sanitize || false;\n        this.innerHTML = sanitize ? content : this.sanitizer.bypassSecurityTrustHtml(content);\n      }\n    }\n  }\n  QuillViewHTMLComponent.ɵfac = function QuillViewHTMLComponent_Factory(t) {\n    return new (t || QuillViewHTMLComponent)(i0.ɵɵdirectiveInject(DomSanitizer), i0.ɵɵdirectiveInject(QuillService));\n  };\n  QuillViewHTMLComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: QuillViewHTMLComponent,\n    selectors: [[\"quill-view-html\"]],\n    inputs: {\n      content: \"content\",\n      theme: \"theme\",\n      sanitize: \"sanitize\"\n    },\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n    decls: 2,\n    vars: 2,\n    consts: [[1, \"ql-container\", 3, \"ngClass\"], [1, \"ql-editor\", 3, \"innerHTML\"]],\n    template: function QuillViewHTMLComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"div\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.themeClass);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"innerHTML\", ctx.innerHTML, i0.ɵɵsanitizeHtml);\n      }\n    },\n    dependencies: [CommonModule, i3.NgClass],\n    styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"],\n    encapsulation: 2\n  });\n  return QuillViewHTMLComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet QuillViewComponent = /*#__PURE__*/(() => {\n  class QuillViewComponent {\n    constructor(elementRef, renderer, zone, service, domSanitizer, platformId) {\n      this.elementRef = elementRef;\n      this.renderer = renderer;\n      this.zone = zone;\n      this.service = service;\n      this.domSanitizer = domSanitizer;\n      this.platformId = platformId;\n      this.strict = true;\n      this.customModules = [];\n      this.customOptions = [];\n      this.preserveWhitespace = false;\n      this.onEditorCreated = new EventEmitter();\n      this.preserve = false;\n      this.quillSubscription = null;\n      this.valueSetter = (quillEditor, value) => {\n        const format = getFormat(this.format, this.service.config.format);\n        let content = value;\n        if (format === 'text') {\n          quillEditor.setText(content);\n        } else {\n          if (format === 'html') {\n            const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : this.service.config.sanitize || false;\n            if (sanitize) {\n              value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n            }\n            content = quillEditor.clipboard.convert(value);\n          } else if (format === 'json') {\n            try {\n              content = JSON.parse(value);\n            } catch (e) {\n              content = [{\n                insert: value\n              }];\n            }\n          }\n          quillEditor.setContents(content);\n        }\n      };\n    }\n    ngOnInit() {\n      this.preserve = this.preserveWhitespace;\n    }\n    ngOnChanges(changes) {\n      if (!this.quillEditor) {\n        return;\n      }\n      if (changes.content) {\n        this.valueSetter(this.quillEditor, changes.content.currentValue);\n      }\n    }\n    ngAfterViewInit() {\n      if (isPlatformServer(this.platformId)) {\n        return;\n      }\n      this.quillSubscription = this.service.getQuill().pipe(mergeMap(Quill => {\n        const promises = [this.service.registerCustomModules(Quill, this.customModules)];\n        const beforeRender = this.beforeRender ?? this.service.config.beforeRender;\n        if (beforeRender) {\n          promises.push(beforeRender());\n        }\n        return Promise.all(promises).then(() => Quill);\n      })).subscribe(Quill => {\n        const modules = Object.assign({}, this.modules || this.service.config.modules);\n        modules.toolbar = false;\n        this.customOptions.forEach(customOption => {\n          const newCustomOption = Quill.import(customOption.import);\n          newCustomOption.whitelist = customOption.whitelist;\n          Quill.register(newCustomOption, true);\n        });\n        let debug = this.debug;\n        if (!debug && debug !== false && this.service.config.debug) {\n          debug = this.service.config.debug;\n        }\n        let formats = this.formats;\n        if (!formats && formats === undefined) {\n          formats = this.service.config.formats ? Object.assign({}, this.service.config.formats) : this.service.config.formats === null ? null : undefined;\n        }\n        const theme = this.theme || (this.service.config.theme ? this.service.config.theme : 'snow');\n        this.editorElem = this.elementRef.nativeElement.querySelector('[quill-view-element]');\n        this.zone.runOutsideAngular(() => {\n          this.quillEditor = new Quill(this.editorElem, {\n            debug: debug,\n            formats: formats,\n            modules,\n            readOnly: true,\n            strict: this.strict,\n            theme\n          });\n        });\n        this.renderer.addClass(this.editorElem, 'ngx-quill-view');\n        if (this.content) {\n          this.valueSetter(this.quillEditor, this.content);\n        }\n        // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n        // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n        if (!this.onEditorCreated.observers.length) {\n          return;\n        }\n        // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n        // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n        // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n        requestAnimationFrame(() => {\n          this.onEditorCreated.emit(this.quillEditor);\n          this.onEditorCreated.complete();\n        });\n      });\n    }\n    ngOnDestroy() {\n      this.quillSubscription?.unsubscribe();\n      this.quillSubscription = null;\n    }\n  }\n  QuillViewComponent.ɵfac = function QuillViewComponent_Factory(t) {\n    return new (t || QuillViewComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(QuillService), i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  QuillViewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: QuillViewComponent,\n    selectors: [[\"quill-view\"]],\n    inputs: {\n      format: \"format\",\n      theme: \"theme\",\n      modules: \"modules\",\n      debug: \"debug\",\n      formats: \"formats\",\n      sanitize: \"sanitize\",\n      beforeRender: \"beforeRender\",\n      strict: \"strict\",\n      content: \"content\",\n      customModules: \"customModules\",\n      customOptions: \"customOptions\",\n      preserveWhitespace: \"preserveWhitespace\"\n    },\n    outputs: {\n      onEditorCreated: \"onEditorCreated\"\n    },\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n    decls: 2,\n    vars: 2,\n    consts: [[\"quill-view-element\", \"\", 4, \"ngIf\"], [\"quill-view-element\", \"\"]],\n    template: function QuillViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, QuillViewComponent_div_0_Template, 1, 0, \"div\", 0);\n        i0.ɵɵtemplate(1, QuillViewComponent_pre_1_Template, 1, 0, \"pre\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.preserve);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.preserve);\n      }\n    },\n    dependencies: [CommonModule, i3.NgIf],\n    styles: [\".ql-container.ngx-quill-view{border:0}\\n\"],\n    encapsulation: 2\n  });\n  return QuillViewComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet QuillModule = /*#__PURE__*/(() => {\n  class QuillModule {\n    static forRoot(config) {\n      return {\n        ngModule: QuillModule,\n        providers: [{\n          provide: QUILL_CONFIG_TOKEN,\n          useValue: config\n        }]\n      };\n    }\n  }\n  QuillModule.ɵfac = function QuillModule_Factory(t) {\n    return new (t || QuillModule)();\n  };\n  QuillModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: QuillModule\n  });\n  QuillModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent]\n  });\n  return QuillModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of ngx-quill\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QuillEditorBase, QuillEditorComponent, QuillModule, QuillService, QuillViewComponent, QuillViewHTMLComponent };\n//# sourceMappingURL=ngx-quill.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}