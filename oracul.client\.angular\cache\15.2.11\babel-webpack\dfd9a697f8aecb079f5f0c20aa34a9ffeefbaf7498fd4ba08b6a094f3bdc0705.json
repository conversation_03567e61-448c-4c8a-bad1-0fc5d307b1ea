{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../shared/services/article.service\";\nimport * as i2 from \"../auth/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../core/i18n/translation.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nfunction TestArticlePreviewComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function TestArticlePreviewComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleAuth());\n    });\n    i0.ɵɵtext(1, \" Login for Testing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TestArticlePreviewComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function TestArticlePreviewComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.logout());\n    });\n    i0.ɵɵtext(1, \" Logout for Testing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TestArticlePreviewComponent_mat_card_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 10);\n    i0.ɵɵlistener(\"click\", function TestArticlePreviewComponent_mat_card_16_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const article_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.viewArticle(article_r7));\n    });\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\")(6, \"div\", 12)(7, \"span\", 13);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 14);\n    i0.ɵɵtext(10, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 15);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"mat-card-content\")(14, \"p\", 16);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-chip\", 17);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-card-actions\")(19, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TestArticlePreviewComponent_mat_card_16_Template_button_click_19_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const article_r7 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      ctx_r10.viewArticle(article_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const article_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", article_r7.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", article_r7.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r7.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(article_r7.author);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", article_r7.readTime, \" \", ctx_r2.t.home.minRead, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r7.excerpt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(article_r7.category);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.home.readArticle, \" \");\n  }\n}\nexport let TestArticlePreviewComponent = /*#__PURE__*/(() => {\n  class TestArticlePreviewComponent {\n    constructor(articleService, authService, router, t) {\n      this.articleService = articleService;\n      this.authService = authService;\n      this.router = router;\n      this.t = t;\n      this.testArticles = [];\n      this.isAuthenticated = false;\n    }\n    ngOnInit() {\n      // Subscribe to authentication state\n      this.authService.isAuthenticated$.subscribe(isAuth => {\n        this.isAuthenticated = isAuth;\n      });\n      // Load test articles\n      this.loadTestArticles();\n    }\n    loadTestArticles() {\n      // Load articles from database instead of using mock data\n      this.articleService.getFeaturedArticles(6).subscribe({\n        next: articles => {\n          this.testArticles = articles;\n        },\n        error: error => {\n          console.error('Error loading articles from database:', error);\n          // Only show error message, don't fall back to mock data\n          this.testArticles = [];\n        }\n      });\n    }\n    viewArticle(article) {\n      this.router.navigate(['/articles', article.slug]);\n    }\n    toggleAuth() {\n      // For testing purposes, navigate to login\n      this.router.navigate(['/login']);\n    }\n    logout() {\n      this.authService.logout().subscribe(() => {\n        console.log('Logged out for testing');\n      });\n    }\n    static {\n      this.ɵfac = function TestArticlePreviewComponent_Factory(t) {\n        return new (t || TestArticlePreviewComponent)(i0.ɵɵdirectiveInject(i1.ArticleService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.TranslationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TestArticlePreviewComponent,\n        selectors: [[\"app-test-article-preview\"]],\n        decls: 37,\n        vars: 4,\n        consts: [[1, \"test-container\"], [1, \"auth-status\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\", 4, \"ngIf\"], [1, \"articles-section\"], [1, \"articles-grid\"], [\"class\", \"article-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"test-instructions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"article-card\", 3, \"click\"], [\"mat-card-image\", \"\", 1, \"article-image\", 3, \"src\", \"alt\"], [1, \"article-meta\"], [1, \"author\"], [1, \"separator\"], [1, \"read-time\"], [1, \"article-excerpt\"], [1, \"category-chip\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n        template: function TestArticlePreviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n            i0.ɵɵtext(2, \"Article Preview Feature Test\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\", 1)(4, \"h2\");\n            i0.ɵɵtext(5, \"Authentication Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\");\n            i0.ɵɵtext(7, \"User is: \");\n            i0.ɵɵelementStart(8, \"strong\");\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(10, TestArticlePreviewComponent_button_10_Template, 2, 0, \"button\", 2);\n            i0.ɵɵtemplate(11, TestArticlePreviewComponent_button_11_Template, 2, 0, \"button\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 4)(13, \"h2\");\n            i0.ɵɵtext(14, \"Test Articles\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 5);\n            i0.ɵɵtemplate(16, TestArticlePreviewComponent_mat_card_16_Template, 23, 9, \"mat-card\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"h2\");\n            i0.ɵɵtext(19, \"Test Instructions\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"ol\")(21, \"li\")(22, \"strong\");\n            i0.ɵɵtext(23, \"Anonymous User Test:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(24, \" Make sure you're logged out, then click on any article to see the preview with blur effect and authentication prompt.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"li\")(26, \"strong\");\n            i0.ɵɵtext(27, \"Authenticated User Test:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(28, \" Login using the button above, then click on any article to see the full content without restrictions.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"li\")(30, \"strong\");\n            i0.ɵɵtext(31, \"Navigation Test:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(32, \" Verify that clicking articles navigates to /articles/[slug] route.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"li\")(34, \"strong\");\n            i0.ɵɵtext(35, \"Authentication Prompt Test:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(36, \" As anonymous user, verify the authentication prompt appears with proper Bulgarian text and working login/register buttons.\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate(ctx.isAuthenticated ? \"Authenticated\" : \"Anonymous\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.testArticles);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i6.MatButton, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardImage, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatIcon, i9.MatChip],\n        styles: [\".test-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:2rem}.auth-status[_ngcontent-%COMP%]{background:var(--theme-accent-light);padding:1rem;border-radius:8px;margin-bottom:2rem;text-align:center}.articles-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1.5rem;margin-bottom:2rem}.article-card[_ngcontent-%COMP%]{cursor:pointer;transition:transform .2s ease,box-shadow .2s ease}.article-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 25px #00000026}.article-image[_ngcontent-%COMP%]{height:200px;object-fit:cover}.article-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:var(--theme-text-secondary);font-size:.9rem}.separator[_ngcontent-%COMP%]{color:var(--theme-text-disabled)}.article-excerpt[_ngcontent-%COMP%]{margin-bottom:1rem;line-height:1.5}.category-chip[_ngcontent-%COMP%]{background:var(--theme-accent);color:var(--theme-text-primary)}.test-instructions[_ngcontent-%COMP%]{background:var(--theme-surface);padding:1.5rem;border-radius:8px;border-left:4px solid var(--theme-primary)}.test-instructions[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin:1rem 0}.test-instructions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:.75rem;line-height:1.5}\"]\n      });\n    }\n  }\n  return TestArticlePreviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}