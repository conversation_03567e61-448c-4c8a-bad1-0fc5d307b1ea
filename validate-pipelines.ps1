# Pipeline Validation Script for Oracul Application
# This script validates that the local environment can build both backend and frontend
# before pushing to Azure DevOps pipelines

param(
    [switch]$SkipTests,
    [switch]$SkipFrontend,
    [switch]$SkipBackend,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Oracul Pipeline Validation Script" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to run command with error handling
function Invoke-SafeCommand {
    param(
        [string]$Command,
        [string]$WorkingDirectory = $PWD,
        [string]$Description
    )
    
    Write-Host "📋 $Description" -ForegroundColor Yellow
    if ($Verbose) {
        Write-Host "   Command: $Command" -ForegroundColor Gray
        Write-Host "   Directory: $WorkingDirectory" -ForegroundColor Gray
    }
    
    try {
        Push-Location $WorkingDirectory
        Invoke-Expression $Command
        if ($LASTEXITCODE -ne 0) {
            throw "Command failed with exit code $LASTEXITCODE"
        }
        Write-Host "✅ $Description completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ $Description failed: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
    finally {
        Pop-Location
    }
}

# Check prerequisites
Write-Host "`n🔍 Checking Prerequisites..." -ForegroundColor Magenta

$prerequisites = @(
    @{ Name = "dotnet"; Description = ".NET SDK" },
    @{ Name = "node"; Description = "Node.js" },
    @{ Name = "npm"; Description = "NPM" }
)

foreach ($prereq in $prerequisites) {
    if (Test-Command $prereq.Name) {
        $version = & $prereq.Name --version 2>$null
        Write-Host "✅ $($prereq.Description): $version" -ForegroundColor Green
    } else {
        Write-Host "❌ $($prereq.Description) not found" -ForegroundColor Red
        exit 1
    }
}

# Validate Backend
if (-not $SkipBackend) {
    Write-Host "`n🏗️ Validating Backend (.NET 9.0)..." -ForegroundColor Magenta
    
    # Restore packages
    Invoke-SafeCommand -Command "dotnet restore Oracul.sln" -Description "Restoring NuGet packages"
    
    # Build solution
    Invoke-SafeCommand -Command "dotnet build Oracul.sln --configuration Release --no-restore" -Description "Building solution"
    
    # Run tests (if not skipped)
    if (-not $SkipTests) {
        if (Test-Path "Oracul.Server.Tests/Oracul.Server.Tests.csproj") {
            Invoke-SafeCommand -Command "dotnet test Oracul.Server.Tests/Oracul.Server.Tests.csproj --configuration Release --no-build --logger trx" -Description "Running backend tests"
        } else {
            Write-Host "⚠️ No backend tests found, skipping..." -ForegroundColor Yellow
        }
    }
    
    # Publish application
    Invoke-SafeCommand -Command "dotnet publish Oracul.Server/Oracul.Server.csproj --configuration Release --output ./publish/backend --no-build" -Description "Publishing backend application"
    
    Write-Host "✅ Backend validation completed successfully" -ForegroundColor Green
}

# Validate Frontend
if (-not $SkipFrontend) {
    Write-Host "`n🎨 Validating Frontend (Angular 15)..." -ForegroundColor Magenta
    
    $frontendPath = "oracul.client"
    
    if (-not (Test-Path $frontendPath)) {
        Write-Host "❌ Frontend directory not found: $frontendPath" -ForegroundColor Red
        exit 1
    }
    
    # Install dependencies
    Invoke-SafeCommand -Command "npm ci" -WorkingDirectory $frontendPath -Description "Installing frontend dependencies"
    
    # Run linting (if available)
    if (-not $SkipTests) {
        try {
            Invoke-SafeCommand -Command "npx ng lint" -WorkingDirectory $frontendPath -Description "Running frontend linting"
        } catch {
            Write-Host "⚠️ Linting failed or not configured, continuing..." -ForegroundColor Yellow
        }
        
        # Run tests
        try {
            Invoke-SafeCommand -Command "npm run test -- --watch=false --browsers=ChromeHeadless" -WorkingDirectory $frontendPath -Description "Running frontend tests"
        } catch {
            Write-Host "⚠️ Frontend tests failed or not properly configured, continuing..." -ForegroundColor Yellow
        }
    }
    
    # Build for production
    Invoke-SafeCommand -Command "npm run build -- --configuration=production" -WorkingDirectory $frontendPath -Description "Building frontend for production"
    
    Write-Host "✅ Frontend validation completed successfully" -ForegroundColor Green
}

# Summary
Write-Host "`n🎉 Pipeline Validation Summary" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

if (-not $SkipBackend) {
    Write-Host "✅ Backend (.NET 9.0) - Ready for deployment" -ForegroundColor Green
}

if (-not $SkipFrontend) {
    Write-Host "✅ Frontend (Angular 15) - Ready for deployment" -ForegroundColor Green
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Magenta
Write-Host "1. Commit your changes to Git" -ForegroundColor White
Write-Host "2. Push to develop branch for staging deployment" -ForegroundColor White
Write-Host "3. Create pull request to main branch for production deployment" -ForegroundColor White
Write-Host "4. Monitor Azure DevOps pipelines for deployment status" -ForegroundColor White

Write-Host "`n🔗 Useful Commands:" -ForegroundColor Magenta
Write-Host "- Run with verbose output: .\validate-pipelines.ps1 -Verbose" -ForegroundColor Gray
Write-Host "- Skip tests: .\validate-pipelines.ps1 -SkipTests" -ForegroundColor Gray
Write-Host "- Skip frontend: .\validate-pipelines.ps1 -SkipFrontend" -ForegroundColor Gray
Write-Host "- Skip backend: .\validate-pipelines.ps1 -SkipBackend" -ForegroundColor Gray

Write-Host "`n✨ All validations completed successfully! Ready for Azure DevOps deployment." -ForegroundColor Green
