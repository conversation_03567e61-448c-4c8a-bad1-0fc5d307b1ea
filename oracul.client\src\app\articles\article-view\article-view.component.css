/* Loading and Error States */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
}

.error-card {
  max-width: 500px;
  margin: 2rem auto;
}

.error-content {
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  color: var(--theme-error);
  margin-bottom: 1rem;
}

/* Article Container */
.article-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
  background: linear-gradient(
    135deg,
    var(--theme-background) 0%,
    var(--theme-accent-light) 100%
  );
  min-height: 100vh;
  position: relative;
}

.article-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(
    135deg,
    rgba(210, 166, 208, 0.1) 0%,
    transparent 100%
  );
  pointer-events: none;
  z-index: 0;
}

.article-container > * {
  position: relative;
  z-index: 1;
}

/* Article Header */
.article-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
}

.back-button {
  color: var(--theme-primary);
}

.article-actions {
  display: flex;
  gap: 0.5rem;
}

/* Featured Image */
.featured-image {
  width: 100%;
  margin-bottom: 2rem;
  border-radius: 16px;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(103, 69, 92, 0.15),
    0 4px 16px rgba(210, 166, 208, 0.1);
  position: relative;
}

.featured-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(210, 166, 208, 0.1) 0%,
    transparent 50%,
    rgba(103, 69, 92, 0.1) 100%
  );
  pointer-events: none;
}

.article-featured-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.featured-image:hover .article-featured-image {
  transform: scale(1.02);
}

/* Article Meta */
.article-meta {
  margin-bottom: 1.5rem;
}

.category-chip {
  background: var(--theme-accent);
  color: var(--theme-text-primary);
  margin-bottom: 1rem;
}

.meta-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-text-secondary);
  font-size: 0.9rem;
}

.separator {
  color: var(--theme-text-disabled);
}

/* Article Title */
.article-title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--theme-text-primary);
  margin-bottom: 1rem;
}

/* Article Excerpt */
.article-excerpt {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--theme-text-secondary);
  margin-bottom: 2rem;
  font-style: italic;
  padding: 1rem;
  background: var(--theme-accent-light);
  border-radius: 8px;
  border-left: 4px solid var(--theme-accent);
}

/* Article Tags */
.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.tag-chip {
  background: var(--theme-primary-light);
  color: white;
  font-size: 0.8rem;
}

/* Article Content */
.article-content-wrapper {
  position: relative;
  margin-bottom: 2rem;
  overflow: hidden;
  border-radius: 12px;
}

.article-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--theme-text-primary);
  padding: 1.5rem;
  background: var(--theme-surface);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.article-content-wrapper.blurred .article-content {
  filter: blur(2px);
  pointer-events: none;
  user-select: none;
  transition: filter 0.3s ease;
}

/* Enhanced Blur Overlay */
.blur-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.fade-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 250px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(230, 219, 236, 0.3) 20%,
    rgba(230, 219, 236, 0.6) 40%,
    rgba(230, 219, 236, 0.8) 60%,
    rgba(230, 219, 236, 0.95) 80%,
    var(--theme-accent-light) 100%
  );
  border-radius: 0 0 12px 12px;
}

/* Mystical overlay effect */
.blur-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center bottom,
    rgba(210, 166, 208, 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
}

/* Authentication Prompt Overlay */
.auth-prompt-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.auth-prompt-card {
  max-width: 500px;
  width: 100%;
  background: var(--theme-surface);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.auth-prompt-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-primary);
  font-size: 1.5rem;
}

.auth-prompt-card mat-card-title mat-icon {
  color: var(--theme-accent);
}

.auth-benefits {
  margin: 1.5rem 0;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: var(--theme-text-primary);
}

.benefit-item mat-icon {
  color: var(--theme-accent);
  font-size: 1.2rem;
}

.auth-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  padding: 1rem;
}

.auth-actions button {
  min-width: 120px;
}

/* Reading Progress */
.reading-progress {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--theme-accent-light);
  border-radius: 8px;
  border: 1px solid var(--theme-accent);
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--theme-primary);
}

.progress-info mat-icon {
  color: var(--theme-accent);
}

/* Social Proof */
.social-proof {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, var(--theme-accent-light) 0%, var(--theme-background) 100%);
  border-radius: 8px;
  font-style: italic;
  color: var(--theme-text-secondary);
}

.social-proof mat-icon {
  color: var(--theme-accent);
}

/* Responsive Design */
@media (max-width: 768px) {
  .article-container {
    padding: 0.5rem;
  }

  .article-title {
    font-size: 2rem;
  }

  .article-excerpt {
    font-size: 1.1rem;
    padding: 0.75rem;
  }

  .article-content {
    font-size: 1rem;
  }

  .meta-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .separator {
    display: none;
  }

  .auth-actions {
    flex-direction: column;
  }

  .auth-actions button {
    width: 100%;
  }
}

/* Article Content Styling */
.article-content h2 {
  color: var(--theme-primary);
  margin: 2rem 0 1rem 0;
  font-size: 1.5rem;
}

.article-content h3 {
  color: var(--theme-primary-light);
  margin: 1.5rem 0 0.75rem 0;
  font-size: 1.3rem;
}

.article-content p {
  margin-bottom: 1.5rem;
}

.article-content blockquote {
  border-left: 4px solid var(--theme-accent);
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: var(--theme-text-secondary);
  background: var(--theme-accent-light);
  padding: 1rem;
  border-radius: 0 8px 8px 0;
}

.article-content ul, .article-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.article-content li {
  margin-bottom: 0.5rem;
}
