.article-management-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.management-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-filter {
  min-width: 200px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

.articles-table {
  width: 100%;
  min-width: 800px;
}

.title-cell {
  max-width: 300px;
}

.title-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.article-title {
  font-weight: 500;
  color: #3f2f4e;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-category {
  font-size: 12px;
  color: #666;
  background-color: #e6dbec;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  max-width: fit-content;
}

.not-published {
  color: #999;
  font-style: italic;
}

.read-stats {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #666;
}

.delete-action {
  color: #f44336;
}

.delete-action mat-icon {
  color: #f44336;
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-data-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #ccc;
  margin-bottom: 16px;
}

.no-data h3 {
  margin: 16px 0 8px 0;
  color: #3f2f4e;
}

.no-data p {
  margin-bottom: 24px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Status chip colors */
::ng-deep .mat-chip.mat-primary {
  background-color: #67455c;
  color: white;
}

::ng-deep .mat-chip.mat-accent {
  background-color: #d2a6d0;
  color: #3f2f4e;
}

::ng-deep .mat-chip.mat-warn {
  background-color: #ff9800;
  color: white;
}

/* Table styling */
.mat-table {
  background: transparent;
}

.mat-header-cell {
  color: #3f2f4e;
  font-weight: 600;
  border-bottom: 2px solid #e6dbec;
}

.mat-cell {
  border-bottom: 1px solid #f0f0f0;
}

.mat-row:hover {
  background-color: #fafafa;
}

/* Responsive design */
@media (max-width: 768px) {
  .article-management-container {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .status-filter {
    min-width: auto;
    width: 100%;
  }
  
  .table-container {
    margin: 0 -10px;
  }
  
  .articles-table {
    min-width: 600px;
  }
  
  .title-cell {
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .articles-table {
    min-width: 500px;
  }
  
  .title-cell {
    max-width: 150px;
  }
  
  .article-title {
    font-size: 14px;
  }
  
  .article-category {
    font-size: 10px;
    padding: 1px 6px;
  }
}

/* Menu styling */
.mat-menu-panel {
  min-width: 180px;
}

.mat-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mat-menu-item mat-icon {
  margin-right: 0;
}

/* Paginator styling */
.mat-paginator {
  background: transparent;
  border-top: 1px solid #e0e0e0;
  margin-top: 16px;
}

/* Card header styling */
.mat-card-header {
  margin-bottom: 16px;
}

.mat-card-title {
  color: #3f2f4e;
  font-size: 24px;
  font-weight: 500;
}

.mat-card-subtitle {
  color: #666;
  margin-top: 4px;
}
