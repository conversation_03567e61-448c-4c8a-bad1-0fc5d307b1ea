export interface Translations {
  // Common
  common: {
    loading: string;
    save: string;
    cancel: string;
    delete: string;
    edit: string;
    view: string;
    search: string;
    close: string;
    yes: string;
    no: string;
    back: string;
    next: string;
    previous: string;
    submit: string;
    login: string;
    register: string;
    logout: string;
    profile: string;
    dashboard: string;
    home: string;
    contact: string;
    about: string;
    settings: string;
    themes: string;
  };

  // Navigation
  nav: {
    brand: string;
    dashboard: string;
    profileDemo: string;
    myProfile: string;
    findProfessionals: string;
    themes: string;
    logout: string;
    signIn: string;
    createAccount: string;
  };

  // Authentication
  auth: {
    loginTitle: string;
    registerTitle: string;
    email: string;
    password: string;
    confirmPassword: string;
    firstName: string;
    lastName: string;
    rememberMe: string;
    forgotPassword: string;
    dontHaveAccount: string;
    alreadyHaveAccount: string;
    createAccount: string;
    signIn: string;
    signInWith: string;
    orContinueWith: string;
    loginSuccess: string;
    registerSuccess: string;
    loginError: string;
    registerError: string;
    emailRequired: string;
    passwordRequired: string;
    firstNameRequired: string;
    lastNameRequired: string;
    passwordsNotMatch: string;
    invalidEmail: string;
    passwordTooShort: string;
    logoutSuccess: string;
    logoutError: string;
  };

  // Home Page
  home: {
    heroTitle: string;
    heroSubtitle: string;
    startJourney: string;
    featuredAstrologers: string;
    featuredAstrologersSubtitle: string;
    browseAllAstrologers: string;
    cosmicWisdomArticles: string;
    cosmicWisdomSubtitle: string;
    dailyHoroscope: string;
    dailyHoroscopeSubtitle: string;
    ctaTitle: string;
    ctaSubtitle: string;
    createFreeAccount: string;
    alreadyMember: string;
    viewProfile: string;
    readArticle: string;
    fullReading: string;
    views: string;
    endorsements: string;
    minRead: string;
    luckyNumber: string;
    element: string;
    nav: {
      home: string;
      astrologers: string;
      articles: string;
      horoscope: string;
    };
  };

  // Dashboard
  dashboard: {
    title: string;
    welcome: string;
    weatherForecast: string;
    profileSystem: string;
    profileSystemDescription: string;
    tryProfileDemo: string;
    viewSampleProfile: string;
    readingPortfolio: string;
    astrologicalSkills: string;
    spiritualJourney: string;
    cosmicAnalytics: string;
  };

  // Profile
  profile: {
    title: string;
    professionalTitle: string;
    location: string;
    summary: string;
    skills: string;
    experience: string;
    portfolio: string;
    achievements: string;
    certifications: string;
    blogPosts: string;
    socialLinks: string;
    contactInfo: string;
    analytics: string;
    endorsements: string;
    profileViews: string;
    editProfile: string;
    viewFullProfile: string;
    categories: {
      coreAstrology: string;
      relationshipAstrology: string;
      predictiveAstrology: string;
      divinationArts: string;
      energyWork: string;
      ancientSystems: string;
      lunarMagic: string;
    };
  };

  // Articles
  articles: {
    title: string;
    author: string;
    publishedAt: string;
    readTime: string;
    category: string;
    share: string;
    bookmark: string;
    preview: {
      signInToRead: string;
      joinCommunity: string;
      valuableContent: string;
      readingProgress: string;
      unlimitedAccess: string;
      connectWithAstrologers: string;
      saveArticles: string;
      notifications: string;
      socialProof: string;
      createFreeAccount: string;
    };
    categories: {
      astrologyBasics: string;
      planetaryTransits: string;
      lunarMagic: string;
      crystalHealing: string;
    };
  };

  // Horoscope
  horoscope: {
    title: string;
    todaysPrediction: string;
    luckyNumber: string;
    luckyColor: string;
    compatibility: string;
    element: string;
    dateRange: string;
    signs: {
      aries: string;
      taurus: string;
      gemini: string;
      cancer: string;
      leo: string;
      virgo: string;
      libra: string;
      scorpio: string;
      sagittarius: string;
      capricorn: string;
      aquarius: string;
      pisces: string;
    };
    elements: {
      fire: string;
      earth: string;
      air: string;
      water: string;
    };
    colors: {
      red: string;
      green: string;
      yellow: string;
      silver: string;
      gold: string;
      navyBlue: string;
    };
  };

  // Themes
  themes: {
    title: string;
    mysticalPurple: string;
    deepPurpleAmber: string;
    blueOrange: string;
    greenTeal: string;
    darkTheme: string;
    themeChanged: string;
  };

  // Errors
  errors: {
    general: string;
    networkError: string;
    notFound: string;
    unauthorized: string;
    forbidden: string;
    serverError: string;
  };
}

export const BULGARIAN_TRANSLATIONS: Translations = {
  common: {
    loading: 'Зареждане',
    save: 'Запази',
    cancel: 'Отказ',
    delete: 'Изтрий',
    edit: 'Редактирай',
    view: 'Виж',
    search: 'Търси',
    close: 'Затвори',
    yes: 'Да',
    no: 'Не',
    back: 'Назад',
    next: 'Напред',
    previous: 'Предишен',
    submit: 'Изпрати',
    login: 'Вход',
    register: 'Регистрация',
    logout: 'Изход',
    profile: 'Профил',
    dashboard: 'Табло',
    home: 'Начало',
    contact: 'Контакт',
    about: 'За нас',
    settings: 'Настройки',
    themes: 'Теми'
  },

  nav: {
    brand: 'Оракул',
    dashboard: 'Табло',
    profileDemo: 'Демо Профил',
    myProfile: 'Моят Профил',
    findProfessionals: 'Намери Професионалисти',
    themes: 'Теми',
    logout: 'Изход',
    signIn: 'Вход',
    createAccount: 'Създай Акаунт'
  },

  auth: {
    loginTitle: 'Влезте в акаунта си',
    registerTitle: 'Създайте нов акаунт',
    email: 'Имейл',
    password: 'Парола',
    confirmPassword: 'Потвърди парола',
    firstName: 'Име',
    lastName: 'Фамилия',
    rememberMe: 'Запомни ме',
    forgotPassword: 'Забравена парола?',
    dontHaveAccount: 'Нямате акаунт?',
    alreadyHaveAccount: 'Вече имате акаунт?',
    createAccount: 'Създай акаунт',
    signIn: 'Влез',
    signInWith: 'Влез с',
    orContinueWith: 'или продължи с',
    loginSuccess: 'Успешен вход!',
    registerSuccess: 'Успешна регистрация!',
    loginError: 'Грешка при вход',
    registerError: 'Грешка при регистрация',
    emailRequired: 'Имейлът е задължителен',
    passwordRequired: 'Паролата е задължителна',
    firstNameRequired: 'Името е задължително',
    lastNameRequired: 'Фамилията е задължителна',
    passwordsNotMatch: 'Паролите не съвпадат',
    invalidEmail: 'Невалиден имейл',
    passwordTooShort: 'Паролата е твърде кратка',
    logoutSuccess: 'Успешен изход',
    logoutError: 'Грешка при изход'
  },

  home: {
    heroTitle: 'Открийте Вашето Космическо Пътешествие',
    heroSubtitle: 'Свържете се с професионални астролози, изследвайте вашата натална карта и разкрийте мистериите на вселената. Присъединете се към нашата мистична общност и намерете ръководство за вашия духовен път.',
    startJourney: 'Започнете Пътешествието',
    featuredAstrologers: 'Препоръчани Астролози',
    featuredAstrologersSubtitle: 'Свържете се с нашите най-опитни космически водачи',
    browseAllAstrologers: 'Разгледайте Всички Астролози',
    cosmicWisdomArticles: 'Статии за Космическа Мъдрост',
    cosmicWisdomSubtitle: 'Разширете знанията си с прозрения от нашите експерт астролози',
    dailyHoroscope: 'Дневен Хороскоп',
    dailyHoroscopeSubtitle: 'Открийте какво ви готвят звездите днес',
    ctaTitle: 'Готови ли сте да изследвате космическата си съдба?',
    ctaSubtitle: 'Присъединете се към хиляди търсещи, които са открили истинския си път чрез мъдростта на звездите. Създайте акаунт днес и отключете персонализирани четения, свържете се с експерт астролози и започнете духовното си пътешествие.',
    createFreeAccount: 'Създай Безплатен Акаунт',
    alreadyMember: 'Вече сте член? Влезте',
    viewProfile: 'Виж Профил',
    readArticle: 'Прочети Статия',
    fullReading: 'Пълно Четене',
    views: 'прегледа',
    endorsements: 'препоръки',
    minRead: 'мин четене',
    luckyNumber: 'Щастливо число',
    element: 'Елемент',
    nav: {
      home: 'Начало',
      astrologers: 'Астролози',
      articles: 'Статии',
      horoscope: 'Хороскоп'
    }
  },

  dashboard: {
    title: 'Табло',
    welcome: 'Добре дошли',
    weatherForecast: 'Прогноза за времето',
    profileSystem: 'Професионална Профилна Система',
    profileSystemDescription: 'Пълнофункционална профилна система с портфолио, умения, опит и аналитика',
    tryProfileDemo: 'Опитайте Астрологичен Профил Демо',
    viewSampleProfile: 'Виж Профил на Астролог',
    readingPortfolio: 'Портфолио от Четения',
    astrologicalSkills: 'Астрологични Умения',
    spiritualJourney: 'Духовно Пътешествие',
    cosmicAnalytics: 'Космическа Аналитика'
  },

  profile: {
    title: 'Профил',
    professionalTitle: 'Професионална титла',
    location: 'Местоположение',
    summary: 'Резюме',
    skills: 'Умения',
    experience: 'Опит',
    portfolio: 'Портфолио',
    achievements: 'Постижения',
    certifications: 'Сертификати',
    blogPosts: 'Блог публикации',
    socialLinks: 'Социални връзки',
    contactInfo: 'Контактна информация',
    analytics: 'Аналитика',
    endorsements: 'Препоръки',
    profileViews: 'Прегледи на профила',
    editProfile: 'Редактирай профил',
    viewFullProfile: 'Виж пълен профил',
    categories: {
      coreAstrology: 'Основна Астрология',
      relationshipAstrology: 'Астрология на Отношенията',
      predictiveAstrology: 'Предсказателна Астрология',
      divinationArts: 'Изкуства на Гаданието',
      energyWork: 'Енергийна Работа',
      ancientSystems: 'Древни Системи',
      lunarMagic: 'Лунна Магия'
    }
  },

  articles: {
    title: 'Статии',
    author: 'Автор',
    publishedAt: 'Публикувано на',
    readTime: 'Време за четене',
    category: 'Категория',
    share: 'Сподели',
    bookmark: 'Отбележи',
    preview: {
      signInToRead: 'Влезте, за да прочетете пълната статия',
      joinCommunity: 'Присъединете се към нашата общност и получете достъп до пълното съдържание',
      valuableContent: 'Тази статия съдържа ценна информация за астрологията и духовното развитие. Създайте безплатен акаунт или влезте, за да прочетете пълното съдържание.',
      readingProgress: 'Прочетохте {percentage}% от статията',
      unlimitedAccess: 'Неограничен достъп до всички статии',
      connectWithAstrologers: 'Свързване с професионални астролози',
      saveArticles: 'Запазване на любими статии',
      notifications: 'Известия за нови публикации',
      socialProof: 'Присъединете се към над 5,000 души, които изследват космическата мъдрост',
      createFreeAccount: 'Създайте безплатен акаунт днес'
    },
    categories: {
      astrologyBasics: 'Основи на Астрологията',
      planetaryTransits: 'Планетарни Транзити',
      lunarMagic: 'Лунна Магия',
      crystalHealing: 'Кристално Лечение'
    }
  },

  horoscope: {
    title: 'Хороскоп',
    todaysPrediction: 'Днешна прогноза',
    luckyNumber: 'Щастливо число',
    luckyColor: 'Щастлив цвят',
    compatibility: 'Съвместимост',
    element: 'Елемент',
    dateRange: 'Период',
    signs: {
      aries: 'Овен',
      taurus: 'Телец',
      gemini: 'Близнаци',
      cancer: 'Рак',
      leo: 'Лъв',
      virgo: 'Дева',
      libra: 'Везни',
      scorpio: 'Скорпион',
      sagittarius: 'Стрелец',
      capricorn: 'Козирог',
      aquarius: 'Водолей',
      pisces: 'Риби'
    },
    elements: {
      fire: 'Огън',
      earth: 'Земя',
      air: 'Въздух',
      water: 'Вода'
    },
    colors: {
      red: 'Червен',
      green: 'Зелен',
      yellow: 'Жълт',
      silver: 'Сребърен',
      gold: 'Златен',
      navyBlue: 'Тъмносин'
    }
  },

  themes: {
    title: 'Теми',
    mysticalPurple: 'Мистично Лилаво',
    deepPurpleAmber: 'Дълбоко Лилаво и Кехлибар',
    blueOrange: 'Синьо и Оранжево',
    greenTeal: 'Зелено и Тюркоазено',
    darkTheme: 'Тъмна Тема',
    themeChanged: 'Темата е сменена на'
  },

  errors: {
    general: 'Възникна грешка',
    networkError: 'Мрежова грешка',
    notFound: 'Не е намерено',
    unauthorized: 'Неоторизиран достъп',
    forbidden: 'Забранен достъп',
    serverError: 'Сървърна грешка'
  }
};
