import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { ArticleService } from '../../shared/services/article.service';
import { ArticleManagementDto } from '../../shared/models/article.models';
import { ArticlePreviewDialogComponent, ArticlePreviewData } from '../components/article-preview-dialog/article-preview-dialog.component';

export interface CreateArticleRequest {
  title: string;
  excerpt: string;
  content?: string;
  category?: string;
  tags: string[];
  featuredImageUrl?: string;
  metaDescription?: string;
  metaKeywords?: string;
  allowComments: boolean;
  isFeatured: boolean;
  saveAsDraft: boolean;
}

export interface UpdateArticleRequest {
  title: string;
  excerpt: string;
  content?: string;
  category?: string;
  tags: string[];
  featuredImageUrl?: string;
  metaDescription?: string;
  metaKeywords?: string;
  allowComments: boolean;
  isFeatured: boolean;
}



@Component({
  selector: 'app-article-editor',
  templateUrl: './article-editor.component.html',
  styleUrls: ['./article-editor.component.css']
})
export class ArticleEditorComponent implements OnInit {
  articleForm: FormGroup;
  isEditMode = false;
  articleId?: number;
  isLoading = false;
  isSaving = false;

  // Quill editor configuration
  quillConfig = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link', 'image', 'video']
    ]
  };

  categories = [
    'Астрология',
    'Таро',
    'Нумерология',
    'Медитация',
    'Кристали',
    'Духовност',
    'Хороскопи',
    'Сънища',
    'Енергия',
    'Чакри'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private articleService: ArticleService
  ) {
    this.articleForm = this.createForm();
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.articleId = +params['id'];
        this.loadArticle();
      }
    });
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(200)]],
      excerpt: ['', [Validators.required, Validators.maxLength(500)]],
      content: [''],
      category: [''],
      tags: [''],
      featuredImageUrl: [''],
      metaDescription: ['', Validators.maxLength(160)],
      metaKeywords: ['', Validators.maxLength(500)],
      allowComments: [true],
      isFeatured: [false]
    });
  }

  private loadArticle(): void {
    if (!this.articleId) return;

    this.isLoading = true;
    this.articleService.getArticleForEdit(this.articleId).subscribe({
      next: (article) => {
        this.populateForm(article);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading article:', error);
        this.snackBar.open('Грешка при зареждането на статията', 'Затвори', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  private populateForm(article: ArticleManagementDto): void {
    this.articleForm.patchValue({
      title: article.title,
      excerpt: article.excerpt,
      content: article.content,
      category: article.category,
      tags: article.tags.join(', '),
      featuredImageUrl: article.featuredImageUrl,
      metaDescription: article.metaDescription,
      metaKeywords: article.metaKeywords,
      allowComments: article.allowComments,
      isFeatured: article.isFeatured
    });
  }

  onSaveDraft(): void {
    this.saveArticle(true);
  }

  onPublish(): void {
    this.saveArticle(false);
  }

  private saveArticle(saveAsDraft: boolean): void {
    if (this.articleForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isSaving = true;
    const formValue = this.articleForm.value;
    const tags = this.parseTags(formValue.tags);

    if (this.isEditMode && this.articleId) {
      const updateRequest: UpdateArticleRequest = {
        title: formValue.title,
        excerpt: formValue.excerpt,
        content: formValue.content,
        category: formValue.category,
        tags: tags,
        featuredImageUrl: formValue.featuredImageUrl,
        metaDescription: formValue.metaDescription,
        metaKeywords: formValue.metaKeywords,
        allowComments: formValue.allowComments,
        isFeatured: formValue.isFeatured
      };

      this.articleService.updateArticle(this.articleId, updateRequest).subscribe({
        next: (article) => {
          this.snackBar.open('Статията е обновена успешно', 'Затвори', { duration: 3000 });
          if (!saveAsDraft) {
            this.publishArticle(article.id);
          } else {
            this.router.navigate(['/articles/manage']);
          }
          this.isSaving = false;
        },
        error: (error) => {
          console.error('Error updating article:', error);
          this.snackBar.open('Грешка при обновяването на статията', 'Затвори', { duration: 3000 });
          this.isSaving = false;
        }
      });
    } else {
      const createRequest: CreateArticleRequest = {
        title: formValue.title,
        excerpt: formValue.excerpt,
        content: formValue.content,
        category: formValue.category,
        tags: tags,
        featuredImageUrl: formValue.featuredImageUrl,
        metaDescription: formValue.metaDescription,
        metaKeywords: formValue.metaKeywords,
        allowComments: formValue.allowComments,
        isFeatured: formValue.isFeatured,
        saveAsDraft: saveAsDraft
      };

      this.articleService.createArticle(createRequest).subscribe({
        next: (article) => {
          this.snackBar.open('Статията е създадена успешно', 'Затвори', { duration: 3000 });
          this.router.navigate(['/articles/manage']);
          this.isSaving = false;
        },
        error: (error) => {
          console.error('Error creating article:', error);
          this.snackBar.open('Грешка при създаването на статията', 'Затвори', { duration: 3000 });
          this.isSaving = false;
        }
      });
    }
  }

  private publishArticle(articleId: number): void {
    this.articleService.publishArticle(articleId).subscribe({
      next: () => {
        this.snackBar.open('Статията е публикувана успешно', 'Затвори', { duration: 3000 });
        this.router.navigate(['/articles/manage']);
      },
      error: (error) => {
        console.error('Error publishing article:', error);
        this.snackBar.open('Грешка при публикуването на статията', 'Затвори', { duration: 3000 });
      }
    });
  }

  private parseTags(tagsString: string): string[] {
    if (!tagsString) return [];
    return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.articleForm.controls).forEach(key => {
      const control = this.articleForm.get(key);
      control?.markAsTouched();
    });
  }

  onCancel(): void {
    this.router.navigate(['/articles/manage']);
  }

  onPreview(): void {
    if (this.articleForm.invalid) {
      this.markFormGroupTouched();
      this.snackBar.open('Моля, попълнете задължителните полета', 'Затвори', { duration: 3000 });
      return;
    }

    const formValue = this.articleForm.value;
    const tags = this.parseTags(formValue.tags);

    const previewData: ArticlePreviewData = {
      title: formValue.title,
      excerpt: formValue.excerpt,
      content: formValue.content,
      category: formValue.category,
      tags: tags,
      featuredImageUrl: formValue.featuredImageUrl,
      author: 'Вие', // Current user
      estimatedReadTime: this.calculateReadTime(formValue.content)
    };

    this.dialog.open(ArticlePreviewDialogComponent, {
      width: '90vw',
      maxWidth: '800px',
      maxHeight: '90vh',
      data: previewData
    });
  }

  private calculateReadTime(content?: string): number {
    if (!content) return 1;

    // Remove HTML tags for word count
    const textContent = content.replace(/<[^>]*>/g, '');
    const words = textContent.trim().split(/\s+/).length;

    // Average reading speed is 200-250 words per minute
    const wordsPerMinute = 225;
    const readTime = Math.ceil(words / wordsPerMinute);

    return Math.max(1, readTime); // Minimum 1 minute
  }

  onFeaturedImageUploaded(imageUrl: string): void {
    this.articleForm.patchValue({ featuredImageUrl: imageUrl });
  }

  onFeaturedImageRemoved(): void {
    this.articleForm.patchValue({ featuredImageUrl: '' });
  }
}
