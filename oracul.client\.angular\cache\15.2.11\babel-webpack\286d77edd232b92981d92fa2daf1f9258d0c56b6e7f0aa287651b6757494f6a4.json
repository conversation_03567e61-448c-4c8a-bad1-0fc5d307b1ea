{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../auth/services/auth.service\";\nimport * as i3 from \"../../core/i18n/translation.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/divider\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"../../core/theme/theme-selector/theme-selector.component\";\nfunction NavigationComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToLogin());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_24_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToRegister());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.t.common.login, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.t.home.startJourney, \" \");\n  }\n}\nfunction NavigationComponent_div_25_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r11 = ctx.ngIf;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r7.t.dashboard.welcome, \", \", user_r11.firstName, \"! \");\n  }\n}\nfunction NavigationComponent_div_25_div_4_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 38);\n  }\n  if (rf & 2) {\n    const user_r12 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵproperty(\"src\", user_r12.profilePictureUrl, i0.ɵɵsanitizeUrl)(\"alt\", user_r12.firstName + \" \" + user_r12.lastName);\n  }\n}\nfunction NavigationComponent_div_25_div_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 39);\n    i0.ɵɵtext(1, \"account_circle\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavigationComponent_div_25_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, NavigationComponent_div_25_div_4_img_1_Template, 1, 2, \"img\", 36);\n    i0.ɵɵtemplate(2, NavigationComponent_div_25_div_4_ng_template_2_Template, 2, 0, \"ng-template\", null, 37, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r12 = ctx.ngIf;\n    const _r14 = i0.ɵɵreference(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r12.profilePictureUrl)(\"ngIfElse\", _r14);\n  }\n}\nfunction NavigationComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, NavigationComponent_div_25_span_1_Template, 2, 2, \"span\", 16);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementStart(3, \"button\", 17);\n    i0.ɵɵtemplate(4, NavigationComponent_div_25_div_4_Template, 4, 2, \"div\", 18);\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-menu\", 19, 20)(8, \"button\", 21)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 22)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 23)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"button\", 24)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"article\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \"\\u041C\\u043E\\u0438\\u0442\\u0435 \\u0441\\u0442\\u0430\\u0442\\u0438\\u0438\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"button\", 25)(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"\\u041D\\u043E\\u0432\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"button\", 26)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"api\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"Test API Connection\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"button\", 27)(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"palette\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"mat-icon\", 28);\n    i0.ɵɵtext(44, \"chevron_right\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"button\", 29)(46, \"mat-icon\");\n    i0.ɵɵtext(47, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"span\");\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(50, \"mat-divider\");\n    i0.ɵɵelementStart(51, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_25_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.logout());\n    });\n    i0.ɵɵelementStart(52, \"mat-icon\");\n    i0.ɵɵtext(53, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"span\");\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(56, \"mat-menu\", 31, 32)(58, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_25_Template_div_click_58_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelement(59, \"app-theme-selector\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r9 = i0.ɵɵreference(7);\n    const _r10 = i0.ɵɵreference(57);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 10, ctx_r1.authService.currentUser$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(5, 12, ctx_r1.authService.currentUser$));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.myProfile);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.findProfessionals);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.dashboard);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r10);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.themes);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.t.common.settings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.t.nav.logout);\n  }\n}\nfunction NavigationComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_46_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.navigateToLogin());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_46_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.navigateToRegister());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"star\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.common.login, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.t.home.startJourney, \" \");\n  }\n}\nfunction NavigationComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"button\", 44)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 45)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 46)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function NavigationComponent_div_47_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.logout());\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.nav.myProfile, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.nav.findProfessionals, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.nav.dashboard, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.t.nav.logout, \" \");\n  }\n}\nexport let NavigationComponent = /*#__PURE__*/(() => {\n  class NavigationComponent {\n    constructor(router, route, authService, t) {\n      this.router = router;\n      this.route = route;\n      this.authService = authService;\n      this.t = t;\n      this.currentRoute = '';\n      this.isMobileMenuOpen = false;\n      this.isHomePage = false;\n      this.activeSection = 'hero';\n      // Track current route\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        this.currentRoute = event.url;\n        const wasHomePage = this.isHomePage;\n        this.isHomePage = this.currentRoute === '/' || this.currentRoute === '/home' || this.currentRoute.startsWith('/home');\n        this.closeMobileMenu();\n        // Initialize scroll detection when navigating to home page\n        if (this.isHomePage && !wasHomePage) {\n          // Clean up previous observer if it exists\n          if (this.intersectionObserver) {\n            this.intersectionObserver.disconnect();\n          }\n          this.initializeScrollDetection();\n        } else if (!this.isHomePage && this.intersectionObserver) {\n          // Clean up observer when leaving home page\n          this.intersectionObserver.disconnect();\n          this.activeSection = 'hero'; // Reset to default\n        }\n      });\n    }\n\n    ngOnInit() {\n      // Check initial route\n      this.isHomePage = this.currentRoute === '/' || this.currentRoute === '/home' || this.currentRoute.startsWith('/home');\n      // Handle fragment navigation when arriving at home page\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n        // Check for fragment after navigation\n        const fragment = this.route.snapshot.fragment;\n        if (fragment && this.isHomePage) {\n          setTimeout(() => {\n            this.scrollToSection(fragment);\n          }, 300); // Increased delay to ensure DOM is ready\n        }\n      });\n      // Initialize scroll detection when on home page\n      if (this.isHomePage) {\n        this.initializeScrollDetection();\n      }\n    }\n    ngOnDestroy() {\n      // Clean up intersection observer\n      if (this.intersectionObserver) {\n        this.intersectionObserver.disconnect();\n      }\n    }\n    // Check if user is authenticated\n    isAuthenticated() {\n      return this.authService.isAuthenticated();\n    }\n    // Navigation methods - adapt behavior based on current page\n    navigateToSection(sectionId) {\n      this.closeMobileMenu();\n      if (this.isHomePage) {\n        // If on home page, scroll to section\n        this.scrollToSection(sectionId);\n        // Update active section immediately for better UX\n        this.activeSection = sectionId;\n      } else {\n        // If on other page, navigate to home with fragment\n        this.router.navigate(['/home'], {\n          fragment: sectionId\n        });\n      }\n    }\n    // Smooth scroll to section (only works when on home page)\n    scrollToSection(sectionId) {\n      const element = document.getElementById(sectionId);\n      if (element) {\n        element.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start',\n          inline: 'nearest'\n        });\n      }\n    }\n    // Authentication navigation\n    navigateToLogin() {\n      this.closeMobileMenu();\n      this.router.navigate(['/login']);\n    }\n    navigateToRegister() {\n      this.closeMobileMenu();\n      this.router.navigate(['/register']);\n    }\n    // Mobile menu controls\n    toggleMobileMenu() {\n      this.isMobileMenuOpen = !this.isMobileMenuOpen;\n    }\n    closeMobileMenu() {\n      this.isMobileMenuOpen = false;\n    }\n    // Logout functionality\n    logout() {\n      this.authService.logout().subscribe({\n        next: () => {\n          this.router.navigate(['/home']);\n        },\n        error: () => {\n          // Still navigate to home even if logout fails\n          this.router.navigate(['/home']);\n        }\n      });\n    }\n    // Initialize scroll detection using Intersection Observer\n    initializeScrollDetection() {\n      // Wait for DOM to be ready\n      setTimeout(() => {\n        const sections = ['hero', 'astrologers', 'articles', 'horoscope'];\n        const sectionElements = sections.map(id => document.getElementById(id)).filter(el => el !== null);\n        if (sectionElements.length === 0) {\n          return; // No sections found, skip initialization\n        }\n        // Create intersection observer\n        this.intersectionObserver = new IntersectionObserver(entries => {\n          entries.forEach(entry => {\n            if (entry.isIntersecting) {\n              // Update active section when section comes into view\n              this.activeSection = entry.target.id;\n            }\n          });\n        }, {\n          // Trigger when section is 30% visible\n          threshold: 0.3,\n          // Offset from top to account for fixed navigation\n          rootMargin: '-80px 0px -50% 0px'\n        });\n        // Observe all sections\n        sectionElements.forEach(section => {\n          if (section) {\n            this.intersectionObserver.observe(section);\n          }\n        });\n      }, 500);\n    }\n    // Check if a navigation link is active\n    isActiveSection(sectionId) {\n      return this.isHomePage && this.activeSection === sectionId;\n    }\n    static {\n      this.ɵfac = function NavigationComponent_Factory(t) {\n        return new (t || NavigationComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.TranslationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NavigationComponent,\n        selectors: [[\"app-navigation\"]],\n        decls: 48,\n        vars: 34,\n        consts: [[1, \"unified-navigation\"], [1, \"nav-container\"], [1, \"nav-brand\", 3, \"click\"], [1, \"nav-links\"], [\"mat-button\", \"\", 1, \"nav-link\", 3, \"click\"], [\"class\", \"nav-actions\", 4, \"ngIf\"], [\"class\", \"nav-user-menu\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"mobile-menu-toggle\", 3, \"click\"], [1, \"mobile-menu\"], [\"mat-button\", \"\", 1, \"mobile-nav-link\", 3, \"click\"], [\"class\", \"mobile-actions\", 4, \"ngIf\"], [\"class\", \"mobile-user-actions\", 4, \"ngIf\"], [1, \"nav-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"login-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"register-btn\", 3, \"click\"], [1, \"nav-user-menu\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"user-avatar-button\", 3, \"matMenuTriggerFor\"], [\"class\", \"user-avatar\", 4, \"ngIf\"], [1, \"user-menu\"], [\"userMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile/edit\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profiles/search\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/dashboard\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/articles/manage\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/articles/editor\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/test-api\"], [\"mat-menu-item\", \"\", 3, \"matMenuTriggerFor\"], [1, \"submenu-arrow\"], [\"mat-menu-item\", \"\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"theme-menu\"], [\"themeMenu\", \"matMenu\"], [1, \"theme-menu-content\", 3, \"click\"], [1, \"user-info\"], [1, \"user-avatar\"], [\"class\", \"avatar-image\", 3, \"src\", \"alt\", 4, \"ngIf\", \"ngIfElse\"], [\"defaultAvatar\", \"\"], [1, \"avatar-image\", 3, \"src\", \"alt\"], [1, \"default-avatar\"], [1, \"mobile-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"mobile-login-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"mobile-register-btn\", 3, \"click\"], [1, \"mobile-user-actions\"], [\"mat-button\", \"\", \"routerLink\", \"/profile/edit\", 1, \"mobile-nav-link\"], [\"mat-button\", \"\", \"routerLink\", \"/profiles/search\", 1, \"mobile-nav-link\"], [\"mat-button\", \"\", \"routerLink\", \"/dashboard\", 1, \"mobile-nav-link\"]],\n        template: function NavigationComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_div_click_2_listener() {\n              return ctx.navigateToSection(\"hero\");\n            });\n            i0.ɵɵelementStart(3, \"mat-icon\");\n            i0.ɵɵtext(4, \"auto_awesome\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"span\");\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 3)(8, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_8_listener() {\n              return ctx.navigateToSection(\"hero\");\n            });\n            i0.ɵɵelementStart(9, \"mat-icon\");\n            i0.ɵɵtext(10, \"home\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_12_listener() {\n              return ctx.navigateToSection(\"astrologers\");\n            });\n            i0.ɵɵelementStart(13, \"mat-icon\");\n            i0.ɵɵtext(14, \"people\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_16_listener() {\n              return ctx.navigateToSection(\"articles\");\n            });\n            i0.ɵɵelementStart(17, \"mat-icon\");\n            i0.ɵɵtext(18, \"article\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_20_listener() {\n              return ctx.navigateToSection(\"horoscope\");\n            });\n            i0.ɵɵelementStart(21, \"mat-icon\");\n            i0.ɵɵtext(22, \"stars\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(24, NavigationComponent_div_24_Template, 9, 2, \"div\", 5);\n            i0.ɵɵtemplate(25, NavigationComponent_div_25_Template, 60, 14, \"div\", 6);\n            i0.ɵɵelementStart(26, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_26_listener() {\n              return ctx.toggleMobileMenu();\n            });\n            i0.ɵɵelementStart(27, \"mat-icon\");\n            i0.ɵɵtext(28);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"div\", 8)(30, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_30_listener() {\n              return ctx.navigateToSection(\"hero\");\n            });\n            i0.ɵɵelementStart(31, \"mat-icon\");\n            i0.ɵɵtext(32, \"home\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_34_listener() {\n              return ctx.navigateToSection(\"astrologers\");\n            });\n            i0.ɵɵelementStart(35, \"mat-icon\");\n            i0.ɵɵtext(36, \"people\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(37);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_38_listener() {\n              return ctx.navigateToSection(\"articles\");\n            });\n            i0.ɵɵelementStart(39, \"mat-icon\");\n            i0.ɵɵtext(40, \"article\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(41);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function NavigationComponent_Template_button_click_42_listener() {\n              return ctx.navigateToSection(\"horoscope\");\n            });\n            i0.ɵɵelementStart(43, \"mat-icon\");\n            i0.ɵɵtext(44, \"stars\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(45);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(46, NavigationComponent_div_46_Template, 9, 2, \"div\", 10);\n            i0.ɵɵtemplate(47, NavigationComponent_div_47_Template, 17, 4, \"div\", 11);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.t.nav.brand);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"hero\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.home, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"astrologers\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.astrologers, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"articles\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.articles, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"horoscope\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.horoscope, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated());\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.isMobileMenuOpen);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.isMobileMenuOpen ? \"close\" : \"menu\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"open\", ctx.isMobileMenuOpen);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"hero\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.home, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"astrologers\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.astrologers, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"articles\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.articles, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"active\", ctx.isActiveSection(\"horoscope\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.home.nav.horoscope, \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated());\n          }\n        },\n        dependencies: [i4.NgIf, i1.RouterLink, i5.MatButton, i5.MatIconButton, i6.MatIcon, i7.MatDivider, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.ThemeSelectorComponent, i4.AsyncPipe],\n        styles: [\".unified-navigation[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;z-index:1000;background:rgba(255,255,255,.95);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-bottom:1px solid var(--theme-accent-light);box-shadow:0 2px 10px #0000001a}.nav-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 20px;display:flex;align-items:center;justify-content:space-between;height:64px}.nav-brand[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:1.5rem;font-weight:700;color:var(--theme-primary);cursor:pointer;transition:color .3s ease}.nav-brand[_ngcontent-%COMP%]:hover{color:var(--theme-accent)}.nav-brand[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;width:28px;height:28px}.nav-links[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.nav-link[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-weight:500;color:var(--theme-text);transition:all .3s ease;border-radius:8px;padding:8px 16px;position:relative}.nav-link[_ngcontent-%COMP%]:hover{background-color:var(--theme-accent-light);color:var(--theme-primary)}.nav-link.active[_ngcontent-%COMP%]{background-color:var(--theme-primary);color:#fff;font-weight:600;box-shadow:0 2px 8px #67455c4d}.nav-link.active[_ngcontent-%COMP%]:hover{background-color:var(--theme-accent);color:#fff}.nav-link.active[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-2px;left:50%;transform:translate(-50%);width:80%;height:2px;background:linear-gradient(90deg,var(--theme-accent),var(--theme-primary));border-radius:1px}.nav-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px}.nav-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.login-btn[_ngcontent-%COMP%], .register-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-weight:500;border-radius:8px;padding:8px 16px}.register-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--theme-primary),var(--theme-accent));color:#fff}.nav-user-menu[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.user-info[_ngcontent-%COMP%]{font-size:.9rem;color:var(--theme-text-secondary);font-weight:500}.user-avatar-button[_ngcontent-%COMP%]{padding:4px}.user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;overflow:hidden;display:flex;align-items:center;justify-content:center;background:var(--theme-primary-light)}.avatar-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%}.default-avatar[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;color:var(--theme-primary)}.mobile-menu-toggle[_ngcontent-%COMP%]{display:none;color:var(--theme-primary)}.mobile-menu-toggle.active[_ngcontent-%COMP%]{color:var(--theme-accent)}.mobile-menu[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;background:rgba(255,255,255,.98);-webkit-backdrop-filter:blur(15px);backdrop-filter:blur(15px);border-bottom:1px solid var(--theme-accent-light);box-shadow:0 4px 20px #0000001a;transform:translateY(-100%);opacity:0;visibility:hidden;transition:all .3s ease;padding:20px;display:flex;flex-direction:column;gap:8px}.mobile-menu.open[_ngcontent-%COMP%]{transform:translateY(0);opacity:1;visibility:visible}.mobile-nav-link[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px 16px;border-radius:8px;font-weight:500;color:var(--theme-text);transition:all .3s ease;justify-content:flex-start;width:100%;position:relative}.mobile-nav-link[_ngcontent-%COMP%]:hover{background-color:var(--theme-accent-light);color:var(--theme-primary)}.mobile-nav-link.active[_ngcontent-%COMP%]{background-color:var(--theme-primary);color:#fff;font-weight:600;box-shadow:0 2px 8px #67455c4d}.mobile-nav-link.active[_ngcontent-%COMP%]:hover{background-color:var(--theme-accent);color:#fff}.mobile-nav-link.active[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:0;top:50%;transform:translateY(-50%);width:4px;height:60%;background:linear-gradient(180deg,var(--theme-accent),white);border-radius:0 2px 2px 0}.mobile-actions[_ngcontent-%COMP%], .mobile-user-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-top:16px;padding-top:16px;border-top:1px solid var(--theme-accent-light)}.mobile-login-btn[_ngcontent-%COMP%], .mobile-register-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;padding:12px 24px;border-radius:8px;font-weight:500;width:100%}.mobile-register-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--theme-primary),var(--theme-accent));color:#fff}.user-menu[_ngcontent-%COMP%]{margin-top:8px}.user-menu[_ngcontent-%COMP%]   .mat-menu-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px 16px;font-weight:500}.submenu-arrow[_ngcontent-%COMP%]{margin-left:auto;font-size:18px}.theme-menu-content[_ngcontent-%COMP%]{padding:16px;min-width:200px}@media (max-width: 768px){.nav-links[_ngcontent-%COMP%], .nav-actions[_ngcontent-%COMP%], .nav-user-menu[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]{display:none}.mobile-menu-toggle[_ngcontent-%COMP%]{display:flex}.nav-container[_ngcontent-%COMP%]{padding:0 16px}.nav-brand[_ngcontent-%COMP%]{font-size:1.3rem}.nav-brand[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}}@media (max-width: 480px){.nav-container[_ngcontent-%COMP%]{padding:0 12px}.nav-brand[_ngcontent-%COMP%]{font-size:1.2rem}.mobile-menu[_ngcontent-%COMP%]{padding:16px}}.dark-theme[_ngcontent-%COMP%]   .unified-navigation[_ngcontent-%COMP%]{background:rgba(30,30,30,.95);border-bottom-color:var(--theme-accent-dark)}.dark-theme[_ngcontent-%COMP%]   .mobile-menu[_ngcontent-%COMP%]{background:rgba(30,30,30,.98);border-bottom-color:var(--theme-accent-dark)}.nav-link[_ngcontent-%COMP%], .mobile-nav-link[_ngcontent-%COMP%], .login-btn[_ngcontent-%COMP%], .register-btn[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.nav-link[_ngcontent-%COMP%]:focus, .mobile-nav-link[_ngcontent-%COMP%]:focus, .login-btn[_ngcontent-%COMP%]:focus, .register-btn[_ngcontent-%COMP%]:focus{outline:2px solid var(--theme-accent);outline-offset:2px}body[_ngcontent-%COMP%]{padding-top:64px}\"]\n      });\n    }\n  }\n  return NavigationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}