{"ast": null, "code": "import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { AppComponent } from './app.component';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n// Authentication Components\nimport { LoginComponent } from './auth/login/login.component';\nimport { RegisterComponent } from './auth/register/register.component';\n// Dashboard Component\nimport { DashboardComponent } from './dashboard/dashboard.component';\n// Profile Module\nimport { ProfileModule } from './profile/profile.module';\n// Shared Module\nimport { SharedModule } from './shared/shared.module';\n// Profile Demo Component\nimport { ProfileDemoComponent } from './profile-demo/profile-demo.component';\nimport { ProfileCardDemoComponent } from './profile-card-demo/profile-card-demo.component';\n// Home Component\nimport { HomeComponent } from './home/<USER>';\n// Test Components\nimport { TestApiConnectionComponent } from './test-api-connection.component';\nimport { TestArticlePreviewComponent } from './test-article-preview/test-article-preview.component';\n// Services and Guards\nimport { AuthService } from './auth/services/auth.service';\nimport { OAuthService } from './auth/services/oauth.service';\nimport { ThemeService } from './core/theme/theme.service';\nimport { AuthGuard, RoleGuard } from './auth/guards/auth.guard';\nimport { AuthInterceptor } from './auth/interceptors/auth.interceptor';\n// Angular Material Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {\n    static {\n      this.ɵfac = function AppModule_Factory(t) {\n        return new (t || AppModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppModule,\n        bootstrap: [AppComponent]\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [AuthService, OAuthService, ThemeService, AuthGuard, RoleGuard, {\n          provide: HTTP_INTERCEPTORS,\n          useClass: AuthInterceptor,\n          multi: true\n        }],\n        imports: [BrowserModule, CommonModule, HttpClientModule, BrowserAnimationsModule, ReactiveFormsModule, FormsModule, ProfileModule, SharedModule, RouterModule.forRoot([{\n          path: 'home',\n          component: HomeComponent\n        }, {\n          path: 'login',\n          component: LoginComponent\n        }, {\n          path: 'register',\n          component: RegisterComponent\n        }, {\n          path: 'dashboard',\n          component: DashboardComponent,\n          canActivate: [AuthGuard]\n        }, {\n          path: 'profile-demo',\n          component: ProfileDemoComponent,\n          canActivate: [AuthGuard]\n        }, {\n          path: 'profile-card-demo',\n          component: ProfileCardDemoComponent,\n          canActivate: [AuthGuard]\n        }, {\n          path: 'test-api',\n          component: TestApiConnectionComponent\n        }, {\n          path: 'test-article-preview',\n          component: TestArticlePreviewComponent\n        }, {\n          path: 'articles',\n          loadChildren: () => import('./articles/articles.module').then(m => m.ArticlesModule)\n        }, {\n          path: '',\n          redirectTo: '/home',\n          pathMatch: 'full'\n        }, {\n          path: '**',\n          redirectTo: '/home'\n        }]),\n        // Material Modules\n        MatToolbarModule, MatButtonModule, MatCardModule, MatTableModule, MatIconModule, MatProgressSpinnerModule, MatProgressBarModule, MatSnackBarModule, MatDialogModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatCheckboxModule, MatSidenavModule, MatListModule, MatMenuModule, MatTabsModule, MatDividerModule, MatChipsModule, MatDatepickerModule, MatNativeDateModule]\n      });\n    }\n  }\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}