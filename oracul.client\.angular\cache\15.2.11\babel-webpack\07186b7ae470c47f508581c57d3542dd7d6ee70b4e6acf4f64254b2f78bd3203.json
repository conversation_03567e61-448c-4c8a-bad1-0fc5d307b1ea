{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nfunction ArticlePreviewDialogComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r0.getImageUrl(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.data.title);\n  }\n}\nfunction ArticlePreviewDialogComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.data.category);\n  }\n}\nfunction ArticlePreviewDialogComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.data.content, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ArticlePreviewDialogComponent_div_28_mat_chip_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r5 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r5, \" \");\n  }\n}\nfunction ArticlePreviewDialogComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"h4\");\n    i0.ɵɵtext(2, \"\\u0422\\u0430\\u0433\\u043E\\u0432\\u0435:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵtemplate(4, ArticlePreviewDialogComponent_div_28_mat_chip_4_Template, 2, 1, \"mat-chip\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.data.tags);\n  }\n}\nexport let ArticlePreviewDialogComponent = /*#__PURE__*/(() => {\n  class ArticlePreviewDialogComponent {\n    constructor(dialogRef, data) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n    }\n    onClose() {\n      this.dialogRef.close();\n    }\n    calculateReadTime(content) {\n      if (!content) return 0;\n      // Remove HTML tags for word count\n      const textContent = content.replace(/<[^>]*>/g, '');\n      const words = textContent.trim().split(/\\s+/).length;\n      // Average reading speed is 200-250 words per minute\n      const wordsPerMinute = 225;\n      const readTime = Math.ceil(words / wordsPerMinute);\n      return Math.max(1, readTime); // Minimum 1 minute\n    }\n\n    getReadTime() {\n      return this.data.estimatedReadTime || this.calculateReadTime(this.data.content);\n    }\n    getImageUrl() {\n      if (!this.data.featuredImageUrl) {\n        return 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop';\n      }\n      if (this.data.featuredImageUrl.startsWith('http')) {\n        return this.data.featuredImageUrl;\n      }\n      // Assume it's a relative path from our API\n      return `${environment.apiUrl.replace('/api', '')}${this.data.featuredImageUrl}`;\n    }\n    static {\n      this.ɵfac = function ArticlePreviewDialogComponent_Factory(t) {\n        return new (t || ArticlePreviewDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ArticlePreviewDialogComponent,\n        selectors: [[\"app-article-preview-dialog\"]],\n        decls: 36,\n        vars: 9,\n        consts: [[1, \"preview-dialog\"], [1, \"dialog-header\"], [\"mat-dialog-title\", \"\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [\"mat-dialog-content\", \"\", 1, \"dialog-content\"], [1, \"article-preview\"], [\"class\", \"article-image\", 4, \"ngIf\"], [1, \"article-header\"], [1, \"article-meta\"], [\"class\", \"category\", 4, \"ngIf\"], [1, \"read-time\"], [1, \"article-title\"], [1, \"article-info\"], [1, \"author\"], [1, \"publish-date\"], [1, \"article-excerpt\"], [\"class\", \"article-content\", 4, \"ngIf\"], [\"class\", \"article-tags\", 4, \"ngIf\"], [\"mat-dialog-actions\", \"\", 1, \"dialog-actions\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"article-image\"], [1, \"featured-image\", 3, \"src\", \"alt\"], [1, \"category\"], [1, \"article-content\"], [1, \"content-html\", 3, \"innerHTML\"], [1, \"article-tags\"], [1, \"tags-container\"], [\"class\", \"tag-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"tag-chip\"]],\n        template: function ArticlePreviewDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n            i0.ɵɵtext(3, \"\\u041F\\u0440\\u0435\\u0433\\u043B\\u0435\\u0434 \\u043D\\u0430 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function ArticlePreviewDialogComponent_Template_button_click_4_listener() {\n              return ctx.onClose();\n            });\n            i0.ɵɵelementStart(5, \"mat-icon\");\n            i0.ɵɵtext(6, \"close\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"article\", 5);\n            i0.ɵɵtemplate(9, ArticlePreviewDialogComponent_div_9_Template, 2, 2, \"div\", 6);\n            i0.ɵɵelementStart(10, \"header\", 7)(11, \"div\", 8);\n            i0.ɵɵtemplate(12, ArticlePreviewDialogComponent_span_12_Template, 2, 1, \"span\", 9);\n            i0.ɵɵelementStart(13, \"span\", 10)(14, \"mat-icon\");\n            i0.ɵɵtext(15, \"schedule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"h1\", 11);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 12)(20, \"span\", 13);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"span\", 14);\n            i0.ɵɵtext(23);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(24, \"div\", 15)(25, \"p\");\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(27, ArticlePreviewDialogComponent_div_27_Template, 2, 1, \"div\", 16);\n            i0.ɵɵtemplate(28, ArticlePreviewDialogComponent_div_28_Template, 5, 1, \"div\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 18)(30, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function ArticlePreviewDialogComponent_Template_button_click_30_listener() {\n              return ctx.onClose();\n            });\n            i0.ɵɵtext(31, \"\\u0417\\u0430\\u0442\\u0432\\u043E\\u0440\\u0438\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function ArticlePreviewDialogComponent_Template_button_click_32_listener() {\n              return ctx.onClose();\n            });\n            i0.ɵɵelementStart(33, \"mat-icon\");\n            i0.ɵɵtext(34, \"edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(35, \" \\u041F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438 \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435\\u0442\\u043E \");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.data.featuredImageUrl);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.data.category);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getReadTime(), \" \\u043C\\u0438\\u043D \\u0447\\u0435\\u0442\\u0435\\u043D\\u0435 \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.data.title);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\\u043E\\u0442 \", ctx.data.author, \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(\"\\u0414\\u043D\\u0435\\u0441\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.data.excerpt);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.data.content);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.data.tags && ctx.data.tags.length > 0);\n          }\n        },\n        styles: [\".preview-dialog[_ngcontent-%COMP%]{max-width:800px;width:100%}.dialog-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 24px;border-bottom:1px solid #e0e0e0}.dialog-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;color:#3f2f4e}.close-button[_ngcontent-%COMP%]{color:#666}.dialog-content[_ngcontent-%COMP%]{padding:0;max-height:70vh;overflow-y:auto}.article-preview[_ngcontent-%COMP%]{padding:24px}.article-image[_ngcontent-%COMP%]{margin-bottom:24px;border-radius:8px;overflow:hidden}.featured-image[_ngcontent-%COMP%]{width:100%;height:300px;object-fit:cover;display:block}.article-header[_ngcontent-%COMP%]{margin-bottom:24px}.article-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;margin-bottom:12px}.category[_ngcontent-%COMP%]{background-color:#e6dbec;color:#3f2f4e;padding:4px 12px;border-radius:16px;font-size:12px;font-weight:500;text-transform:uppercase}.read-time[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;color:#666;font-size:14px}.read-time[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.article-title[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#3f2f4e;line-height:1.2;margin:0 0 16px}.article-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;color:#666;font-size:14px}.author[_ngcontent-%COMP%]{font-weight:500}.article-excerpt[_ngcontent-%COMP%]{margin-bottom:24px;padding:16px;background-color:#f8f9fa;border-left:4px solid #d2a6d0;border-radius:4px}.article-excerpt[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:16px;line-height:1.6;color:#555;font-style:italic}.article-content[_ngcontent-%COMP%]{margin-bottom:32px}.content-html[_ngcontent-%COMP%]{font-size:16px;line-height:1.7;color:#333}.content-html[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#3f2f4e;margin-top:24px;margin-bottom:12px}.content-html[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:28px}.content-html[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px}.content-html[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px}.content-html[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:16px}.content-html[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .content-html[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin-bottom:16px;padding-left:24px}.content-html[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px}.content-html[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]{border-left:4px solid #d2a6d0;padding-left:16px;margin:16px 0;font-style:italic;color:#666}.content-html[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;border-radius:8px;margin:16px 0}.content-html[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:2px 6px;border-radius:4px;font-family:Courier New,monospace;font-size:14px}.content-html[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:8px;overflow-x:auto;margin:16px 0}.article-tags[_ngcontent-%COMP%]{border-top:1px solid #e0e0e0;padding-top:24px}.article-tags[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#3f2f4e;font-size:16px}.tags-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px}.tag-chip[_ngcontent-%COMP%]{background-color:#e6dbec;color:#3f2f4e}.dialog-actions[_ngcontent-%COMP%]{padding:16px 24px;border-top:1px solid #e0e0e0;gap:8px}@media (max-width: 768px){.preview-dialog[_ngcontent-%COMP%]{max-width:100vw;height:100vh}.dialog-content[_ngcontent-%COMP%]{max-height:calc(100vh - 120px)}.article-preview[_ngcontent-%COMP%]{padding:16px}.article-title[_ngcontent-%COMP%]{font-size:24px}.featured-image[_ngcontent-%COMP%]{height:200px}}\"]\n      });\n    }\n  }\n  return ArticlePreviewDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}