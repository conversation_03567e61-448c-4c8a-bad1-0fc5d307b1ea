import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { Article } from '../../shared/models/article.models';
import { ArticleService } from '../../shared/services/article.service';
import { AuthService } from '../../auth/services/auth.service';
import { TranslationService } from '../../core/i18n/translation.service';

@Component({
  selector: 'app-article-view',
  templateUrl: './article-view.component.html',
  styleUrls: ['./article-view.component.css']
})
export class ArticleViewComponent implements OnInit, OnDestroy {
  article: Article | null = null;
  isLoading = true;
  isAuthenticated = false;
  showAuthPrompt = false;
  error: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private articleService: ArticleService,
    private authService: AuthService,
    public t: TranslationService
  ) {}

  ngOnInit(): void {
    // Get authentication state and article slug
    combineLatest([
      this.authService.isAuthenticated$,
      this.route.params
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([isAuthenticated, params]) => {
      this.isAuthenticated = isAuthenticated;
      const slug = params['slug'];
      if (slug) {
        this.loadArticle(slug);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadArticle(slug: string): void {
    this.isLoading = true;
    this.error = null;

    this.articleService.getArticleBySlug(slug).subscribe({
      next: (article) => {
        this.article = article;
        this.isLoading = false;

        // Record article view for analytics
        this.articleService.recordArticleView(article.id).subscribe();

        // Show auth prompt for anonymous users if content is limited
        if (!this.isAuthenticated && (!article.content || article.content.length < 500)) {
          this.showAuthPrompt = true;
        }
      },
      error: (error) => {
        this.error = error.message;
        this.isLoading = false;
        console.error('Error loading article:', error);
      }
    });
  }

  onLoginClick(): void {
    this.router.navigate(['/login'], {
      queryParams: { returnUrl: this.router.url }
    });
  }

  onRegisterClick(): void {
    this.router.navigate(['/register'], {
      queryParams: { returnUrl: this.router.url }
    });
  }

  onCloseAuthPrompt(): void {
    this.showAuthPrompt = false;
  }

  goBack(): void {
    this.router.navigate(['/home']);
  }

  shareArticle(): void {
    if (navigator.share && this.article) {
      navigator.share({
        title: this.article.title,
        text: this.article.excerpt,
        url: window.location.href
      }).catch(console.error);
    } else if (this.article) {
      // Fallback: copy URL to clipboard
      navigator.clipboard.writeText(window.location.href).then(() => {
        // Could show a toast notification here
        console.log('URL copied to clipboard');
      }).catch(console.error);
    }
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('bg-BG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getContentPreview(): string {
    if (!this.article?.content) return '';

    // For authenticated users, show full content
    if (this.isAuthenticated) {
      return this.article.content;
    }

    // For anonymous users, show limited content
    const content = this.article.content;
    const words = content.split(' ');

    // Show first 150 words or first 3 paragraphs, whichever is shorter
    const paragraphs = content.split('\n\n');
    const firstThreeParagraphs = paragraphs.slice(0, 3).join('\n\n');
    const first150Words = words.slice(0, 150).join(' ');

    return firstThreeParagraphs.length < first150Words.length
      ? firstThreeParagraphs
      : first150Words;
  }

  shouldShowBlur(): boolean {
    return !this.isAuthenticated && !!this.article?.content && this.article.content.length > 500;
  }

  shouldShowAuthPrompt(): boolean {
    return !this.isAuthenticated && !!this.article?.content && this.article.content.length > 300;
  }

  getContentCompletionPercentage(): number {
    if (!this.article?.content || this.isAuthenticated) return 100;

    const previewLength = this.getContentPreview().length;
    const totalLength = this.article.content.length;

    return Math.round((previewLength / totalLength) * 100);
  }
}
