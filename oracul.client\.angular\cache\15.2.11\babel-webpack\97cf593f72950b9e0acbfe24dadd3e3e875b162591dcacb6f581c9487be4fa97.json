{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ArticleService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/article`;\n  }\n  /**\r\n   * Get featured articles for home page\r\n   */\n  getFeaturedArticles(count = 6) {\n    const params = new HttpParams().set('count', count.toString());\n    return this.http.get(`${this.API_URL}/featured`, {\n      params\n    }).pipe(map(response => response.articles), catchError(error => {\n      console.error('Error loading featured articles from database:', error);\n      throw error;\n    }));\n  }\n  /**\r\n   * Get article by slug - returns full content for authenticated users,\r\n   * limited content for anonymous users\r\n   */\n  getArticleBySlug(slug) {\n    return this.http.get(`${this.API_URL}/slug/${slug}`).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Get article preview by slug - always returns limited content\r\n   */\n  getArticlePreview(slug) {\n    return this.http.get(`${this.API_URL}/preview/${slug}`).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Search articles with filters\r\n   */\n  searchArticles(request) {\n    return this.http.post(`${this.API_URL}/search`, request).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Get articles by category\r\n   */\n  getArticlesByCategory(category, page = 1, pageSize = 10) {\n    const params = new HttpParams().set('category', category).set('page', page.toString()).set('pageSize', pageSize.toString());\n    return this.http.get(`${this.API_URL}/category`, {\n      params\n    }).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Get articles by author\r\n   */\n  getArticlesByAuthor(authorId, page = 1, pageSize = 10) {\n    const params = new HttpParams().set('authorId', authorId.toString()).set('page', page.toString()).set('pageSize', pageSize.toString());\n    return this.http.get(`${this.API_URL}/author`, {\n      params\n    }).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Record article view for analytics\r\n   */\n  recordArticleView(articleId) {\n    return this.http.post(`${this.API_URL}/${articleId}/view`, {}).pipe(catchError(this.handleError));\n  }\n  // Article Management Methods\n  /**\r\n   * Get user's articles for management\r\n   */\n  getMyArticles(page = 1, pageSize = 10, status) {\n    let params = new HttpParams().set('page', page.toString()).set('pageSize', pageSize.toString());\n    if (status) {\n      params = params.set('status', status);\n    }\n    return this.http.get(`${this.API_URL}/my-articles`, {\n      params\n    }).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Get article by ID for editing\r\n   */\n  getArticleForEdit(articleId) {\n    return this.http.get(`${this.API_URL}/${articleId}`).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Create a new article\r\n   */\n  createArticle(request) {\n    return this.http.post(`${this.API_URL}`, request).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Update an existing article\r\n   */\n  updateArticle(articleId, request) {\n    return this.http.put(`${this.API_URL}/${articleId}`, request).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Delete an article\r\n   */\n  deleteArticle(articleId) {\n    return this.http.delete(`${this.API_URL}/${articleId}`).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Publish an article\r\n   */\n  publishArticle(articleId, publishAt) {\n    const request = publishAt ? {\n      publishAt\n    } : {};\n    return this.http.post(`${this.API_URL}/${articleId}/publish`, request).pipe(catchError(this.handleError));\n  }\n  /**\r\n   * Unpublish an article (set to draft)\r\n   */\n  unpublishArticle(articleId) {\n    return this.http.post(`${this.API_URL}/${articleId}/unpublish`, {}).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    let errorMessage = 'Възникна неочаквана грешка при зареждането на статиите';\n    if (error.error && error.error.message) {\n      errorMessage = error.error.message;\n    } else if (error.status === 0) {\n      errorMessage = 'Няма връзка със сървъра';\n    } else if (error.status === 404) {\n      errorMessage = 'Статията не е намерена';\n    } else if (error.status === 401) {\n      errorMessage = 'Необходима е автентификация за достъп до пълното съдържание';\n    } else if (error.status === 403) {\n      errorMessage = 'Нямате права за достъп до това съдържание';\n    } else if (error.status >= 500) {\n      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\n    }\n    console.error('Article service error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function ArticleService_Factory(t) {\n      return new (t || ArticleService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ArticleService,\n      factory: ArticleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAwCA,UAAU,QAAQ,sBAAsB;AAChF,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,mCAAmC;;;AAa/D,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IAFP,YAAO,GAAG,GAAGH,WAAW,CAACI,MAAM,UAAU;EAEnB;EAEvC;;;EAGAC,mBAAmB,CAACC,QAAgB,CAAC;IACnC,MAAMC,MAAM,GAAG,IAAIX,UAAU,EAAE,CAACY,GAAG,CAAC,OAAO,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC;IAC9D,OAAO,IAAI,CAACN,IAAI,CAACO,GAAG,CAAsB,GAAG,IAAI,CAACC,OAAO,WAAW,EAAE;MAAEJ;IAAM,CAAE,CAAC,CAC9EK,IAAI,CACHb,GAAG,CAACc,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,EAClChB,UAAU,CAACiB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAIA;;;;EAIAE,gBAAgB,CAACC,IAAY;IAC3B,OAAO,IAAI,CAACf,IAAI,CAACO,GAAG,CAAU,GAAG,IAAI,CAACC,OAAO,SAASO,IAAI,EAAE,CAAC,CAC1DN,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAC,iBAAiB,CAACF,IAAY;IAC5B,OAAO,IAAI,CAACf,IAAI,CAACO,GAAG,CAAiB,GAAG,IAAI,CAACC,OAAO,YAAYO,IAAI,EAAE,CAAC,CACpEN,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAE,cAAc,CAACC,OAA6B;IAC1C,OAAO,IAAI,CAACnB,IAAI,CAACoB,IAAI,CAAsB,GAAG,IAAI,CAACZ,OAAO,SAAS,EAAEW,OAAO,CAAC,CAC1EV,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAK,qBAAqB,CAACC,QAAgB,EAAEC,OAAe,CAAC,EAAEC,WAAmB,EAAE;IAC7E,MAAMpB,MAAM,GAAG,IAAIX,UAAU,EAAE,CAC5BY,GAAG,CAAC,UAAU,EAAEiB,QAAQ,CAAC,CACzBjB,GAAG,CAAC,MAAM,EAAEkB,IAAI,CAACjB,QAAQ,EAAE,CAAC,CAC5BD,GAAG,CAAC,UAAU,EAAEmB,QAAQ,CAAClB,QAAQ,EAAE,CAAC;IAEvC,OAAO,IAAI,CAACN,IAAI,CAACO,GAAG,CAAsB,GAAG,IAAI,CAACC,OAAO,WAAW,EAAE;MAAEJ;IAAM,CAAE,CAAC,CAC9EK,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAS,mBAAmB,CAACC,QAAgB,EAAEH,OAAe,CAAC,EAAEC,WAAmB,EAAE;IAC3E,MAAMpB,MAAM,GAAG,IAAIX,UAAU,EAAE,CAC5BY,GAAG,CAAC,UAAU,EAAEqB,QAAQ,CAACpB,QAAQ,EAAE,CAAC,CACpCD,GAAG,CAAC,MAAM,EAAEkB,IAAI,CAACjB,QAAQ,EAAE,CAAC,CAC5BD,GAAG,CAAC,UAAU,EAAEmB,QAAQ,CAAClB,QAAQ,EAAE,CAAC;IAEvC,OAAO,IAAI,CAACN,IAAI,CAACO,GAAG,CAAsB,GAAG,IAAI,CAACC,OAAO,SAAS,EAAE;MAAEJ;IAAM,CAAE,CAAC,CAC5EK,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAW,iBAAiB,CAACC,SAAiB;IACjC,OAAO,IAAI,CAAC5B,IAAI,CAACoB,IAAI,CAAO,GAAG,IAAI,CAACZ,OAAO,IAAIoB,SAAS,OAAO,EAAE,EAAE,CAAC,CACjEnB,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;EAEA;;;EAGAa,aAAa,CAACN,OAAe,CAAC,EAAEC,WAAmB,EAAE,EAAEM,MAAe;IACpE,IAAI1B,MAAM,GAAG,IAAIX,UAAU,EAAE,CAC1BY,GAAG,CAAC,MAAM,EAAEkB,IAAI,CAACjB,QAAQ,EAAE,CAAC,CAC5BD,GAAG,CAAC,UAAU,EAAEmB,QAAQ,CAAClB,QAAQ,EAAE,CAAC;IAEvC,IAAIwB,MAAM,EAAE;MACV1B,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAEyB,MAAM,CAAC;;IAGvC,OAAO,IAAI,CAAC9B,IAAI,CAACO,GAAG,CAAgC,GAAG,IAAI,CAACC,OAAO,cAAc,EAAE;MAAEJ;IAAM,CAAE,CAAC,CAC3FK,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAe,iBAAiB,CAACH,SAAiB;IACjC,OAAO,IAAI,CAAC5B,IAAI,CAACO,GAAG,CAAuB,GAAG,IAAI,CAACC,OAAO,IAAIoB,SAAS,EAAE,CAAC,CACvEnB,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAgB,aAAa,CAACb,OAAY;IACxB,OAAO,IAAI,CAACnB,IAAI,CAACoB,IAAI,CAAuB,GAAG,IAAI,CAACZ,OAAO,EAAE,EAAEW,OAAO,CAAC,CACpEV,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAiB,aAAa,CAACL,SAAiB,EAAET,OAAY;IAC3C,OAAO,IAAI,CAACnB,IAAI,CAACkC,GAAG,CAAuB,GAAG,IAAI,CAAC1B,OAAO,IAAIoB,SAAS,EAAE,EAAET,OAAO,CAAC,CAChFV,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAmB,aAAa,CAACP,SAAiB;IAC7B,OAAO,IAAI,CAAC5B,IAAI,CAACoC,MAAM,CAAO,GAAG,IAAI,CAAC5B,OAAO,IAAIoB,SAAS,EAAE,CAAC,CAC1DnB,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAqB,cAAc,CAACT,SAAiB,EAAEU,SAAgB;IAChD,MAAMnB,OAAO,GAAGmB,SAAS,GAAG;MAAEA;IAAS,CAAE,GAAG,EAAE;IAC9C,OAAO,IAAI,CAACtC,IAAI,CAACoB,IAAI,CAAuB,GAAG,IAAI,CAACZ,OAAO,IAAIoB,SAAS,UAAU,EAAET,OAAO,CAAC,CACzFV,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAEA;;;EAGAuB,gBAAgB,CAACX,SAAiB;IAChC,OAAO,IAAI,CAAC5B,IAAI,CAACoB,IAAI,CAAuB,GAAG,IAAI,CAACZ,OAAO,IAAIoB,SAAS,YAAY,EAAE,EAAE,CAAC,CACtFnB,IAAI,CAACd,UAAU,CAAC,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EAIQA,WAAW,CAACJ,KAAwB;IAC1C,IAAI4B,YAAY,GAAG,wDAAwD;IAE3E,IAAI5B,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC6B,OAAO,EAAE;MACtCD,YAAY,GAAG5B,KAAK,CAACA,KAAK,CAAC6B,OAAO;KACnC,MAAM,IAAI7B,KAAK,CAACkB,MAAM,KAAK,CAAC,EAAE;MAC7BU,YAAY,GAAG,yBAAyB;KACzC,MAAM,IAAI5B,KAAK,CAACkB,MAAM,KAAK,GAAG,EAAE;MAC/BU,YAAY,GAAG,wBAAwB;KACxC,MAAM,IAAI5B,KAAK,CAACkB,MAAM,KAAK,GAAG,EAAE;MAC/BU,YAAY,GAAG,6DAA6D;KAC7E,MAAM,IAAI5B,KAAK,CAACkB,MAAM,KAAK,GAAG,EAAE;MAC/BU,YAAY,GAAG,2CAA2C;KAC3D,MAAM,IAAI5B,KAAK,CAACkB,MAAM,IAAI,GAAG,EAAE;MAC9BU,YAAY,GAAG,iDAAiD;;IAGlE3B,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAOlB,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAACF,YAAY,CAAC,CAAC;EAClD;;;uBAzKW1C,cAAc;IAAA;EAAA;;;aAAdA,cAAc;MAAA6C,SAAd7C,cAAc;MAAA8C,YAFb;IAAM;EAAA", "names": ["HttpParams", "throwError", "catchError", "map", "environment", "ArticleService", "constructor", "http", "apiUrl", "getFeaturedArticles", "count", "params", "set", "toString", "get", "API_URL", "pipe", "response", "articles", "error", "console", "getArticleBySlug", "slug", "handleError", "getArticlePreview", "searchArticles", "request", "post", "getArticlesByCategory", "category", "page", "pageSize", "getArticlesByAuthor", "authorId", "recordArticleView", "articleId", "getMyArticles", "status", "getArticleForEdit", "createArticle", "updateArticle", "put", "deleteArticle", "delete", "publishArticle", "publishAt", "unpublishArticle", "errorMessage", "message", "Error", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\shared\\services\\article.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, map } from 'rxjs/operators';\r\nimport { environment } from '../../../environments/environment';\r\nimport {\r\n  Article,\r\n  ArticlePreview,\r\n  ArticleListResponse,\r\n  ArticleSearchRequest,\r\n  ArticleManagementDto,\r\n  ArticleManagementListResponse\r\n} from '../models/article.models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ArticleService {\r\n  private readonly API_URL = `${environment.apiUrl}/article`;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  /**\r\n   * Get featured articles for home page\r\n   */\r\n  getFeaturedArticles(count: number = 6): Observable<ArticlePreview[]> {\r\n    const params = new HttpParams().set('count', count.toString());\r\n    return this.http.get<ArticleListResponse>(`${this.API_URL}/featured`, { params })\r\n      .pipe(\r\n        map(response => response.articles),\r\n        catchError(error => {\r\n          console.error('Error loading featured articles from database:', error);\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * Get article by slug - returns full content for authenticated users,\r\n   * limited content for anonymous users\r\n   */\r\n  getArticleBySlug(slug: string): Observable<Article> {\r\n    return this.http.get<Article>(`${this.API_URL}/slug/${slug}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Get article preview by slug - always returns limited content\r\n   */\r\n  getArticlePreview(slug: string): Observable<ArticlePreview> {\r\n    return this.http.get<ArticlePreview>(`${this.API_URL}/preview/${slug}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Search articles with filters\r\n   */\r\n  searchArticles(request: ArticleSearchRequest): Observable<ArticleListResponse> {\r\n    return this.http.post<ArticleListResponse>(`${this.API_URL}/search`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Get articles by category\r\n   */\r\n  getArticlesByCategory(category: string, page: number = 1, pageSize: number = 10): Observable<ArticleListResponse> {\r\n    const params = new HttpParams()\r\n      .set('category', category)\r\n      .set('page', page.toString())\r\n      .set('pageSize', pageSize.toString());\r\n\r\n    return this.http.get<ArticleListResponse>(`${this.API_URL}/category`, { params })\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Get articles by author\r\n   */\r\n  getArticlesByAuthor(authorId: number, page: number = 1, pageSize: number = 10): Observable<ArticleListResponse> {\r\n    const params = new HttpParams()\r\n      .set('authorId', authorId.toString())\r\n      .set('page', page.toString())\r\n      .set('pageSize', pageSize.toString());\r\n\r\n    return this.http.get<ArticleListResponse>(`${this.API_URL}/author`, { params })\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Record article view for analytics\r\n   */\r\n  recordArticleView(articleId: number): Observable<void> {\r\n    return this.http.post<void>(`${this.API_URL}/${articleId}/view`, {})\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  // Article Management Methods\r\n\r\n  /**\r\n   * Get user's articles for management\r\n   */\r\n  getMyArticles(page: number = 1, pageSize: number = 10, status?: string): Observable<ArticleManagementListResponse> {\r\n    let params = new HttpParams()\r\n      .set('page', page.toString())\r\n      .set('pageSize', pageSize.toString());\r\n\r\n    if (status) {\r\n      params = params.set('status', status);\r\n    }\r\n\r\n    return this.http.get<ArticleManagementListResponse>(`${this.API_URL}/my-articles`, { params })\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Get article by ID for editing\r\n   */\r\n  getArticleForEdit(articleId: number): Observable<ArticleManagementDto> {\r\n    return this.http.get<ArticleManagementDto>(`${this.API_URL}/${articleId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Create a new article\r\n   */\r\n  createArticle(request: any): Observable<ArticleManagementDto> {\r\n    return this.http.post<ArticleManagementDto>(`${this.API_URL}`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Update an existing article\r\n   */\r\n  updateArticle(articleId: number, request: any): Observable<ArticleManagementDto> {\r\n    return this.http.put<ArticleManagementDto>(`${this.API_URL}/${articleId}`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Delete an article\r\n   */\r\n  deleteArticle(articleId: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.API_URL}/${articleId}`)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Publish an article\r\n   */\r\n  publishArticle(articleId: number, publishAt?: Date): Observable<ArticleManagementDto> {\r\n    const request = publishAt ? { publishAt } : {};\r\n    return this.http.post<ArticleManagementDto>(`${this.API_URL}/${articleId}/publish`, request)\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n  /**\r\n   * Unpublish an article (set to draft)\r\n   */\r\n  unpublishArticle(articleId: number): Observable<ArticleManagementDto> {\r\n    return this.http.post<ArticleManagementDto>(`${this.API_URL}/${articleId}/unpublish`, {})\r\n      .pipe(catchError(this.handleError));\r\n  }\r\n\r\n\r\n\r\n  private handleError(error: HttpErrorResponse): Observable<never> {\r\n    let errorMessage = 'Възникна неочаквана грешка при зареждането на статиите';\r\n\r\n    if (error.error && error.error.message) {\r\n      errorMessage = error.error.message;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Няма връзка със сървъра';\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Статията не е намерена';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Необходима е автентификация за достъп до пълното съдържание';\r\n    } else if (error.status === 403) {\r\n      errorMessage = 'Нямате права за достъп до това съдържание';\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';\r\n    }\r\n\r\n    console.error('Article service error:', error);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}