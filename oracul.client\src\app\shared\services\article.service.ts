import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import {
  Article,
  ArticlePreview,
  ArticleListResponse,
  ArticleSearchRequest,
  ArticleManagementDto,
  ArticleManagementListResponse
} from '../models/article.models';

@Injectable({
  providedIn: 'root'
})
export class ArticleService {
  private readonly API_URL = `${environment.apiUrl}/article`;

  constructor(private http: HttpClient) {}

  /**
   * Get featured articles for home page
   */
  getFeaturedArticles(count: number = 6): Observable<ArticlePreview[]> {
    const params = new HttpParams().set('count', count.toString());
    return this.http.get<ArticleListResponse>(`${this.API_URL}/featured`, { params })
      .pipe(
        map(response => response.articles),
        catchError(error => {
          console.error('Error loading featured articles from database:', error);
          throw error;
        })
      );
  }



  /**
   * Get article by slug - returns full content for authenticated users,
   * limited content for anonymous users
   */
  getArticleBySlug(slug: string): Observable<Article> {
    return this.http.get<Article>(`${this.API_URL}/slug/${slug}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get article preview by slug - always returns limited content
   */
  getArticlePreview(slug: string): Observable<ArticlePreview> {
    return this.http.get<ArticlePreview>(`${this.API_URL}/preview/${slug}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Search articles with filters
   */
  searchArticles(request: ArticleSearchRequest): Observable<ArticleListResponse> {
    return this.http.post<ArticleListResponse>(`${this.API_URL}/search`, request)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get articles by category
   */
  getArticlesByCategory(category: string, page: number = 1, pageSize: number = 10): Observable<ArticleListResponse> {
    const params = new HttpParams()
      .set('category', category)
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());

    return this.http.get<ArticleListResponse>(`${this.API_URL}/category`, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get articles by author
   */
  getArticlesByAuthor(authorId: number, page: number = 1, pageSize: number = 10): Observable<ArticleListResponse> {
    const params = new HttpParams()
      .set('authorId', authorId.toString())
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());

    return this.http.get<ArticleListResponse>(`${this.API_URL}/author`, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Record article view for analytics
   */
  recordArticleView(articleId: number): Observable<void> {
    return this.http.post<void>(`${this.API_URL}/${articleId}/view`, {})
      .pipe(catchError(this.handleError));
  }

  // Article Management Methods

  /**
   * Get user's articles for management
   */
  getMyArticles(page: number = 1, pageSize: number = 10, status?: string): Observable<ArticleManagementListResponse> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());

    if (status) {
      params = params.set('status', status);
    }

    return this.http.get<ArticleManagementListResponse>(`${this.API_URL}/my-articles`, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get article by ID for editing
   */
  getArticleForEdit(articleId: number): Observable<ArticleManagementDto> {
    return this.http.get<ArticleManagementDto>(`${this.API_URL}/${articleId}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new article
   */
  createArticle(request: any): Observable<ArticleManagementDto> {
    return this.http.post<ArticleManagementDto>(`${this.API_URL}`, request)
      .pipe(catchError(this.handleError));
  }

  /**
   * Update an existing article
   */
  updateArticle(articleId: number, request: any): Observable<ArticleManagementDto> {
    return this.http.put<ArticleManagementDto>(`${this.API_URL}/${articleId}`, request)
      .pipe(catchError(this.handleError));
  }

  /**
   * Delete an article
   */
  deleteArticle(articleId: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${articleId}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Publish an article
   */
  publishArticle(articleId: number, publishAt?: Date): Observable<ArticleManagementDto> {
    const request = publishAt ? { publishAt } : {};
    return this.http.post<ArticleManagementDto>(`${this.API_URL}/${articleId}/publish`, request)
      .pipe(catchError(this.handleError));
  }

  /**
   * Unpublish an article (set to draft)
   */
  unpublishArticle(articleId: number): Observable<ArticleManagementDto> {
    return this.http.post<ArticleManagementDto>(`${this.API_URL}/${articleId}/unpublish`, {})
      .pipe(catchError(this.handleError));
  }



  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Възникна неочаквана грешка при зареждането на статиите';

    if (error.error && error.error.message) {
      errorMessage = error.error.message;
    } else if (error.status === 0) {
      errorMessage = 'Няма връзка със сървъра';
    } else if (error.status === 404) {
      errorMessage = 'Статията не е намерена';
    } else if (error.status === 401) {
      errorMessage = 'Необходима е автентификация за достъп до пълното съдържание';
    } else if (error.status === 403) {
      errorMessage = 'Нямате права за достъп до това съдържание';
    } else if (error.status >= 500) {
      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';
    }

    console.error('Article service error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
