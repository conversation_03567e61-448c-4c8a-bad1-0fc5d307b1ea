{"ast": null, "code": "import { Subject, takeUntil, combineLatest } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../shared/services/article.service\";\nimport * as i3 from \"../../auth/services/auth.service\";\nimport * as i4 from \"../../core/i18n/translation.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/progress-bar\";\nfunction ArticleViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.t.common.loading, \"...\");\n  }\n}\nfunction ArticleViewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"mat-card\", 5)(2, \"mat-card-content\")(3, \"div\", 6)(4, \"mat-icon\", 7);\n    i0.ɵɵtext(5, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_1_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.goBack());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.t.errors.general);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.t.common.back, \" \");\n  }\n}\nfunction ArticleViewComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"img\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r5.article.featuredImageUrl || ctx_r5.article.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r5.article.title);\n  }\n}\nfunction ArticleViewComponent_div_2_div_32_mat_chip_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(tag_r10);\n  }\n}\nfunction ArticleViewComponent_div_2_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, ArticleViewComponent_div_2_div_32_mat_chip_1_Template, 2, 1, \"mat-chip\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.article.tags);\n  }\n}\nfunction ArticleViewComponent_div_2_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ArticleViewComponent_div_2_div_36_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"trending_up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"mat-progress-bar\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u041F\\u0440\\u043E\\u0447\\u0435\\u0442\\u043E\\u0445\\u0442\\u0435 \", ctx_r11.getContentCompletionPercentage(), \"% \\u043E\\u0442 \\u0441\\u0442\\u0430\\u0442\\u0438\\u044F\\u0442\\u0430\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r11.getContentCompletionPercentage());\n  }\n}\nfunction ArticleViewComponent_div_2_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"mat-card\", 38)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\");\n    i0.ɵɵtemplate(10, ArticleViewComponent_div_2_div_36_div_10_Template, 7, 2, \"div\", 39);\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 40)(14, \"div\", 41)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"auto_stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 41)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"psychology\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 41)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"bookmark_added\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 41)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 42)(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"mat-card-actions\", 43)(40, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_div_36_Template_button_click_40_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.onLoginClick());\n    });\n    i0.ɵɵelementStart(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_div_36_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onRegisterClick());\n    });\n    i0.ɵɵelementStart(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_div_36_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onCloseAuthPrompt());\n    });\n    i0.ɵɵelementStart(49, \"mat-icon\");\n    i0.ɵɵtext(50, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.articles.preview.signInToRead, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.articles.preview.joinCommunity, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.isAuthenticated);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.valuableContent);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.unlimitedAccess);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.connectWithAstrologers);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.saveArticles);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.notifications);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.t.articles.preview.socialProof);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.auth.signIn, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.auth.createAccount, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.t.common.close, \" \");\n  }\n}\nfunction ArticleViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.goBack());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"arrow_back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ArticleViewComponent_div_2_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.shareArticle());\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"share\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(9, ArticleViewComponent_div_2_div_9_Template, 2, 2, \"div\", 14);\n    i0.ɵɵelementStart(10, \"div\", 15)(11, \"mat-chip\", 16);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 17)(14, \"span\", 18);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 19);\n    i0.ɵɵtext(17, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 20);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 19);\n    i0.ɵɵtext(21, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 21);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 19);\n    i0.ɵɵtext(25, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 22);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"h1\", 23);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 24);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, ArticleViewComponent_div_2_div_32_Template, 2, 1, \"div\", 25);\n    i0.ɵɵelementStart(33, \"div\", 26);\n    i0.ɵɵelement(34, \"div\", 27);\n    i0.ɵɵtemplate(35, ArticleViewComponent_div_2_div_35_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, ArticleViewComponent_div_2_div_36_Template, 52, 12, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.t.common.back);\n    i0.ɵɵadvance(4);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.t.articles.share);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.article.featuredImageUrl || ctx_r2.article.imageUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.article.category);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.t.articles.author, \": \", ctx_r2.article.author, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(ctx_r2.article.publishedAt));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.article.readTime, \" \", ctx_r2.t.home.minRead, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.article.readCount, \" \", ctx_r2.t.home.views, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.article.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.article.excerpt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.article.tags && ctx_r2.article.tags.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"blurred\", ctx_r2.shouldShowBlur());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getContentPreview(), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shouldShowBlur());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shouldShowAuthPrompt());\n  }\n}\nexport let ArticleViewComponent = /*#__PURE__*/(() => {\n  class ArticleViewComponent {\n    constructor(route, router, articleService, authService, t) {\n      this.route = route;\n      this.router = router;\n      this.articleService = articleService;\n      this.authService = authService;\n      this.t = t;\n      this.article = null;\n      this.isLoading = true;\n      this.isAuthenticated = false;\n      this.showAuthPrompt = false;\n      this.error = null;\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      // Get authentication state and article slug\n      combineLatest([this.authService.isAuthenticated$, this.route.params]).pipe(takeUntil(this.destroy$)).subscribe(([isAuthenticated, params]) => {\n        this.isAuthenticated = isAuthenticated;\n        const slug = params['slug'];\n        if (slug) {\n          this.loadArticle(slug);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadArticle(slug) {\n      this.isLoading = true;\n      this.error = null;\n      this.articleService.getArticleBySlug(slug).subscribe({\n        next: article => {\n          this.article = article;\n          this.isLoading = false;\n          // Record article view for analytics\n          this.articleService.recordArticleView(article.id).subscribe();\n          // Show auth prompt for anonymous users if content is limited\n          if (!this.isAuthenticated && (!article.content || article.content.length < 500)) {\n            this.showAuthPrompt = true;\n          }\n        },\n        error: error => {\n          this.error = error.message;\n          this.isLoading = false;\n          console.error('Error loading article:', error);\n        }\n      });\n    }\n    onLoginClick() {\n      this.router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: this.router.url\n        }\n      });\n    }\n    onRegisterClick() {\n      this.router.navigate(['/register'], {\n        queryParams: {\n          returnUrl: this.router.url\n        }\n      });\n    }\n    onCloseAuthPrompt() {\n      this.showAuthPrompt = false;\n    }\n    goBack() {\n      this.router.navigate(['/home']);\n    }\n    shareArticle() {\n      if (navigator.share && this.article) {\n        navigator.share({\n          title: this.article.title,\n          text: this.article.excerpt,\n          url: window.location.href\n        }).catch(console.error);\n      } else if (this.article) {\n        // Fallback: copy URL to clipboard\n        navigator.clipboard.writeText(window.location.href).then(() => {\n          // Could show a toast notification here\n          console.log('URL copied to clipboard');\n        }).catch(console.error);\n      }\n    }\n    formatDate(date) {\n      return new Date(date).toLocaleDateString('bg-BG', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    }\n    getContentPreview() {\n      if (!this.article?.content) return '';\n      // For authenticated users, show full content\n      if (this.isAuthenticated) {\n        return this.article.content;\n      }\n      // For anonymous users, show limited content\n      const content = this.article.content;\n      const words = content.split(' ');\n      // Show first 150 words or first 3 paragraphs, whichever is shorter\n      const paragraphs = content.split('\\n\\n');\n      const firstThreeParagraphs = paragraphs.slice(0, 3).join('\\n\\n');\n      const first150Words = words.slice(0, 150).join(' ');\n      return firstThreeParagraphs.length < first150Words.length ? firstThreeParagraphs : first150Words;\n    }\n    shouldShowBlur() {\n      return !this.isAuthenticated && !!this.article?.content && this.article.content.length > 500;\n    }\n    shouldShowAuthPrompt() {\n      return !this.isAuthenticated && !!this.article?.content && this.article.content.length > 300;\n    }\n    getContentCompletionPercentage() {\n      if (!this.article?.content || this.isAuthenticated) return 100;\n      const previewLength = this.getContentPreview().length;\n      const totalLength = this.article.content.length;\n      return Math.round(previewLength / totalLength * 100);\n    }\n    static {\n      this.ɵfac = function ArticleViewComponent_Factory(t) {\n        return new (t || ArticleViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ArticleService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.TranslationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ArticleViewComponent,\n        selectors: [[\"app-article-view\"]],\n        decls: 3,\n        vars: 3,\n        consts: [[\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"article-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"error-container\"], [1, \"error-card\"], [1, \"error-content\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"article-container\"], [1, \"article-header\"], [\"mat-icon-button\", \"\", 1, \"back-button\", 3, \"click\"], [1, \"article-actions\"], [\"mat-icon-button\", \"\", 3, \"click\"], [\"class\", \"featured-image\", 4, \"ngIf\"], [1, \"article-meta\"], [1, \"category-chip\"], [1, \"meta-info\"], [1, \"author\"], [1, \"separator\"], [1, \"date\"], [1, \"read-time\"], [1, \"read-count\"], [1, \"article-title\"], [1, \"article-excerpt\"], [\"class\", \"article-tags\", 4, \"ngIf\"], [1, \"article-content-wrapper\"], [1, \"article-content\", 3, \"innerHTML\"], [\"class\", \"blur-overlay\", 4, \"ngIf\"], [\"class\", \"auth-prompt-overlay\", 4, \"ngIf\"], [1, \"featured-image\"], [1, \"article-featured-image\", 3, \"src\", \"alt\"], [1, \"article-tags\"], [\"class\", \"tag-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"tag-chip\"], [1, \"blur-overlay\"], [1, \"fade-gradient\"], [1, \"auth-prompt-overlay\"], [1, \"auth-prompt-card\"], [\"class\", \"reading-progress\", 4, \"ngIf\"], [1, \"auth-benefits\"], [1, \"benefit-item\"], [1, \"social-proof\"], [1, \"auth-actions\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"reading-progress\"], [1, \"progress-info\"], [\"mode\", \"determinate\", \"color\", \"accent\", 3, \"value\"]],\n        template: function ArticleViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ArticleViewComponent_div_0_Template, 4, 1, \"div\", 0);\n            i0.ɵɵtemplate(1, ArticleViewComponent_div_1_Template, 14, 3, \"div\", 1);\n            i0.ɵɵtemplate(2, ArticleViewComponent_div_2_Template, 37, 19, \"div\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.article && !ctx.isLoading && !ctx.error);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.MatChip, i10.MatProgressSpinner, i11.MatProgressBar],\n        styles: [\".loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;padding:2rem}.error-card[_ngcontent-%COMP%]{max-width:500px;margin:2rem auto}.error-content[_ngcontent-%COMP%]{text-align:center}.error-icon[_ngcontent-%COMP%]{font-size:3rem;color:var(--theme-error);margin-bottom:1rem}.article-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:1rem;background:linear-gradient(135deg,var(--theme-background) 0%,var(--theme-accent-light) 100%);min-height:100vh;position:relative}.article-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:200px;background:linear-gradient(135deg,rgba(210,166,208,.1) 0%,transparent 100%);pointer-events:none;z-index:0}.article-container[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{position:relative;z-index:1}.article-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem;padding:.5rem 0}.back-button[_ngcontent-%COMP%]{color:var(--theme-primary)}.article-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem}.featured-image[_ngcontent-%COMP%]{width:100%;margin-bottom:2rem;border-radius:16px;overflow:hidden;box-shadow:0 8px 32px #67455c26,0 4px 16px #d2a6d01a;position:relative}.featured-image[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,rgba(210,166,208,.1) 0%,transparent 50%,rgba(103,69,92,.1) 100%);pointer-events:none}.article-featured-image[_ngcontent-%COMP%]{width:100%;height:300px;object-fit:cover;display:block;transition:transform .3s ease}.featured-image[_ngcontent-%COMP%]:hover   .article-featured-image[_ngcontent-%COMP%]{transform:scale(1.02)}.article-meta[_ngcontent-%COMP%]{margin-bottom:1.5rem}.category-chip[_ngcontent-%COMP%]{background:var(--theme-accent);color:var(--theme-text-primary);margin-bottom:1rem}.meta-info[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;align-items:center;gap:.5rem;color:var(--theme-text-secondary);font-size:.9rem}.separator[_ngcontent-%COMP%]{color:var(--theme-text-disabled)}.article-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;line-height:1.2;color:var(--theme-text-primary);margin-bottom:1rem}.article-excerpt[_ngcontent-%COMP%]{font-size:1.2rem;line-height:1.6;color:var(--theme-text-secondary);margin-bottom:2rem;font-style:italic;padding:1rem;background:var(--theme-accent-light);border-radius:8px;border-left:4px solid var(--theme-accent)}.article-tags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;margin-bottom:2rem}.tag-chip[_ngcontent-%COMP%]{background:var(--theme-primary-light);color:#fff;font-size:.8rem}.article-content-wrapper[_ngcontent-%COMP%]{position:relative;margin-bottom:2rem;overflow:hidden;border-radius:12px}.article-content[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.8;color:var(--theme-text-primary);padding:1.5rem;background:var(--theme-surface);border-radius:12px;box-shadow:0 2px 8px #0000000d}.article-content-wrapper.blurred[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]{filter:blur(2px);pointer-events:none;-webkit-user-select:none;user-select:none;transition:filter .3s ease}.blur-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;pointer-events:none;z-index:1}.fade-gradient[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;height:250px;background:linear-gradient(to bottom,transparent 0%,rgba(230,219,236,.3) 20%,rgba(230,219,236,.6) 40%,rgba(230,219,236,.8) 60%,rgba(230,219,236,.95) 80%,var(--theme-accent-light) 100%);border-radius:0 0 12px 12px}.blur-overlay[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:radial-gradient(ellipse at center bottom,rgba(210,166,208,.1) 0%,transparent 70%);pointer-events:none}.auth-prompt-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(0,0,0,.6);display:flex;align-items:center;justify-content:center;z-index:1000;padding:1rem}.auth-prompt-card[_ngcontent-%COMP%]{max-width:500px;width:100%;background:var(--theme-surface);border-radius:16px;box-shadow:0 8px 32px #0003}.auth-prompt-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:var(--theme-primary);font-size:1.5rem}.auth-prompt-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--theme-accent)}.auth-benefits[_ngcontent-%COMP%]{margin:1.5rem 0}.benefit-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:1rem;color:var(--theme-text-primary)}.benefit-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--theme-accent);font-size:1.2rem}.auth-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap;justify-content:center;padding:1rem}.auth-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.reading-progress[_ngcontent-%COMP%]{margin-bottom:1.5rem;padding:1rem;background:var(--theme-accent-light);border-radius:8px;border:1px solid var(--theme-accent)}.progress-info[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin-bottom:.5rem;font-weight:500;color:var(--theme-primary)}.progress-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--theme-accent)}.social-proof[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-top:1.5rem;padding:1rem;background:linear-gradient(135deg,var(--theme-accent-light) 0%,var(--theme-background) 100%);border-radius:8px;font-style:italic;color:var(--theme-text-secondary)}.social-proof[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--theme-accent)}@media (max-width: 768px){.article-container[_ngcontent-%COMP%]{padding:.5rem}.article-title[_ngcontent-%COMP%]{font-size:2rem}.article-excerpt[_ngcontent-%COMP%]{font-size:1.1rem;padding:.75rem}.article-content[_ngcontent-%COMP%]{font-size:1rem}.meta-info[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.25rem}.separator[_ngcontent-%COMP%]{display:none}.auth-actions[_ngcontent-%COMP%]{flex-direction:column}.auth-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}.article-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--theme-primary);margin:2rem 0 1rem;font-size:1.5rem}.article-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--theme-primary-light);margin:1.5rem 0 .75rem;font-size:1.3rem}.article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:1.5rem}.article-content[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]{border-left:4px solid var(--theme-accent);padding-left:1rem;margin:1.5rem 0;font-style:italic;color:var(--theme-text-secondary);background:var(--theme-accent-light);padding:1rem;border-radius:0 8px 8px 0}.article-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .article-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin-bottom:1.5rem;padding-left:2rem}.article-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:.5rem}\"]\n      });\n    }\n  }\n  return ArticleViewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}