{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, NgModule } from '@angular/core';\nconst defaultModules = {\n  toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{\n    header: 1\n  }, {\n    header: 2\n  }], [{\n    list: 'ordered'\n  }, {\n    list: 'bullet'\n  }], [{\n    script: 'sub'\n  }, {\n    script: 'super'\n  }], [{\n    indent: '-1'\n  }, {\n    indent: '+1'\n  }], [{\n    direction: 'rtl'\n  }], [{\n    size: ['small', false, 'large', 'huge']\n  }], [{\n    header: [1, 2, 3, 4, 5, 6, false]\n  }], [{\n    color: []\n  }, {\n    background: []\n  }], [{\n    font: []\n  }], [{\n    align: []\n  }], ['clean'], ['link', 'image', 'video'] // link and image, video\n  ]\n};\n\nconst QUILL_CONFIG_TOKEN = new InjectionToken('config', {\n  providedIn: 'root',\n  factory: () => ({\n    modules: defaultModules\n  })\n});\n\n/**\n * This `NgModule` provides a global Quill config on the root level, e.g., in `AppModule`.\n * But this eliminates the need to import the entire `ngx-quill` library into the main bundle.\n * The `quill-editor` itself may be rendered in any lazy-loaded module, but importing `QuillModule`\n * into the `AppModule` will bundle the `ngx-quill` into the vendor.\n */\nlet QuillConfigModule = /*#__PURE__*/(() => {\n  class QuillConfigModule {\n    static forRoot(config) {\n      return {\n        ngModule: QuillConfigModule,\n        providers: [{\n          provide: QUILL_CONFIG_TOKEN,\n          useValue: config\n        }]\n      };\n    }\n  }\n  QuillConfigModule.ɵfac = function QuillConfigModule_Factory(t) {\n    return new (t || QuillConfigModule)();\n  };\n  QuillConfigModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: QuillConfigModule\n  });\n  QuillConfigModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return QuillConfigModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of ngx-quill/config\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QUILL_CONFIG_TOKEN, QuillConfigModule, defaultModules };\n//# sourceMappingURL=ngx-quill-config.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}