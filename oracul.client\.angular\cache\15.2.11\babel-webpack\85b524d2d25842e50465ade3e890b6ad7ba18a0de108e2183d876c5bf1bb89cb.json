{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/avatar.service\";\nexport class LazyLoadImageDirective {\n  constructor(elementRef, renderer, avatarService) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.avatarService = avatarService;\n    this.appLazyLoadImage = null; // The image URL to load\n    this.fallbackFirstName = '';\n    this.fallbackLastName = '';\n    this.useDefaultImage = false;\n    this.placeholder = ''; // Optional placeholder image\n    this.loadingClass = 'loading'; // CSS class for loading state\n    this.errorClass = 'error'; // CSS class for error state\n    this.loadedClass = 'loaded'; // CSS class for loaded state\n    this.imageLoaded = new EventEmitter();\n    this.imageError = new EventEmitter();\n    this.isLoaded = false;\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.setupIntersectionObserver();\n    this.setPlaceholder();\n  }\n  ngOnDestroy() {\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect();\n    }\n  }\n  setupIntersectionObserver() {\n    // Check if IntersectionObserver is supported\n    if (!('IntersectionObserver' in window)) {\n      // Fallback: load image immediately\n      this.loadImage();\n      return;\n    }\n    this.intersectionObserver = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting && !this.isLoaded && !this.isLoading) {\n          this.loadImage();\n          // Stop observing once we start loading\n          this.intersectionObserver?.unobserve(entry.target);\n        }\n      });\n    }, {\n      // Start loading when image is 100px away from viewport\n      rootMargin: '100px 0px',\n      threshold: 0.01\n    });\n    this.intersectionObserver.observe(this.elementRef.nativeElement);\n  }\n  setPlaceholder() {\n    const img = this.elementRef.nativeElement;\n    if (this.placeholder) {\n      this.renderer.setAttribute(img, 'src', this.placeholder);\n    } else {\n      // Set a minimal placeholder or generated avatar\n      const placeholderUrl = this.avatarService.generateAvatarUrl(this.fallbackFirstName, this.fallbackLastName);\n      this.renderer.setAttribute(img, 'src', placeholderUrl);\n    }\n    this.renderer.addClass(img, this.loadingClass);\n  }\n  loadImage() {\n    if (this.isLoading || this.isLoaded) {\n      return;\n    }\n    this.isLoading = true;\n    const img = this.elementRef.nativeElement;\n    // Use the enhanced avatar service for loading\n    this.avatarService.loadImageWithFallback(this.appLazyLoadImage, this.fallbackFirstName, this.fallbackLastName, this.useDefaultImage).subscribe({\n      next: result => {\n        this.isLoading = false;\n        this.isLoaded = true;\n        // Update image source\n        this.renderer.setAttribute(img, 'src', result.url);\n        // Update CSS classes\n        this.renderer.removeClass(img, this.loadingClass);\n        if (result.success && !result.fallbackUsed) {\n          this.renderer.addClass(img, this.loadedClass);\n          this.imageLoaded.emit(true);\n        } else {\n          this.renderer.addClass(img, this.errorClass);\n          this.imageLoaded.emit(false);\n          this.imageError.emit(result.error || 'Failed to load image');\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.isLoaded = true;\n        // Set fallback image\n        const fallbackUrl = this.useDefaultImage ? this.avatarService.getDefaultProfileImage() : this.avatarService.generateAvatarUrl(this.fallbackFirstName, this.fallbackLastName);\n        this.renderer.setAttribute(img, 'src', fallbackUrl);\n        this.renderer.removeClass(img, this.loadingClass);\n        this.renderer.addClass(img, this.errorClass);\n        this.imageLoaded.emit(false);\n        this.imageError.emit(error.message || 'Unknown error');\n      }\n    });\n  }\n  /**\r\n   * Force load the image (useful for programmatic loading)\r\n   */\n  forceLoad() {\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect();\n    }\n    this.loadImage();\n  }\n  /**\r\n   * Retry loading the image\r\n   */\n  retry() {\n    this.isLoaded = false;\n    this.isLoading = false;\n    const img = this.elementRef.nativeElement;\n    this.renderer.removeClass(img, this.errorClass);\n    this.renderer.removeClass(img, this.loadedClass);\n    this.setPlaceholder();\n    this.loadImage();\n  }\n  static {\n    this.ɵfac = function LazyLoadImageDirective_Factory(t) {\n      return new (t || LazyLoadImageDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.AvatarService));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: LazyLoadImageDirective,\n      selectors: [[\"\", \"appLazyLoadImage\", \"\"]],\n      inputs: {\n        appLazyLoadImage: \"appLazyLoadImage\",\n        fallbackFirstName: \"fallbackFirstName\",\n        fallbackLastName: \"fallbackLastName\",\n        useDefaultImage: \"useDefaultImage\",\n        placeholder: \"placeholder\",\n        loadingClass: \"loadingClass\",\n        errorClass: \"errorClass\",\n        loadedClass: \"loadedClass\"\n      },\n      outputs: {\n        imageLoaded: \"imageLoaded\",\n        imageError: \"imageError\"\n      }\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAQEA,YAAY,QACP,eAAe;;;AAMtB,OAAM,MAAOC,sBAAsB;EAiBjCC,YACUC,UAAwC,EACxCC,QAAmB,EACnBC,aAA4B;IAF5B,eAAU,GAAVF,UAAU;IACV,aAAQ,GAARC,QAAQ;IACR,kBAAa,GAAbC,aAAa;IAnBd,qBAAgB,GAA8B,IAAI,CAAC,CAAC;IACpD,sBAAiB,GAAW,EAAE;IAC9B,qBAAgB,GAAW,EAAE;IAC7B,oBAAe,GAAY,KAAK;IAChC,gBAAW,GAAW,EAAE,CAAC,CAAC;IAC1B,iBAAY,GAAW,SAAS,CAAC,CAAC;IAClC,eAAU,GAAW,OAAO,CAAC,CAAC;IAC9B,gBAAW,GAAW,QAAQ,CAAC,CAAC;IAE/B,gBAAW,GAAG,IAAIL,YAAY,EAAW;IACzC,eAAU,GAAG,IAAIA,YAAY,EAAU;IAGzC,aAAQ,GAAG,KAAK;IAChB,cAAS,GAAG,KAAK;EAMtB;EAEHM,QAAQ;IACN,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAW;IACT,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACC,UAAU,EAAE;;EAE1C;EAEQJ,yBAAyB;IAC/B;IACA,IAAI,EAAE,sBAAsB,IAAIK,MAAM,CAAC,EAAE;MACvC;MACA,IAAI,CAACC,SAAS,EAAE;MAChB;;IAGF,IAAI,CAACH,oBAAoB,GAAG,IAAII,oBAAoB,CACjDC,OAAO,IAAI;MACVA,OAAO,CAACC,OAAO,CAACC,KAAK,IAAG;QACtB,IAAIA,KAAK,CAACC,cAAc,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;UAC7D,IAAI,CAACP,SAAS,EAAE;UAChB;UACA,IAAI,CAACH,oBAAoB,EAAEW,SAAS,CAACJ,KAAK,CAACK,MAAM,CAAC;;MAEtD,CAAC,CAAC;IACJ,CAAC,EACD;MACE;MACAC,UAAU,EAAE,WAAW;MACvBC,SAAS,EAAE;KACZ,CACF;IAED,IAAI,CAACd,oBAAoB,CAACe,OAAO,CAAC,IAAI,CAACtB,UAAU,CAACuB,aAAa,CAAC;EAClE;EAEQlB,cAAc;IACpB,MAAMmB,GAAG,GAAG,IAAI,CAACxB,UAAU,CAACuB,aAAa;IAEzC,IAAI,IAAI,CAACE,WAAW,EAAE;MACpB,IAAI,CAACxB,QAAQ,CAACyB,YAAY,CAACF,GAAG,EAAE,KAAK,EAAE,IAAI,CAACC,WAAW,CAAC;KACzD,MAAM;MACL;MACA,MAAME,cAAc,GAAG,IAAI,CAACzB,aAAa,CAAC0B,iBAAiB,CACzD,IAAI,CAACC,iBAAiB,EACtB,IAAI,CAACC,gBAAgB,CACtB;MACD,IAAI,CAAC7B,QAAQ,CAACyB,YAAY,CAACF,GAAG,EAAE,KAAK,EAAEG,cAAc,CAAC;;IAGxD,IAAI,CAAC1B,QAAQ,CAAC8B,QAAQ,CAACP,GAAG,EAAE,IAAI,CAACQ,YAAY,CAAC;EAChD;EAEQtB,SAAS;IACf,IAAI,IAAI,CAACO,SAAS,IAAI,IAAI,CAACD,QAAQ,EAAE;MACnC;;IAGF,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,MAAMO,GAAG,GAAG,IAAI,CAACxB,UAAU,CAACuB,aAAa;IAEzC;IACA,IAAI,CAACrB,aAAa,CAAC+B,qBAAqB,CACtC,IAAI,CAACC,gBAAgB,EACrB,IAAI,CAACL,iBAAiB,EACtB,IAAI,CAACC,gBAAgB,EACrB,IAAI,CAACK,eAAe,CACrB,CAACC,SAAS,CAAC;MACVC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACrB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACD,QAAQ,GAAG,IAAI;QAEpB;QACA,IAAI,CAACf,QAAQ,CAACyB,YAAY,CAACF,GAAG,EAAE,KAAK,EAAEc,MAAM,CAACC,GAAG,CAAC;QAElD;QACA,IAAI,CAACtC,QAAQ,CAACuC,WAAW,CAAChB,GAAG,EAAE,IAAI,CAACQ,YAAY,CAAC;QAEjD,IAAIM,MAAM,CAACG,OAAO,IAAI,CAACH,MAAM,CAACI,YAAY,EAAE;UAC1C,IAAI,CAACzC,QAAQ,CAAC8B,QAAQ,CAACP,GAAG,EAAE,IAAI,CAACmB,WAAW,CAAC;UAC7C,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;SAC5B,MAAM;UACL,IAAI,CAAC5C,QAAQ,CAAC8B,QAAQ,CAACP,GAAG,EAAE,IAAI,CAACsB,UAAU,CAAC;UAC5C,IAAI,CAACF,WAAW,CAACC,IAAI,CAAC,KAAK,CAAC;UAC5B,IAAI,CAACE,UAAU,CAACF,IAAI,CAACP,MAAM,CAACU,KAAK,IAAI,sBAAsB,CAAC;;MAEhE,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/B,SAAS,GAAG,KAAK;QACtB,IAAI,CAACD,QAAQ,GAAG,IAAI;QAEpB;QACA,MAAMiC,WAAW,GAAG,IAAI,CAACd,eAAe,GACpC,IAAI,CAACjC,aAAa,CAACgD,sBAAsB,EAAE,GAC3C,IAAI,CAAChD,aAAa,CAAC0B,iBAAiB,CAAC,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,gBAAgB,CAAC;QAEvF,IAAI,CAAC7B,QAAQ,CAACyB,YAAY,CAACF,GAAG,EAAE,KAAK,EAAEyB,WAAW,CAAC;QACnD,IAAI,CAAChD,QAAQ,CAACuC,WAAW,CAAChB,GAAG,EAAE,IAAI,CAACQ,YAAY,CAAC;QACjD,IAAI,CAAC/B,QAAQ,CAAC8B,QAAQ,CAACP,GAAG,EAAE,IAAI,CAACsB,UAAU,CAAC;QAE5C,IAAI,CAACF,WAAW,CAACC,IAAI,CAAC,KAAK,CAAC;QAC5B,IAAI,CAACE,UAAU,CAACF,IAAI,CAACG,KAAK,CAACG,OAAO,IAAI,eAAe,CAAC;MACxD;KACD,CAAC;EACJ;EAEA;;;EAGAC,SAAS;IACP,IAAI,IAAI,CAAC7C,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACC,UAAU,EAAE;;IAExC,IAAI,CAACE,SAAS,EAAE;EAClB;EAEA;;;EAGA2C,KAAK;IACH,IAAI,CAACrC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IAEtB,MAAMO,GAAG,GAAG,IAAI,CAACxB,UAAU,CAACuB,aAAa;IACzC,IAAI,CAACtB,QAAQ,CAACuC,WAAW,CAAChB,GAAG,EAAE,IAAI,CAACsB,UAAU,CAAC;IAC/C,IAAI,CAAC7C,QAAQ,CAACuC,WAAW,CAAChB,GAAG,EAAE,IAAI,CAACmB,WAAW,CAAC;IAEhD,IAAI,CAACtC,cAAc,EAAE;IACrB,IAAI,CAACK,SAAS,EAAE;EAClB;;;uBA3JWZ,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAwD;MAAAC;QAAArB;QAAAL;QAAAC;QAAAK;QAAAV;QAAAO;QAAAc;QAAAH;MAAA;MAAAa;QAAAZ;QAAAG;MAAA;IAAA;EAAA", "names": ["EventEmitter", "LazyLoadImageDirective", "constructor", "elementRef", "renderer", "avatarService", "ngOnInit", "setupIntersectionObserver", "setPlaceholder", "ngOnDestroy", "intersectionObserver", "disconnect", "window", "loadImage", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "isLoaded", "isLoading", "unobserve", "target", "rootMargin", "threshold", "observe", "nativeElement", "img", "placeholder", "setAttribute", "placeholderUrl", "generateAvatarUrl", "fallbackFirstName", "fallbackLastName", "addClass", "loadingClass", "loadImageWithFallback", "appLazyLoadImage", "useDefaultImage", "subscribe", "next", "result", "url", "removeClass", "success", "fallbackUsed", "loadedClass", "imageLoaded", "emit", "errorClass", "imageError", "error", "fallbackUrl", "getDefaultProfileImage", "message", "forceLoad", "retry", "selectors", "inputs", "outputs"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\shared\\directives\\lazy-load-image.directive.ts"], "sourcesContent": ["import { \n  Directive, \n  ElementRef, \n  Input, \n  OnInit, \n  OnDestroy, \n  Renderer2,\n  Output,\n  EventEmitter\n} from '@angular/core';\nimport { AvatarService } from '../services/avatar.service';\n\n@Directive({\n  selector: '[appLazyLoadImage]'\n})\nexport class LazyLoadImageDirective implements OnInit, OnDestroy {\n  @Input() appLazyLoadImage: string | null | undefined = null; // The image URL to load\n  @Input() fallbackFirstName: string = '';\n  @Input() fallbackLastName: string = '';\n  @Input() useDefaultImage: boolean = false;\n  @Input() placeholder: string = ''; // Optional placeholder image\n  @Input() loadingClass: string = 'loading'; // CSS class for loading state\n  @Input() errorClass: string = 'error'; // CSS class for error state\n  @Input() loadedClass: string = 'loaded'; // CSS class for loaded state\n  \n  @Output() imageLoaded = new EventEmitter<boolean>();\n  @Output() imageError = new EventEmitter<string>();\n\n  private intersectionObserver?: IntersectionObserver;\n  private isLoaded = false;\n  private isLoading = false;\n\n  constructor(\n    private elementRef: ElementRef<HTMLImageElement>,\n    private renderer: Renderer2,\n    private avatarService: AvatarService\n  ) {}\n\n  ngOnInit(): void {\n    this.setupIntersectionObserver();\n    this.setPlaceholder();\n  }\n\n  ngOnDestroy(): void {\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect();\n    }\n  }\n\n  private setupIntersectionObserver(): void {\n    // Check if IntersectionObserver is supported\n    if (!('IntersectionObserver' in window)) {\n      // Fallback: load image immediately\n      this.loadImage();\n      return;\n    }\n\n    this.intersectionObserver = new IntersectionObserver(\n      (entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting && !this.isLoaded && !this.isLoading) {\n            this.loadImage();\n            // Stop observing once we start loading\n            this.intersectionObserver?.unobserve(entry.target);\n          }\n        });\n      },\n      {\n        // Start loading when image is 100px away from viewport\n        rootMargin: '100px 0px',\n        threshold: 0.01\n      }\n    );\n\n    this.intersectionObserver.observe(this.elementRef.nativeElement);\n  }\n\n  private setPlaceholder(): void {\n    const img = this.elementRef.nativeElement;\n    \n    if (this.placeholder) {\n      this.renderer.setAttribute(img, 'src', this.placeholder);\n    } else {\n      // Set a minimal placeholder or generated avatar\n      const placeholderUrl = this.avatarService.generateAvatarUrl(\n        this.fallbackFirstName, \n        this.fallbackLastName\n      );\n      this.renderer.setAttribute(img, 'src', placeholderUrl);\n    }\n    \n    this.renderer.addClass(img, this.loadingClass);\n  }\n\n  private loadImage(): void {\n    if (this.isLoading || this.isLoaded) {\n      return;\n    }\n\n    this.isLoading = true;\n    const img = this.elementRef.nativeElement;\n\n    // Use the enhanced avatar service for loading\n    this.avatarService.loadImageWithFallback(\n      this.appLazyLoadImage,\n      this.fallbackFirstName,\n      this.fallbackLastName,\n      this.useDefaultImage\n    ).subscribe({\n      next: (result) => {\n        this.isLoading = false;\n        this.isLoaded = true;\n\n        // Update image source\n        this.renderer.setAttribute(img, 'src', result.url);\n        \n        // Update CSS classes\n        this.renderer.removeClass(img, this.loadingClass);\n        \n        if (result.success && !result.fallbackUsed) {\n          this.renderer.addClass(img, this.loadedClass);\n          this.imageLoaded.emit(true);\n        } else {\n          this.renderer.addClass(img, this.errorClass);\n          this.imageLoaded.emit(false);\n          this.imageError.emit(result.error || 'Failed to load image');\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.isLoaded = true;\n        \n        // Set fallback image\n        const fallbackUrl = this.useDefaultImage \n          ? this.avatarService.getDefaultProfileImage()\n          : this.avatarService.generateAvatarUrl(this.fallbackFirstName, this.fallbackLastName);\n        \n        this.renderer.setAttribute(img, 'src', fallbackUrl);\n        this.renderer.removeClass(img, this.loadingClass);\n        this.renderer.addClass(img, this.errorClass);\n        \n        this.imageLoaded.emit(false);\n        this.imageError.emit(error.message || 'Unknown error');\n      }\n    });\n  }\n\n  /**\n   * Force load the image (useful for programmatic loading)\n   */\n  forceLoad(): void {\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect();\n    }\n    this.loadImage();\n  }\n\n  /**\n   * Retry loading the image\n   */\n  retry(): void {\n    this.isLoaded = false;\n    this.isLoading = false;\n    \n    const img = this.elementRef.nativeElement;\n    this.renderer.removeClass(img, this.errorClass);\n    this.renderer.removeClass(img, this.loadedClass);\n    \n    this.setPlaceholder();\n    this.loadImage();\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}