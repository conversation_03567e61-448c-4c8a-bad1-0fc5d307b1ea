<div class="article-management-container">
  <mat-card class="management-card">
    <mat-card-header>
      <mat-card-title>Управление на статии</mat-card-title>
      <mat-card-subtitle>Създавайте, редактирайте и управлявайте вашите статии</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <!-- Toolbar -->
      <div class="toolbar">
        <div class="toolbar-left">
          <mat-form-field appearance="outline" class="status-filter">
            <mat-label>Филтър по статус</mat-label>
            <mat-select [(value)]="selectedStatus" (selectionChange)="onStatusFilterChange()">
              <mat-option *ngFor="let option of statusOptions" [value]="option.value">
                {{ option.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        
        <div class="toolbar-right">
          <button mat-raised-button color="primary" (click)="createNewArticle()">
            <mat-icon>add</mat-icon>
            Нова статия
          </button>
        </div>
      </div>

      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="loading-container">
        <mat-progress-spinner mode="indeterminate"></mat-progress-spinner>
        <p>Зареждане на статиите...</p>
      </div>

      <!-- Articles Table -->
      <div *ngIf="!isLoading" class="table-container">
        <table mat-table [dataSource]="dataSource" matSort class="articles-table">
          
          <!-- Title Column -->
          <ng-container matColumnDef="title">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Заглавие</th>
            <td mat-cell *matCellDef="let article" class="title-cell">
              <div class="title-content">
                <span class="article-title">{{ article.title }}</span>
                <span class="article-category" *ngIf="article.category">{{ article.category }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Статус</th>
            <td mat-cell *matCellDef="let article">
              <mat-chip [color]="getStatusColor(article.status)" selected>
                {{ article.status }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Category Column -->
          <ng-container matColumnDef="category">
            <th mat-header-cell *matHeaderCellDef>Категория</th>
            <td mat-cell *matCellDef="let article">
              {{ article.category || 'Без категория' }}
            </td>
          </ng-container>

          <!-- Published Date Column -->
          <ng-container matColumnDef="publishedAt">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Дата на публикуване</th>
            <td mat-cell *matCellDef="let article">
              <div *ngIf="article.publishedAt; else notPublished">
                {{ article.publishedAt | date:'dd.MM.yyyy HH:mm' }}
              </div>
              <ng-template #notPublished>
                <span class="not-published">Не е публикувана</span>
              </ng-template>
            </td>
          </ng-container>

          <!-- Read Count Column -->
          <ng-container matColumnDef="readCount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Прегледи</th>
            <td mat-cell *matCellDef="let article">
              <div class="read-stats">
                <mat-icon class="stats-icon">visibility</mat-icon>
                <span>{{ article.readCount }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Действия</th>
            <td mat-cell *matCellDef="let article">
              <button mat-icon-button [matMenuTriggerFor]="actionMenu" 
                      [matMenuTriggerData]="{article: article}">
                <mat-icon>more_vert</mat-icon>
              </button>
              
              <mat-menu #actionMenu="matMenu">
                <ng-template matMenuContent let-article="article">
                  <button mat-menu-item (click)="viewArticle(article)">
                    <mat-icon>visibility</mat-icon>
                    <span>Преглед</span>
                  </button>
                  
                  <button mat-menu-item (click)="editArticle(article.id)">
                    <mat-icon>edit</mat-icon>
                    <span>Редактиране</span>
                  </button>
                  
                  <button mat-menu-item (click)="publishArticle(article.id)" 
                          *ngIf="canPublish(article.status)">
                    <mat-icon>publish</mat-icon>
                    <span>Публикувай</span>
                  </button>
                  
                  <button mat-menu-item (click)="unpublishArticle(article.id)" 
                          *ngIf="canUnpublish(article.status)">
                    <mat-icon>unpublished</mat-icon>
                    <span>Скрий</span>
                  </button>
                  
                  <mat-divider></mat-divider>
                  
                  <button mat-menu-item (click)="deleteArticle(article.id, article.title)" 
                          class="delete-action">
                    <mat-icon>delete</mat-icon>
                    <span>Изтрий</span>
                  </button>
                </ng-template>
              </mat-menu>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data Message -->
        <div *ngIf="dataSource.data.length === 0" class="no-data">
          <mat-icon class="no-data-icon">article</mat-icon>
          <h3>Няма намерени статии</h3>
          <p>Започнете да създавате съдържание, като натиснете бутона "Нова статия".</p>
          <button mat-raised-button color="primary" (click)="createNewArticle()">
            <mat-icon>add</mat-icon>
            Създай първата си статия
          </button>
        </div>

        <!-- Paginator -->
        <mat-paginator 
          *ngIf="dataSource.data.length > 0"
          [length]="totalCount"
          [pageSize]="pageSize"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>
