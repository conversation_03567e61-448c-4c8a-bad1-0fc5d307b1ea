# Azure DevOps Pipeline for Oracul Frontend (Angular 15 with Material Design)
# This pipeline builds and deploys the Angular frontend application (no backend components)

trigger:
  branches:
    include:
    - main
    - master
    - develop
  paths:
    include:
    - oracul.client/*

pr:
  branches:
    include:
    - main
    - master
    - develop
  paths:
    include:
    - oracul.client/*

variables:
  nodeVersion: '18.x'
  vmImageName: 'ubuntu-latest'
  workingDirectory: 'oracul.client'

  # Azure Static Web Apps / App Service Configuration
  azureSubscription: 'Azure-Service-Connection' # Service connection name
  appServiceName: 'ora-fe' # Frontend App Service name
  resourceGroupName: 'egishe' # Resource group name

  # Application Settings
  backendUrl: 'https://fb-be-h5eqc6fwa9acd6ew.northeurope-01.azurewebsites.net'
  
  # Static Web Apps Configuration (Alternative to App Service)
  staticWebAppName: 'oracul-frontend-swa' # Replace with your Static Web App name

stages:
- stage: Build
  displayName: 'Build Frontend'
  jobs:
  - job: Build
    displayName: 'Build Angular Application'
    pool:
      vmImage: $(vmImageName)
    
    steps:
    - checkout: self
      fetchDepth: 0
    
    - task: NodeTool@0
      displayName: 'Use Node.js $(nodeVersion)'
      inputs:
        versionSpec: $(nodeVersion)
    
    - task: Cache@2
      displayName: 'Cache node_modules'
      inputs:
        key: 'npm | "$(Agent.OS)" | $(workingDirectory)/package-lock.json'
        restoreKeys: |
          npm | "$(Agent.OS)"
        path: '$(workingDirectory)/node_modules'
    
    - script: |
        cd $(workingDirectory)
        npm install --legacy-peer-deps
      displayName: 'Install dependencies with legacy peer deps'
    

    - script: |
        cd $(workingDirectory)
        npm run build -- --configuration=production
      displayName: 'Build for production'
    
    - task: PowerShell@2
      displayName: 'Copy server files to build output'
      inputs:
        targetType: 'inline'
        script: |
          Copy-Item "$(workingDirectory)/server.js" "$(workingDirectory)/dist/oracul.client/"
          Copy-Item "$(workingDirectory)/deployment-package.json" "$(workingDirectory)/dist/oracul.client/package.json"

    - script: |
        cd $(workingDirectory)/dist/oracul.client
        npm install --production
      displayName: 'Install production dependencies in build output'

    - task: ArchiveFiles@2
      displayName: 'Archive frontend build'
      inputs:
        rootFolderOrFile: '$(workingDirectory)/dist/oracul.client'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/frontend-$(Build.BuildId).zip'
        replaceExistingArchive: true
    
    - task: PublishBuildArtifacts@1
      displayName: 'Publish frontend artifacts'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'frontend-drop'
        publishLocation: 'Container'

- stage: DeployProduction
  displayName: 'Deploy to Production'
  dependsOn: Build
  condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.SourceBranch'], 'refs/heads/master'), eq(variables['Build.SourceBranch'], 'refs/heads/develop')))
  jobs:
  - deployment: DeployFrontendProduction
    displayName: 'Deploy Frontend to Production'
    pool:
      vmImage: $(vmImageName)
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: frontend-drop

          # Deploy to Azure App Service
          - task: AzureRmWebAppDeployment@4
            displayName: 'Deploy to Azure App Service (Production)'
            inputs:
              ConnectionType: 'AzureRM'
              azureSubscription: $(azureSubscription)
              appType: 'webApp'
              WebAppName: '$(appServiceName)'
              ResourceGroupName: $(resourceGroupName)
              packageForLinux: '$(Pipeline.Workspace)/frontend-drop/frontend-$(Build.BuildId).zip'

          # Configure App Settings separately
          - task: AzureAppServiceSettings@1
            displayName: 'Configure App Settings'
            inputs:
              azureSubscription: $(azureSubscription)
              appName: '$(appServiceName)'
              resourceGroupName: $(resourceGroupName)
              appSettings: |
                [
                  {
                    "name": "WEBSITE_NODE_DEFAULT_VERSION",
                    "value": "~22"
                  },
                  {
                    "name": "SCM_DO_BUILD_DURING_DEPLOYMENT",
                    "value": "false"
                  },
                  {
                    "name": "WEBSITE_WEBDEPLOY_USE_SCM",
                    "value": "true"
                  }
                ]


