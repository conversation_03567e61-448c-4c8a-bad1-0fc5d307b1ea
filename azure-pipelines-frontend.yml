# Azure DevOps Pipeline for Oracul Frontend (Angular 15 with Material Design)
# This pipeline builds, tests, and deploys the Angular frontend application

trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - oracul.client/*

pr:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - oracul.client/*

variables:
  nodeVersion: '18.x'
  vmImageName: 'ubuntu-latest'
  workingDirectory: 'oracul.client'

  # Azure Static Web Apps / App Service Configuration
  azureSubscription: '6dd88dbc-9333-4643-834f-978cc8b01dc8' # Azure Subscription ID
  appServiceName: 'ora-fe' # Frontend App Service name
  resourceGroupName: 'egishe' # Resource group name

  # Application Settings
  backendUrl: 'https://fb-be-h5eqc6fwa9acd6ew.northeurope-01.azurewebsites.net'
  
  # Static Web Apps Configuration (Alternative to App Service)
  staticWebAppName: 'oracul-frontend-swa' # Replace with your Static Web App name

stages:
- stage: Build
  displayName: 'Build and Test Frontend'
  jobs:
  - job: Build
    displayName: 'Build Angular Application'
    pool:
      vmImage: $(vmImageName)
    
    steps:
    - checkout: self
      fetchDepth: 0
    
    - task: NodeTool@0
      displayName: 'Use Node.js $(nodeVersion)'
      inputs:
        versionSpec: $(nodeVersion)
    
    - task: Cache@2
      displayName: 'Cache node_modules'
      inputs:
        key: 'npm | "$(Agent.OS)" | $(workingDirectory)/package-lock.json'
        restoreKeys: |
          npm | "$(Agent.OS)"
        path: '$(workingDirectory)/node_modules'
    
    - script: |
        cd $(workingDirectory)
        npm ci
      displayName: 'Install dependencies'
    
    - script: |
        cd $(workingDirectory)
        npx ng lint
      displayName: 'Run linting'
      continueOnError: true

    - script: |
        cd $(workingDirectory)
        npm run test -- --watch=false --browsers=ChromeHeadless --code-coverage --reporters=progress,junit
      displayName: 'Run unit tests'
      continueOnError: true
    
    - task: PublishTestResults@2
      displayName: 'Publish test results'
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: '$(workingDirectory)/test-results.xml'
        mergeTestResults: true
        failTaskOnFailedTests: false
      condition: succeededOrFailed()
    
    - task: PublishCodeCoverageResults@1
      displayName: 'Publish code coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(workingDirectory)/coverage/cobertura-coverage.xml'
        reportDirectory: '$(workingDirectory)/coverage'
      condition: succeededOrFailed()
    
    - script: |
        cd $(workingDirectory)
        npm run build -- --configuration=production
      displayName: 'Build for production'
    
    - task: ArchiveFiles@2
      displayName: 'Archive frontend build'
      inputs:
        rootFolderOrFile: '$(workingDirectory)/dist/oracul.client'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/frontend-$(Build.BuildId).zip'
        replaceExistingArchive: true
    
    - task: PublishBuildArtifacts@1
      displayName: 'Publish frontend artifacts'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'frontend-drop'
        publishLocation: 'Container'

- stage: DeployProduction
  displayName: 'Deploy to Production'
  dependsOn: Build
  condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.SourceBranch'], 'refs/heads/develop')))
  jobs:
  - deployment: DeployFrontendProduction
    displayName: 'Deploy Frontend to Production'
    pool:
      vmImage: $(vmImageName)
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: frontend-drop

          # Deploy to Azure App Service
          - task: AzureRmWebAppDeployment@4
            displayName: 'Deploy to Azure App Service (Production)'
            inputs:
              ConnectionType: 'AzureRM'
              azureSubscription: $(azureSubscription)
              appType: 'webApp'
              WebAppName: '$(appServiceName)'
              ResourceGroupName: $(resourceGroupName)
              packageForLinux: '$(Pipeline.Workspace)/frontend-drop/frontend-$(Build.BuildId).zip'
              AppSettings: |
                -WEBSITE_NODE_DEFAULT_VERSION "18.17.0"
                -SCM_DO_BUILD_DURING_DEPLOYMENT "false"


