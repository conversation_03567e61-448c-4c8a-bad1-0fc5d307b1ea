using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Oracul.Data.Data;
using Oracul.Data.Models;
using Oracul.Server.Models;
using System.Net.Http;
using System.Text;
using Xunit;

namespace Oracul.Server.Tests.Integration
{
    /// <summary>
    /// Integration tests to verify that all registration fields are properly stored in the database
    /// </summary>
    public class RegistrationFieldStorageTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public RegistrationFieldStorageTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task RegisterGeneralUser_ShouldStoreAllFieldsCorrectly()
        {
            // Arrange
            var registerRequest = new RegisterRequest
            {
                FirstName = "Test",
                LastName = "User",
                Email = "<EMAIL>",
                PhoneNumber = "+359888123456",
                Password = "TestPassword123!",
                ConfirmPassword = "TestPassword123!",
                AcceptTerms = true
            };

            var json = JsonConvert.SerializeObject(registerRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/auth/register", content);

            // Assert
            Assert.True(response.IsSuccessStatusCode, $"Registration failed with status: {response.StatusCode}");

            // Verify data in database
            using var scope = _factory.Services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<OraculDbContext>();

            var user = await dbContext.Users
                .FirstOrDefaultAsync(u => u.Email == registerRequest.Email);

            Assert.NotNull(user);
            Assert.Equal(registerRequest.FirstName, user.FirstName);
            Assert.Equal(registerRequest.LastName, user.LastName);
            Assert.Equal(registerRequest.Email, user.Email);
            Assert.Equal(registerRequest.PhoneNumber, user.PhoneNumber);
            Assert.NotNull(user.PasswordHash);
            Assert.NotEqual(registerRequest.Password, user.PasswordHash); // Should be hashed
            Assert.True(user.IsActive);
            Assert.False(user.EmailConfirmed); // Should be false initially
            Assert.NotNull(user.EmailConfirmationToken);

            // Verify user role assignment
            var userRole = await dbContext.UserRoles
                .Include(ur => ur.Role)
                .FirstOrDefaultAsync(ur => ur.UserId == user.Id);

            Assert.NotNull(userRole);
            Assert.Equal("User", userRole.Role.Name);
        }

        [Fact]
        public async Task RegisterOracleUser_ShouldStoreAllFieldsCorrectly()
        {
            // Arrange
            var oracleRequest = new OracleRegisterRequest
            {
                // Basic information
                FirstName = "Oracle",
                LastName = "Tester",
                Email = "<EMAIL>",
                PhoneNumber = "+************",
                Password = "OraclePassword123!",
                ConfirmPassword = "OraclePassword123!",
                AcceptTerms = true,

                // Professional information
                ProfessionalTitle = "Master Astrologer",
                Headline = "Experienced astrologer specializing in natal charts and spiritual guidance",
                Summary = "With over 10 years of experience in astrology and spiritual counseling, I provide deep insights into your life path through the wisdom of the stars.",
                PrimarySpecialization = "Астрология",
                YearsOfExperience = 10,

                // Location information
                City = "София",
                State = "София-град",
                Country = "България",
                DisplayLocation = "София, България",

                // Oracle-specific information
                BirthDate = new DateTime(1985, 6, 15),
                BirthTime = new TimeSpan(14, 30, 0), // 2:30 PM
                BirthLocation = "Пловдив, България",
                AstrologicalSign = "Близнаци",
                OracleTypes = new List<string> { "Таро карти", "Астрологични карти", "Кристали" },
                LanguagesSpoken = new List<string> { "Български", "Английски", "Руски" },
                Skills = new List<string> { "Натална астрология", "Таро четене", "Кристални лечения", "Медитация" },

                // Contact & business information
                Website = "https://oracletester.com",
                PortfolioUrl = "https://portfolio.oracletester.com",
                BusinessAddress = new BusinessAddressDto
                {
                    Street = "ул. Витоша 123",
                    City = "София",
                    State = "София-град",
                    PostalCode = "1000",
                    Country = "България",
                    IsPublic = true
                },

                // Consultation rates
                ConsultationRates = new ConsultationRatesDto
                {
                    HourlyRate = 80,
                    SessionRate = 120,
                    Currency = "BGN"
                }
            };

            var json = JsonConvert.SerializeObject(oracleRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/auth/register-oracle", content);

            // Assert
            Assert.True(response.IsSuccessStatusCode, $"Oracle registration failed with status: {response.StatusCode}");

            // Verify data in database
            using var scope = _factory.Services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<OraculDbContext>();

            await VerifyOracleUserInDatabase(dbContext, oracleRequest);
        }

        private async Task VerifyOracleUserInDatabase(OraculDbContext dbContext, OracleRegisterRequest request)
        {
            // Verify User table
            var user = await dbContext.Users
                .FirstOrDefaultAsync(u => u.Email == request.Email);

            Assert.NotNull(user);
            Assert.Equal(request.FirstName, user.FirstName);
            Assert.Equal(request.LastName, user.LastName);
            Assert.Equal(request.Email, user.Email);
            Assert.Equal(request.PhoneNumber, user.PhoneNumber);
            Assert.NotNull(user.PasswordHash);
            Assert.True(user.IsActive);

            // Verify Oracle role assignment
            var userRole = await dbContext.UserRoles
                .Include(ur => ur.Role)
                .FirstOrDefaultAsync(ur => ur.UserId == user.Id && ur.Role.Name == "Oracle");

            Assert.NotNull(userRole);

            // Verify UserProfile table
            var profile = await dbContext.UserProfiles
                .Include(p => p.Location)
                .Include(p => p.ContactInfo)
                .ThenInclude(c => c.BusinessAddress)
                .Include(p => p.Skills)
                .FirstOrDefaultAsync(p => p.UserId == user.Id);

            Assert.NotNull(profile);
            Assert.Equal(request.FirstName, profile.FirstName);
            Assert.Equal(request.LastName, profile.LastName);
            Assert.Equal(request.ProfessionalTitle, profile.ProfessionalTitle);
            Assert.Equal(request.Headline, profile.Headline);
            Assert.Equal(request.Summary, profile.Summary);
            Assert.True(profile.IsPublic);
            Assert.True(profile.ProfileCompletionPercentage > 0);

            // Verify ProfileLocation
            Assert.NotNull(profile.Location);
            Assert.Equal(request.City, profile.Location.City);
            Assert.Equal(request.State, profile.Location.State);
            Assert.Equal(request.Country, profile.Location.Country);
            Assert.Equal(request.DisplayLocation, profile.Location.DisplayLocation);

            // Verify ContactInformation
            Assert.NotNull(profile.ContactInfo);
            Assert.Equal(request.Website, profile.ContactInfo.Website);
            Assert.Equal(request.PortfolioUrl, profile.ContactInfo.PortfolioUrl);

            // Verify BusinessAddress
            if (request.BusinessAddress != null)
            {
                Assert.NotNull(profile.ContactInfo.BusinessAddress);
                Assert.Equal(request.BusinessAddress.Street, profile.ContactInfo.BusinessAddress.Street);
                Assert.Equal(request.BusinessAddress.City, profile.ContactInfo.BusinessAddress.City);
                Assert.Equal(request.BusinessAddress.State, profile.ContactInfo.BusinessAddress.State);
                Assert.Equal(request.BusinessAddress.PostalCode, profile.ContactInfo.BusinessAddress.PostalCode);
                Assert.Equal(request.BusinessAddress.Country, profile.ContactInfo.BusinessAddress.Country);
                Assert.Equal(request.BusinessAddress.IsPublic, profile.ContactInfo.BusinessAddress.IsPublic);
            }

            // Verify Skills
            Assert.NotEmpty(profile.Skills);
            Assert.Equal(request.Skills.Count, profile.Skills.Count);
            
            foreach (var skillName in request.Skills)
            {
                Assert.Contains(profile.Skills, s => s.Name == skillName);
            }

            // Verify skills have correct category (should be primary specialization)
            foreach (var skill in profile.Skills)
            {
                Assert.Equal(request.PrimarySpecialization, skill.Category);
                Assert.Equal("advanced", skill.ProficiencyLevel);
                Assert.Equal(0, skill.Endorsements);
            }
        }

        [Fact]
        public async Task RegisterOracleUser_WithMinimalData_ShouldStoreCorrectly()
        {
            // Test with minimal required fields only
            var minimalRequest = new OracleRegisterRequest
            {
                FirstName = "Minimal",
                LastName = "Oracle",
                Email = "<EMAIL>",
                Password = "MinimalPassword123!",
                ConfirmPassword = "MinimalPassword123!",
                AcceptTerms = true,
                ProfessionalTitle = "Astrologer",
                Headline = "Basic astrology services",
                Summary = "Providing basic astrology consultations",
                PrimarySpecialization = "Астрология",
                YearsOfExperience = 1,
                City = "Варна",
                Country = "България",
                BirthDate = new DateTime(1990, 1, 1),
                BirthLocation = "Варна, България",
                OracleTypes = new List<string> { "Астрологични карти" },
                LanguagesSpoken = new List<string> { "Български" },
                Skills = new List<string> { "Астрология" }
            };

            var json = JsonConvert.SerializeObject(minimalRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _client.PostAsync("/api/auth/register-oracle", content);

            Assert.True(response.IsSuccessStatusCode);

            using var scope = _factory.Services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<OraculDbContext>();

            var user = await dbContext.Users.FirstOrDefaultAsync(u => u.Email == minimalRequest.Email);
            Assert.NotNull(user);

            var profile = await dbContext.UserProfiles
                .Include(p => p.Location)
                .Include(p => p.Skills)
                .FirstOrDefaultAsync(p => p.UserId == user.Id);

            Assert.NotNull(profile);
            Assert.NotNull(profile.Location);
            Assert.Single(profile.Skills);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _client?.Dispose();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
