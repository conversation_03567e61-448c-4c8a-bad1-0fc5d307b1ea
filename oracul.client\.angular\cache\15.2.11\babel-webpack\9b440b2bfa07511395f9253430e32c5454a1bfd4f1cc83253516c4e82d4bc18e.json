{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Quill Editor\nimport { QuillModule } from 'ngx-quill';\n// Angular Material Modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDividerModule } from '@angular/material/divider';\n// Components\nimport { ArticleViewComponent } from './article-view/article-view.component';\nimport { ArticleEditorComponent } from './article-editor/article-editor.component';\nimport { ArticleManagementComponent, ConfirmDeleteDialogComponent } from './article-management/article-management.component';\nimport { ImageUploadComponent } from './components/image-upload/image-upload.component';\nimport { ArticlePreviewDialogComponent } from './components/article-preview-dialog/article-preview-dialog.component';\n// Services\nimport { ArticleService } from '../shared/services/article.service';\nconst routes = [{\n  path: 'manage',\n  component: ArticleManagementComponent\n}, {\n  path: 'editor',\n  component: ArticleEditorComponent\n}, {\n  path: 'editor/:id',\n  component: ArticleEditorComponent\n}, {\n  path: ':slug',\n  component: ArticleViewComponent\n}];\nlet ArticlesModule = class ArticlesModule {};\nArticlesModule = __decorate([NgModule({\n  declarations: [ArticleViewComponent, ArticleEditorComponent, ArticleManagementComponent, ConfirmDeleteDialogComponent, ImageUploadComponent, ArticlePreviewDialogComponent],\n  imports: [CommonModule, RouterModule.forChild(routes), ReactiveFormsModule, FormsModule, QuillModule.forRoot(), MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, MatProgressSpinnerModule, MatProgressBarModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatCheckboxModule, MatTableModule, MatPaginatorModule, MatSortModule, MatDialogModule, MatSnackBarModule, MatTabsModule, MatMenuModule, MatTooltipModule, MatDividerModule],\n  providers: [ArticleService]\n})], ArticlesModule);\nexport { ArticlesModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}