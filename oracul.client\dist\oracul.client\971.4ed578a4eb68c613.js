(self.webpackChunkoracul_client=self.webpackChunkoracul_client||[]).push([[971],{9971:function(ct){typeof self<"u"&&self,ct.exports=function(B){var O={};function p(P){if(O[P])return O[P].exports;var T=O[P]={i:P,l:!1,exports:{}};return B[P].call(T.exports,T,T.exports,p),T.l=!0,T.exports}return p.m=B,p.c=O,p.d=function(P,T,w){p.o(P,T)||Object.defineProperty(P,T,{configurable:!1,enumerable:!0,get:w})},p.n=function(P){var T=P&&P.__esModule?function(){return P.default}:function(){return P};return p.d(T,"a",T),T},p.o=function(P,T){return Object.prototype.hasOwnProperty.call(P,T)},p.p="",p(p.s=109)}([function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=p(17),T=p(18),w=p(19),m=p(45),y=p(46),c=p(47),o=p(48),t=p(49),e=p(12),u=p(32),l=p(33),a=p(31),r=p(1);O.default={Scope:r.Scope,create:r.create,find:r.find,query:r.query,register:r.register,Container:P.default,Format:T.default,Leaf:w.default,Embed:o.default,Scroll:m.default,Block:c.default,Inline:y.default,Text:t.default,Attributor:{Attribute:e.default,Class:u.default,Style:l.default,Store:a.default}}},function(B,O,p){"use strict";var a,P=this&&this.__extends||(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var f in i)i.hasOwnProperty(f)&&(r[f]=i[f])},function(r,i){function f(){this.constructor=r}a(r,i),r.prototype=null===i?Object.create(i):(f.prototype=i.prototype,new f)});Object.defineProperty(O,"__esModule",{value:!0});var T=function(a){function r(i){var f=this;return(f=a.call(this,i="[Parchment] "+i)||this).message=i,f.name=f.constructor.name,f}return P(r,a),r}(Error);O.ParchmentError=T;var o,w={},m={},y={},c={};function u(a,r){var i;if(void 0===r&&(r=o.ANY),"string"==typeof a)i=c[a]||w[a];else if(a instanceof Text||a.nodeType===Node.TEXT_NODE)i=c.text;else if("number"==typeof a)a&o.LEVEL&o.BLOCK?i=c.block:a&o.LEVEL&o.INLINE&&(i=c.inline);else if(a instanceof HTMLElement){var f=(a.getAttribute("class")||"").split(/\s+/);for(var n in f)if(i=m[f[n]])break;i=i||y[a.tagName]}return null==i?null:r&o.LEVEL&i.scope&&r&o.TYPE&i.scope?i:null}O.DATA_KEY="__blot",function(a){a[a.TYPE=3]="TYPE",a[a.LEVEL=12]="LEVEL",a[a.ATTRIBUTE=13]="ATTRIBUTE",a[a.BLOT=14]="BLOT",a[a.INLINE=7]="INLINE",a[a.BLOCK=11]="BLOCK",a[a.BLOCK_BLOT=10]="BLOCK_BLOT",a[a.INLINE_BLOT=6]="INLINE_BLOT",a[a.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",a[a.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",a[a.ANY=15]="ANY"}(o=O.Scope||(O.Scope={})),O.create=function t(a,r){var i=u(a);if(null==i)throw new T("Unable to create "+a+" blot");var f=i,n=a instanceof Node||a.nodeType===Node.TEXT_NODE?a:f.create(r);return new f(n,r)},O.find=function e(a,r){return void 0===r&&(r=!1),null==a?null:null!=a[O.DATA_KEY]?a[O.DATA_KEY].blot:r?e(a.parentNode,r):null},O.query=u,O.register=function l(){for(var a=[],r=0;r<arguments.length;r++)a[r]=arguments[r];if(a.length>1)return a.map(function(n){return l(n)});var i=a[0];if("string"!=typeof i.blotName&&"string"!=typeof i.attrName)throw new T("Invalid definition");if("abstract"===i.blotName)throw new T("Cannot register abstract class");return c[i.blotName||i.attrName]=i,"string"==typeof i.keyName?w[i.keyName]=i:(null!=i.className&&(m[i.className]=i),null!=i.tagName&&(i.tagName=Array.isArray(i.tagName)?i.tagName.map(function(n){return n.toUpperCase()}):i.tagName.toUpperCase(),(Array.isArray(i.tagName)?i.tagName:[i.tagName]).forEach(function(n){(null==y[n]||null==i.className)&&(y[n]=i)}))),i}},function(B,O,p){var P=p(51),T=p(11),w=p(3),m=p(20),y=String.fromCharCode(0),c=function(o){this.ops=Array.isArray(o)?o:null!=o&&Array.isArray(o.ops)?o.ops:[]};c.prototype.insert=function(o,t){var e={};return 0===o.length?this:(e.insert=o,null!=t&&"object"==typeof t&&Object.keys(t).length>0&&(e.attributes=t),this.push(e))},c.prototype.delete=function(o){return o<=0?this:this.push({delete:o})},c.prototype.retain=function(o,t){if(o<=0)return this;var e={retain:o};return null!=t&&"object"==typeof t&&Object.keys(t).length>0&&(e.attributes=t),this.push(e)},c.prototype.push=function(o){var t=this.ops.length,e=this.ops[t-1];if(o=w(!0,{},o),"object"==typeof e){if("number"==typeof o.delete&&"number"==typeof e.delete)return this.ops[t-1]={delete:e.delete+o.delete},this;if("number"==typeof e.delete&&null!=o.insert&&"object"!=typeof(e=this.ops[(t-=1)-1]))return this.ops.unshift(o),this;if(T(o.attributes,e.attributes)){if("string"==typeof o.insert&&"string"==typeof e.insert)return this.ops[t-1]={insert:e.insert+o.insert},"object"==typeof o.attributes&&(this.ops[t-1].attributes=o.attributes),this;if("number"==typeof o.retain&&"number"==typeof e.retain)return this.ops[t-1]={retain:e.retain+o.retain},"object"==typeof o.attributes&&(this.ops[t-1].attributes=o.attributes),this}}return t===this.ops.length?this.ops.push(o):this.ops.splice(t,0,o),this},c.prototype.chop=function(){var o=this.ops[this.ops.length-1];return o&&o.retain&&!o.attributes&&this.ops.pop(),this},c.prototype.filter=function(o){return this.ops.filter(o)},c.prototype.forEach=function(o){this.ops.forEach(o)},c.prototype.map=function(o){return this.ops.map(o)},c.prototype.partition=function(o){var t=[],e=[];return this.forEach(function(u){(o(u)?t:e).push(u)}),[t,e]},c.prototype.reduce=function(o,t){return this.ops.reduce(o,t)},c.prototype.changeLength=function(){return this.reduce(function(o,t){return t.insert?o+m.length(t):t.delete?o-t.delete:o},0)},c.prototype.length=function(){return this.reduce(function(o,t){return o+m.length(t)},0)},c.prototype.slice=function(o,t){o=o||0,"number"!=typeof t&&(t=1/0);for(var e=[],u=m.iterator(this.ops),l=0;l<t&&u.hasNext();){var a;l<o?a=u.next(o-l):(a=u.next(t-l),e.push(a)),l+=m.length(a)}return new c(e)},c.prototype.compose=function(o){var t=m.iterator(this.ops),e=m.iterator(o.ops),u=[],l=e.peek();if(null!=l&&"number"==typeof l.retain&&null==l.attributes){for(var a=l.retain;"insert"===t.peekType()&&t.peekLength()<=a;)a-=t.peekLength(),u.push(t.next());l.retain-a>0&&e.next(l.retain-a)}for(var r=new c(u);t.hasNext()||e.hasNext();)if("insert"===e.peekType())r.push(e.next());else if("delete"===t.peekType())r.push(t.next());else{var i=Math.min(t.peekLength(),e.peekLength()),f=t.next(i),n=e.next(i);if("number"==typeof n.retain){var s={};"number"==typeof f.retain?s.retain=i:s.insert=f.insert;var A=m.attributes.compose(f.attributes,n.attributes,"number"==typeof f.retain);if(A&&(s.attributes=A),r.push(s),!e.hasNext()&&T(r.ops[r.ops.length-1],s)){var g=new c(t.rest());return r.concat(g).chop()}}else"number"==typeof n.delete&&"number"==typeof f.retain&&r.push(n)}return r.chop()},c.prototype.concat=function(o){var t=new c(this.ops.slice());return o.ops.length>0&&(t.push(o.ops[0]),t.ops=t.ops.concat(o.ops.slice(1))),t},c.prototype.diff=function(o,t){if(this.ops===o.ops)return new c;var e=[this,o].map(function(i){return i.map(function(f){if(null!=f.insert)return"string"==typeof f.insert?f.insert:y;throw new Error("diff() called "+(i===o?"on":"with")+" non-document")}).join("")}),u=new c,l=P(e[0],e[1],t),a=m.iterator(this.ops),r=m.iterator(o.ops);return l.forEach(function(i){for(var f=i[1].length;f>0;){var n=0;switch(i[0]){case P.INSERT:n=Math.min(r.peekLength(),f),u.push(r.next(n));break;case P.DELETE:n=Math.min(f,a.peekLength()),a.next(n),u.delete(n);break;case P.EQUAL:n=Math.min(a.peekLength(),r.peekLength(),f);var s=a.next(n),A=r.next(n);T(s.insert,A.insert)?u.retain(n,m.attributes.diff(s.attributes,A.attributes)):u.push(A).delete(n)}f-=n}}),u.chop()},c.prototype.eachLine=function(o,t){t=t||"\n";for(var e=m.iterator(this.ops),u=new c,l=0;e.hasNext();){if("insert"!==e.peekType())return;var a=e.peek(),r=m.length(a)-e.peekLength(),i="string"==typeof a.insert?a.insert.indexOf(t,r)-r:-1;if(i<0)u.push(e.next());else if(i>0)u.push(e.next(i));else{if(!1===o(u,e.next(1).attributes||{},l))return;l+=1,u=new c}}u.length()>0&&o(u,{},l)},c.prototype.transform=function(o,t){if(t=!!t,"number"==typeof o)return this.transformPosition(o,t);for(var e=m.iterator(this.ops),u=m.iterator(o.ops),l=new c;e.hasNext()||u.hasNext();)if("insert"!==e.peekType()||!t&&"insert"===u.peekType())if("insert"===u.peekType())l.push(u.next());else{var a=Math.min(e.peekLength(),u.peekLength()),r=e.next(a),i=u.next(a);if(r.delete)continue;i.delete?l.push(i):l.retain(a,m.attributes.transform(r.attributes,i.attributes,t))}else l.retain(m.length(e.next()));return l.chop()},c.prototype.transformPosition=function(o,t){t=!!t;for(var e=m.iterator(this.ops),u=0;e.hasNext()&&u<=o;){var l=e.peekLength(),a=e.peekType();e.next(),"delete"!==a?("insert"===a&&(u<o||!t)&&(o+=l),u+=l):o-=Math.min(l,o-u)}return o},B.exports=c},function(B,O){"use strict";var p=Object.prototype.hasOwnProperty,P=Object.prototype.toString,T=Object.defineProperty,w=Object.getOwnPropertyDescriptor,m=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===P.call(e)},y=function(e){if(!e||"[object Object]"!==P.call(e))return!1;var a,u=p.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&p.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!u&&!l)return!1;for(a in e);return typeof a>"u"||p.call(e,a)},c=function(e,u){T&&"__proto__"===u.name?T(e,u.name,{enumerable:!0,configurable:!0,value:u.newValue,writable:!0}):e[u.name]=u.newValue},o=function(e,u){if("__proto__"===u){if(!p.call(e,u))return;if(w)return w(e,u).value}return e[u]};B.exports=function t(){var e,u,l,a,r,i,f=arguments[0],n=1,s=arguments.length,A=!1;for("boolean"==typeof f&&(A=f,f=arguments[1]||{},n=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});n<s;++n)if(null!=(e=arguments[n]))for(u in e)l=o(f,u),f!==(a=o(e,u))&&(A&&a&&(y(a)||(r=m(a)))?(r?(r=!1,i=l&&m(l)?l:[]):i=l&&y(l)?l:{},c(f,{name:u,newValue:t(A,i,a)})):typeof a<"u"&&c(f,{name:u,newValue:a}));return f}},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.BlockEmbed=O.bubbleFormats=void 0;var P=function(){function h(d,k){for(var _=0;_<k.length;_++){var q=k[_];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(d,q.key,q)}}return function(d,k,_){return k&&h(d.prototype,k),_&&h(d,_),d}}(),T=function h(d,k,_){null===d&&(d=Function.prototype);var q=Object.getOwnPropertyDescriptor(d,k);if(void 0===q){var D=Object.getPrototypeOf(d);return null===D?void 0:h(D,k,_)}if("value"in q)return q.value;var C=q.get;return void 0===C?void 0:C.call(_)},m=f(p(3)),c=f(p(2)),t=f(p(0)),u=f(p(16)),a=f(p(6)),i=f(p(7));function f(h){return h&&h.__esModule?h:{default:h}}function n(h,d){if(!(h instanceof d))throw new TypeError("Cannot call a class as a function")}function s(h,d){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!d||"object"!=typeof d&&"function"!=typeof d?h:d}function A(h,d){if("function"!=typeof d&&null!==d)throw new TypeError("Super expression must either be null or a function, not "+typeof d);h.prototype=Object.create(d&&d.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(h,d):h.__proto__=d)}var g=1,b=function(h){function d(){return n(this,d),s(this,(d.__proto__||Object.getPrototypeOf(d)).apply(this,arguments))}return A(d,h),P(d,[{key:"attach",value:function(){T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"attach",this).call(this),this.attributes=new t.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return(new c.default).insert(this.value(),(0,m.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(_,q){var D=t.default.query(_,t.default.Scope.BLOCK_ATTRIBUTE);null!=D&&this.attributes.attribute(D,q)}},{key:"formatAt",value:function(_,q,D,C){this.format(D,C)}},{key:"insertAt",value:function(_,q,D){if("string"==typeof q&&q.endsWith("\n")){var C=t.default.create(N.blotName);this.parent.insertBefore(C,0===_?this:this.next),C.insertAt(0,q.slice(0,-1))}else T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"insertAt",this).call(this,_,q,D)}}]),d}(t.default.Embed);b.scope=t.default.Scope.BLOCK_BLOT;var N=function(h){function d(k){n(this,d);var _=s(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,k));return _.cache={},_}return A(d,h),P(d,[{key:"delta",value:function(){return null==this.cache.delta&&(this.cache.delta=this.descendants(t.default.Leaf).reduce(function(_,q){return 0===q.length()?_:_.insert(q.value(),v(q))},new c.default).insert("\n",v(this))),this.cache.delta}},{key:"deleteAt",value:function(_,q){T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"deleteAt",this).call(this,_,q),this.cache={}}},{key:"formatAt",value:function(_,q,D,C){q<=0||(t.default.query(D,t.default.Scope.BLOCK)?_+q===this.length()&&this.format(D,C):T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"formatAt",this).call(this,_,Math.min(q,this.length()-_-1),D,C),this.cache={})}},{key:"insertAt",value:function(_,q,D){if(null!=D)return T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"insertAt",this).call(this,_,q,D);if(0!==q.length){var C=q.split("\n"),Z=C.shift();Z.length>0&&(_<this.length()-1||null==this.children.tail?T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"insertAt",this).call(this,Math.min(_,this.length()-1),Z):this.children.tail.insertAt(this.children.tail.length(),Z),this.cache={});var I=this;C.reduce(function(R,E){return(I=I.split(R,!0)).insertAt(0,E),E.length},_+Z.length)}}},{key:"insertBefore",value:function(_,q){var D=this.children.head;T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"insertBefore",this).call(this,_,q),D instanceof u.default&&D.remove(),this.cache={}}},{key:"length",value:function(){return null==this.cache.length&&(this.cache.length=T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"length",this).call(this)+g),this.cache.length}},{key:"moveChildren",value:function(_,q){T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"moveChildren",this).call(this,_,q),this.cache={}}},{key:"optimize",value:function(_){T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"optimize",this).call(this,_),this.cache={}}},{key:"path",value:function(_){return T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"path",this).call(this,_,!0)}},{key:"removeChild",value:function(_){T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"removeChild",this).call(this,_),this.cache={}}},{key:"split",value:function(_){var q=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(q&&(0===_||_>=this.length()-g)){var D=this.clone();return 0===_?(this.parent.insertBefore(D,this),this):(this.parent.insertBefore(D,this.next),D)}var C=T(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"split",this).call(this,_,q);return this.cache={},C}}]),d}(t.default.Block);function v(h){var d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return null==h||("function"==typeof h.formats&&(d=(0,m.default)(d,h.formats())),null==h.parent||"scroll"==h.parent.blotName||h.parent.statics.scope!==h.statics.scope)?d:v(h.parent,d)}N.blotName="block",N.tagName="P",N.defaultChild="break",N.allowedChildren=[a.default,t.default.Embed,i.default],O.bubbleFormats=v,O.BlockEmbed=b,O.default=N},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.overload=O.expandConfig=void 0;var P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(I){return typeof I}:function(I){return I&&"function"==typeof Symbol&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},T=function(R,E){if(Array.isArray(R))return R;if(Symbol.iterator in Object(R))return function I(R,E){var S=[],L=!0,F=!1,M=void 0;try{for(var j,x=R[Symbol.iterator]();!(L=(j=x.next()).done)&&(S.push(j.value),!E||S.length!==E);L=!0);}catch(U){F=!0,M=U}finally{try{!L&&x.return&&x.return()}finally{if(F)throw M}}return S}(R,E);throw new TypeError("Invalid attempt to destructure non-iterable instance")},w=function(){function I(R,E){for(var S=0;S<E.length;S++){var L=E[S];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(R,L.key,L)}}return function(R,E,S){return E&&I(R.prototype,E),S&&I(R,S),R}}();p(50);var y=v(p(2)),o=v(p(14)),e=v(p(8)),l=v(p(9)),r=v(p(0)),i=p(15),f=v(i),s=v(p(3)),g=v(p(10)),N=v(p(34));function v(I){return I&&I.__esModule?I:{default:I}}function h(I,R,E){return R in I?Object.defineProperty(I,R,{value:E,enumerable:!0,configurable:!0,writable:!0}):I[R]=E,I}var k=(0,g.default)("quill"),_=function(){function I(R){var E=this,S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function d(I,R){if(!(I instanceof R))throw new TypeError("Cannot call a class as a function")}(this,I),this.options=q(R,S),this.container=this.options.container,null==this.container)return k.error("Invalid Quill container",R);this.options.debug&&I.debug(this.options.debug);var L=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new e.default,this.scroll=r.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new o.default(this.scroll),this.selection=new f.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(e.default.events.EDITOR_CHANGE,function(M){M===e.default.events.TEXT_CHANGE&&E.root.classList.toggle("ql-blank",E.editor.isBlank())}),this.emitter.on(e.default.events.SCROLL_UPDATE,function(M,x){var j=E.selection.lastRange,U=j&&0===j.length?j.index:void 0;D.call(E,function(){return E.editor.update(null,x,U)},M)});var F=this.clipboard.convert("<div class='ql-editor' style=\"white-space: normal;\">"+L+"<p><br></p></div>");this.setContents(F),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return w(I,null,[{key:"debug",value:function(E){!0===E&&(E="log"),g.default.level(E)}},{key:"find",value:function(E){return E.__quill||r.default.find(E)}},{key:"import",value:function(E){return null==this.imports[E]&&k.error("Cannot import "+E+". Are you sure it was registered?"),this.imports[E]}},{key:"register",value:function(E,S){var L=this,F=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("string"!=typeof E){var M=E.attrName||E.blotName;"string"==typeof M?this.register("formats/"+M,E,S):Object.keys(E).forEach(function(x){L.register(x,E[x],S)})}else null!=this.imports[E]&&!F&&k.warn("Overwriting "+E+" with",S),this.imports[E]=S,(E.startsWith("blots/")||E.startsWith("formats/"))&&"abstract"!==S.blotName?r.default.register(S):E.startsWith("modules")&&"function"==typeof S.register&&S.register()}}]),w(I,[{key:"addContainer",value:function(E){var S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof E){var L=E;(E=document.createElement("div")).classList.add(L)}return this.container.insertBefore(E,S),E}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(E,S,L){var F=this,M=C(E,S,L),x=T(M,4);return D.call(this,function(){return F.editor.deleteText(E,S)},L=x[3],E=x[0],-1*(S=x[1]))}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var E=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(E),this.container.classList.toggle("ql-disabled",!E)}},{key:"focus",value:function(){var E=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=E,this.scrollIntoView()}},{key:"format",value:function(E,S){var L=this;return D.call(this,function(){var M=L.getSelection(!0),x=new y.default;if(null==M)return x;if(r.default.query(E,r.default.Scope.BLOCK))x=L.editor.formatLine(M.index,M.length,h({},E,S));else{if(0===M.length)return L.selection.format(E,S),x;x=L.editor.formatText(M.index,M.length,h({},E,S))}return L.setSelection(M,e.default.sources.SILENT),x},arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.default.sources.API)}},{key:"formatLine",value:function(E,S,L,F,M){var j,x=this,U=C(E,S,L,F,M),H=T(U,4);return S=H[1],j=H[2],D.call(this,function(){return x.editor.formatLine(E,S,j)},M=H[3],E=H[0],0)}},{key:"formatText",value:function(E,S,L,F,M){var j,x=this,U=C(E,S,L,F,M),H=T(U,4);return S=H[1],j=H[2],D.call(this,function(){return x.editor.formatText(E,S,j)},M=H[3],E=H[0],0)}},{key:"getBounds",value:function(E){var L;L="number"==typeof E?this.selection.getBounds(E,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0):this.selection.getBounds(E.index,E.length);var F=this.container.getBoundingClientRect();return{bottom:L.bottom-F.top,height:L.height,left:L.left-F.left,right:L.right-F.left,top:L.top-F.top,width:L.width}}},{key:"getContents",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-E,L=C(E,S),F=T(L,2);return this.editor.getContents(E=F[0],S=F[1])}},{key:"getFormat",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0);return"number"==typeof E?this.editor.getFormat(E,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0):this.editor.getFormat(E.index,E.length)}},{key:"getIndex",value:function(E){return E.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(E){return this.scroll.leaf(E)}},{key:"getLine",value:function(E){return this.scroll.line(E)}},{key:"getLines",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof E?this.scroll.lines(E.index,E.length):this.scroll.lines(E,S)}},{key:"getModule",value:function(E){return this.theme.modules[E]}},{key:"getSelection",value:function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-E,L=C(E,S),F=T(L,2);return this.editor.getText(E=F[0],S=F[1])}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(E,S,L){var F=this;return D.call(this,function(){return F.editor.insertEmbed(E,S,L)},arguments.length>3&&void 0!==arguments[3]?arguments[3]:I.sources.API,E)}},{key:"insertText",value:function(E,S,L,F,M){var j,x=this,U=C(E,0,L,F,M),H=T(U,4);return j=H[2],D.call(this,function(){return x.editor.insertText(E,S,j)},M=H[3],E=H[0],S.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(E,S,L){this.clipboard.dangerouslyPasteHTML(E,S,L)}},{key:"removeFormat",value:function(E,S,L){var F=this,M=C(E,S,L),x=T(M,4);return S=x[1],D.call(this,function(){return F.editor.removeFormat(E,S)},L=x[3],E=x[0])}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(E){var S=this;return D.call(this,function(){E=new y.default(E);var F=S.getLength(),M=S.editor.deleteText(0,F),x=S.editor.applyDelta(E),j=x.ops[x.ops.length-1];return null!=j&&"string"==typeof j.insert&&"\n"===j.insert[j.insert.length-1]&&(S.editor.deleteText(S.getLength()-1,1),x.delete(1)),M.compose(x)},arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.default.sources.API)}},{key:"setSelection",value:function(E,S,L){if(null==E)this.selection.setRange(null,S||I.sources.API);else{var F=C(E,S,L),M=T(F,4);L=M[3],this.selection.setRange(new i.Range(E=M[0],S=M[1]),L),L!==e.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(E){var S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.default.sources.API,L=(new y.default).insert(E);return this.setContents(L,S)}},{key:"update",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.default.sources.USER,S=this.scroll.update(E);return this.selection.update(E),S}},{key:"updateContents",value:function(E){var S=this,L=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.default.sources.API;return D.call(this,function(){return E=new y.default(E),S.editor.applyDelta(E,L)},L,!0)}}]),I}();function q(I,R){if((R=(0,s.default)(!0,{container:I,modules:{clipboard:!0,keyboard:!0,history:!0}},R)).theme&&R.theme!==_.DEFAULTS.theme){if(R.theme=_.import("themes/"+R.theme),null==R.theme)throw new Error("Invalid theme "+R.theme+". Did you register it?")}else R.theme=N.default;var E=(0,s.default)(!0,{},R.theme.DEFAULTS);[E,R].forEach(function(F){F.modules=F.modules||{},Object.keys(F.modules).forEach(function(M){!0===F.modules[M]&&(F.modules[M]={})})});var L=Object.keys(E.modules).concat(Object.keys(R.modules)).reduce(function(F,M){var x=_.import("modules/"+M);return null==x?k.error("Cannot load "+M+" module. Are you sure you registered it?"):F[M]=x.DEFAULTS||{},F},{});return null!=R.modules&&R.modules.toolbar&&R.modules.toolbar.constructor!==Object&&(R.modules.toolbar={container:R.modules.toolbar}),R=(0,s.default)(!0,{},_.DEFAULTS,{modules:L},E,R),["bounds","container","scrollingContainer"].forEach(function(F){"string"==typeof R[F]&&(R[F]=document.querySelector(R[F]))}),R.modules=Object.keys(R.modules).reduce(function(F,M){return R.modules[M]&&(F[M]=R.modules[M]),F},{}),R}function D(I,R,E,S){if(this.options.strict&&!this.isEnabled()&&R===e.default.sources.USER)return new y.default;var L=null==E?null:this.getSelection(),F=this.editor.delta,M=I();if(null!=L&&(!0===E&&(E=L.index),null==S?L=Z(L,M,R):0!==S&&(L=Z(L,E,S,R)),this.setSelection(L,e.default.sources.SILENT)),M.length()>0){var x,U,j=[e.default.events.TEXT_CHANGE,M,F,R];(x=this.emitter).emit.apply(x,[e.default.events.EDITOR_CHANGE].concat(j)),R!==e.default.sources.SILENT&&(U=this.emitter).emit.apply(U,j)}return M}function C(I,R,E,S,L){var F={};return"number"==typeof I.index&&"number"==typeof I.length?"number"!=typeof R?(L=S,S=E,E=R,R=I.length,I=I.index):(R=I.length,I=I.index):"number"!=typeof R&&(L=S,S=E,E=R,R=0),"object"===(typeof E>"u"?"undefined":P(E))?(F=E,L=S):"string"==typeof E&&(null!=S?F[E]=S:L=E),[I,R,F,L=L||e.default.sources.API]}function Z(I,R,E,S){if(null==I)return null;var L=void 0,F=void 0;if(R instanceof y.default){var M=[I.index,I.index+I.length].map(function(H){return R.transformPosition(H,S!==e.default.sources.USER)}),x=T(M,2);L=x[0],F=x[1]}else{var j=[I.index,I.index+I.length].map(function(H){return H<R||H===R&&S===e.default.sources.USER?H:E>=0?H+E:Math.max(R,H+E)}),U=T(j,2);L=U[0],F=U[1]}return new i.Range(L,F-L)}_.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},_.events=e.default.events,_.sources=e.default.sources,_.version="1.3.7",_.imports={delta:y.default,parchment:r.default,"core/module":l.default,"core/theme":N.default},O.expandConfig=q,O.overload=C,O.default=_},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),T=function a(r,i,f){null===r&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(void 0===n){var s=Object.getPrototypeOf(r);return null===s?void 0:a(s,i,f)}if("value"in n)return n.value;var A=n.get;return void 0===A?void 0:A.call(f)},m=o(p(7)),c=o(p(0));function o(a){return a&&a.__esModule?a:{default:a}}var l=function(a){function r(){return function t(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}(this,r),function e(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?a:r}(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return function u(a,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}(r,a),P(r,[{key:"formatAt",value:function(f,n,s,A){if(r.compare(this.statics.blotName,s)<0&&c.default.query(s,c.default.Scope.BLOT)){var g=this.isolate(f,n);A&&g.wrap(s,A)}else T(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"formatAt",this).call(this,f,n,s,A)}},{key:"optimize",value:function(f){if(T(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"optimize",this).call(this,f),this.parent instanceof r&&r.compare(this.statics.blotName,this.parent.statics.blotName)>0){var n=this.parent.isolate(this.offset(),this.length());this.moveChildren(n),n.wrap(this)}}}],[{key:"compare",value:function(f,n){var s=r.order.indexOf(f),A=r.order.indexOf(n);return s>=0||A>=0?s-A:f===n?0:f<n?-1:1}}]),r}(c.default.Inline);l.allowedChildren=[l,c.default.Embed,m.default],l.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],O.default=l},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var o=function(t){function e(){return function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function c(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(function w(t){return t&&t.__esModule?t:{default:t}}(p(0)).default.Text);O.default=o},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function i(f,n){for(var s=0;s<n.length;s++){var A=n[s];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(f,A.key,A)}}return function(f,n,s){return n&&i(f.prototype,n),s&&i(f,s),f}}(),T=function i(f,n,s){null===f&&(f=Function.prototype);var A=Object.getOwnPropertyDescriptor(f,n);if(void 0===A){var g=Object.getPrototypeOf(f);return null===g?void 0:i(g,n,s)}if("value"in A)return A.value;var b=A.get;return void 0===b?void 0:b.call(s)},m=o(p(54));function o(i){return i&&i.__esModule?i:{default:i}}var l=(0,o(p(10)).default)("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(function(i){document.addEventListener(i,function(){for(var f=arguments.length,n=Array(f),s=0;s<f;s++)n[s]=arguments[s];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(A){var g;A.__quill&&A.__quill.emitter&&(g=A.__quill.emitter).handleDOM.apply(g,n)})})});var r=function(i){function f(){!function t(i,f){if(!(i instanceof f))throw new TypeError("Cannot call a class as a function")}(this,f);var n=function e(i,f){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!=typeof f&&"function"!=typeof f?i:f}(this,(f.__proto__||Object.getPrototypeOf(f)).call(this));return n.listeners={},n.on("error",l.error),n}return function u(i,f){if("function"!=typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);i.prototype=Object.create(f&&f.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(i,f):i.__proto__=f)}(f,i),P(f,[{key:"emit",value:function(){l.log.apply(l,arguments),T(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(s){for(var A=arguments.length,g=Array(A>1?A-1:0),b=1;b<A;b++)g[b-1]=arguments[b];(this.listeners[s.type]||[]).forEach(function(N){var v=N.node,h=N.handler;(s.target===v||v.contains(s.target))&&h.apply(void 0,[s].concat(g))})}},{key:"listenDOM",value:function(s,A,g){this.listeners[s]||(this.listeners[s]=[]),this.listeners[s].push({node:A,handler:g})}}]),f}(m.default);r.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},r.sources={API:"api",SILENT:"silent",USER:"user"},O.default=r},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var T=function w(m){var y=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(function P(w,m){if(!(w instanceof m))throw new TypeError("Cannot call a class as a function")})(this,w),this.quill=m,this.options=y};T.DEFAULTS={},O.default=T},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=["error","warn","log","info"],T="warn";function w(y){if(P.indexOf(y)<=P.indexOf(T)){for(var c,o=arguments.length,t=Array(o>1?o-1:0),e=1;e<o;e++)t[e-1]=arguments[e];(c=console)[y].apply(c,t)}}function m(y){return P.reduce(function(c,o){return c[o]=w.bind(console,o,y),c},{})}w.level=m.level=function(y){T=y},O.default=m},function(B,O,p){var P=Array.prototype.slice,T=p(52),w=p(53),m=B.exports=function(t,e,u){return u||(u={}),t===e||(t instanceof Date&&e instanceof Date?t.getTime()===e.getTime():!t||!e||"object"!=typeof t&&"object"!=typeof e?u.strict?t===e:t==e:function o(t,e,u){var l,a;if(y(t)||y(e)||t.prototype!==e.prototype)return!1;if(w(t))return!!w(e)&&(t=P.call(t),e=P.call(e),m(t,e,u));if(c(t)){if(!c(e)||t.length!==e.length)return!1;for(l=0;l<t.length;l++)if(t[l]!==e[l])return!1;return!0}try{var r=T(t),i=T(e)}catch{return!1}if(r.length!=i.length)return!1;for(r.sort(),i.sort(),l=r.length-1;l>=0;l--)if(r[l]!=i[l])return!1;for(l=r.length-1;l>=0;l--)if(!m(t[a=r[l]],e[a],u))return!1;return typeof t==typeof e}(t,e,u))};function y(t){return null==t}function c(t){return!(!t||"object"!=typeof t||"number"!=typeof t.length||"function"!=typeof t.copy||"function"!=typeof t.slice||t.length>0&&"number"!=typeof t[0])}},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=p(1),T=function(){function w(m,y,c){void 0===c&&(c={}),this.attrName=m,this.keyName=y,this.scope=null!=c.scope?c.scope&P.Scope.LEVEL|P.Scope.TYPE&P.Scope.ATTRIBUTE:P.Scope.ATTRIBUTE,null!=c.whitelist&&(this.whitelist=c.whitelist)}return w.keys=function(m){return[].map.call(m.attributes,function(y){return y.name})},w.prototype.add=function(m,y){return!!this.canAdd(m,y)&&(m.setAttribute(this.keyName,y),!0)},w.prototype.canAdd=function(m,y){return null!=P.query(m,P.Scope.BLOT&(this.scope|P.Scope.TYPE))&&(null==this.whitelist||("string"==typeof y?this.whitelist.indexOf(y.replace(/["']/g,""))>-1:this.whitelist.indexOf(y)>-1))},w.prototype.remove=function(m){m.removeAttribute(this.keyName)},w.prototype.value=function(m){var y=m.getAttribute(this.keyName);return this.canAdd(m,y)&&y?y:""},w}();O.default=T},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.Code=void 0;var P=function(N,v){if(Array.isArray(N))return N;if(Symbol.iterator in Object(N))return function b(N,v){var h=[],d=!0,k=!1,_=void 0;try{for(var D,q=N[Symbol.iterator]();!(d=(D=q.next()).done)&&(h.push(D.value),!v||h.length!==v);d=!0);}catch(C){k=!0,_=C}finally{try{!d&&q.return&&q.return()}finally{if(k)throw _}}return h}(N,v);throw new TypeError("Invalid attempt to destructure non-iterable instance")},T=function(){function b(N,v){for(var h=0;h<v.length;h++){var d=v[h];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(N,d.key,d)}}return function(N,v,h){return v&&b(N.prototype,v),h&&b(N,h),N}}(),w=function b(N,v,h){null===N&&(N=Function.prototype);var d=Object.getOwnPropertyDescriptor(N,v);if(void 0===d){var k=Object.getPrototypeOf(N);return null===k?void 0:b(k,v,h)}if("value"in d)return d.value;var _=d.get;return void 0===_?void 0:_.call(h)},y=i(p(2)),o=i(p(0)),e=i(p(4)),l=i(p(6)),r=i(p(7));function i(b){return b&&b.__esModule?b:{default:b}}function f(b,N){if(!(b instanceof N))throw new TypeError("Cannot call a class as a function")}function n(b,N){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!N||"object"!=typeof N&&"function"!=typeof N?b:N}function s(b,N){if("function"!=typeof N&&null!==N)throw new TypeError("Super expression must either be null or a function, not "+typeof N);b.prototype=Object.create(N&&N.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),N&&(Object.setPrototypeOf?Object.setPrototypeOf(b,N):b.__proto__=N)}var A=function(b){function N(){return f(this,N),n(this,(N.__proto__||Object.getPrototypeOf(N)).apply(this,arguments))}return s(N,b),N}(l.default);A.blotName="code",A.tagName="CODE";var g=function(b){function N(){return f(this,N),n(this,(N.__proto__||Object.getPrototypeOf(N)).apply(this,arguments))}return s(N,b),T(N,[{key:"delta",value:function(){var h=this,d=this.domNode.textContent;return d.endsWith("\n")&&(d=d.slice(0,-1)),d.split("\n").reduce(function(k,_){return k.insert(_).insert("\n",h.formats())},new y.default)}},{key:"format",value:function(h,d){if(h!==this.statics.blotName||!d){var k=this.descendant(r.default,this.length()-1),q=P(k,1)[0];q?.deleteAt(q.length()-1,1),w(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"format",this).call(this,h,d)}}},{key:"formatAt",value:function(h,d,k,_){if(0!==d&&null!=o.default.query(k,o.default.Scope.BLOCK)&&(k!==this.statics.blotName||_!==this.statics.formats(this.domNode))){var q=this.newlineIndex(h);if(!(q<0||q>=h+d)){var D=this.newlineIndex(h,!0)+1,C=q-D+1,Z=this.isolate(D,C),I=Z.next;Z.format(k,_),I instanceof N&&I.formatAt(0,h-D+d-C,k,_)}}}},{key:"insertAt",value:function(h,d,k){if(null==k){var _=this.descendant(r.default,h),q=P(_,2);q[0].insertAt(q[1],d)}}},{key:"length",value:function(){var h=this.domNode.textContent.length;return this.domNode.textContent.endsWith("\n")?h:h+1}},{key:"newlineIndex",value:function(h){if(arguments.length>1&&void 0!==arguments[1]&&arguments[1])return this.domNode.textContent.slice(0,h).lastIndexOf("\n");var k=this.domNode.textContent.slice(h).indexOf("\n");return k>-1?h+k:-1}},{key:"optimize",value:function(h){this.domNode.textContent.endsWith("\n")||this.appendChild(o.default.create("text","\n")),w(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"optimize",this).call(this,h);var d=this.next;null!=d&&d.prev===this&&d.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===d.statics.formats(d.domNode)&&(d.optimize(h),d.moveChildren(this),d.remove())}},{key:"replace",value:function(h){w(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"replace",this).call(this,h),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(d){var k=o.default.find(d);null==k?d.parentNode.removeChild(d):k instanceof o.default.Embed?k.remove():k.unwrap()})}}],[{key:"create",value:function(h){var d=w(N.__proto__||Object.getPrototypeOf(N),"create",this).call(this,h);return d.setAttribute("spellcheck",!1),d}},{key:"formats",value:function(){return!0}}]),N}(e.default);g.blotName="code-block",g.tagName="PRE",g.TAB="  ",O.Code=A,O.default=g},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(I){return typeof I}:function(I){return I&&"function"==typeof Symbol&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},T=function(R,E){if(Array.isArray(R))return R;if(Symbol.iterator in Object(R))return function I(R,E){var S=[],L=!0,F=!1,M=void 0;try{for(var j,x=R[Symbol.iterator]();!(L=(j=x.next()).done)&&(S.push(j.value),!E||S.length!==E);L=!0);}catch(U){F=!0,M=U}finally{try{!L&&x.return&&x.return()}finally{if(F)throw M}}return S}(R,E);throw new TypeError("Invalid attempt to destructure non-iterable instance")},w=function(){function I(R,E){for(var S=0;S<E.length;S++){var L=E[S];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(R,L.key,L)}}return function(R,E,S){return E&&I(R.prototype,E),S&&I(R,S),R}}(),y=d(p(2)),o=d(p(20)),e=d(p(0)),l=d(p(13)),r=d(p(24)),i=p(4),f=d(i),s=d(p(16)),g=d(p(21)),N=d(p(11)),h=d(p(3));function d(I){return I&&I.__esModule?I:{default:I}}var q=/^[ -~]*$/,D=function(){function I(R){(function _(I,R){if(!(I instanceof R))throw new TypeError("Cannot call a class as a function")})(this,I),this.scroll=R,this.delta=this.getDelta()}return w(I,[{key:"applyDelta",value:function(E){var S=this,L=!1;this.scroll.update();var F=this.scroll.length();return this.scroll.batchStart(),(E=function Z(I){return I.reduce(function(R,E){if(1===E.insert){var S=(0,g.default)(E.attributes);return delete S.image,R.insert({image:E.attributes.image},S)}if(null!=E.attributes&&(!0===E.attributes.list||!0===E.attributes.bullet)&&((E=(0,g.default)(E)).attributes.list?E.attributes.list="ordered":(E.attributes.list="bullet",delete E.attributes.bullet)),"string"==typeof E.insert){var L=E.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return R.insert(L,E.attributes)}return R.push(E)},new y.default)}(E)).reduce(function(M,x){var j=x.retain||x.delete||x.insert.length||1,U=x.attributes||{};if(null!=x.insert){if("string"==typeof x.insert){var H=x.insert;H.endsWith("\n")&&L&&(L=!1,H=H.slice(0,-1)),M>=F&&!H.endsWith("\n")&&(L=!0),S.scroll.insertAt(M,H);var V=S.scroll.line(M),Y=T(V,2),X=Y[0],Q=Y[1],nt=(0,h.default)({},(0,i.bubbleFormats)(X));if(X instanceof f.default){var rt=X.descendant(e.default.Leaf,Q),at=T(rt,1);nt=(0,h.default)(nt,(0,i.bubbleFormats)(at[0]))}U=o.default.attributes.diff(nt,U)||{}}else if("object"===P(x.insert)){var z=Object.keys(x.insert)[0];if(null==z)return M;S.scroll.insertAt(M,z,x.insert[z])}F+=j}return Object.keys(U).forEach(function(K){S.scroll.formatAt(M,j,K,U[K])}),M+j},0),E.reduce(function(M,x){return"number"==typeof x.delete?(S.scroll.deleteAt(M,x.delete),M):M+(x.retain||x.insert.length||1)},0),this.scroll.batchEnd(),this.update(E)}},{key:"deleteText",value:function(E,S){return this.scroll.deleteAt(E,S),this.update((new y.default).retain(E).delete(S))}},{key:"formatLine",value:function(E,S){var L=this,F=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.scroll.update(),Object.keys(F).forEach(function(M){if(null==L.scroll.whitelist||L.scroll.whitelist[M]){var x=L.scroll.lines(E,Math.max(S,1)),j=S;x.forEach(function(U){var H=U.length();if(U instanceof l.default){var V=E-U.offset(L.scroll),Y=U.newlineIndex(V+j)-V+1;U.formatAt(V,Y,M,F[M])}else U.format(M,F[M]);j-=H})}}),this.scroll.optimize(),this.update((new y.default).retain(E).retain(S,(0,g.default)(F)))}},{key:"formatText",value:function(E,S){var L=this,F=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Object.keys(F).forEach(function(M){L.scroll.formatAt(E,S,M,F[M])}),this.update((new y.default).retain(E).retain(S,(0,g.default)(F)))}},{key:"getContents",value:function(E,S){return this.delta.slice(E,E+S)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(E,S){return E.concat(S.delta())},new y.default)}},{key:"getFormat",value:function(E){var S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,L=[],F=[];0===S?this.scroll.path(E).forEach(function(x){var U=T(x,1)[0];U instanceof f.default?L.push(U):U instanceof e.default.Leaf&&F.push(U)}):(L=this.scroll.lines(E,S),F=this.scroll.descendants(e.default.Leaf,E,S));var M=[L,F].map(function(x){if(0===x.length)return{};for(var j=(0,i.bubbleFormats)(x.shift());Object.keys(j).length>0;){var U=x.shift();if(null==U)return j;j=C((0,i.bubbleFormats)(U),j)}return j});return h.default.apply(h.default,M)}},{key:"getText",value:function(E,S){return this.getContents(E,S).filter(function(L){return"string"==typeof L.insert}).map(function(L){return L.insert}).join("")}},{key:"insertEmbed",value:function(E,S,L){return this.scroll.insertAt(E,S,L),this.update((new y.default).retain(E).insert(function k(I,R,E){return R in I?Object.defineProperty(I,R,{value:E,enumerable:!0,configurable:!0,writable:!0}):I[R]=E,I}({},S,L)))}},{key:"insertText",value:function(E,S){var L=this,F=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return S=S.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(E,S),Object.keys(F).forEach(function(M){L.scroll.formatAt(E,S.length,M,F[M])}),this.update((new y.default).retain(E).insert(S,(0,g.default)(F)))}},{key:"isBlank",value:function(){if(0==this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;var E=this.scroll.children.head;return!(E.statics.blotName!==f.default.blotName||E.children.length>1)&&E.children.head instanceof s.default}},{key:"removeFormat",value:function(E,S){var L=this.getText(E,S),F=this.scroll.line(E+S),M=T(F,2),x=M[0],j=M[1],U=0,H=new y.default;null!=x&&(U=x instanceof l.default?x.newlineIndex(j)-j+1:x.length()-j,H=x.delta().slice(j,j+U-1).insert("\n"));var Y=this.getContents(E,S+U).diff((new y.default).insert(L).concat(H)),X=(new y.default).retain(E).concat(Y);return this.applyDelta(X)}},{key:"update",value:function(E){var S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],L=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,F=this.delta;if(1===S.length&&"characterData"===S[0].type&&S[0].target.data.match(q)&&e.default.find(S[0].target)){var M=e.default.find(S[0].target),x=(0,i.bubbleFormats)(M),j=M.offset(this.scroll),U=S[0].oldValue.replace(r.default.CONTENTS,""),H=(new y.default).insert(U),V=(new y.default).insert(M.value());E=(new y.default).retain(j).concat(H.diff(V,L)).reduce(function(X,Q){return Q.insert?X.insert(Q.insert,x):X.push(Q)},new y.default),this.delta=F.compose(E)}else this.delta=this.getDelta(),(!E||!(0,N.default)(F.compose(E),this.delta))&&(E=F.diff(this.delta,L));return E}}]),I}();function C(I,R){return Object.keys(R).reduce(function(E,S){return null==I[S]||(R[S]===I[S]?E[S]=R[S]:Array.isArray(R[S])?R[S].indexOf(I[S])<0&&(E[S]=R[S].concat([I[S]])):E[S]=[R[S],I[S]]),E},{})}O.default=D},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.Range=void 0;var P=function(N,v){if(Array.isArray(N))return N;if(Symbol.iterator in Object(N))return function b(N,v){var h=[],d=!0,k=!1,_=void 0;try{for(var D,q=N[Symbol.iterator]();!(d=(D=q.next()).done)&&(h.push(D.value),!v||h.length!==v);d=!0);}catch(C){k=!0,_=C}finally{try{!d&&q.return&&q.return()}finally{if(k)throw _}}return h}(N,v);throw new TypeError("Invalid attempt to destructure non-iterable instance")},T=function(){function b(N,v){for(var h=0;h<v.length;h++){var d=v[h];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(N,d.key,d)}}return function(N,v,h){return v&&b(N.prototype,v),h&&b(N,h),N}}(),m=r(p(0)),c=r(p(21)),t=r(p(11)),u=r(p(8));function r(b){return b&&b.__esModule?b:{default:b}}function i(b){if(Array.isArray(b)){for(var N=0,v=Array(b.length);N<b.length;N++)v[N]=b[N];return v}return Array.from(b)}function f(b,N){if(!(b instanceof N))throw new TypeError("Cannot call a class as a function")}var n=(0,r(p(10)).default)("quill:selection"),s=function b(N){var v=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;f(this,b),this.index=N,this.length=v},A=function(){function b(N,v){var h=this;f(this,b),this.emitter=v,this.scroll=N,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=m.default.create("cursor",this),this.lastRange=this.savedRange=new s(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){h.mouseDown||setTimeout(h.update.bind(h,u.default.sources.USER),1)}),this.emitter.on(u.default.events.EDITOR_CHANGE,function(d,k){d===u.default.events.TEXT_CHANGE&&k.length()>0&&h.update(u.default.sources.SILENT)}),this.emitter.on(u.default.events.SCROLL_BEFORE_UPDATE,function(){if(h.hasFocus()){var d=h.getNativeRange();null!=d&&d.start.node!==h.cursor.textNode&&h.emitter.once(u.default.events.SCROLL_UPDATE,function(){try{h.setNativeRange(d.start.node,d.start.offset,d.end.node,d.end.offset)}catch{}})}}),this.emitter.on(u.default.events.SCROLL_OPTIMIZE,function(d,k){if(k.range){var _=k.range;h.setNativeRange(_.startNode,_.startOffset,_.endNode,_.endOffset)}}),this.update(u.default.sources.SILENT)}return T(b,[{key:"handleComposition",value:function(){var v=this;this.root.addEventListener("compositionstart",function(){v.composing=!0}),this.root.addEventListener("compositionend",function(){if(v.composing=!1,v.cursor.parent){var h=v.cursor.restore();if(!h)return;setTimeout(function(){v.setNativeRange(h.startNode,h.startOffset,h.endNode,h.endOffset)},1)}})}},{key:"handleDragging",value:function(){var v=this;this.emitter.listenDOM("mousedown",document.body,function(){v.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){v.mouseDown=!1,v.update(u.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(v,h){if(null==this.scroll.whitelist||this.scroll.whitelist[v]){this.scroll.update();var d=this.getNativeRange();if(null!=d&&d.native.collapsed&&!m.default.query(v,m.default.Scope.BLOCK)){if(d.start.node!==this.cursor.textNode){var k=m.default.find(d.start.node,!1);if(null==k)return;if(k instanceof m.default.Leaf){var _=k.split(d.start.offset);k.parent.insertBefore(this.cursor,_)}else k.insertBefore(this.cursor,d.start.node);this.cursor.attach()}this.cursor.format(v,h),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(v){var h=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,d=this.scroll.length();v=Math.min(v,d-1),h=Math.min(v+h,d-1)-v;var k=void 0,_=this.scroll.leaf(v),q=P(_,2),D=q[0],C=q[1];if(null==D)return null;var Z=D.position(C,!0),I=P(Z,2);k=I[0],C=I[1];var R=document.createRange();if(h>0){R.setStart(k,C);var E=this.scroll.leaf(v+h),S=P(E,2);if(null==(D=S[0]))return null;var L=D.position(C=S[1],!0),F=P(L,2);return R.setEnd(k=F[0],C=F[1]),R.getBoundingClientRect()}var M="left",x=void 0;return k instanceof Text?(C<k.data.length?(R.setStart(k,C),R.setEnd(k,C+1)):(R.setStart(k,C-1),R.setEnd(k,C),M="right"),x=R.getBoundingClientRect()):(x=D.domNode.getBoundingClientRect(),C>0&&(M="right")),{bottom:x.top+x.height,height:x.height,left:x[M],right:x[M],top:x.top,width:0}}},{key:"getNativeRange",value:function(){var v=document.getSelection();if(null==v||v.rangeCount<=0)return null;var h=v.getRangeAt(0);if(null==h)return null;var d=this.normalizeNative(h);return n.info("getNativeRange",d),d}},{key:"getRange",value:function(){var v=this.getNativeRange();return null==v?[null,null]:[this.normalizedToRange(v),v]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(v){var h=this,d=[[v.start.node,v.start.offset]];v.native.collapsed||d.push([v.end.node,v.end.offset]);var k=d.map(function(D){var C=P(D,2),Z=C[0],I=C[1],R=m.default.find(Z,!0),E=R.offset(h.scroll);return 0===I?E:R instanceof m.default.Container?E+R.length():E+R.index(Z,I)}),_=Math.min(Math.max.apply(Math,i(k)),this.scroll.length()-1),q=Math.min.apply(Math,[_].concat(i(k)));return new s(q,_-q)}},{key:"normalizeNative",value:function(v){if(!g(this.root,v.startContainer)||!v.collapsed&&!g(this.root,v.endContainer))return null;var h={start:{node:v.startContainer,offset:v.startOffset},end:{node:v.endContainer,offset:v.endOffset},native:v};return[h.start,h.end].forEach(function(d){for(var k=d.node,_=d.offset;!(k instanceof Text)&&k.childNodes.length>0;)if(k.childNodes.length>_)k=k.childNodes[_],_=0;else{if(k.childNodes.length!==_)break;_=(k=k.lastChild)instanceof Text?k.data.length:k.childNodes.length+1}d.node=k,d.offset=_}),h}},{key:"rangeToNative",value:function(v){var h=this,d=v.collapsed?[v.index]:[v.index,v.index+v.length],k=[],_=this.scroll.length();return d.forEach(function(q,D){q=Math.min(_-1,q);var Z=h.scroll.leaf(q),I=P(Z,2),E=I[1],S=I[0].position(E,0!==D),L=P(S,2);k.push(L[0],E=L[1])}),k.length<2&&(k=k.concat(k)),k}},{key:"scrollIntoView",value:function(v){var h=this.lastRange;if(null!=h){var d=this.getBounds(h.index,h.length);if(null!=d){var k=this.scroll.length()-1,_=this.scroll.line(Math.min(h.index,k)),D=P(_,1)[0],C=D;if(h.length>0){var Z=this.scroll.line(Math.min(h.index+h.length,k));C=P(Z,1)[0]}if(null!=D&&null!=C){var R=v.getBoundingClientRect();d.top<R.top?v.scrollTop-=R.top-d.top:d.bottom>R.bottom&&(v.scrollTop+=d.bottom-R.bottom)}}}}},{key:"setNativeRange",value:function(v,h){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:v,k=arguments.length>3&&void 0!==arguments[3]?arguments[3]:h,_=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(n.info("setNativeRange",v,h,d,k),null==v||null!=this.root.parentNode&&null!=v.parentNode&&null!=d.parentNode){var q=document.getSelection();if(null!=q)if(null!=v){this.hasFocus()||this.root.focus();var D=(this.getNativeRange()||{}).native;if(null==D||_||v!==D.startContainer||h!==D.startOffset||d!==D.endContainer||k!==D.endOffset){"BR"==v.tagName&&(h=[].indexOf.call(v.parentNode.childNodes,v),v=v.parentNode),"BR"==d.tagName&&(k=[].indexOf.call(d.parentNode.childNodes,d),d=d.parentNode);var C=document.createRange();C.setStart(v,h),C.setEnd(d,k),q.removeAllRanges(),q.addRange(C)}}else q.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(v){var h=arguments.length>1&&void 0!==arguments[1]&&arguments[1],d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u.default.sources.API;if("string"==typeof h&&(d=h,h=!1),n.info("setRange",v),null!=v){var k=this.rangeToNative(v);this.setNativeRange.apply(this,i(k).concat([h]))}else this.setNativeRange(null);this.update(d)}},{key:"update",value:function(){var v=arguments.length>0&&void 0!==arguments[0]?arguments[0]:u.default.sources.USER,h=this.lastRange,d=this.getRange(),k=P(d,2),q=k[1];if(this.lastRange=k[0],null!=this.lastRange&&(this.savedRange=this.lastRange),!(0,t.default)(h,this.lastRange)){var D;!this.composing&&null!=q&&q.native.collapsed&&q.start.node!==this.cursor.textNode&&this.cursor.restore();var Z,C=[u.default.events.SELECTION_CHANGE,(0,c.default)(this.lastRange),(0,c.default)(h),v];(D=this.emitter).emit.apply(D,[u.default.events.EDITOR_CHANGE].concat(C)),v!==u.default.sources.SILENT&&(Z=this.emitter).emit.apply(Z,C)}}}]),b}();function g(b,N){return N instanceof Text&&(N=N.parentNode),b.contains(N)}O.Range=s,O.default=A},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),T=function u(l,a,r){null===l&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(void 0===i){var f=Object.getPrototypeOf(l);return null===f?void 0:u(f,a,r)}if("value"in i)return i.value;var n=i.get;return void 0===n?void 0:n.call(r)};var e=function(u){function l(){return function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}(this,l),function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!l||"object"!=typeof l&&"function"!=typeof l?u:l}(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return function t(u,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}(l,u),P(l,[{key:"insertInto",value:function(r,i){0===r.children.length?T(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"insertInto",this).call(this,r,i):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),l}(function y(u){return u&&u.__esModule?u:{default:u}}(p(0)).default.Embed);e.blotName="break",e.tagName="BR",O.default=e},function(B,O,p){"use strict";var o,P=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var u in e)e.hasOwnProperty(u)&&(t[u]=e[u])},function(t,e){function u(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(u.prototype=e.prototype,new u)});Object.defineProperty(O,"__esModule",{value:!0});var T=p(44),w=p(30),m=p(1),y=function(o){function t(e){var u=o.call(this,e)||this;return u.build(),u}return P(t,o),t.prototype.appendChild=function(e){this.insertBefore(e)},t.prototype.attach=function(){o.prototype.attach.call(this),this.children.forEach(function(e){e.attach()})},t.prototype.build=function(){var e=this;this.children=new T.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(u){try{var l=c(u);e.insertBefore(l,e.children.head||void 0)}catch(a){if(a instanceof m.ParchmentError)return;throw a}})},t.prototype.deleteAt=function(e,u){if(0===e&&u===this.length())return this.remove();this.children.forEachAt(e,u,function(l,a,r){l.deleteAt(a,r)})},t.prototype.descendant=function(e,u){var l=this.children.find(u),a=l[0],r=l[1];return null==e.blotName&&e(a)||null!=e.blotName&&a instanceof e?[a,r]:a instanceof t?a.descendant(e,r):[null,-1]},t.prototype.descendants=function(e,u,l){void 0===u&&(u=0),void 0===l&&(l=Number.MAX_VALUE);var a=[],r=l;return this.children.forEachAt(u,l,function(i,f,n){(null==e.blotName&&e(i)||null!=e.blotName&&i instanceof e)&&a.push(i),i instanceof t&&(a=a.concat(i.descendants(e,f,r))),r-=n}),a},t.prototype.detach=function(){this.children.forEach(function(e){e.detach()}),o.prototype.detach.call(this)},t.prototype.formatAt=function(e,u,l,a){this.children.forEachAt(e,u,function(r,i,f){r.formatAt(i,f,l,a)})},t.prototype.insertAt=function(e,u,l){var a=this.children.find(e),r=a[0];if(r)r.insertAt(a[1],u,l);else{var f=null==l?m.create("text",u):m.create(u,l);this.appendChild(f)}},t.prototype.insertBefore=function(e,u){if(null!=this.statics.allowedChildren&&!this.statics.allowedChildren.some(function(l){return e instanceof l}))throw new m.ParchmentError("Cannot insert "+e.statics.blotName+" into "+this.statics.blotName);e.insertInto(this,u)},t.prototype.length=function(){return this.children.reduce(function(e,u){return e+u.length()},0)},t.prototype.moveChildren=function(e,u){this.children.forEach(function(l){e.insertBefore(l,u)})},t.prototype.optimize=function(e){if(o.prototype.optimize.call(this,e),0===this.children.length)if(null!=this.statics.defaultChild){var u=m.create(this.statics.defaultChild);this.appendChild(u),u.optimize(e)}else this.remove()},t.prototype.path=function(e,u){void 0===u&&(u=!1);var l=this.children.find(e,u),a=l[0],r=l[1],i=[[this,e]];return a instanceof t?i.concat(a.path(r,u)):(null!=a&&i.push([a,r]),i)},t.prototype.removeChild=function(e){this.children.remove(e)},t.prototype.replace=function(e){e instanceof t&&e.moveChildren(this),o.prototype.replace.call(this,e)},t.prototype.split=function(e,u){if(void 0===u&&(u=!1),!u){if(0===e)return this;if(e===this.length())return this.next}var l=this.clone();return this.parent.insertBefore(l,this.next),this.children.forEachAt(e,this.length(),function(a,r,i){a=a.split(r,u),l.appendChild(a)}),l},t.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},t.prototype.update=function(e,u){var l=this,a=[],r=[];e.forEach(function(i){i.target===l.domNode&&"childList"===i.type&&(a.push.apply(a,i.addedNodes),r.push.apply(r,i.removedNodes))}),r.forEach(function(i){if(!(null!=i.parentNode&&"IFRAME"!==i.tagName&&document.body.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var f=m.find(i);null!=f&&(null==f.domNode.parentNode||f.domNode.parentNode===l.domNode)&&f.detach()}}),a.filter(function(i){return i.parentNode==l.domNode}).sort(function(i,f){return i===f?0:i.compareDocumentPosition(f)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(i){var f=null;null!=i.nextSibling&&(f=m.find(i.nextSibling));var n=c(i);(n.next!=f||null==n.next)&&(null!=n.parent&&n.parent.removeChild(l),l.insertBefore(n,f||void 0))})},t}(w.default);function c(o){var t=m.find(o);if(null==t)try{t=m.create(o)}catch{t=m.create(m.Scope.INLINE),[].slice.call(o.childNodes).forEach(function(u){t.domNode.appendChild(u)}),o.parentNode&&o.parentNode.replaceChild(t.domNode,o),t.attach()}return t}O.default=y},function(B,O,p){"use strict";var o,P=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var u in e)e.hasOwnProperty(u)&&(t[u]=e[u])},function(t,e){function u(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(u.prototype=e.prototype,new u)});Object.defineProperty(O,"__esModule",{value:!0});var T=p(12),w=p(31),m=p(17),y=p(1),c=function(o){function t(e){var u=o.call(this,e)||this;return u.attributes=new w.default(u.domNode),u}return P(t,o),t.formats=function(e){return"string"==typeof this.tagName||(Array.isArray(this.tagName)?e.tagName.toLowerCase():void 0)},t.prototype.format=function(e,u){var l=y.query(e);l instanceof T.default?this.attributes.attribute(l,u):u&&null!=l&&(e!==this.statics.blotName||this.formats()[e]!==u)&&this.replaceWith(e,u)},t.prototype.formats=function(){var e=this.attributes.values(),u=this.statics.formats(this.domNode);return null!=u&&(e[this.statics.blotName]=u),e},t.prototype.replaceWith=function(e,u){var l=o.prototype.replaceWith.call(this,e,u);return this.attributes.copy(l),l},t.prototype.update=function(e,u){var l=this;o.prototype.update.call(this,e,u),e.some(function(a){return a.target===l.domNode&&"attributes"===a.type})&&this.attributes.build()},t.prototype.wrap=function(e,u){var l=o.prototype.wrap.call(this,e,u);return l instanceof t&&l.statics.scope===this.statics.scope&&this.attributes.move(l),l},t}(m.default);O.default=c},function(B,O,p){"use strict";var y,P=this&&this.__extends||(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var t in o)o.hasOwnProperty(t)&&(c[t]=o[t])},function(c,o){function t(){this.constructor=c}y(c,o),c.prototype=null===o?Object.create(o):(t.prototype=o.prototype,new t)});Object.defineProperty(O,"__esModule",{value:!0});var T=p(30),w=p(1),m=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return P(c,y),c.value=function(o){return!0},c.prototype.index=function(o,t){return this.domNode===o||this.domNode.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(t,1):-1},c.prototype.position=function(o,t){var e=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return o>0&&(e+=1),[this.parent.domNode,e]},c.prototype.value=function(){var o;return(o={})[this.statics.blotName]=this.statics.value(this.domNode)||!0,o},c.scope=w.Scope.INLINE_BLOT,c}(T.default);O.default=m},function(B,O,p){var P=p(11),T=p(3),w={attributes:{compose:function(y,c,o){"object"!=typeof y&&(y={}),"object"!=typeof c&&(c={});var t=T(!0,{},c);for(var e in o||(t=Object.keys(t).reduce(function(u,l){return null!=t[l]&&(u[l]=t[l]),u},{})),y)void 0!==y[e]&&void 0===c[e]&&(t[e]=y[e]);return Object.keys(t).length>0?t:void 0},diff:function(y,c){"object"!=typeof y&&(y={}),"object"!=typeof c&&(c={});var o=Object.keys(y).concat(Object.keys(c)).reduce(function(t,e){return P(y[e],c[e])||(t[e]=void 0===c[e]?null:c[e]),t},{});return Object.keys(o).length>0?o:void 0},transform:function(y,c,o){if("object"!=typeof y)return c;if("object"==typeof c){if(!o)return c;var t=Object.keys(c).reduce(function(e,u){return void 0===y[u]&&(e[u]=c[u]),e},{});return Object.keys(t).length>0?t:void 0}}},iterator:function(y){return new m(y)},length:function(y){return"number"==typeof y.delete?y.delete:"number"==typeof y.retain?y.retain:"string"==typeof y.insert?y.insert.length:1}};function m(y){this.ops=y,this.index=0,this.offset=0}m.prototype.hasNext=function(){return this.peekLength()<1/0},m.prototype.next=function(y){y||(y=1/0);var c=this.ops[this.index];if(c){var o=this.offset,t=w.length(c);if(y>=t-o?(y=t-o,this.index+=1,this.offset=0):this.offset+=y,"number"==typeof c.delete)return{delete:y};var e={};return c.attributes&&(e.attributes=c.attributes),"number"==typeof c.retain?e.retain=y:e.insert="string"==typeof c.insert?c.insert.substr(o,y):c.insert,e}return{retain:1/0}},m.prototype.peek=function(){return this.ops[this.index]},m.prototype.peekLength=function(){return this.ops[this.index]?w.length(this.ops[this.index])-this.offset:1/0},m.prototype.peekType=function(){return this.ops[this.index]?"number"==typeof this.ops[this.index].delete?"delete":"number"==typeof this.ops[this.index].retain?"retain":"insert":"retain"},m.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var y=this.offset,c=this.index,o=this.next(),t=this.ops.slice(this.index);return this.offset=y,this.index=c,[o].concat(t)}return[]},B.exports=w},function(B,O){var p=function(){"use strict";function P(l,a){return null!=a&&l instanceof a}var T,w,m;try{T=Map}catch{T=function(){}}try{w=Set}catch{w=function(){}}try{m=Promise}catch{m=function(){}}function y(l,a,r,i,f){"object"==typeof a&&(r=a.depth,i=a.prototype,f=a.includeNonEnumerable,a=a.circular);var n=[],s=[],A=typeof Buffer<"u";return typeof a>"u"&&(a=!0),typeof r>"u"&&(r=1/0),function g(b,N){if(null===b)return null;if(0===N)return b;var v,h;if("object"!=typeof b)return b;if(P(b,T))v=new T;else if(P(b,w))v=new w;else if(P(b,m))v=new m(function(R,E){b.then(function(S){R(g(S,N-1))},function(S){E(g(S,N-1))})});else if(y.__isArray(b))v=[];else if(y.__isRegExp(b))v=new RegExp(b.source,u(b)),b.lastIndex&&(v.lastIndex=b.lastIndex);else if(y.__isDate(b))v=new Date(b.getTime());else{if(A&&Buffer.isBuffer(b))return v=Buffer.allocUnsafe?Buffer.allocUnsafe(b.length):new Buffer(b.length),b.copy(v),v;P(b,Error)?v=Object.create(b):typeof i>"u"?(h=Object.getPrototypeOf(b),v=Object.create(h)):(v=Object.create(i),h=i)}if(a){var d=n.indexOf(b);if(-1!=d)return s[d];n.push(b),s.push(v)}for(var k in P(b,T)&&b.forEach(function(R,E){var S=g(E,N-1),L=g(R,N-1);v.set(S,L)}),P(b,w)&&b.forEach(function(R){var E=g(R,N-1);v.add(E)}),b){var _;h&&(_=Object.getOwnPropertyDescriptor(h,k)),(!_||null!=_.set)&&(v[k]=g(b[k],N-1))}if(Object.getOwnPropertySymbols){var q=Object.getOwnPropertySymbols(b);for(k=0;k<q.length;k++){var D=q[k];(C=Object.getOwnPropertyDescriptor(b,D))&&!C.enumerable&&!f||(v[D]=g(b[D],N-1),C.enumerable||Object.defineProperty(v,D,{enumerable:!1}))}}if(f){var Z=Object.getOwnPropertyNames(b);for(k=0;k<Z.length;k++){var C,I=Z[k];(C=Object.getOwnPropertyDescriptor(b,I))&&C.enumerable||(v[I]=g(b[I],N-1),Object.defineProperty(v,I,{enumerable:!1}))}}return v}(l,r)}function c(l){return Object.prototype.toString.call(l)}function u(l){var a="";return l.global&&(a+="g"),l.ignoreCase&&(a+="i"),l.multiline&&(a+="m"),a}return y.clonePrototype=function(a){if(null===a)return null;var r=function(){};return r.prototype=a,new r},y.__objToStr=c,y.__isDate=function o(l){return"object"==typeof l&&"[object Date]"===c(l)},y.__isArray=function t(l){return"object"==typeof l&&"[object Array]"===c(l)},y.__isRegExp=function e(l){return"object"==typeof l&&"[object RegExp]"===c(l)},y.__getRegExpFlags=u,y}();"object"==typeof B&&B.exports&&(B.exports=p)},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(h,d){if(Array.isArray(h))return h;if(Symbol.iterator in Object(h))return function v(h,d){var k=[],_=!0,q=!1,D=void 0;try{for(var Z,C=h[Symbol.iterator]();!(_=(Z=C.next()).done)&&(k.push(Z.value),!d||k.length!==d);_=!0);}catch(I){q=!0,D=I}finally{try{!_&&C.return&&C.return()}finally{if(q)throw D}}return k}(h,d);throw new TypeError("Invalid attempt to destructure non-iterable instance")},T=function(){function v(h,d){for(var k=0;k<d.length;k++){var _=d[k];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(h,_.key,_)}}return function(h,d,k){return d&&v(h.prototype,d),k&&v(h,k),h}}(),w=function v(h,d,k){null===h&&(h=Function.prototype);var _=Object.getOwnPropertyDescriptor(h,d);if(void 0===_){var q=Object.getPrototypeOf(h);return null===q?void 0:v(q,d,k)}if("value"in _)return _.value;var D=_.get;return void 0===D?void 0:D.call(k)},y=n(p(0)),o=n(p(8)),t=p(4),e=n(t),l=n(p(16)),r=n(p(13)),f=n(p(25));function n(v){return v&&v.__esModule?v:{default:v}}function b(v){return v instanceof e.default||v instanceof t.BlockEmbed}var N=function(v){function h(d,k){!function s(v,h){if(!(v instanceof h))throw new TypeError("Cannot call a class as a function")}(this,h);var _=function A(v,h){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!h||"object"!=typeof h&&"function"!=typeof h?v:h}(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,d));return _.emitter=k.emitter,Array.isArray(k.whitelist)&&(_.whitelist=k.whitelist.reduce(function(q,D){return q[D]=!0,q},{})),_.domNode.addEventListener("DOMNodeInserted",function(){}),_.optimize(),_.enable(),_}return function g(v,h){if("function"!=typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+typeof h);v.prototype=Object.create(h&&h.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(v,h):v.__proto__=h)}(h,v),T(h,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(k,_){var q=this.line(k),D=P(q,2),C=D[0],Z=D[1],I=this.line(k+_),E=P(I,1)[0];if(w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"deleteAt",this).call(this,k,_),null!=E&&C!==E&&Z>0){if(C instanceof t.BlockEmbed||E instanceof t.BlockEmbed)return void this.optimize();if(C instanceof r.default){var S=C.newlineIndex(C.length(),!0);if(S>-1&&(C=C.split(S+1))===E)return void this.optimize()}else if(E instanceof r.default){var L=E.newlineIndex(0);L>-1&&E.split(L+1)}C.moveChildren(E,E.children.head instanceof l.default?null:E.children.head),C.remove()}this.optimize()}},{key:"enable",value:function(){this.domNode.setAttribute("contenteditable",!(arguments.length>0&&void 0!==arguments[0])||arguments[0])}},{key:"formatAt",value:function(k,_,q,D){null!=this.whitelist&&!this.whitelist[q]||(w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"formatAt",this).call(this,k,_,q,D),this.optimize())}},{key:"insertAt",value:function(k,_,q){if(null==q||null==this.whitelist||this.whitelist[_]){if(k>=this.length())if(null==q||null==y.default.query(_,y.default.Scope.BLOCK)){var D=y.default.create(this.statics.defaultChild);this.appendChild(D),null==q&&_.endsWith("\n")&&(_=_.slice(0,-1)),D.insertAt(0,_,q)}else{var C=y.default.create(_,q);this.appendChild(C)}else w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"insertAt",this).call(this,k,_,q);this.optimize()}}},{key:"insertBefore",value:function(k,_){if(k.statics.scope===y.default.Scope.INLINE_BLOT){var q=y.default.create(this.statics.defaultChild);q.appendChild(k),k=q}w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"insertBefore",this).call(this,k,_)}},{key:"leaf",value:function(k){return this.path(k).pop()||[null,-1]}},{key:"line",value:function(k){return k===this.length()?this.line(k-1):this.descendant(b,k)}},{key:"lines",value:function(){return function D(C,Z,I){var R=[],E=I;return C.children.forEachAt(Z,I,function(S,L,F){b(S)?R.push(S):S instanceof y.default.Container&&(R=R.concat(D(S,L,E))),E-=F}),R}(this,arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE)}},{key:"optimize",value:function(){var k=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],_=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"optimize",this).call(this,k,_),k.length>0&&this.emitter.emit(o.default.events.SCROLL_OPTIMIZE,k,_))}},{key:"path",value:function(k){return w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"path",this).call(this,k).slice(1)}},{key:"update",value:function(k){if(!0!==this.batch){var _=o.default.sources.USER;"string"==typeof k&&(_=k),Array.isArray(k)||(k=this.observer.takeRecords()),k.length>0&&this.emitter.emit(o.default.events.SCROLL_BEFORE_UPDATE,_,k),w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"update",this).call(this,k.concat([])),k.length>0&&this.emitter.emit(o.default.events.SCROLL_UPDATE,_,k)}}}]),h}(y.default.Scroll);N.blotName="scroll",N.className="ql-editor",N.tagName="DIV",N.defaultChild="block",N.allowedChildren=[e.default,t.BlockEmbed,f.default],O.default=N},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.SHORTKEY=O.default=void 0;var P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(x){return typeof x}:function(x){return x&&"function"==typeof Symbol&&x.constructor===Symbol&&x!==Symbol.prototype?"symbol":typeof x},T=function(j,U){if(Array.isArray(j))return j;if(Symbol.iterator in Object(j))return function x(j,U){var H=[],V=!0,Y=!1,X=void 0;try{for(var nt,Q=j[Symbol.iterator]();!(V=(nt=Q.next()).done)&&(H.push(nt.value),!U||H.length!==U);V=!0);}catch(rt){Y=!0,X=rt}finally{try{!V&&Q.return&&Q.return()}finally{if(Y)throw X}}return H}(j,U);throw new TypeError("Invalid attempt to destructure non-iterable instance")},w=function(){function x(j,U){for(var H=0;H<U.length;H++){var V=U[H];V.enumerable=V.enumerable||!1,V.configurable=!0,"value"in V&&(V.writable=!0),Object.defineProperty(j,V.key,V)}}return function(j,U,H){return U&&x(j.prototype,U),H&&x(j,H),j}}(),y=v(p(21)),o=v(p(11)),e=v(p(3)),l=v(p(2)),r=v(p(20)),f=v(p(0)),s=v(p(5)),g=v(p(10)),N=v(p(9));function v(x){return x&&x.__esModule?x:{default:x}}function h(x,j,U){return j in x?Object.defineProperty(x,j,{value:U,enumerable:!0,configurable:!0,writable:!0}):x[j]=U,x}var q=(0,g.default)("quill:keyboard"),D=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",C=function(x){function j(U,H){!function d(x,j){if(!(x instanceof j))throw new TypeError("Cannot call a class as a function")}(this,j);var V=function k(x,j){if(!x)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!j||"object"!=typeof j&&"function"!=typeof j?x:j}(this,(j.__proto__||Object.getPrototypeOf(j)).call(this,U,H));return V.bindings={},Object.keys(V.options.bindings).forEach(function(Y){"list autofill"===Y&&null!=U.scroll.whitelist&&!U.scroll.whitelist.list||V.options.bindings[Y]&&V.addBinding(V.options.bindings[Y])}),V.addBinding({key:j.keys.ENTER,shiftKey:null},S),V.addBinding({key:j.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(V.addBinding({key:j.keys.BACKSPACE},{collapsed:!0},I),V.addBinding({key:j.keys.DELETE},{collapsed:!0},R)):(V.addBinding({key:j.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},I),V.addBinding({key:j.keys.DELETE},{collapsed:!0,suffix:/^.?$/},R)),V.addBinding({key:j.keys.BACKSPACE},{collapsed:!1},E),V.addBinding({key:j.keys.DELETE},{collapsed:!1},E),V.addBinding({key:j.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},I),V.listen(),V}return function _(x,j){if("function"!=typeof j&&null!==j)throw new TypeError("Super expression must either be null or a function, not "+typeof j);x.prototype=Object.create(j&&j.prototype,{constructor:{value:x,enumerable:!1,writable:!0,configurable:!0}}),j&&(Object.setPrototypeOf?Object.setPrototypeOf(x,j):x.__proto__=j)}(j,x),w(j,null,[{key:"match",value:function(H,V){return V=M(V),!["altKey","ctrlKey","metaKey","shiftKey"].some(function(Y){return!!V[Y]!==H[Y]&&null!==V[Y]})&&V.key===(H.which||H.keyCode)}}]),w(j,[{key:"addBinding",value:function(H){var V=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},Y=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},X=M(H);if(null==X||null==X.key)return q.warn("Attempted to add invalid keyboard binding",X);"function"==typeof V&&(V={handler:V}),"function"==typeof Y&&(Y={handler:Y}),X=(0,e.default)(X,V,Y),this.bindings[X.key]=this.bindings[X.key]||[],this.bindings[X.key].push(X)}},{key:"listen",value:function(){var H=this;this.quill.root.addEventListener("keydown",function(V){if(!V.defaultPrevented){var X=(H.bindings[V.which||V.keyCode]||[]).filter(function(ot){return j.match(V,ot)});if(0!==X.length){var Q=H.quill.getSelection();if(null!=Q&&H.quill.hasFocus()){var nt=H.quill.getLine(Q.index),rt=T(nt,2),at=rt[0],lt=rt[1],z=H.quill.getLeaf(Q.index),K=T(z,2),$=K[0],G=K[1],W=0===Q.length?[$,G]:H.quill.getLeaf(Q.index+Q.length),J=T(W,2),tt=J[0],et=J[1],ut=$ instanceof f.default.Text?$.value().slice(0,G):"",ft=tt instanceof f.default.Text?tt.value().slice(et):"",it={collapsed:0===Q.length,empty:0===Q.length&&at.length()<=1,format:H.quill.getFormat(Q),offset:lt,prefix:ut,suffix:ft};X.some(function(ot){if(null!=ot.collapsed&&ot.collapsed!==it.collapsed||null!=ot.empty&&ot.empty!==it.empty||null!=ot.offset&&ot.offset!==it.offset)return!1;if(Array.isArray(ot.format)){if(ot.format.every(function(st){return null==it.format[st]}))return!1}else if("object"===P(ot.format)&&!Object.keys(ot.format).every(function(st){return!0===ot.format[st]?null!=it.format[st]:!1===ot.format[st]?null==it.format[st]:(0,o.default)(ot.format[st],it.format[st])}))return!1;return!(null!=ot.prefix&&!ot.prefix.test(it.prefix)||null!=ot.suffix&&!ot.suffix.test(it.suffix))&&!0!==ot.handler.call(H,Q,it)})&&V.preventDefault()}}}})}}]),j}(N.default);function Z(x,j){var U;return h(U={key:x,shiftKey:j,altKey:null},x===C.keys.LEFT?"prefix":"suffix",/^$/),h(U,"handler",function(Y){var X=Y.index;x===C.keys.RIGHT&&(X+=Y.length+1);var Q=this.quill.getLeaf(X);return!(T(Q,1)[0]instanceof f.default.Embed&&(x===C.keys.LEFT?j?this.quill.setSelection(Y.index-1,Y.length+1,s.default.sources.USER):this.quill.setSelection(Y.index-1,s.default.sources.USER):j?this.quill.setSelection(Y.index,Y.length+1,s.default.sources.USER):this.quill.setSelection(Y.index+Y.length+1,s.default.sources.USER),1))}),U}function I(x,j){if(!(0===x.index||this.quill.getLength()<=1)){var U=this.quill.getLine(x.index),V=T(U,1)[0],Y={};if(0===j.offset){var X=this.quill.getLine(x.index-1),nt=T(X,1)[0];if(null!=nt&&nt.length()>1){var rt=V.formats(),at=this.quill.getFormat(x.index-1,1);Y=r.default.attributes.diff(rt,at)||{}}}var lt=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(j.prefix)?2:1;this.quill.deleteText(x.index-lt,lt,s.default.sources.USER),Object.keys(Y).length>0&&this.quill.formatLine(x.index-lt,lt,Y,s.default.sources.USER),this.quill.focus()}}function R(x,j){var U=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(j.suffix)?2:1;if(!(x.index>=this.quill.getLength()-U)){var H={},V=0,Y=this.quill.getLine(x.index),Q=T(Y,1)[0];if(j.offset>=Q.length()-1){var nt=this.quill.getLine(x.index+1),at=T(nt,1)[0];if(at){var lt=Q.formats(),z=this.quill.getFormat(x.index,1);H=r.default.attributes.diff(lt,z)||{},V=at.length()}}this.quill.deleteText(x.index,U,s.default.sources.USER),Object.keys(H).length>0&&this.quill.formatLine(x.index+V-1,U,H,s.default.sources.USER)}}function E(x){var j=this.quill.getLines(x),U={};if(j.length>1){var H=j[0].formats(),V=j[j.length-1].formats();U=r.default.attributes.diff(V,H)||{}}this.quill.deleteText(x,s.default.sources.USER),Object.keys(U).length>0&&this.quill.formatLine(x.index,1,U,s.default.sources.USER),this.quill.setSelection(x.index,s.default.sources.SILENT),this.quill.focus()}function S(x,j){var U=this;x.length>0&&this.quill.scroll.deleteAt(x.index,x.length);var H=Object.keys(j.format).reduce(function(V,Y){return f.default.query(Y,f.default.Scope.BLOCK)&&!Array.isArray(j.format[Y])&&(V[Y]=j.format[Y]),V},{});this.quill.insertText(x.index,"\n",H,s.default.sources.USER),this.quill.setSelection(x.index+1,s.default.sources.SILENT),this.quill.focus(),Object.keys(j.format).forEach(function(V){null==H[V]&&(Array.isArray(j.format[V])||"link"!==V&&U.quill.format(V,j.format[V],s.default.sources.USER))})}function L(x){return{key:C.keys.TAB,shiftKey:!x,format:{"code-block":!0},handler:function(U){var H=f.default.query("code-block"),V=U.index,Y=U.length,X=this.quill.scroll.descendant(H,V),Q=T(X,2),nt=Q[0],rt=Q[1];if(null!=nt){var at=this.quill.getIndex(nt),lt=nt.newlineIndex(rt,!0)+1,z=nt.newlineIndex(at+rt+Y),K=nt.domNode.textContent.slice(lt,z).split("\n");rt=0,K.forEach(function($,G){x?(nt.insertAt(lt+rt,H.TAB),rt+=H.TAB.length,0===G?V+=H.TAB.length:Y+=H.TAB.length):$.startsWith(H.TAB)&&(nt.deleteAt(lt+rt,H.TAB.length),rt-=H.TAB.length,0===G?V-=H.TAB.length:Y-=H.TAB.length),rt+=$.length+1}),this.quill.update(s.default.sources.USER),this.quill.setSelection(V,Y,s.default.sources.SILENT)}}}}function F(x){return{key:x[0].toUpperCase(),shortKey:!0,handler:function(U,H){this.quill.format(x,!H.format[x],s.default.sources.USER)}}}function M(x){if("string"==typeof x||"number"==typeof x)return M({key:x});if("object"===(typeof x>"u"?"undefined":P(x))&&(x=(0,y.default)(x,!1)),"string"==typeof x.key)if(null!=C.keys[x.key.toUpperCase()])x.key=C.keys[x.key.toUpperCase()];else{if(1!==x.key.length)return null;x.key=x.key.toUpperCase().charCodeAt(0)}return x.shortKey&&(x[D]=x.shortKey,delete x.shortKey),x}C.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},C.DEFAULTS={bindings:{bold:F("bold"),italic:F("italic"),underline:F("underline"),indent:{key:C.keys.TAB,format:["blockquote","indent","list"],handler:function(j,U){if(U.collapsed&&0!==U.offset)return!0;this.quill.format("indent","+1",s.default.sources.USER)}},outdent:{key:C.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(j,U){if(U.collapsed&&0!==U.offset)return!0;this.quill.format("indent","-1",s.default.sources.USER)}},"outdent backspace":{key:C.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(j,U){null!=U.format.indent?this.quill.format("indent","-1",s.default.sources.USER):null!=U.format.list&&this.quill.format("list",!1,s.default.sources.USER)}},"indent code-block":L(!0),"outdent code-block":L(!1),"remove tab":{key:C.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(j){this.quill.deleteText(j.index-1,1,s.default.sources.USER)}},tab:{key:C.keys.TAB,handler:function(j){this.quill.history.cutoff();var U=(new l.default).retain(j.index).delete(j.length).insert("\t");this.quill.updateContents(U,s.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(j.index+1,s.default.sources.SILENT)}},"list empty enter":{key:C.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(j,U){this.quill.format("list",!1,s.default.sources.USER),U.format.indent&&this.quill.format("indent",!1,s.default.sources.USER)}},"checklist enter":{key:C.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(j){var U=this.quill.getLine(j.index),H=T(U,2),V=H[0],Y=H[1],X=(0,e.default)({},V.formats(),{list:"checked"}),Q=(new l.default).retain(j.index).insert("\n",X).retain(V.length()-Y-1).retain(1,{list:"unchecked"});this.quill.updateContents(Q,s.default.sources.USER),this.quill.setSelection(j.index+1,s.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:C.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(j,U){var H=this.quill.getLine(j.index),V=T(H,2),Y=V[0],X=V[1],Q=(new l.default).retain(j.index).insert("\n",U.format).retain(Y.length()-X-1).retain(1,{header:null});this.quill.updateContents(Q,s.default.sources.USER),this.quill.setSelection(j.index+1,s.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(j,U){var H=U.prefix.length,V=this.quill.getLine(j.index),Y=T(V,2),X=Y[0],Q=Y[1];if(Q>H)return!0;var nt=void 0;switch(U.prefix.trim()){case"[]":case"[ ]":nt="unchecked";break;case"[x]":nt="checked";break;case"-":case"*":nt="bullet";break;default:nt="ordered"}this.quill.insertText(j.index," ",s.default.sources.USER),this.quill.history.cutoff();var rt=(new l.default).retain(j.index-Q).delete(H+1).retain(X.length()-2-Q).retain(1,{list:nt});this.quill.updateContents(rt,s.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(j.index-H,s.default.sources.SILENT)}},"code exit":{key:C.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(j){var U=this.quill.getLine(j.index),H=T(U,2),V=H[0],Y=H[1],X=(new l.default).retain(j.index+V.length()-Y-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(X,s.default.sources.USER)}},"embed left":Z(C.keys.LEFT,!1),"embed left shift":Z(C.keys.LEFT,!0),"embed right":Z(C.keys.RIGHT,!1),"embed right shift":Z(C.keys.RIGHT,!0)}},O.default=C,O.SHORTKEY=D},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(i,f){if(Array.isArray(i))return i;if(Symbol.iterator in Object(i))return function r(i,f){var n=[],s=!0,A=!1,g=void 0;try{for(var N,b=i[Symbol.iterator]();!(s=(N=b.next()).done)&&(n.push(N.value),!f||n.length!==f);s=!0);}catch(v){A=!0,g=v}finally{try{!s&&b.return&&b.return()}finally{if(A)throw g}}return n}(i,f);throw new TypeError("Invalid attempt to destructure non-iterable instance")},T=function r(i,f,n){null===i&&(i=Function.prototype);var s=Object.getOwnPropertyDescriptor(i,f);if(void 0===s){var A=Object.getPrototypeOf(i);return null===A?void 0:r(A,f,n)}if("value"in s)return s.value;var g=s.get;return void 0===g?void 0:g.call(n)},w=function(){function r(i,f){for(var n=0;n<f.length;n++){var s=f[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(i,s.key,s)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),y=t(p(0)),o=t(p(7));function t(r){return r&&r.__esModule?r:{default:r}}var a=function(r){function i(f,n){!function e(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i);var s=function u(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!i||"object"!=typeof i&&"function"!=typeof i?r:i}(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,f));return s.selection=n,s.textNode=document.createTextNode(i.CONTENTS),s.domNode.appendChild(s.textNode),s._length=0,s}return function l(r,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}(i,r),w(i,null,[{key:"value",value:function(){}}]),w(i,[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(n,s){if(0!==this._length)return T(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"format",this).call(this,n,s);for(var A=this,g=0;null!=A&&A.statics.scope!==y.default.Scope.BLOCK_BLOT;)g+=A.offset(A.parent),A=A.parent;null!=A&&(this._length=i.CONTENTS.length,A.optimize(),A.formatAt(g,i.CONTENTS.length,n,s),this._length=0)}},{key:"index",value:function(n,s){return n===this.textNode?0:T(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"index",this).call(this,n,s)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){T(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!this.selection.composing&&null!=this.parent){var n=this.textNode,s=this.selection.getNativeRange(),A=void 0,g=void 0,b=void 0;if(null!=s&&s.start.node===n&&s.end.node===n){var N=[n,s.start.offset,s.end.offset];A=N[0],g=N[1],b=N[2]}for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==i.CONTENTS){var v=this.textNode.data.split(i.CONTENTS).join("");this.next instanceof o.default?(A=this.next.domNode,this.next.insertAt(0,v),this.textNode.data=i.CONTENTS):(this.textNode.data=v,this.parent.insertBefore(y.default.create(this.textNode),this),this.textNode=document.createTextNode(i.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),null!=g){var h=[g,b].map(function(k){return Math.max(0,Math.min(A.data.length,k-1))}),d=P(h,2);return{startNode:A,startOffset:g=d[0],endNode:A,endOffset:b=d[1]}}}}},{key:"update",value:function(n,s){var A=this;if(n.some(function(b){return"characterData"===b.type&&b.target===A.textNode})){var g=this.restore();g&&(s.range=g)}}},{key:"value",value:function(){return""}}]),i}(y.default.Embed);a.blotName="cursor",a.className="ql-cursor",a.tagName="span",a.CONTENTS="\ufeff",O.default=a},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var T=y(p(0)),w=p(4),m=y(w);function y(u){return u&&u.__esModule?u:{default:u}}var e=function(u){function l(){return function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}(this,l),function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!l||"object"!=typeof l&&"function"!=typeof l?u:l}(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return function t(u,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}(l,u),l}(T.default.Container);e.allowedChildren=[m.default,w.BlockEmbed,e],O.default=e},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.ColorStyle=O.ColorClass=O.ColorAttributor=void 0;var P=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),T=function a(r,i,f){null===r&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(void 0===n){var s=Object.getPrototypeOf(r);return null===s?void 0:a(s,i,f)}if("value"in n)return n.value;var A=n.get;return void 0===A?void 0:A.call(f)},m=function y(a){return a&&a.__esModule?a:{default:a}}(p(0));var e=function(a){function r(){return function c(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}(this,r),function o(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?a:r}(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return function t(a,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}(r,a),P(r,[{key:"value",value:function(f){var n=T(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"value",this).call(this,f);return n.startsWith("rgb(")?"#"+(n=n.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"")).split(",").map(function(s){return("00"+parseInt(s).toString(16)).slice(-2)}).join(""):n}}]),r}(m.default.Attributor.Style),u=new m.default.Attributor.Class("color","ql-color",{scope:m.default.Scope.INLINE}),l=new e("color","color",{scope:m.default.Scope.INLINE});O.ColorAttributor=e,O.ColorClass=u,O.ColorStyle=l},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.sanitize=O.default=void 0;var P=function(){function l(a,r){for(var i=0;i<r.length;i++){var f=r[i];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(a,f.key,f)}}return function(a,r,i){return r&&l(a.prototype,r),i&&l(a,i),a}}(),T=function l(a,r,i){null===a&&(a=Function.prototype);var f=Object.getOwnPropertyDescriptor(a,r);if(void 0===f){var n=Object.getPrototypeOf(a);return null===n?void 0:l(n,r,i)}if("value"in f)return f.value;var s=f.get;return void 0===s?void 0:s.call(i)};var e=function(l){function a(){return function c(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}(this,a),function o(l,a){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!a||"object"!=typeof a&&"function"!=typeof a?l:a}(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return function t(l,a){if("function"!=typeof a&&null!==a)throw new TypeError("Super expression must either be null or a function, not "+typeof a);l.prototype=Object.create(a&&a.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(l,a):l.__proto__=a)}(a,l),P(a,[{key:"format",value:function(i,f){if(i!==this.statics.blotName||!f)return T(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"format",this).call(this,i,f);f=this.constructor.sanitize(f),this.domNode.setAttribute("href",f)}}],[{key:"create",value:function(i){var f=T(a.__proto__||Object.getPrototypeOf(a),"create",this).call(this,i);return i=this.sanitize(i),f.setAttribute("href",i),f.setAttribute("rel","noopener noreferrer"),f.setAttribute("target","_blank"),f}},{key:"formats",value:function(i){return i.getAttribute("href")}},{key:"sanitize",value:function(i){return u(i,this.PROTOCOL_WHITELIST)?i:this.SANITIZED_URL}}]),a}(function y(l){return l&&l.__esModule?l:{default:l}}(p(6)).default);function u(l,a){var r=document.createElement("a");r.href=l;var i=r.href.slice(0,r.href.indexOf(":"));return a.indexOf(i)>-1}e.blotName="link",e.tagName="A",e.SANITIZED_URL="about:blank",e.PROTOCOL_WHITELIST=["http","https","mailto","tel"],O.default=e,O.sanitize=u},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},T=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=o(p(23)),c=o(p(107));function o(a){return a&&a.__esModule?a:{default:a}}var e=0;function u(a,r){a.setAttribute(r,"true"!==a.getAttribute(r))}var l=function(){function a(r){var i=this;(function t(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")})(this,a),this.select=r,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){i.togglePicker()}),this.label.addEventListener("keydown",function(f){switch(f.keyCode){case m.default.keys.ENTER:i.togglePicker();break;case m.default.keys.ESCAPE:i.escape(),f.preventDefault()}}),this.select.addEventListener("change",this.update.bind(this))}return T(a,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),u(this.label,"aria-expanded"),u(this.options,"aria-hidden")}},{key:"buildItem",value:function(i){var f=this,n=document.createElement("span");return n.tabIndex="0",n.setAttribute("role","button"),n.classList.add("ql-picker-item"),i.hasAttribute("value")&&n.setAttribute("data-value",i.getAttribute("value")),i.textContent&&n.setAttribute("data-label",i.textContent),n.addEventListener("click",function(){f.selectItem(n,!0)}),n.addEventListener("keydown",function(s){switch(s.keyCode){case m.default.keys.ENTER:f.selectItem(n,!0),s.preventDefault();break;case m.default.keys.ESCAPE:f.escape(),s.preventDefault()}}),n}},{key:"buildLabel",value:function(){var i=document.createElement("span");return i.classList.add("ql-picker-label"),i.innerHTML=c.default,i.tabIndex="0",i.setAttribute("role","button"),i.setAttribute("aria-expanded","false"),this.container.appendChild(i),i}},{key:"buildOptions",value:function(){var i=this,f=document.createElement("span");f.classList.add("ql-picker-options"),f.setAttribute("aria-hidden","true"),f.tabIndex="-1",f.id="ql-picker-options-"+e,e+=1,this.label.setAttribute("aria-controls",f.id),this.options=f,[].slice.call(this.select.options).forEach(function(n){var s=i.buildItem(n);f.appendChild(s),!0===n.selected&&i.selectItem(s)}),this.container.appendChild(f)}},{key:"buildPicker",value:function(){var i=this;[].slice.call(this.select.attributes).forEach(function(f){i.container.setAttribute(f.name,f.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var i=this;this.close(),setTimeout(function(){return i.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(i){var f=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.container.querySelector(".ql-selected");if(i!==n&&(n?.classList.remove("ql-selected"),null!=i&&(i.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(i.parentNode.children,i),i.hasAttribute("data-value")?this.label.setAttribute("data-value",i.getAttribute("data-value")):this.label.removeAttribute("data-value"),i.hasAttribute("data-label")?this.label.setAttribute("data-label",i.getAttribute("data-label")):this.label.removeAttribute("data-label"),f))){if("function"==typeof Event)this.select.dispatchEvent(new Event("change"));else if("object"===(typeof Event>"u"?"undefined":P(Event))){var s=document.createEvent("Event");s.initEvent("change",!0,!0),this.select.dispatchEvent(s)}this.close()}}},{key:"update",value:function(){var i=void 0;if(this.select.selectedIndex>-1){var f=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];i=this.select.options[this.select.selectedIndex],this.selectItem(f)}else this.selectItem(null);var n=null!=i&&i!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",n)}}]),a}();O.default=l},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var T=q(p(0)),m=q(p(5)),y=p(4),c=q(y),t=q(p(16)),u=q(p(25)),a=q(p(24)),i=q(p(35)),n=q(p(6)),A=q(p(22)),b=q(p(7)),v=q(p(55)),d=q(p(42)),_=q(p(23));function q(D){return D&&D.__esModule?D:{default:D}}m.default.register({"blots/block":c.default,"blots/block/embed":y.BlockEmbed,"blots/break":t.default,"blots/container":u.default,"blots/cursor":a.default,"blots/embed":i.default,"blots/inline":n.default,"blots/scroll":A.default,"blots/text":b.default,"modules/clipboard":v.default,"modules/history":d.default,"modules/keyboard":_.default}),T.default.register(c.default,t.default,a.default,n.default,A.default,b.default),O.default=m.default},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=p(1),T=function(){function w(m){this.domNode=m,this.domNode[P.DATA_KEY]={blot:this}}return Object.defineProperty(w.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),w.create=function(m){if(null==this.tagName)throw new P.ParchmentError("Blot definition missing tagName");var y;return Array.isArray(this.tagName)?("string"==typeof m&&(m=m.toUpperCase(),parseInt(m).toString()===m&&(m=parseInt(m))),y="number"==typeof m?document.createElement(this.tagName[m-1]):this.tagName.indexOf(m)>-1?document.createElement(m):document.createElement(this.tagName[0])):y=document.createElement(this.tagName),this.className&&y.classList.add(this.className),y},w.prototype.attach=function(){null!=this.parent&&(this.scroll=this.parent.scroll)},w.prototype.clone=function(){var m=this.domNode.cloneNode(!1);return P.create(m)},w.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),delete this.domNode[P.DATA_KEY]},w.prototype.deleteAt=function(m,y){this.isolate(m,y).remove()},w.prototype.formatAt=function(m,y,c,o){var t=this.isolate(m,y);if(null!=P.query(c,P.Scope.BLOT)&&o)t.wrap(c,o);else if(null!=P.query(c,P.Scope.ATTRIBUTE)){var e=P.create(this.statics.scope);t.wrap(e),e.format(c,o)}},w.prototype.insertAt=function(m,y,c){var o=null==c?P.create("text",y):P.create(y,c),t=this.split(m);this.parent.insertBefore(o,t)},w.prototype.insertInto=function(m,y){void 0===y&&(y=null),null!=this.parent&&this.parent.children.remove(this);var c=null;m.children.insertBefore(this,y),null!=y&&(c=y.domNode),(this.domNode.parentNode!=m.domNode||this.domNode.nextSibling!=c)&&m.domNode.insertBefore(this.domNode,c),this.parent=m,this.attach()},w.prototype.isolate=function(m,y){var c=this.split(m);return c.split(y),c},w.prototype.length=function(){return 1},w.prototype.offset=function(m){return void 0===m&&(m=this.parent),null==this.parent||this==m?0:this.parent.children.offset(this)+this.parent.offset(m)},w.prototype.optimize=function(m){null!=this.domNode[P.DATA_KEY]&&delete this.domNode[P.DATA_KEY].mutations},w.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},w.prototype.replace=function(m){null!=m.parent&&(m.parent.insertBefore(this,m.next),m.remove())},w.prototype.replaceWith=function(m,y){var c="string"==typeof m?P.create(m,y):m;return c.replace(this),c},w.prototype.split=function(m,y){return 0===m?this:this.next},w.prototype.update=function(m,y){},w.prototype.wrap=function(m,y){var c="string"==typeof m?P.create(m,y):m;return null!=this.parent&&this.parent.insertBefore(c,this.next),c.appendChild(this),c},w.blotName="abstract",w}();O.default=T},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=p(12),T=p(32),w=p(33),m=p(1),y=function(){function c(o){this.attributes={},this.domNode=o,this.build()}return c.prototype.attribute=function(o,t){t?o.add(this.domNode,t)&&(null!=o.value(this.domNode)?this.attributes[o.attrName]=o:delete this.attributes[o.attrName]):(o.remove(this.domNode),delete this.attributes[o.attrName])},c.prototype.build=function(){var o=this;this.attributes={};var t=P.default.keys(this.domNode),e=T.default.keys(this.domNode),u=w.default.keys(this.domNode);t.concat(e).concat(u).forEach(function(l){var a=m.query(l,m.Scope.ATTRIBUTE);a instanceof P.default&&(o.attributes[a.attrName]=a)})},c.prototype.copy=function(o){var t=this;Object.keys(this.attributes).forEach(function(e){var u=t.attributes[e].value(t.domNode);o.format(e,u)})},c.prototype.move=function(o){var t=this;this.copy(o),Object.keys(this.attributes).forEach(function(e){t.attributes[e].remove(t.domNode)}),this.attributes={}},c.prototype.values=function(){var o=this;return Object.keys(this.attributes).reduce(function(t,e){return t[e]=o.attributes[e].value(o.domNode),t},{})},c}();O.default=y},function(B,O,p){"use strict";var y,P=this&&this.__extends||(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var t in o)o.hasOwnProperty(t)&&(c[t]=o[t])},function(c,o){function t(){this.constructor=c}y(c,o),c.prototype=null===o?Object.create(o):(t.prototype=o.prototype,new t)});function w(y,c){return(y.getAttribute("class")||"").split(/\s+/).filter(function(t){return 0===t.indexOf(c+"-")})}Object.defineProperty(O,"__esModule",{value:!0});var m=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return P(c,y),c.keys=function(o){return(o.getAttribute("class")||"").split(/\s+/).map(function(t){return t.split("-").slice(0,-1).join("-")})},c.prototype.add=function(o,t){return!!this.canAdd(o,t)&&(this.remove(o),o.classList.add(this.keyName+"-"+t),!0)},c.prototype.remove=function(o){w(o,this.keyName).forEach(function(e){o.classList.remove(e)}),0===o.classList.length&&o.removeAttribute("class")},c.prototype.value=function(o){var e=(w(o,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(o,e)?e:""},c}(p(12).default);O.default=m},function(B,O,p){"use strict";var y,P=this&&this.__extends||(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var t in o)o.hasOwnProperty(t)&&(c[t]=o[t])},function(c,o){function t(){this.constructor=c}y(c,o),c.prototype=null===o?Object.create(o):(t.prototype=o.prototype,new t)});function w(y){var c=y.split("-"),o=c.slice(1).map(function(t){return t[0].toUpperCase()+t.slice(1)}).join("");return c[0]+o}Object.defineProperty(O,"__esModule",{value:!0});var m=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return P(c,y),c.keys=function(o){return(o.getAttribute("style")||"").split(";").map(function(t){return t.split(":")[0].trim()})},c.prototype.add=function(o,t){return!!this.canAdd(o,t)&&(o.style[w(this.keyName)]=t,!0)},c.prototype.remove=function(o){o.style[w(this.keyName)]="",o.getAttribute("style")||o.removeAttribute("style")},c.prototype.value=function(o){var t=o.style[w(this.keyName)];return this.canAdd(o,t)?t:""},c}(p(12).default);O.default=m},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function m(y,c){for(var o=0;o<c.length;o++){var t=c[o];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(y,t.key,t)}}return function(y,c,o){return c&&m(y.prototype,c),o&&m(y,o),y}}();var w=function(){function m(y,c){(function T(m,y){if(!(m instanceof y))throw new TypeError("Cannot call a class as a function")})(this,m),this.quill=y,this.options=c,this.modules={}}return P(m,[{key:"init",value:function(){var c=this;Object.keys(this.options.modules).forEach(function(o){null==c.modules[o]&&c.addModule(o)})}},{key:"addModule",value:function(c){var o=this.quill.constructor.import("modules/"+c);return this.modules[c]=new o(this.quill,this.options.modules[c]||{}),this.modules[c]}}]),m}();w.DEFAULTS={modules:{}},w.themes={default:w},O.default=w},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function r(i,f){for(var n=0;n<f.length;n++){var s=f[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(i,s.key,s)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),T=function r(i,f,n){null===i&&(i=Function.prototype);var s=Object.getOwnPropertyDescriptor(i,f);if(void 0===s){var A=Object.getPrototypeOf(i);return null===A?void 0:r(A,f,n)}if("value"in s)return s.value;var g=s.get;return void 0===g?void 0:g.call(n)},m=o(p(0)),c=o(p(7));function o(r){return r&&r.__esModule?r:{default:r}}var l="\ufeff",a=function(r){function i(f){!function t(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i);var n=function e(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!i||"object"!=typeof i&&"function"!=typeof i?r:i}(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,f));return n.contentNode=document.createElement("span"),n.contentNode.setAttribute("contenteditable",!1),[].slice.call(n.domNode.childNodes).forEach(function(s){n.contentNode.appendChild(s)}),n.leftGuard=document.createTextNode(l),n.rightGuard=document.createTextNode(l),n.domNode.appendChild(n.leftGuard),n.domNode.appendChild(n.contentNode),n.domNode.appendChild(n.rightGuard),n}return function u(r,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}(i,r),P(i,[{key:"index",value:function(n,s){return n===this.leftGuard?0:n===this.rightGuard?1:T(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"index",this).call(this,n,s)}},{key:"restore",value:function(n){var s=void 0,A=void 0,g=n.data.split(l).join("");if(n===this.leftGuard)if(this.prev instanceof c.default){var b=this.prev.length();this.prev.insertAt(b,g),s={startNode:this.prev.domNode,startOffset:b+g.length}}else A=document.createTextNode(g),this.parent.insertBefore(m.default.create(A),this),s={startNode:A,startOffset:g.length};else n===this.rightGuard&&(this.next instanceof c.default?(this.next.insertAt(0,g),s={startNode:this.next.domNode,startOffset:g.length}):(A=document.createTextNode(g),this.parent.insertBefore(m.default.create(A),this.next),s={startNode:A,startOffset:g.length}));return n.data=l,s}},{key:"update",value:function(n,s){var A=this;n.forEach(function(g){if("characterData"===g.type&&(g.target===A.leftGuard||g.target===A.rightGuard)){var b=A.restore(g.target);b&&(s.range=b)}})}}]),i}(m.default.Embed);O.default=a},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.AlignStyle=O.AlignClass=O.AlignAttribute=void 0;var T=function w(t){return t&&t.__esModule?t:{default:t}}(p(0));var m={scope:T.default.Scope.BLOCK,whitelist:["right","center","justify"]},y=new T.default.Attributor.Attribute("align","align",m),c=new T.default.Attributor.Class("align","ql-align",m),o=new T.default.Attributor.Style("align","text-align",m);O.AlignAttribute=y,O.AlignClass=c,O.AlignStyle=o},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.BackgroundStyle=O.BackgroundClass=void 0;var T=function m(o){return o&&o.__esModule?o:{default:o}}(p(0)),w=p(26);var y=new T.default.Attributor.Class("background","ql-bg",{scope:T.default.Scope.INLINE}),c=new w.ColorAttributor("background","background-color",{scope:T.default.Scope.INLINE});O.BackgroundClass=y,O.BackgroundStyle=c},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.DirectionStyle=O.DirectionClass=O.DirectionAttribute=void 0;var T=function w(t){return t&&t.__esModule?t:{default:t}}(p(0));var m={scope:T.default.Scope.BLOCK,whitelist:["rtl"]},y=new T.default.Attributor.Attribute("direction","dir",m),c=new T.default.Attributor.Class("direction","ql-direction",m),o=new T.default.Attributor.Style("direction","direction",m);O.DirectionAttribute=y,O.DirectionClass=c,O.DirectionStyle=o},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.FontClass=O.FontStyle=void 0;var P=function(){function r(i,f){for(var n=0;n<f.length;n++){var s=f[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(i,s.key,s)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),T=function r(i,f,n){null===i&&(i=Function.prototype);var s=Object.getOwnPropertyDescriptor(i,f);if(void 0===s){var A=Object.getPrototypeOf(i);return null===A?void 0:r(A,f,n)}if("value"in s)return s.value;var g=s.get;return void 0===g?void 0:g.call(n)},m=function y(r){return r&&r.__esModule?r:{default:r}}(p(0));var e={scope:m.default.Scope.INLINE,whitelist:["serif","monospace"]},u=new m.default.Attributor.Class("font","ql-font",e),l=function(r){function i(){return function c(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i),function o(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!i||"object"!=typeof i&&"function"!=typeof i?r:i}(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return function t(r,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}(i,r),P(i,[{key:"value",value:function(n){return T(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"value",this).call(this,n).replace(/["']/g,"")}}]),i}(m.default.Attributor.Style),a=new l("font","font-family",e);O.FontStyle=a,O.FontClass=u},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.SizeStyle=O.SizeClass=void 0;var T=function w(c){return c&&c.__esModule?c:{default:c}}(p(0));var m=new T.default.Attributor.Class("size","ql-size",{scope:T.default.Scope.INLINE,whitelist:["small","large","huge"]}),y=new T.default.Attributor.Style("size","font-size",{scope:T.default.Scope.INLINE,whitelist:["10px","18px","32px"]});O.SizeClass=m,O.SizeStyle=y},function(B,O,p){"use strict";B.exports={align:{"":p(76),center:p(77),right:p(78),justify:p(79)},background:p(80),blockquote:p(81),bold:p(82),clean:p(83),code:p(58),"code-block":p(58),color:p(84),direction:{"":p(85),rtl:p(86)},float:{center:p(87),full:p(88),left:p(89),right:p(90)},formula:p(91),header:{1:p(92),2:p(93)},italic:p(94),image:p(95),indent:{"+1":p(96),"-1":p(97)},link:p(98),list:{ordered:p(99),bullet:p(100),check:p(101)},script:{sub:p(102),super:p(103)},strike:p(104),underline:p(105),video:p(106)}},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.getLastChangeIndex=O.default=void 0;var P=function(){function f(n,s){for(var A=0;A<s.length;A++){var g=s[A];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(n,g.key,g)}}return function(n,s,A){return s&&f(n.prototype,s),A&&f(n,A),n}}(),w=t(p(0)),y=t(p(5));function t(f){return f&&f.__esModule?f:{default:f}}var a=function(f){function n(s,A){!function e(f,n){if(!(f instanceof n))throw new TypeError("Cannot call a class as a function")}(this,n);var g=function u(f,n){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!n||"object"!=typeof n&&"function"!=typeof n?f:n}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,s,A));return g.lastRecorded=0,g.ignoreChange=!1,g.clear(),g.quill.on(y.default.events.EDITOR_CHANGE,function(b,N,v,h){b!==y.default.events.TEXT_CHANGE||g.ignoreChange||(g.options.userOnly&&h!==y.default.sources.USER?g.transform(N):g.record(N,v))}),g.quill.keyboard.addBinding({key:"Z",shortKey:!0},g.undo.bind(g)),g.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},g.redo.bind(g)),/Win/i.test(navigator.platform)&&g.quill.keyboard.addBinding({key:"Y",shortKey:!0},g.redo.bind(g)),g}return function l(f,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function, not "+typeof n);f.prototype=Object.create(n&&n.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(f,n):f.__proto__=n)}(n,f),P(n,[{key:"change",value:function(A,g){if(0!==this.stack[A].length){var b=this.stack[A].pop();this.stack[g].push(b),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(b[A],y.default.sources.USER),this.ignoreChange=!1;var N=i(b[A]);this.quill.setSelection(N)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(A,g){if(0!==A.ops.length){this.stack.redo=[];var b=this.quill.getContents().diff(g),N=Date.now();if(this.lastRecorded+this.options.delay>N&&this.stack.undo.length>0){var v=this.stack.undo.pop();b=b.compose(v.undo),A=v.redo.compose(A)}else this.lastRecorded=N;this.stack.undo.push({redo:A,undo:b}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(A){this.stack.undo.forEach(function(g){g.undo=A.transform(g.undo,!0),g.redo=A.transform(g.redo,!0)}),this.stack.redo.forEach(function(g){g.undo=A.transform(g.undo,!0),g.redo=A.transform(g.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),n}(t(p(9)).default);function i(f){var n=f.reduce(function(A,g){return A+(g.delete||0)},0),s=f.length()-n;return function r(f){var n=f.ops[f.ops.length-1];return null!=n&&(null!=n.insert?"string"==typeof n.insert&&n.insert.endsWith("\n"):null!=n.attributes&&Object.keys(n.attributes).some(function(s){return null!=w.default.query(s,w.default.Scope.BLOCK)}))}(f)&&(s-=1),s}a.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1},O.default=a,O.getLastChangeIndex=i},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.BaseTooltip=void 0;var P=function(){function S(L,F){for(var M=0;M<F.length;M++){var x=F[M];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(L,x.key,x)}}return function(L,F,M){return F&&S(L.prototype,F),M&&S(L,M),L}}(),T=function S(L,F,M){null===L&&(L=Function.prototype);var x=Object.getOwnPropertyDescriptor(L,F);if(void 0===x){var j=Object.getPrototypeOf(L);return null===j?void 0:S(j,F,M)}if("value"in x)return x.value;var U=x.get;return void 0===U?void 0:U.call(M)},m=N(p(3)),c=N(p(2)),t=N(p(8)),u=N(p(23)),a=N(p(34)),i=N(p(59)),n=N(p(60)),A=N(p(28)),b=N(p(61));function N(S){return S&&S.__esModule?S:{default:S}}function v(S,L){if(!(S instanceof L))throw new TypeError("Cannot call a class as a function")}function h(S,L){if(!S)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!L||"object"!=typeof L&&"function"!=typeof L?S:L}function d(S,L){if("function"!=typeof L&&null!==L)throw new TypeError("Super expression must either be null or a function, not "+typeof L);S.prototype=Object.create(L&&L.prototype,{constructor:{value:S,enumerable:!1,writable:!0,configurable:!0}}),L&&(Object.setPrototypeOf?Object.setPrototypeOf(S,L):S.__proto__=L)}var k=[!1,"center","right","justify"],_=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],q=[!1,"serif","monospace"],D=["1","2","3",!1],C=["small",!1,"large","huge"],Z=function(S){function L(F,M){v(this,L);var x=h(this,(L.__proto__||Object.getPrototypeOf(L)).call(this,F,M));return F.emitter.listenDOM("click",document.body,function U(H){if(!document.body.contains(F.root))return document.body.removeEventListener("click",U);null!=x.tooltip&&!x.tooltip.root.contains(H.target)&&document.activeElement!==x.tooltip.textbox&&!x.quill.hasFocus()&&x.tooltip.hide(),null!=x.pickers&&x.pickers.forEach(function(V){V.container.contains(H.target)||V.close()})}),x}return d(L,S),P(L,[{key:"addModule",value:function(M){var x=T(L.prototype.__proto__||Object.getPrototypeOf(L.prototype),"addModule",this).call(this,M);return"toolbar"===M&&this.extendToolbar(x),x}},{key:"buildButtons",value:function(M,x){M.forEach(function(j){(j.getAttribute("class")||"").split(/\s+/).forEach(function(H){if(H.startsWith("ql-")&&(H=H.slice(3),null!=x[H]))if("direction"===H)j.innerHTML=x[H][""]+x[H].rtl;else if("string"==typeof x[H])j.innerHTML=x[H];else{var V=j.value||"";null!=V&&x[H][V]&&(j.innerHTML=x[H][V])}})})}},{key:"buildPickers",value:function(M,x){var j=this;this.pickers=M.map(function(H){if(H.classList.contains("ql-align"))return null==H.querySelector("option")&&E(H,k),new n.default(H,x.align);if(H.classList.contains("ql-background")||H.classList.contains("ql-color")){var V=H.classList.contains("ql-background")?"background":"color";return null==H.querySelector("option")&&E(H,_,"background"===V?"#ffffff":"#000000"),new i.default(H,x[V])}return null==H.querySelector("option")&&(H.classList.contains("ql-font")?E(H,q):H.classList.contains("ql-header")?E(H,D):H.classList.contains("ql-size")&&E(H,C)),new A.default(H)}),this.quill.on(t.default.events.EDITOR_CHANGE,function(){j.pickers.forEach(function(V){V.update()})})}}]),L}(a.default);Z.DEFAULTS=(0,m.default)(!0,{},a.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var L=this,F=this.container.querySelector("input.ql-image[type=file]");null==F&&((F=document.createElement("input")).setAttribute("type","file"),F.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),F.classList.add("ql-image"),F.addEventListener("change",function(){if(null!=F.files&&null!=F.files[0]){var M=new FileReader;M.onload=function(x){var j=L.quill.getSelection(!0);L.quill.updateContents((new c.default).retain(j.index).delete(j.length).insert({image:x.target.result}),t.default.sources.USER),L.quill.setSelection(j.index+1,t.default.sources.SILENT),F.value=""},M.readAsDataURL(F.files[0])}}),this.container.appendChild(F)),F.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var I=function(S){function L(F,M){v(this,L);var x=h(this,(L.__proto__||Object.getPrototypeOf(L)).call(this,F,M));return x.textbox=x.root.querySelector('input[type="text"]'),x.listen(),x}return d(L,S),P(L,[{key:"listen",value:function(){var M=this;this.textbox.addEventListener("keydown",function(x){u.default.match(x,"enter")?(M.save(),x.preventDefault()):u.default.match(x,"escape")&&(M.cancel(),x.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var M=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"link",x=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null!=x?this.textbox.value=x:M!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+M)||""),this.root.setAttribute("data-mode",M)}},{key:"restoreFocus",value:function(){var M=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=M}},{key:"save",value:function(){var M=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":var x=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",M,t.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",M,t.default.sources.USER)),this.quill.root.scrollTop=x;break;case"video":M=function R(S){var L=S.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||S.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return L?(L[1]||"https")+"://www.youtube.com/embed/"+L[2]+"?showinfo=0":(L=S.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(L[1]||"https")+"://player.vimeo.com/video/"+L[2]+"/":S}(M);case"formula":if(!M)break;var j=this.quill.getSelection(!0);if(null!=j){var U=j.index+j.length;this.quill.insertEmbed(U,this.root.getAttribute("data-mode"),M,t.default.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(U+1," ",t.default.sources.USER),this.quill.setSelection(U+2,t.default.sources.USER)}}this.textbox.value="",this.hide()}}]),L}(b.default);function E(S,L){var F=arguments.length>2&&void 0!==arguments[2]&&arguments[2];L.forEach(function(M){var x=document.createElement("option");M===F?x.setAttribute("selected","selected"):x.setAttribute("value",M),S.appendChild(x)})}O.BaseTooltip=I,O.default=Z},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function T(){this.head=this.tail=null,this.length=0}return T.prototype.append=function(){for(var w=[],m=0;m<arguments.length;m++)w[m]=arguments[m];this.insertBefore(w[0],null),w.length>1&&this.append.apply(this,w.slice(1))},T.prototype.contains=function(w){for(var m,y=this.iterator();m=y();)if(m===w)return!0;return!1},T.prototype.insertBefore=function(w,m){w&&(w.next=m,null!=m?(w.prev=m.prev,null!=m.prev&&(m.prev.next=w),m.prev=w,m===this.head&&(this.head=w)):null!=this.tail?(this.tail.next=w,w.prev=this.tail,this.tail=w):(w.prev=null,this.head=this.tail=w),this.length+=1)},T.prototype.offset=function(w){for(var m=0,y=this.head;null!=y;){if(y===w)return m;m+=y.length(),y=y.next}return-1},T.prototype.remove=function(w){this.contains(w)&&(null!=w.prev&&(w.prev.next=w.next),null!=w.next&&(w.next.prev=w.prev),w===this.head&&(this.head=w.next),w===this.tail&&(this.tail=w.prev),this.length-=1)},T.prototype.iterator=function(w){return void 0===w&&(w=this.head),function(){var m=w;return null!=w&&(w=w.next),m}},T.prototype.find=function(w,m){void 0===m&&(m=!1);for(var y,c=this.iterator();y=c();){var o=y.length();if(w<o||m&&w===o&&(null==y.next||0!==y.next.length()))return[y,w];w-=o}return[null,0]},T.prototype.forEach=function(w){for(var m,y=this.iterator();m=y();)w(m)},T.prototype.forEachAt=function(w,m,y){if(!(m<=0))for(var e,c=this.find(w),u=w-c[1],l=this.iterator(c[0]);(e=l())&&u<w+m;){var a=e.length();w>u?y(e,w-u,Math.min(m,u+a-w)):y(e,0,Math.min(a,w+m-u)),u+=a}},T.prototype.map=function(w){return this.reduce(function(m,y){return m.push(w(y)),m},[])},T.prototype.reduce=function(w,m){for(var y,c=this.iterator();y=c();)m=w(m,y);return m},T}();O.default=P},function(B,O,p){"use strict";var o,P=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var u in e)e.hasOwnProperty(u)&&(t[u]=e[u])},function(t,e){function u(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(u.prototype=e.prototype,new u)});Object.defineProperty(O,"__esModule",{value:!0});var T=p(17),w=p(1),m={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},c=function(o){function t(e){var u=o.call(this,e)||this;return u.scroll=u,u.observer=new MutationObserver(function(l){u.update(l)}),u.observer.observe(u.domNode,m),u.attach(),u}return P(t,o),t.prototype.detach=function(){o.prototype.detach.call(this),this.observer.disconnect()},t.prototype.deleteAt=function(e,u){this.update(),0===e&&u===this.length()?this.children.forEach(function(l){l.remove()}):o.prototype.deleteAt.call(this,e,u)},t.prototype.formatAt=function(e,u,l,a){this.update(),o.prototype.formatAt.call(this,e,u,l,a)},t.prototype.insertAt=function(e,u,l){this.update(),o.prototype.insertAt.call(this,e,u,l)},t.prototype.optimize=function(e,u){var l=this;void 0===e&&(e=[]),void 0===u&&(u={}),o.prototype.optimize.call(this,u);for(var a=[].slice.call(this.observer.takeRecords());a.length>0;)e.push(a.pop());for(var r=function(s,A){void 0===A&&(A=!0),null!=s&&s!==l&&null!=s.domNode.parentNode&&(null==s.domNode[w.DATA_KEY].mutations&&(s.domNode[w.DATA_KEY].mutations=[]),A&&r(s.parent))},i=function(s){null==s.domNode[w.DATA_KEY]||null==s.domNode[w.DATA_KEY].mutations||(s instanceof T.default&&s.children.forEach(i),s.optimize(u))},f=e,n=0;f.length>0;n+=1){if(n>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(f.forEach(function(s){var A=w.find(s.target,!0);null!=A&&(A.domNode===s.target&&("childList"===s.type?(r(w.find(s.previousSibling,!1)),[].forEach.call(s.addedNodes,function(g){var b=w.find(g,!1);r(b,!1),b instanceof T.default&&b.children.forEach(function(N){r(N,!1)})})):"attributes"===s.type&&r(A.prev)),r(A))}),this.children.forEach(i),a=(f=[].slice.call(this.observer.takeRecords())).slice();a.length>0;)e.push(a.pop())}},t.prototype.update=function(e,u){var l=this;void 0===u&&(u={}),(e=e||this.observer.takeRecords()).map(function(a){var r=w.find(a.target,!0);return null==r?null:null==r.domNode[w.DATA_KEY].mutations?(r.domNode[w.DATA_KEY].mutations=[a],r):(r.domNode[w.DATA_KEY].mutations.push(a),null)}).forEach(function(a){null==a||a===l||null==a.domNode[w.DATA_KEY]||a.update(a.domNode[w.DATA_KEY].mutations||[],u)}),null!=this.domNode[w.DATA_KEY].mutations&&o.prototype.update.call(this,this.domNode[w.DATA_KEY].mutations,u),this.optimize(e,u)},t.blotName="scroll",t.defaultChild="block",t.scope=w.Scope.BLOCK_BLOT,t.tagName="DIV",t}(T.default);O.default=c},function(B,O,p){"use strict";var c,P=this&&this.__extends||(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,t){o.__proto__=t}||function(o,t){for(var e in t)t.hasOwnProperty(e)&&(o[e]=t[e])},function(o,t){function e(){this.constructor=o}c(o,t),o.prototype=null===t?Object.create(t):(e.prototype=t.prototype,new e)});Object.defineProperty(O,"__esModule",{value:!0});var T=p(18),w=p(1);var y=function(c){function o(){return null!==c&&c.apply(this,arguments)||this}return P(o,c),o.formats=function(t){if(t.tagName!==o.tagName)return c.formats.call(this,t)},o.prototype.format=function(t,e){var u=this;t!==this.statics.blotName||e?c.prototype.format.call(this,t,e):(this.children.forEach(function(l){l instanceof T.default||(l=l.wrap(o.blotName,!0)),u.attributes.copy(l)}),this.unwrap())},o.prototype.formatAt=function(t,e,u,l){null!=this.formats()[u]||w.query(u,w.Scope.ATTRIBUTE)?this.isolate(t,e).format(u,l):c.prototype.formatAt.call(this,t,e,u,l)},o.prototype.optimize=function(t){c.prototype.optimize.call(this,t);var e=this.formats();if(0===Object.keys(e).length)return this.unwrap();var u=this.next;u instanceof o&&u.prev===this&&function m(c,o){if(Object.keys(c).length!==Object.keys(o).length)return!1;for(var t in c)if(c[t]!==o[t])return!1;return!0}(e,u.formats())&&(u.moveChildren(this),u.remove())},o.blotName="inline",o.scope=w.Scope.INLINE_BLOT,o.tagName="SPAN",o}(T.default);O.default=y},function(B,O,p){"use strict";var y,P=this&&this.__extends||(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var t in o)o.hasOwnProperty(t)&&(c[t]=o[t])},function(c,o){function t(){this.constructor=c}y(c,o),c.prototype=null===o?Object.create(o):(t.prototype=o.prototype,new t)});Object.defineProperty(O,"__esModule",{value:!0});var T=p(18),w=p(1),m=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return P(c,y),c.formats=function(o){var t=w.query(c.blotName).tagName;if(o.tagName!==t)return y.formats.call(this,o)},c.prototype.format=function(o,t){null!=w.query(o,w.Scope.BLOCK)&&(o!==this.statics.blotName||t?y.prototype.format.call(this,o,t):this.replaceWith(c.blotName))},c.prototype.formatAt=function(o,t,e,u){null!=w.query(e,w.Scope.BLOCK)?this.format(e,u):y.prototype.formatAt.call(this,o,t,e,u)},c.prototype.insertAt=function(o,t,e){if(null==e||null!=w.query(t,w.Scope.INLINE))y.prototype.insertAt.call(this,o,t,e);else{var u=this.split(o),l=w.create(t,e);u.parent.insertBefore(l,u)}},c.prototype.update=function(o,t){navigator.userAgent.match(/Trident/)?this.build():y.prototype.update.call(this,o,t)},c.blotName="block",c.scope=w.Scope.BLOCK_BLOT,c.tagName="P",c}(T.default);O.default=m},function(B,O,p){"use strict";var m,P=this&&this.__extends||(m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,c){y.__proto__=c}||function(y,c){for(var o in c)c.hasOwnProperty(o)&&(y[o]=c[o])},function(y,c){function o(){this.constructor=y}m(y,c),y.prototype=null===c?Object.create(c):(o.prototype=c.prototype,new o)});Object.defineProperty(O,"__esModule",{value:!0});var w=function(m){function y(){return null!==m&&m.apply(this,arguments)||this}return P(y,m),y.formats=function(c){},y.prototype.format=function(c,o){m.prototype.formatAt.call(this,0,this.length(),c,o)},y.prototype.formatAt=function(c,o,t,e){0===c&&o===this.length()?this.format(t,e):m.prototype.formatAt.call(this,c,o,t,e)},y.prototype.formats=function(){return this.statics.formats(this.domNode)},y}(p(19).default);O.default=w},function(B,O,p){"use strict";var y,P=this&&this.__extends||(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,o){c.__proto__=o}||function(c,o){for(var t in o)o.hasOwnProperty(t)&&(c[t]=o[t])},function(c,o){function t(){this.constructor=c}y(c,o),c.prototype=null===o?Object.create(o):(t.prototype=o.prototype,new t)});Object.defineProperty(O,"__esModule",{value:!0});var T=p(19),w=p(1),m=function(y){function c(o){var t=y.call(this,o)||this;return t.text=t.statics.value(t.domNode),t}return P(c,y),c.create=function(o){return document.createTextNode(o)},c.value=function(o){var t=o.data;return t.normalize&&(t=t.normalize()),t},c.prototype.deleteAt=function(o,t){this.domNode.data=this.text=this.text.slice(0,o)+this.text.slice(o+t)},c.prototype.index=function(o,t){return this.domNode===o?t:-1},c.prototype.insertAt=function(o,t,e){null==e?(this.text=this.text.slice(0,o)+t+this.text.slice(o),this.domNode.data=this.text):y.prototype.insertAt.call(this,o,t,e)},c.prototype.length=function(){return this.text.length},c.prototype.optimize=function(o){y.prototype.optimize.call(this,o),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof c&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},c.prototype.position=function(o,t){return void 0===t&&(t=!1),[this.domNode,o]},c.prototype.split=function(o,t){if(void 0===t&&(t=!1),!t){if(0===o)return this;if(o===this.length())return this.next}var e=w.create(this.domNode.splitText(o));return this.parent.insertBefore(e,this.next),this.text=this.statics.value(this.domNode),e},c.prototype.update=function(o,t){var e=this;o.some(function(u){return"characterData"===u.type&&u.target===e.domNode})&&(this.text=this.statics.value(this.domNode))},c.prototype.value=function(){return this.text},c.blotName="text",c.scope=w.Scope.INLINE_BLOT,c}(T.default);O.default=m},function(B,O,p){"use strict";var P=document.createElement("div");if(P.classList.toggle("test-class",!1),P.classList.contains("test-class")){var T=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(w,m){return arguments.length>1&&!this.contains(w)==!m?m:T.call(this,w)}}String.prototype.startsWith||(String.prototype.startsWith=function(w,m){return this.substr(m=m||0,w.length)===w}),String.prototype.endsWith||(String.prototype.endsWith=function(w,m){var y=this.toString();("number"!=typeof m||!isFinite(m)||Math.floor(m)!==m||m>y.length)&&(m=y.length);var c=y.indexOf(w,m-=w.length);return-1!==c&&c===m}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(m){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof m)throw new TypeError("predicate must be a function");for(var t,y=Object(this),c=y.length>>>0,o=arguments[1],e=0;e<c;e++)if(m.call(o,t=y[e],e,y))return t}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(B,O){var p=-1,P=1,T=0;function w(n,s,A){if(n==s)return n?[[T,n]]:[];(A<0||n.length<A)&&(A=null);var g=o(n,s),b=n.substring(0,g);g=t(n=n.substring(g),s=s.substring(g));var N=n.substring(n.length-g),v=function m(n,s){var A;if(!n)return[[P,s]];if(!s)return[[p,n]];var g=n.length>s.length?n:s,b=n.length>s.length?s:n,N=g.indexOf(b);if(-1!=N)return A=[[P,g.substring(0,N)],[T,b],[P,g.substring(N+b.length)]],n.length>s.length&&(A[0][0]=A[2][0]=p),A;if(1==b.length)return[[p,n],[P,s]];var v=function e(n,s){var A=n.length>s.length?n:s,g=n.length>s.length?s:n;if(A.length<4||2*g.length<A.length)return null;function b(C,Z,I){for(var L,F,M,x,R=C.substring(I,I+Math.floor(C.length/4)),E=-1,S="";-1!=(E=Z.indexOf(R,E+1));){var j=o(C.substring(I),Z.substring(E)),U=t(C.substring(0,I),Z.substring(0,E));S.length<U+j&&(S=Z.substring(E-U,E)+Z.substring(E,E+j),L=C.substring(0,I-U),F=C.substring(I+j),M=Z.substring(0,E-U),x=Z.substring(E+j))}return 2*S.length>=C.length?[L,F,M,x,S]:null}var h,d,k,_,q,N=b(A,g,Math.ceil(A.length/4)),v=b(A,g,Math.ceil(A.length/2));return N||v?(h=v?N&&N[4].length>v[4].length?N:v:N,n.length>s.length?(d=h[0],k=h[1],_=h[2],q=h[3]):(_=h[0],q=h[1],d=h[2],k=h[3]),[d,k,_,q,h[4]]):null}(n,s);if(v){var d=v[1],_=v[3],q=v[4],D=w(v[0],v[2]),C=w(d,_);return D.concat([[T,q]],C)}return function y(n,s){for(var A=n.length,g=s.length,b=Math.ceil((A+g)/2),N=b,v=2*b,h=new Array(v),d=new Array(v),k=0;k<v;k++)h[k]=-1,d[k]=-1;h[N+1]=0,d[N+1]=0;for(var _=A-g,q=_%2!=0,D=0,C=0,Z=0,I=0,R=0;R<b;R++){for(var E=-R+D;E<=R-C;E+=2){for(var S=N+E,F=(L=E==-R||E!=R&&h[S-1]<h[S+1]?h[S+1]:h[S-1]+1)-E;L<A&&F<g&&n.charAt(L)==s.charAt(F);)L++,F++;if(h[S]=L,L>A)C+=2;else if(F>g)D+=2;else if(q&&(M=N+_-E)>=0&&M<v&&-1!=d[M]&&L>=(x=A-d[M]))return c(n,s,L,F)}for(var j=-R+Z;j<=R-I;j+=2){for(var x,M=N+j,U=(x=j==-R||j!=R&&d[M-1]<d[M+1]?d[M+1]:d[M-1]+1)-j;x<A&&U<g&&n.charAt(A-x-1)==s.charAt(g-U-1);)x++,U++;if(d[M]=x,x>A)I+=2;else if(U>g)Z+=2;else if(!q){var L;if((S=N+_-j)>=0&&S<v&&-1!=h[S]&&(F=N+(L=h[S])-S,L>=(x=A-x)))return c(n,s,L,F)}}}return[[p,n],[P,s]]}(n,s)}(n=n.substring(0,n.length-g),s=s.substring(0,s.length-g));return b&&v.unshift([T,b]),N&&v.push([T,N]),u(v),null!=A&&(v=function r(n,s){var A=function a(n,s){if(0===s)return[T,n];for(var A=0,g=0;g<n.length;g++){var b=n[g];if(b[0]===p||b[0]===T){var N=A+b[1].length;if(s===N)return[g+1,n];if(s<N){n=n.slice();var v=s-A,h=[b[0],b[1].slice(0,v)],d=[b[0],b[1].slice(v)];return n.splice(g,1,h,d),[g+1,n]}A=N}}throw new Error("cursor_pos is out of bounds!")}(n,s),g=A[1],b=A[0],N=g[b],v=g[b+1];if(null==N)return n;if(N[0]!==T)return n;if(null!=v&&N[1]+v[1]===v[1]+N[1])return g.splice(b,2,v,N),f(g,b,2);if(null!=v&&0===v[1].indexOf(N[1])){g.splice(b,2,[v[0],N[1]],[0,N[1]]);var h=v[1].slice(N[1].length);return h.length>0&&g.splice(b+2,0,[v[0],h]),f(g,b,3)}return n}(v,A)),function i(n){for(var s=!1,A=function(v){return v.charCodeAt(0)>=56320&&v.charCodeAt(0)<=57343},g=function(v){return v.charCodeAt(v.length-1)>=55296&&v.charCodeAt(v.length-1)<=56319},b=2;b<n.length;b+=1)n[b-2][0]===T&&g(n[b-2][1])&&n[b-1][0]===p&&A(n[b-1][1])&&n[b][0]===P&&A(n[b][1])&&(s=!0,n[b-1][1]=n[b-2][1].slice(-1)+n[b-1][1],n[b][1]=n[b-2][1].slice(-1)+n[b][1],n[b-2][1]=n[b-2][1].slice(0,-1));if(!s)return n;var N=[];for(b=0;b<n.length;b+=1)n[b][1].length>0&&N.push(n[b]);return N}(v)}function c(n,s,A,g){var b=n.substring(0,A),N=s.substring(0,g),v=n.substring(A),h=s.substring(g),d=w(b,N),k=w(v,h);return d.concat(k)}function o(n,s){if(!n||!s||n.charAt(0)!=s.charAt(0))return 0;for(var A=0,g=Math.min(n.length,s.length),b=g,N=0;A<b;)n.substring(N,b)==s.substring(N,b)?N=A=b:g=b,b=Math.floor((g-A)/2+A);return b}function t(n,s){if(!n||!s||n.charAt(n.length-1)!=s.charAt(s.length-1))return 0;for(var A=0,g=Math.min(n.length,s.length),b=g,N=0;A<b;)n.substring(n.length-b,n.length-N)==s.substring(s.length-b,s.length-N)?N=A=b:g=b,b=Math.floor((g-A)/2+A);return b}function u(n){n.push([T,""]);for(var v,s=0,A=0,g=0,b="",N="";s<n.length;)switch(n[s][0]){case P:g++,N+=n[s][1],s++;break;case p:A++,b+=n[s][1],s++;break;case T:A+g>1?(0!==A&&0!==g&&(0!==(v=o(N,b))&&(s-A-g>0&&n[s-A-g-1][0]==T?n[s-A-g-1][1]+=N.substring(0,v):(n.splice(0,0,[T,N.substring(0,v)]),s++),N=N.substring(v),b=b.substring(v)),0!==(v=t(N,b))&&(n[s][1]=N.substring(N.length-v)+n[s][1],N=N.substring(0,N.length-v),b=b.substring(0,b.length-v))),0===A?n.splice(s-g,A+g,[P,N]):0===g?n.splice(s-A,A+g,[p,b]):n.splice(s-A-g,A+g,[p,b],[P,N]),s=s-A-g+(A?1:0)+(g?1:0)+1):0!==s&&n[s-1][0]==T?(n[s-1][1]+=n[s][1],n.splice(s,1)):s++,g=0,A=0,b="",N=""}""===n[n.length-1][1]&&n.pop();var h=!1;for(s=1;s<n.length-1;)n[s-1][0]==T&&n[s+1][0]==T&&(n[s][1].substring(n[s][1].length-n[s-1][1].length)==n[s-1][1]?(n[s][1]=n[s-1][1]+n[s][1].substring(0,n[s][1].length-n[s-1][1].length),n[s+1][1]=n[s-1][1]+n[s+1][1],n.splice(s-1,1),h=!0):n[s][1].substring(0,n[s+1][1].length)==n[s+1][1]&&(n[s-1][1]+=n[s+1][1],n[s][1]=n[s][1].substring(n[s+1][1].length)+n[s+1][1],n.splice(s+1,1),h=!0)),s++;h&&u(n)}var l=w;function f(n,s,A){for(var g=s+A-1;g>=0&&g>=s-1;g--)if(g+1<n.length){var b=n[g],N=n[g+1];b[0]===N[1]&&n.splice(g,2,[b[0],b[1]+N[1]])}return n}l.INSERT=P,l.DELETE=p,l.EQUAL=T,B.exports=l},function(B,O){function p(P){var T=[];for(var w in P)T.push(w);return T}(B.exports="function"==typeof Object.keys?Object.keys:p).shim=p},function(B,O){var p="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}();function P(w){return"[object Arguments]"==Object.prototype.toString.call(w)}function T(w){return w&&"object"==typeof w&&"number"==typeof w.length&&Object.prototype.hasOwnProperty.call(w,"callee")&&!Object.prototype.propertyIsEnumerable.call(w,"callee")||!1}(O=B.exports=p?P:T).supported=P,O.unsupported=T},function(B,O){"use strict";var p=Object.prototype.hasOwnProperty,P="~";function T(){}function w(y,c,o){this.fn=y,this.context=c,this.once=o||!1}function m(){this._events=new T,this._eventsCount=0}Object.create&&(T.prototype=Object.create(null),(new T).__proto__||(P=!1)),m.prototype.eventNames=function(){var o,t,c=[];if(0===this._eventsCount)return c;for(t in o=this._events)p.call(o,t)&&c.push(P?t.slice(1):t);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(o)):c},m.prototype.listeners=function(c,o){var e=this._events[P?P+c:c];if(o)return!!e;if(!e)return[];if(e.fn)return[e.fn];for(var u=0,l=e.length,a=new Array(l);u<l;u++)a[u]=e[u].fn;return a},m.prototype.emit=function(c,o,t,e,u,l){var a=P?P+c:c;if(!this._events[a])return!1;var f,n,r=this._events[a],i=arguments.length;if(r.fn){switch(r.once&&this.removeListener(c,r.fn,void 0,!0),i){case 1:return r.fn.call(r.context),!0;case 2:return r.fn.call(r.context,o),!0;case 3:return r.fn.call(r.context,o,t),!0;case 4:return r.fn.call(r.context,o,t,e),!0;case 5:return r.fn.call(r.context,o,t,e,u),!0;case 6:return r.fn.call(r.context,o,t,e,u,l),!0}for(n=1,f=new Array(i-1);n<i;n++)f[n-1]=arguments[n];r.fn.apply(r.context,f)}else{var A,s=r.length;for(n=0;n<s;n++)switch(r[n].once&&this.removeListener(c,r[n].fn,void 0,!0),i){case 1:r[n].fn.call(r[n].context);break;case 2:r[n].fn.call(r[n].context,o);break;case 3:r[n].fn.call(r[n].context,o,t);break;case 4:r[n].fn.call(r[n].context,o,t,e);break;default:if(!f)for(A=1,f=new Array(i-1);A<i;A++)f[A-1]=arguments[A];r[n].fn.apply(r[n].context,f)}}return!0},m.prototype.on=function(c,o,t){var e=new w(o,t||this),u=P?P+c:c;return this._events[u]?this._events[u].fn?this._events[u]=[this._events[u],e]:this._events[u].push(e):(this._events[u]=e,this._eventsCount++),this},m.prototype.once=function(c,o,t){var e=new w(o,t||this,!0),u=P?P+c:c;return this._events[u]?this._events[u].fn?this._events[u]=[this._events[u],e]:this._events[u].push(e):(this._events[u]=e,this._eventsCount++),this},m.prototype.removeListener=function(c,o,t,e){var u=P?P+c:c;if(!this._events[u])return this;if(!o)return 0==--this._eventsCount?this._events=new T:delete this._events[u],this;var l=this._events[u];if(l.fn)l.fn===o&&(!e||l.once)&&(!t||l.context===t)&&(0==--this._eventsCount?this._events=new T:delete this._events[u]);else{for(var a=0,r=[],i=l.length;a<i;a++)(l[a].fn!==o||e&&!l[a].once||t&&l[a].context!==t)&&r.push(l[a]);r.length?this._events[u]=1===r.length?r[0]:r:0==--this._eventsCount?this._events=new T:delete this._events[u]}return this},m.prototype.removeAllListeners=function(c){var o;return c?this._events[o=P?P+c:c]&&(0==--this._eventsCount?this._events=new T:delete this._events[o]):(this._events=new T,this._eventsCount=0),this},m.prototype.off=m.prototype.removeListener,m.prototype.addListener=m.prototype.on,m.prototype.setMaxListeners=function(){return this},m.prefixed=P,m.EventEmitter=m,typeof B<"u"&&(B.exports=m)},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.matchText=O.matchSpacing=O.matchNewline=O.matchBlot=O.matchAttributor=O.default=void 0;var P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(z){return typeof z}:function(z){return z&&"function"==typeof Symbol&&z.constructor===Symbol&&z!==Symbol.prototype?"symbol":typeof z},T=function(K,$){if(Array.isArray(K))return K;if(Symbol.iterator in Object(K))return function z(K,$){var G=[],W=!0,J=!1,tt=void 0;try{for(var ut,et=K[Symbol.iterator]();!(W=(ut=et.next()).done)&&(G.push(ut.value),!$||G.length!==$);W=!0);}catch(ft){J=!0,tt=ft}finally{try{!W&&et.return&&et.return()}finally{if(J)throw tt}}return G}(K,$);throw new TypeError("Invalid attempt to destructure non-iterable instance")},w=function(){function z(K,$){for(var G=0;G<$.length;G++){var W=$[G];W.enumerable=W.enumerable||!1,W.configurable=!0,"value"in W&&(W.writable=!0),Object.defineProperty(K,W.key,W)}}return function(K,$,G){return $&&z(K.prototype,$),G&&z(K,G),K}}(),y=d(p(3)),o=d(p(2)),e=d(p(0)),l=d(p(5)),r=d(p(10)),f=d(p(9)),n=p(36),s=p(37),g=d(p(13)),b=p(26),N=p(38),v=p(39),h=p(40);function d(z){return z&&z.__esModule?z:{default:z}}function k(z,K,$){return K in z?Object.defineProperty(z,K,{value:$,enumerable:!0,configurable:!0,writable:!0}):z[K]=$,z}var C=(0,r.default)("quill:clipboard"),Z="__ql-matcher",I=[[Node.TEXT_NODE,lt],[Node.TEXT_NODE,nt],["br",function Y(z,K){return M(K,"\n")||K.insert("\n"),K}],[Node.ELEMENT_NODE,nt],[Node.ELEMENT_NODE,V],[Node.ELEMENT_NODE,rt],[Node.ELEMENT_NODE,H],[Node.ELEMENT_NODE,function at(z,K){var $={},G=z.style||{};return G.fontStyle&&"italic"===F(z).fontStyle&&($.italic=!0),G.fontWeight&&(F(z).fontWeight.startsWith("bold")||parseInt(F(z).fontWeight)>=700)&&($.bold=!0),Object.keys($).length>0&&(K=L(K,$)),parseFloat(G.textIndent||0)>0&&(K=(new o.default).insert("\t").concat(K)),K}],["li",function Q(z,K){var $=e.default.query(z);if(null==$||"list-item"!==$.blotName||!M(K,"\n"))return K;for(var G=-1,W=z.parentNode;!W.classList.contains("ql-clipboard");)"list"===(e.default.query(W)||{}).blotName&&(G+=1),W=W.parentNode;return G<=0?K:K.compose((new o.default).retain(K.length()-1).retain(1,{indent:G}))}],["b",U.bind(U,"bold")],["i",U.bind(U,"italic")],["style",function X(){return new o.default}]],R=[n.AlignAttribute,N.DirectionAttribute].reduce(function(z,K){return z[K.keyName]=K,z},{}),E=[n.AlignStyle,s.BackgroundStyle,b.ColorStyle,N.DirectionStyle,v.FontStyle,h.SizeStyle].reduce(function(z,K){return z[K.keyName]=K,z},{}),S=function(z){function K($,G){!function _(z,K){if(!(z instanceof K))throw new TypeError("Cannot call a class as a function")}(this,K);var W=function q(z,K){if(!z)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!K||"object"!=typeof K&&"function"!=typeof K?z:K}(this,(K.__proto__||Object.getPrototypeOf(K)).call(this,$,G));return W.quill.root.addEventListener("paste",W.onPaste.bind(W)),W.container=W.quill.addContainer("ql-clipboard"),W.container.setAttribute("contenteditable",!0),W.container.setAttribute("tabindex",-1),W.matchers=[],I.concat(W.options.matchers).forEach(function(J){var tt=T(J,2),ut=tt[1];!G.matchVisual&&ut===rt||W.addMatcher(tt[0],ut)}),W}return function D(z,K){if("function"!=typeof K&&null!==K)throw new TypeError("Super expression must either be null or a function, not "+typeof K);z.prototype=Object.create(K&&K.prototype,{constructor:{value:z,enumerable:!1,writable:!0,configurable:!0}}),K&&(Object.setPrototypeOf?Object.setPrototypeOf(z,K):z.__proto__=K)}(K,z),w(K,[{key:"addMatcher",value:function(G,W){this.matchers.push([G,W])}},{key:"convert",value:function(G){if("string"==typeof G)return this.container.innerHTML=G.replace(/\>\r?\n +\</g,"><"),this.convert();var W=this.quill.getFormat(this.quill.selection.savedRange.index);if(W[g.default.blotName]){var J=this.container.innerText;return this.container.innerHTML="",(new o.default).insert(J,k({},g.default.blotName,W[g.default.blotName]))}var tt=this.prepareMatching(),et=T(tt,2),it=j(this.container,et[0],et[1]);return M(it,"\n")&&null==it.ops[it.ops.length-1].attributes&&(it=it.compose((new o.default).retain(it.length()-1).delete(1))),C.log("convert",this.container.innerHTML,it),this.container.innerHTML="",it}},{key:"dangerouslyPasteHTML",value:function(G,W){var J=arguments.length>2&&void 0!==arguments[2]?arguments[2]:l.default.sources.API;if("string"==typeof G)this.quill.setContents(this.convert(G),W),this.quill.setSelection(0,l.default.sources.SILENT);else{var tt=this.convert(W);this.quill.updateContents((new o.default).retain(G).concat(tt),J),this.quill.setSelection(G+tt.length(),l.default.sources.SILENT)}}},{key:"onPaste",value:function(G){var W=this;if(!G.defaultPrevented&&this.quill.isEnabled()){var J=this.quill.getSelection(),tt=(new o.default).retain(J.index),et=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(l.default.sources.SILENT),setTimeout(function(){tt=tt.concat(W.convert()).delete(J.length),W.quill.updateContents(tt,l.default.sources.USER),W.quill.setSelection(tt.length()-J.length,l.default.sources.SILENT),W.quill.scrollingContainer.scrollTop=et,W.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var G=this,W=[],J=[];return this.matchers.forEach(function(tt){var et=T(tt,2),ut=et[0],ft=et[1];switch(ut){case Node.TEXT_NODE:J.push(ft);break;case Node.ELEMENT_NODE:W.push(ft);break;default:[].forEach.call(G.container.querySelectorAll(ut),function(it){it[Z]=it[Z]||[],it[Z].push(ft)})}}),[W,J]}}]),K}(f.default);function L(z,K,$){return"object"===(typeof K>"u"?"undefined":P(K))?Object.keys(K).reduce(function(G,W){return L(G,W,K[W])},z):z.reduce(function(G,W){return W.attributes&&W.attributes[K]?G.push(W):G.insert(W.insert,(0,y.default)({},k({},K,$),W.attributes))},new o.default)}function F(z){if(z.nodeType!==Node.ELEMENT_NODE)return{};var K="__ql-computed-style";return z[K]||(z[K]=window.getComputedStyle(z))}function M(z,K){for(var $="",G=z.ops.length-1;G>=0&&$.length<K.length;--G){var W=z.ops[G];if("string"!=typeof W.insert)break;$=W.insert+$}return $.slice(-1*K.length)===K}function x(z){if(0===z.childNodes.length)return!1;var K=F(z);return["block","list-item"].indexOf(K.display)>-1}function j(z,K,$){return z.nodeType===z.TEXT_NODE?$.reduce(function(G,W){return W(z,G)},new o.default):z.nodeType===z.ELEMENT_NODE?[].reduce.call(z.childNodes||[],function(G,W){var J=j(W,K,$);return W.nodeType===z.ELEMENT_NODE&&(J=K.reduce(function(tt,et){return et(W,tt)},J),J=(W[Z]||[]).reduce(function(tt,et){return et(W,tt)},J)),G.concat(J)},new o.default):new o.default}function U(z,K,$){return L($,z,!0)}function H(z,K){var $=e.default.Attributor.Attribute.keys(z),G=e.default.Attributor.Class.keys(z),W=e.default.Attributor.Style.keys(z),J={};return $.concat(G).concat(W).forEach(function(tt){var et=e.default.query(tt,e.default.Scope.ATTRIBUTE);null!=et&&(J[et.attrName]=et.value(z),J[et.attrName])||(null!=(et=R[tt])&&(et.attrName===tt||et.keyName===tt)&&(J[et.attrName]=et.value(z)||void 0),null!=(et=E[tt])&&(et.attrName===tt||et.keyName===tt)&&(J[(et=E[tt]).attrName]=et.value(z)||void 0))}),Object.keys(J).length>0&&(K=L(K,J)),K}function V(z,K){var $=e.default.query(z);if(null==$)return K;if($.prototype instanceof e.default.Embed){var G={},W=$.value(z);null!=W&&(G[$.blotName]=W,K=(new o.default).insert(G,$.formats(z)))}else"function"==typeof $.formats&&(K=L(K,$.blotName,$.formats(z)));return K}function nt(z,K){return M(K,"\n")||(x(z)||K.length()>0&&z.nextSibling&&x(z.nextSibling))&&K.insert("\n"),K}function rt(z,K){if(x(z)&&null!=z.nextElementSibling&&!M(K,"\n\n")){var $=z.offsetHeight+parseFloat(F(z).marginTop)+parseFloat(F(z).marginBottom);z.nextElementSibling.offsetTop>z.offsetTop*****$&&K.insert("\n")}return K}function lt(z,K){var $=z.data;if("O:P"===z.parentNode.tagName)return K.insert($.trim());if(0===$.trim().length&&z.parentNode.classList.contains("ql-clipboard"))return K;if(!F(z.parentNode).whiteSpace.startsWith("pre")){var G=function(J,tt){return(tt=tt.replace(/[^\u00a0]/g,"")).length<1&&J?" ":tt};$=($=$.replace(/\r\n/g," ").replace(/\n/g," ")).replace(/\s\s+/g,G.bind(G,!0)),(null==z.previousSibling&&x(z.parentNode)||null!=z.previousSibling&&x(z.previousSibling))&&($=$.replace(/^\s+/,G.bind(G,!1))),(null==z.nextSibling&&x(z.parentNode)||null!=z.nextSibling&&x(z.nextSibling))&&($=$.replace(/\s+$/,G.bind(G,!1)))}return K.insert($)}S.DEFAULTS={matchers:[],matchVisual:!0},O.default=S,O.matchAttributor=H,O.matchBlot=V,O.matchNewline=nt,O.matchSpacing=rt,O.matchText=lt},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),T=function u(l,a,r){null===l&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(void 0===i){var f=Object.getPrototypeOf(l);return null===f?void 0:u(f,a,r)}if("value"in i)return i.value;var n=i.get;return void 0===n?void 0:n.call(r)};var e=function(u){function l(){return function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}(this,l),function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!l||"object"!=typeof l&&"function"!=typeof l?u:l}(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return function t(u,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}(l,u),P(l,[{key:"optimize",value:function(r){T(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"optimize",this).call(this,r),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return T(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),l}(function y(u){return u&&u.__esModule?u:{default:u}}(p(6)).default);e.blotName="bold",e.tagName=["STRONG","B"],O.default=e},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.addControls=O.default=void 0;var P=function(d,k){if(Array.isArray(d))return d;if(Symbol.iterator in Object(d))return function h(d,k){var _=[],q=!0,D=!1,C=void 0;try{for(var I,Z=d[Symbol.iterator]();!(q=(I=Z.next()).done)&&(_.push(I.value),!k||_.length!==k);q=!0);}catch(R){D=!0,C=R}finally{try{!q&&Z.return&&Z.return()}finally{if(D)throw C}}return _}(d,k);throw new TypeError("Invalid attempt to destructure non-iterable instance")},T=function(){function h(d,k){for(var _=0;_<k.length;_++){var q=k[_];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(d,q.key,q)}}return function(d,k,_){return k&&h(d.prototype,k),_&&h(d,_),d}}(),m=r(p(2)),c=r(p(0)),t=r(p(5)),u=r(p(10)),a=r(p(9));function r(h){return h&&h.__esModule?h:{default:h}}function n(h,d){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!d||"object"!=typeof d&&"function"!=typeof d?h:d}var A=(0,u.default)("quill:toolbar"),g=function(h){function d(k,_){!function f(h,d){if(!(h instanceof d))throw new TypeError("Cannot call a class as a function")}(this,d);var C,q=n(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,k,_));if(Array.isArray(q.options.container)){var D=document.createElement("div");N(D,q.options.container),k.container.parentNode.insertBefore(D,k.container),q.container=D}else q.container="string"==typeof q.options.container?document.querySelector(q.options.container):q.options.container;return q.container instanceof HTMLElement?(q.container.classList.add("ql-toolbar"),q.controls=[],q.handlers={},Object.keys(q.options.handlers).forEach(function(Z){q.addHandler(Z,q.options.handlers[Z])}),[].forEach.call(q.container.querySelectorAll("button, select"),function(Z){q.attach(Z)}),q.quill.on(t.default.events.EDITOR_CHANGE,function(Z,I){Z===t.default.events.SELECTION_CHANGE&&q.update(I)}),q.quill.on(t.default.events.SCROLL_OPTIMIZE,function(){var Z=q.quill.selection.getRange(),I=P(Z,1);q.update(I[0])}),q):(C=A.error("Container required for toolbar",q.options),n(q,C))}return function s(h,d){if("function"!=typeof d&&null!==d)throw new TypeError("Super expression must either be null or a function, not "+typeof d);h.prototype=Object.create(d&&d.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(h,d):h.__proto__=d)}(d,h),T(d,[{key:"addHandler",value:function(_,q){this.handlers[_]=q}},{key:"attach",value:function(_){var q=this,D=[].find.call(_.classList,function(Z){return 0===Z.indexOf("ql-")});if(D){if(D=D.slice(3),"BUTTON"===_.tagName&&_.setAttribute("type","button"),null==this.handlers[D]){if(null!=this.quill.scroll.whitelist&&null==this.quill.scroll.whitelist[D])return void A.warn("ignoring attaching to disabled format",D,_);if(null==c.default.query(D))return void A.warn("ignoring attaching to nonexistent format",D,_)}_.addEventListener("SELECT"===_.tagName?"change":"click",function(Z){var I=void 0;if("SELECT"===_.tagName){if(_.selectedIndex<0)return;var R=_.options[_.selectedIndex];I=!R.hasAttribute("selected")&&(R.value||!1)}else I=!_.classList.contains("ql-active")&&(_.value||!_.hasAttribute("value")),Z.preventDefault();q.quill.focus();var E=q.quill.selection.getRange(),L=P(E,1)[0];if(null!=q.handlers[D])q.handlers[D].call(q,I);else if(c.default.query(D).prototype instanceof c.default.Embed){if(!(I=prompt("Enter "+D)))return;q.quill.updateContents((new m.default).retain(L.index).delete(L.length).insert(function i(h,d,k){return d in h?Object.defineProperty(h,d,{value:k,enumerable:!0,configurable:!0,writable:!0}):h[d]=k,h}({},D,I)),t.default.sources.USER)}else q.quill.format(D,I,t.default.sources.USER);q.update(L)}),this.controls.push([D,_])}}},{key:"update",value:function(_){var q=null==_?{}:this.quill.getFormat(_);this.controls.forEach(function(D){var C=P(D,2),Z=C[0],I=C[1];if("SELECT"===I.tagName){var R=void 0;if(null==_)R=null;else if(null==q[Z])R=I.querySelector("option[selected]");else if(!Array.isArray(q[Z])){var E=q[Z];"string"==typeof E&&(E=E.replace(/\"/g,'\\"')),R=I.querySelector('option[value="'+E+'"]')}null==R?(I.value="",I.selectedIndex=-1):R.selected=!0}else if(null==_)I.classList.remove("ql-active");else if(I.hasAttribute("value")){var S=q[Z]===I.getAttribute("value")||null!=q[Z]&&q[Z].toString()===I.getAttribute("value")||null==q[Z]&&!I.getAttribute("value");I.classList.toggle("ql-active",S)}else I.classList.toggle("ql-active",null!=q[Z])})}}]),d}(a.default);function b(h,d,k){var _=document.createElement("button");_.setAttribute("type","button"),_.classList.add("ql-"+d),null!=k&&(_.value=k),h.appendChild(_)}function N(h,d){Array.isArray(d[0])||(d=[d]),d.forEach(function(k){var _=document.createElement("span");_.classList.add("ql-formats"),k.forEach(function(q){if("string"==typeof q)b(_,q);else{var D=Object.keys(q)[0],C=q[D];Array.isArray(C)?function v(h,d,k){var _=document.createElement("select");_.classList.add("ql-"+d),k.forEach(function(q){var D=document.createElement("option");!1!==q?D.setAttribute("value",q):D.setAttribute("selected","selected"),_.appendChild(D)}),h.appendChild(_)}(_,D,C):b(_,D,C)}}),h.appendChild(_)})}g.DEFAULTS={},g.DEFAULTS={container:null,handlers:{clean:function(){var d=this,k=this.quill.getSelection();if(null!=k)if(0==k.length){var _=this.quill.getFormat();Object.keys(_).forEach(function(q){null!=c.default.query(q,c.default.Scope.INLINE)&&d.quill.format(q,!1)})}else this.quill.removeFormat(k,t.default.sources.USER)},direction:function(d){var k=this.quill.getFormat().align;"rtl"===d&&null==k?this.quill.format("align","right",t.default.sources.USER):!d&&"right"===k&&this.quill.format("align",!1,t.default.sources.USER),this.quill.format("direction",d,t.default.sources.USER)},indent:function(d){var k=this.quill.getSelection(),_=this.quill.getFormat(k),q=parseInt(_.indent||0);if("+1"===d||"-1"===d){var D="+1"===d?1:-1;"rtl"===_.direction&&(D*=-1),this.quill.format("indent",q+D,t.default.sources.USER)}},link:function(d){!0===d&&(d=prompt("Enter link URL:")),this.quill.format("link",d,t.default.sources.USER)},list:function(d){var k=this.quill.getSelection(),_=this.quill.getFormat(k);this.quill.format("list","check"===d?"checked"!==_.list&&"unchecked"!==_.list&&"unchecked":d,t.default.sources.USER)}}},O.default=g,O.addControls=N},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),T=function u(l,a,r){null===l&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(void 0===i){var f=Object.getPrototypeOf(l);return null===f?void 0:u(f,a,r)}if("value"in i)return i.value;var n=i.get;return void 0===n?void 0:n.call(r)};var e=function(u){function l(a,r){!function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}(this,l);var i=function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!l||"object"!=typeof l&&"function"!=typeof l?u:l}(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,a));return i.label.innerHTML=r,i.container.classList.add("ql-color-picker"),[].slice.call(i.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(f){f.classList.add("ql-primary")}),i}return function t(u,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}(l,u),P(l,[{key:"buildItem",value:function(r){var i=T(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"buildItem",this).call(this,r);return i.style.backgroundColor=r.getAttribute("value")||"",i}},{key:"selectItem",value:function(r,i){T(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,r,i);var f=this.label.querySelector(".ql-color-label"),n=r&&r.getAttribute("data-value")||"";f&&("line"===f.tagName?f.style.stroke=n:f.style.fill=n)}}]),l}(function y(u){return u&&u.__esModule?u:{default:u}}(p(28)).default);O.default=e},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),T=function u(l,a,r){null===l&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(void 0===i){var f=Object.getPrototypeOf(l);return null===f?void 0:u(f,a,r)}if("value"in i)return i.value;var n=i.get;return void 0===n?void 0:n.call(r)};var e=function(u){function l(a,r){!function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}(this,l);var i=function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!l||"object"!=typeof l&&"function"!=typeof l?u:l}(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,a));return i.container.classList.add("ql-icon-picker"),[].forEach.call(i.container.querySelectorAll(".ql-picker-item"),function(f){f.innerHTML=r[f.getAttribute("data-value")||""]}),i.defaultItem=i.container.querySelector(".ql-selected"),i.selectItem(i.defaultItem),i}return function t(u,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}(l,u),P(l,[{key:"selectItem",value:function(r,i){T(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,r,i),this.label.innerHTML=(r=r||this.defaultItem).innerHTML}}]),l}(function y(u){return u&&u.__esModule?u:{default:u}}(p(28)).default);O.default=e},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function m(y,c){for(var o=0;o<c.length;o++){var t=c[o];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(y,t.key,t)}}return function(y,c,o){return c&&m(y.prototype,c),o&&m(y,o),y}}();var w=function(){function m(y,c){var o=this;(function T(m,y){if(!(m instanceof y))throw new TypeError("Cannot call a class as a function")})(this,m),this.quill=y,this.boundsContainer=c||document.body,this.root=y.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){o.root.style.marginTop=-1*o.quill.root.scrollTop+"px"}),this.hide()}return P(m,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(c){var o=c.left+c.width/2-this.root.offsetWidth/2,t=c.bottom+this.quill.root.scrollTop;this.root.style.left=o+"px",this.root.style.top=t+"px",this.root.classList.remove("ql-flip");var e=this.boundsContainer.getBoundingClientRect(),u=this.root.getBoundingClientRect(),l=0;return u.right>e.right&&(this.root.style.left=o+(l=e.right-u.right)+"px"),u.left<e.left&&(this.root.style.left=o+(l=e.left-u.left)+"px"),u.bottom>e.bottom&&(this.root.style.top=t-(c.bottom-c.top+(u.bottom-u.top))+"px",this.root.classList.add("ql-flip")),l}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),m}();O.default=w},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(h,d){if(Array.isArray(h))return h;if(Symbol.iterator in Object(h))return function v(h,d){var k=[],_=!0,q=!1,D=void 0;try{for(var Z,C=h[Symbol.iterator]();!(_=(Z=C.next()).done)&&(k.push(Z.value),!d||k.length!==d);_=!0);}catch(I){q=!0,D=I}finally{try{!_&&C.return&&C.return()}finally{if(q)throw D}}return k}(h,d);throw new TypeError("Invalid attempt to destructure non-iterable instance")},T=function v(h,d,k){null===h&&(h=Function.prototype);var _=Object.getOwnPropertyDescriptor(h,d);if(void 0===_){var q=Object.getPrototypeOf(h);return null===q?void 0:v(q,d,k)}if("value"in _)return _.value;var D=_.get;return void 0===D?void 0:D.call(k)},w=function(){function v(h,d){for(var k=0;k<d.length;k++){var _=d[k];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(h,_.key,_)}}return function(h,d,k){return d&&v(h.prototype,d),k&&v(h,k),h}}(),y=f(p(3)),o=f(p(8)),t=p(43),e=f(t),l=f(p(27)),a=p(15),i=f(p(41));function f(v){return v&&v.__esModule?v:{default:v}}function n(v,h){if(!(v instanceof h))throw new TypeError("Cannot call a class as a function")}function s(v,h){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!h||"object"!=typeof h&&"function"!=typeof h?v:h}function A(v,h){if("function"!=typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+typeof h);v.prototype=Object.create(h&&h.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(v,h):v.__proto__=h)}var g=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],b=function(v){function h(d,k){n(this,h),null!=k.modules.toolbar&&null==k.modules.toolbar.container&&(k.modules.toolbar.container=g);var _=s(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,d,k));return _.quill.container.classList.add("ql-snow"),_}return A(h,v),w(h,[{key:"extendToolbar",value:function(k){k.container.classList.add("ql-snow"),this.buildButtons([].slice.call(k.container.querySelectorAll("button")),i.default),this.buildPickers([].slice.call(k.container.querySelectorAll("select")),i.default),this.tooltip=new N(this.quill,this.options.bounds),k.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(_,q){k.handlers.link.call(k,!q.format.link)})}}]),h}(e.default);b.DEFAULTS=(0,y.default)(!0,{},e.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(h){if(h){var d=this.quill.getSelection();if(null==d||0==d.length)return;var k=this.quill.getText(d);/^\S+@\S+\.\S+$/.test(k)&&0!==k.indexOf("mailto:")&&(k="mailto:"+k),this.quill.theme.tooltip.edit("link",k)}else this.quill.format("link",!1)}}}}});var N=function(v){function h(d,k){n(this,h);var _=s(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,d,k));return _.preview=_.root.querySelector("a.ql-preview"),_}return A(h,v),w(h,[{key:"listen",value:function(){var k=this;T(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(_){k.root.classList.contains("ql-editing")?k.save():k.edit("link",k.preview.textContent),_.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(_){if(null!=k.linkRange){var q=k.linkRange;k.restoreFocus(),k.quill.formatText(q,"link",!1,o.default.sources.USER),delete k.linkRange}_.preventDefault(),k.hide()}),this.quill.on(o.default.events.SELECTION_CHANGE,function(_,q,D){if(null!=_){if(0===_.length&&D===o.default.sources.USER){var C=k.quill.scroll.descendant(l.default,_.index),Z=P(C,2),I=Z[0];if(null!=I){k.linkRange=new a.Range(_.index-Z[1],I.length());var E=l.default.formats(I.domNode);return k.preview.textContent=E,k.preview.setAttribute("href",E),k.show(),void k.position(k.quill.getBounds(k.linkRange))}}else delete k.linkRange;k.hide()}})}},{key:"show",value:function(){T(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),h}(t.BaseTooltip);N.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),O.default=b},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var T=W(p(29)),w=p(36),m=p(38),y=p(64),o=W(p(65)),e=W(p(66)),u=p(67),l=W(u),a=p(37),r=p(26),i=p(39),f=p(40),s=W(p(56)),g=W(p(68)),N=W(p(27)),h=W(p(69)),k=W(p(70)),q=W(p(71)),C=W(p(72)),I=W(p(73)),R=p(13),E=W(R),L=W(p(74)),M=W(p(75)),j=W(p(57)),H=W(p(41)),Y=W(p(28)),Q=W(p(59)),rt=W(p(60)),lt=W(p(61)),K=W(p(108)),G=W(p(62));function W(J){return J&&J.__esModule?J:{default:J}}T.default.register({"attributors/attribute/direction":m.DirectionAttribute,"attributors/class/align":w.AlignClass,"attributors/class/background":a.BackgroundClass,"attributors/class/color":r.ColorClass,"attributors/class/direction":m.DirectionClass,"attributors/class/font":i.FontClass,"attributors/class/size":f.SizeClass,"attributors/style/align":w.AlignStyle,"attributors/style/background":a.BackgroundStyle,"attributors/style/color":r.ColorStyle,"attributors/style/direction":m.DirectionStyle,"attributors/style/font":i.FontStyle,"attributors/style/size":f.SizeStyle},!0),T.default.register({"formats/align":w.AlignClass,"formats/direction":m.DirectionClass,"formats/indent":y.IndentClass,"formats/background":a.BackgroundStyle,"formats/color":r.ColorStyle,"formats/font":i.FontClass,"formats/size":f.SizeClass,"formats/blockquote":o.default,"formats/code-block":E.default,"formats/header":e.default,"formats/list":l.default,"formats/bold":s.default,"formats/code":R.Code,"formats/italic":g.default,"formats/link":N.default,"formats/script":h.default,"formats/strike":k.default,"formats/underline":q.default,"formats/image":C.default,"formats/video":I.default,"formats/list/item":u.ListItem,"modules/formula":L.default,"modules/syntax":M.default,"modules/toolbar":j.default,"themes/bubble":K.default,"themes/snow":G.default,"ui/icons":H.default,"ui/picker":Y.default,"ui/icon-picker":rt.default,"ui/color-picker":Q.default,"ui/tooltip":lt.default},!0),O.default=T.default},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.IndentClass=void 0;var P=function(){function l(a,r){for(var i=0;i<r.length;i++){var f=r[i];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(a,f.key,f)}}return function(a,r,i){return r&&l(a.prototype,r),i&&l(a,i),a}}(),T=function l(a,r,i){null===a&&(a=Function.prototype);var f=Object.getOwnPropertyDescriptor(a,r);if(void 0===f){var n=Object.getPrototypeOf(a);return null===n?void 0:l(n,r,i)}if("value"in f)return f.value;var s=f.get;return void 0===s?void 0:s.call(i)},m=function y(l){return l&&l.__esModule?l:{default:l}}(p(0));var e=function(l){function a(){return function c(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}(this,a),function o(l,a){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!a||"object"!=typeof a&&"function"!=typeof a?l:a}(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return function t(l,a){if("function"!=typeof a&&null!==a)throw new TypeError("Super expression must either be null or a function, not "+typeof a);l.prototype=Object.create(a&&a.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(l,a):l.__proto__=a)}(a,l),P(a,[{key:"add",value:function(i,f){if("+1"===f||"-1"===f){var n=this.value(i)||0;f="+1"===f?n+1:n-1}return 0===f?(this.remove(i),!0):T(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"add",this).call(this,i,f)}},{key:"canAdd",value:function(i,f){return T(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"canAdd",this).call(this,i,f)||T(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"canAdd",this).call(this,i,parseInt(f))}},{key:"value",value:function(i){return parseInt(T(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"value",this).call(this,i))||void 0}}]),a}(m.default.Attributor.Class),u=new e("indent","ql-indent",{scope:m.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});O.IndentClass=u},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var o=function(t){function e(){return function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function c(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(function w(t){return t&&t.__esModule?t:{default:t}}(p(4)).default);o.blotName="blockquote",o.tagName="blockquote",O.default=o},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function e(u,l){for(var a=0;a<l.length;a++){var r=l[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(u,r.key,r)}}return function(u,l,a){return l&&e(u.prototype,l),a&&e(u,a),u}}();var t=function(e){function u(){return function y(e,u){if(!(e instanceof u))throw new TypeError("Cannot call a class as a function")}(this,u),function c(e,u){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!u||"object"!=typeof u&&"function"!=typeof u?e:u}(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return function o(e,u){if("function"!=typeof u&&null!==u)throw new TypeError("Super expression must either be null or a function, not "+typeof u);e.prototype=Object.create(u&&u.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(e,u):e.__proto__=u)}(u,e),P(u,null,[{key:"formats",value:function(a){return this.tagName.indexOf(a.tagName)+1}}]),u}(function m(e){return e&&e.__esModule?e:{default:e}}(p(4)).default);t.blotName="header",t.tagName=["H1","H2","H3","H4","H5","H6"],O.default=t},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.ListItem=void 0;var P=function(){function n(s,A){for(var g=0;g<A.length;g++){var b=A[g];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(s,b.key,b)}}return function(s,A,g){return A&&n(s.prototype,A),g&&n(s,g),s}}(),T=function n(s,A,g){null===s&&(s=Function.prototype);var b=Object.getOwnPropertyDescriptor(s,A);if(void 0===b){var N=Object.getPrototypeOf(s);return null===N?void 0:n(N,A,g)}if("value"in b)return b.value;var v=b.get;return void 0===v?void 0:v.call(g)},m=e(p(0)),c=e(p(4)),t=e(p(25));function e(n){return n&&n.__esModule?n:{default:n}}function l(n,s){if(!(n instanceof s))throw new TypeError("Cannot call a class as a function")}function a(n,s){if(!n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!s||"object"!=typeof s&&"function"!=typeof s?n:s}function r(n,s){if("function"!=typeof s&&null!==s)throw new TypeError("Super expression must either be null or a function, not "+typeof s);n.prototype=Object.create(s&&s.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(n,s):n.__proto__=s)}var i=function(n){function s(){return l(this,s),a(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return r(s,n),P(s,[{key:"format",value:function(g,b){g!==f.blotName||b?T(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"format",this).call(this,g,b):this.replaceWith(m.default.create(this.statics.scope))}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():T(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(g,b){return this.parent.isolate(this.offset(this.parent),this.length()),g===this.parent.statics.blotName?(this.parent.replaceWith(g,b),this):(this.parent.unwrap(),T(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"replaceWith",this).call(this,g,b))}}],[{key:"formats",value:function(g){return g.tagName===this.tagName?void 0:T(s.__proto__||Object.getPrototypeOf(s),"formats",this).call(this,g)}}]),s}(c.default);i.blotName="list-item",i.tagName="LI";var f=function(n){function s(A){l(this,s);var g=a(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,A)),b=function(v){if(v.target.parentNode===A){var h=g.statics.formats(A),d=m.default.find(v.target);"checked"===h?d.format("list","unchecked"):"unchecked"===h&&d.format("list","checked")}};return A.addEventListener("touchstart",b),A.addEventListener("mousedown",b),g}return r(s,n),P(s,null,[{key:"create",value:function(g){var b="ordered"===g?"OL":"UL",N=T(s.__proto__||Object.getPrototypeOf(s),"create",this).call(this,b);return("checked"===g||"unchecked"===g)&&N.setAttribute("data-checked","checked"===g),N}},{key:"formats",value:function(g){return"OL"===g.tagName?"ordered":"UL"===g.tagName?g.hasAttribute("data-checked")?"true"===g.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}}]),P(s,[{key:"format",value:function(g,b){this.children.length>0&&this.children.tail.format(g,b)}},{key:"formats",value:function(){return function u(n,s,A){return s in n?Object.defineProperty(n,s,{value:A,enumerable:!0,configurable:!0,writable:!0}):n[s]=A,n}({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(g,b){if(g instanceof i)T(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"insertBefore",this).call(this,g,b);else{var N=null==b?this.length():b.offset(this),v=this.split(N);v.parent.insertBefore(g,v)}}},{key:"optimize",value:function(g){T(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"optimize",this).call(this,g);var b=this.next;null!=b&&b.prev===this&&b.statics.blotName===this.statics.blotName&&b.domNode.tagName===this.domNode.tagName&&b.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(b.moveChildren(this),b.remove())}},{key:"replace",value:function(g){if(g.statics.blotName!==this.statics.blotName){var b=m.default.create(this.statics.defaultChild);g.moveChildren(b),this.appendChild(b)}T(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"replace",this).call(this,g)}}]),s}(t.default);f.blotName="list",f.scope=m.default.Scope.BLOCK_BLOT,f.tagName=["OL","UL"],f.defaultChild="list-item",f.allowedChildren=[i],O.ListItem=i,O.default=f},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var o=function(t){function e(){return function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function c(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(function w(t){return t&&t.__esModule?t:{default:t}}(p(56)).default);o.blotName="italic",o.tagName=["EM","I"],O.default=o},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),T=function u(l,a,r){null===l&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(void 0===i){var f=Object.getPrototypeOf(l);return null===f?void 0:u(f,a,r)}if("value"in i)return i.value;var n=i.get;return void 0===n?void 0:n.call(r)};var e=function(u){function l(){return function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}(this,l),function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!l||"object"!=typeof l&&"function"!=typeof l?u:l}(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return function t(u,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}(l,u),P(l,null,[{key:"create",value:function(r){return"super"===r?document.createElement("sup"):"sub"===r?document.createElement("sub"):T(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this,r)}},{key:"formats",value:function(r){return"SUB"===r.tagName?"sub":"SUP"===r.tagName?"super":void 0}}]),l}(function y(u){return u&&u.__esModule?u:{default:u}}(p(6)).default);e.blotName="script",e.tagName=["SUB","SUP"],O.default=e},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var o=function(t){function e(){return function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function c(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(function w(t){return t&&t.__esModule?t:{default:t}}(p(6)).default);o.blotName="strike",o.tagName="S",O.default=o},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var o=function(t){function e(){return function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function c(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(function w(t){return t&&t.__esModule?t:{default:t}}(p(6)).default);o.blotName="underline",o.tagName="U",O.default=o},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),T=function a(r,i,f){null===r&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(void 0===n){var s=Object.getPrototypeOf(r);return null===s?void 0:a(s,i,f)}if("value"in n)return n.value;var A=n.get;return void 0===A?void 0:A.call(f)},m=function c(a){return a&&a.__esModule?a:{default:a}}(p(0)),y=p(27);var u=["alt","height","width"],l=function(a){function r(){return function o(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}(this,r),function t(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?a:r}(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return function e(a,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}(r,a),P(r,[{key:"format",value:function(f,n){u.indexOf(f)>-1?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):T(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(f){var n=T(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,f);return"string"==typeof f&&n.setAttribute("src",this.sanitize(f)),n}},{key:"formats",value:function(f){return u.reduce(function(n,s){return f.hasAttribute(s)&&(n[s]=f.getAttribute(s)),n},{})}},{key:"match",value:function(f){return/\.(jpe?g|gif|png)$/.test(f)||/^data:image\/.+;base64/.test(f)}},{key:"sanitize",value:function(f){return(0,y.sanitize)(f,["http","https","data"])?f:"//:0"}},{key:"value",value:function(f){return f.getAttribute("src")}}]),r}(m.default.Embed);l.blotName="image",l.tagName="IMG",O.default=l},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0});var P=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),T=function a(r,i,f){null===r&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(void 0===n){var s=Object.getPrototypeOf(r);return null===s?void 0:a(s,i,f)}if("value"in n)return n.value;var A=n.get;return void 0===A?void 0:A.call(f)},w=p(4),y=function c(a){return a&&a.__esModule?a:{default:a}}(p(27));var u=["height","width"],l=function(a){function r(){return function o(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}(this,r),function t(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?a:r}(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return function e(a,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}(r,a),P(r,[{key:"format",value:function(f,n){u.indexOf(f)>-1?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):T(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(f){var n=T(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,f);return n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen",!0),n.setAttribute("src",this.sanitize(f)),n}},{key:"formats",value:function(f){return u.reduce(function(n,s){return f.hasAttribute(s)&&(n[s]=f.getAttribute(s)),n},{})}},{key:"sanitize",value:function(f){return y.default.sanitize(f)}},{key:"value",value:function(f){return f.getAttribute("src")}}]),r}(w.BlockEmbed);l.blotName="video",l.className="ql-video",l.tagName="IFRAME",O.default=l},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.FormulaBlot=void 0;var P=function(){function f(n,s){for(var A=0;A<s.length;A++){var g=s[A];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(n,g.key,g)}}return function(n,s,A){return s&&f(n.prototype,s),A&&f(n,A),n}}(),T=function f(n,s,A){null===n&&(n=Function.prototype);var g=Object.getOwnPropertyDescriptor(n,s);if(void 0===g){var b=Object.getPrototypeOf(n);return null===b?void 0:f(b,s,A)}if("value"in g)return g.value;var N=g.get;return void 0===N?void 0:N.call(A)},m=e(p(35)),c=e(p(5)),t=e(p(9));function e(f){return f&&f.__esModule?f:{default:f}}function u(f,n){if(!(f instanceof n))throw new TypeError("Cannot call a class as a function")}function l(f,n){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!n||"object"!=typeof n&&"function"!=typeof n?f:n}function a(f,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function, not "+typeof n);f.prototype=Object.create(n&&n.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(f,n):f.__proto__=n)}var r=function(f){function n(){return u(this,n),l(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return a(n,f),P(n,null,[{key:"create",value:function(A){var g=T(n.__proto__||Object.getPrototypeOf(n),"create",this).call(this,A);return"string"==typeof A&&(window.katex.render(A,g,{throwOnError:!1,errorColor:"#f00"}),g.setAttribute("data-value",A)),g}},{key:"value",value:function(A){return A.getAttribute("data-value")}}]),n}(m.default);r.blotName="formula",r.className="ql-formula",r.tagName="SPAN";var i=function(f){function n(){u(this,n);var s=l(this,(n.__proto__||Object.getPrototypeOf(n)).call(this));if(null==window.katex)throw new Error("Formula module requires KaTeX.");return s}return a(n,f),P(n,null,[{key:"register",value:function(){c.default.register(r,!0)}}]),n}(t.default);O.FormulaBlot=r,O.default=i},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.CodeToken=O.CodeBlock=void 0;var P=function(){function A(g,b){for(var N=0;N<b.length;N++){var v=b[N];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(g,v.key,v)}}return function(g,b,N){return b&&A(g.prototype,b),N&&A(g,N),g}}(),T=function A(g,b,N){null===g&&(g=Function.prototype);var v=Object.getOwnPropertyDescriptor(g,b);if(void 0===v){var h=Object.getPrototypeOf(g);return null===h?void 0:A(h,b,N)}if("value"in v)return v.value;var d=v.get;return void 0===d?void 0:d.call(N)},m=l(p(0)),c=l(p(5)),t=l(p(9));function l(A){return A&&A.__esModule?A:{default:A}}function a(A,g){if(!(A instanceof g))throw new TypeError("Cannot call a class as a function")}function r(A,g){if(!A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!g||"object"!=typeof g&&"function"!=typeof g?A:g}function i(A,g){if("function"!=typeof g&&null!==g)throw new TypeError("Super expression must either be null or a function, not "+typeof g);A.prototype=Object.create(g&&g.prototype,{constructor:{value:A,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(A,g):A.__proto__=g)}var f=function(A){function g(){return a(this,g),r(this,(g.__proto__||Object.getPrototypeOf(g)).apply(this,arguments))}return i(g,A),P(g,[{key:"replaceWith",value:function(N){this.domNode.textContent=this.domNode.textContent,this.attach(),T(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"replaceWith",this).call(this,N)}},{key:"highlight",value:function(N){var v=this.domNode.textContent;this.cachedText!==v&&((v.trim().length>0||null==this.cachedText)&&(this.domNode.innerHTML=N(v),this.domNode.normalize(),this.attach()),this.cachedText=v)}}]),g}(l(p(13)).default);f.className="ql-syntax";var n=new m.default.Attributor.Class("token","hljs",{scope:m.default.Scope.INLINE}),s=function(A){function g(b,N){a(this,g);var v=r(this,(g.__proto__||Object.getPrototypeOf(g)).call(this,b,N));if("function"!=typeof v.options.highlight)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var h=null;return v.quill.on(c.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(h),h=setTimeout(function(){v.highlight(),h=null},v.options.interval)}),v.highlight(),v}return i(g,A),P(g,null,[{key:"register",value:function(){c.default.register(n,!0),c.default.register(f,!0)}}]),P(g,[{key:"highlight",value:function(){var N=this;if(!this.quill.selection.composing){this.quill.update(c.default.sources.USER);var v=this.quill.getSelection();this.quill.scroll.descendants(f).forEach(function(h){h.highlight(N.options.highlight)}),this.quill.update(c.default.sources.SILENT),null!=v&&this.quill.setSelection(v,c.default.sources.SILENT)}}}]),g}(t.default);s.DEFAULTS={highlight:null==window.hljs?null:function(A){return window.hljs.highlightAuto(A).value},interval:1e3},O.CodeBlock=f,O.CodeToken=n,O.default=s},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(B,O){B.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(B,O){B.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(B,O){B.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(B,O){B.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(B,O){B.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(B,O,p){"use strict";Object.defineProperty(O,"__esModule",{value:!0}),O.default=O.BubbleTooltip=void 0;var P=function g(b,N,v){null===b&&(b=Function.prototype);var h=Object.getOwnPropertyDescriptor(b,N);if(void 0===h){var d=Object.getPrototypeOf(b);return null===d?void 0:g(d,N,v)}if("value"in h)return h.value;var k=h.get;return void 0===k?void 0:k.call(v)},T=function(){function g(b,N){for(var v=0;v<N.length;v++){var h=N[v];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(b,h.key,h)}}return function(b,N,v){return N&&g(b.prototype,N),v&&g(b,v),b}}(),m=a(p(3)),c=a(p(8)),o=p(43),t=a(o),e=p(15),l=a(p(41));function a(g){return g&&g.__esModule?g:{default:g}}function r(g,b){if(!(g instanceof b))throw new TypeError("Cannot call a class as a function")}function i(g,b){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?g:b}function f(g,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);g.prototype=Object.create(b&&b.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(g,b):g.__proto__=b)}var n=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],s=function(g){function b(N,v){r(this,b),null!=v.modules.toolbar&&null==v.modules.toolbar.container&&(v.modules.toolbar.container=n);var h=i(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,N,v));return h.quill.container.classList.add("ql-bubble"),h}return f(b,g),T(b,[{key:"extendToolbar",value:function(v){this.tooltip=new A(this.quill,this.options.bounds),this.tooltip.root.appendChild(v.container),this.buildButtons([].slice.call(v.container.querySelectorAll("button")),l.default),this.buildPickers([].slice.call(v.container.querySelectorAll("select")),l.default)}}]),b}(t.default);s.DEFAULTS=(0,m.default)(!0,{},t.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(b){b?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var A=function(g){function b(N,v){r(this,b);var h=i(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,N,v));return h.quill.on(c.default.events.EDITOR_CHANGE,function(d,k,_,q){if(d===c.default.events.SELECTION_CHANGE)if(null!=k&&k.length>0&&q===c.default.sources.USER){h.show(),h.root.style.left="0px",h.root.style.width="",h.root.style.width=h.root.offsetWidth+"px";var D=h.quill.getLines(k.index,k.length);if(1===D.length)h.position(h.quill.getBounds(k));else{var C=D[D.length-1],Z=h.quill.getIndex(C),I=Math.min(C.length()-1,k.index+k.length-Z),R=h.quill.getBounds(new e.Range(Z,I));h.position(R)}}else document.activeElement!==h.textbox&&h.quill.hasFocus()&&h.hide()}),h}return f(b,g),T(b,[{key:"listen",value:function(){var v=this;P(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){v.root.classList.remove("ql-editing")}),this.quill.on(c.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!v.root.classList.contains("ql-hidden")){var h=v.quill.getSelection();null!=h&&v.position(v.quill.getBounds(h))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(v){var h=P(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"position",this).call(this,v),d=this.root.querySelector(".ql-tooltip-arrow");if(d.style.marginLeft="",0===h)return h;d.style.marginLeft=-1*h-d.offsetWidth/2+"px"}}]),b}(o.BaseTooltip);A.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),O.BubbleTooltip=A,O.default=s},function(B,O,p){B.exports=p(63)}]).default}}]);