# Test script for Azure Blob Storage integration
# This script tests the file upload endpoints with the new BlobStorageService

Write-Host "=== Azure Blob Storage Integration Test ===" -ForegroundColor Green

$baseUrl = "http://localhost:5144"
$testImagePath = "test-image.jpg"

# Create a simple test image file (1x1 pixel JPEG)
$jpegBytes = @(
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00, 0x48,
    0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43, 0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08,
    0x07, 0x07, 0x07, 0x09, 0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
    0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20, 0x24, 0x2E, 0x27, 0x20,
    0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29, 0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27,
    0x39, 0x3D, 0x38, 0x32, 0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
    0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01, 0xFF, 0xC4, 0x00, 0x14,
    0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x08, 0xFF, 0xC4, 0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C, 0x03, 0x01, 0x00, 0x02,
    0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x8A, 0x00, 0xFF, 0xD9
)

[System.IO.File]::WriteAllBytes($testImagePath, $jpegBytes)
Write-Host "Created test image: $testImagePath" -ForegroundColor Yellow

# Function to test authentication and get token
function Get-AuthToken {
    Write-Host "`nTesting authentication..." -ForegroundColor Cyan
    
    $loginData = @{
        email = "<EMAIL>"
        password = "Oracle123!"
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
        if ($response.success) {
            Write-Host "✓ Authentication successful" -ForegroundColor Green
            return $response.data.token
        } else {
            Write-Host "✗ Authentication failed: $($response.message)" -ForegroundColor Red
            return $null
        }
    } catch {
        Write-Host "✗ Authentication error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Function to test file upload endpoint
function Test-FileUpload {
    param(
        [string]$endpoint,
        [string]$token,
        [string]$filePath,
        [string]$description
    )
    
    Write-Host "`nTesting $description..." -ForegroundColor Cyan
    
    try {
        # Create multipart form data
        $boundary = [System.Guid]::NewGuid().ToString()
        $fileBytes = [System.IO.File]::ReadAllBytes($filePath)
        $fileName = [System.IO.Path]::GetFileName($filePath)
        
        $bodyLines = @(
            "--$boundary",
            "Content-Disposition: form-data; name=`"file`"; filename=`"$fileName`"",
            "Content-Type: image/jpeg",
            "",
            [System.Text.Encoding]::GetEncoding("iso-8859-1").GetString($fileBytes),
            "--$boundary--"
        )
        
        $body = $bodyLines -join "`r`n"
        $bodyBytes = [System.Text.Encoding]::GetEncoding("iso-8859-1").GetBytes($body)
        
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "multipart/form-data; boundary=$boundary"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl$endpoint" -Method POST -Body $bodyBytes -Headers $headers
        
        if ($response.success) {
            Write-Host "✓ $description successful" -ForegroundColor Green
            Write-Host "  Blob URL: $($response.data)" -ForegroundColor Gray
            return $response.data
        } else {
            Write-Host "✗ $description failed: $($response.message)" -ForegroundColor Red
            if ($response.errors) {
                $response.errors | ForEach-Object { Write-Host "  Error: $_" -ForegroundColor Red }
            }
            return $null
        }
    } catch {
        Write-Host "✗ $description error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Main test execution
try {
    # Get authentication token
    $token = Get-AuthToken
    if (-not $token) {
        Write-Host "Cannot proceed without authentication token" -ForegroundColor Red
        exit 1
    }
    
    # Test profile photo upload
    $profilePhotoUrl = Test-FileUpload -endpoint "/api/profile/upload/profile-photo" -token $token -filePath $testImagePath -description "Profile Photo Upload"
    
    # Test cover photo upload
    $coverPhotoUrl = Test-FileUpload -endpoint "/api/profile/upload/cover-photo" -token $token -filePath $testImagePath -description "Cover Photo Upload"
    
    # Test portfolio image upload
    $portfolioImageUrl = Test-FileUpload -endpoint "/api/profile/upload/portfolio-image" -token $token -filePath $testImagePath -description "Portfolio Image Upload"
    
    # Test document upload
    $documentUrl = Test-FileUpload -endpoint "/api/profile/upload/document" -token $token -filePath $testImagePath -description "Document Upload"
    
    # Summary
    Write-Host "`n=== Test Summary ===" -ForegroundColor Green
    Write-Host "Profile Photo: $(if ($profilePhotoUrl) { '✓ Success' } else { '✗ Failed' })" -ForegroundColor $(if ($profilePhotoUrl) { 'Green' } else { 'Red' })
    Write-Host "Cover Photo: $(if ($coverPhotoUrl) { '✓ Success' } else { '✗ Failed' })" -ForegroundColor $(if ($coverPhotoUrl) { 'Green' } else { 'Red' })
    Write-Host "Portfolio Image: $(if ($portfolioImageUrl) { '✓ Success' } else { '✗ Failed' })" -ForegroundColor $(if ($portfolioImageUrl) { 'Green' } else { 'Red' })
    Write-Host "Document Upload: $(if ($documentUrl) { '✓ Success' } else { '✗ Failed' })" -ForegroundColor $(if ($documentUrl) { 'Green' } else { 'Red' })
    
    $successCount = @($profilePhotoUrl, $coverPhotoUrl, $portfolioImageUrl, $documentUrl | Where-Object { $_ }).Count
    Write-Host "`nOverall: $successCount/4 tests passed" -ForegroundColor $(if ($successCount -eq 4) { 'Green' } else { 'Yellow' })
    
} finally {
    # Cleanup
    if (Test-Path $testImagePath) {
        Remove-Item $testImagePath -Force
        Write-Host "`nCleaned up test file: $testImagePath" -ForegroundColor Gray
    }
}
