import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ArticleService } from '../shared/services/article.service';
import { AuthService } from '../auth/services/auth.service';
import { TranslationService } from '../core/i18n/translation.service';
import { ArticlePreview } from '../shared/models/article.models';

@Component({
  selector: 'app-test-article-preview',
  template: `
    <div class="test-container">
      <h1>Article Preview Feature Test</h1>

      <div class="auth-status">
        <h2>Authentication Status</h2>
        <p>User is: <strong>{{ isAuthenticated ? 'Authenticated' : 'Anonymous' }}</strong></p>
        <button mat-raised-button color="primary" (click)="toggleAuth()" *ngIf="!isAuthenticated">
          Login for Testing
        </button>
        <button mat-raised-button color="warn" (click)="logout()" *ngIf="isAuthenticated">
          Logout for Testing
        </button>
      </div>

      <div class="articles-section">
        <h2>Test Articles</h2>
        <div class="articles-grid">
          <mat-card class="article-card" *ngFor="let article of testArticles" (click)="viewArticle(article)">
            <img mat-card-image [src]="article.imageUrl" [alt]="article.title" class="article-image">

            <mat-card-header>
              <mat-card-title>{{ article.title }}</mat-card-title>
              <mat-card-subtitle>
                <div class="article-meta">
                  <span class="author">{{ article.author }}</span>
                  <span class="separator">•</span>
                  <span class="read-time">{{ article.readTime }} {{ t.home.minRead }}</span>
                </div>
              </mat-card-subtitle>
            </mat-card-header>

            <mat-card-content>
              <p class="article-excerpt">{{ article.excerpt }}</p>
              <mat-chip class="category-chip">{{ article.category }}</mat-chip>
            </mat-card-content>

            <mat-card-actions>
              <button mat-button color="primary" (click)="viewArticle(article); $event.stopPropagation()">
                <mat-icon>visibility</mat-icon>
                {{ t.home.readArticle }}
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>

      <div class="test-instructions">
        <h2>Test Instructions</h2>
        <ol>
          <li><strong>Anonymous User Test:</strong> Make sure you're logged out, then click on any article to see the preview with blur effect and authentication prompt.</li>
          <li><strong>Authenticated User Test:</strong> Login using the button above, then click on any article to see the full content without restrictions.</li>
          <li><strong>Navigation Test:</strong> Verify that clicking articles navigates to /articles/[slug] route.</li>
          <li><strong>Authentication Prompt Test:</strong> As anonymous user, verify the authentication prompt appears with proper Bulgarian text and working login/register buttons.</li>
        </ol>
      </div>
    </div>
  `,
  styles: [`
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }

    .auth-status {
      background: var(--theme-accent-light);
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      text-align: center;
    }

    .articles-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .article-card {
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .article-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .article-image {
      height: 200px;
      object-fit: cover;
    }

    .article-meta {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--theme-text-secondary);
      font-size: 0.9rem;
    }

    .separator {
      color: var(--theme-text-disabled);
    }

    .article-excerpt {
      margin-bottom: 1rem;
      line-height: 1.5;
    }

    .category-chip {
      background: var(--theme-accent);
      color: var(--theme-text-primary);
    }

    .test-instructions {
      background: var(--theme-surface);
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid var(--theme-primary);
    }

    .test-instructions ol {
      margin: 1rem 0;
    }

    .test-instructions li {
      margin-bottom: 0.75rem;
      line-height: 1.5;
    }
  `]
})
export class TestArticlePreviewComponent implements OnInit {
  testArticles: ArticlePreview[] = [];
  isAuthenticated = false;

  constructor(
    private articleService: ArticleService,
    private authService: AuthService,
    private router: Router,
    public t: TranslationService
  ) {}

  ngOnInit(): void {
    // Subscribe to authentication state
    this.authService.isAuthenticated$.subscribe(isAuth => {
      this.isAuthenticated = isAuth;
    });

    // Load test articles
    this.loadTestArticles();
  }

  private loadTestArticles(): void {
    // Load articles from database instead of using mock data
    this.articleService.getFeaturedArticles(6).subscribe({
      next: (articles) => {
        this.testArticles = articles;
      },
      error: (error) => {
        console.error('Error loading articles from database:', error);
        // Only show error message, don't fall back to mock data
        this.testArticles = [];
      }
    });
  }

  viewArticle(article: ArticlePreview): void {
    this.router.navigate(['/articles', article.slug]);
  }

  toggleAuth(): void {
    // For testing purposes, navigate to login
    this.router.navigate(['/login']);
  }

  logout(): void {
    this.authService.logout().subscribe(() => {
      console.log('Logged out for testing');
    });
  }
}
