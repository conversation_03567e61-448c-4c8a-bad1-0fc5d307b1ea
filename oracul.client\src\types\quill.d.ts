declare module 'quill' {
  export interface Delta {
    ops?: any[];
    retain?: number;
    delete?: number;
    insert?: any;
    attributes?: any;
  }

  export default class Quill {
    constructor(container: string | Element, options?: any);
    deleteText(index: number, length: number, source?: string): Delta;
    getContents(index?: number, length?: number): Delta;
    getLength(): number;
    getText(index?: number, length?: number): string;
    insertEmbed(index: number, type: string, value: any, source?: string): Delta;
    insertText(index: number, text: string, source?: string): Delta;
    insertText(index: number, text: string, formats: any, source?: string): Delta;
    setContents(delta: Delta, source?: string): Delta;
    setText(text: string, source?: string): Delta;
    updateContents(delta: Delta, source?: string): Delta;
    format(name: string, value: any, source?: string): Delta;
    formatLine(index: number, length: number, source?: string): Delta;
    formatLine(index: number, length: number, formats: any, source?: string): Delta;
    formatText(index: number, length: number, source?: string): Delta;
    formatText(index: number, length: number, formats: any, source?: string): Delta;
    getFormat(range?: any): any;
    getFormat(index: number, length?: number): any;
    removeFormat(index: number, length: number, source?: string): Delta;
    blur(): void;
    focus(): void;
    getBounds(index: number, length?: number): any;
    getSelection(focus?: boolean): any;
    setSelection(index: number, length?: number, source?: string): void;
    setSelection(range: any, source?: string): void;
    on(eventName: string, handler: (...args: any[]) => void): void;
    off(eventName: string, handler: (...args: any[]) => void): void;
    once(eventName: string, handler: (...args: any[]) => void): void;
    root: HTMLElement;
    clipboard: any;
    container: HTMLElement;
    scroll: any;
    keyboard: any;
    history: any;
    theme: any;
  }
}
