{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../shared/services/file-upload.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nfunction ImageUploadComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"img\", 4);\n    i0.ɵɵelementStart(2, \"div\", 5)(3, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function ImageUploadComponent_div_1_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeImage());\n    });\n    i0.ɵɵelementStart(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r0.getImageUrl(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.placeholder);\n  }\n}\nfunction ImageUploadComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 11);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u041A\\u0430\\u0447\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u043D\\u0438\\u043C\\u043A\\u0430\\u0442\\u0430...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImageUploadComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"mat-icon\", 13);\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 15);\n    i0.ɵɵtext(6, \"\\u041F\\u043B\\u044A\\u0437\\u043D\\u0435\\u0442\\u0435 \\u0438 \\u043F\\u0443\\u0441\\u043D\\u0435\\u0442\\u0435 \\u0444\\u0430\\u0439\\u043B \\u0442\\u0443\\u043A \\u0438\\u043B\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 16, 17);\n    i0.ɵɵlistener(\"change\", function ImageUploadComponent_div_2_div_2_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ImageUploadComponent_div_2_div_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const _r6 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(_r6.click());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"add_photo_alternate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" \\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0444\\u0430\\u0439\\u043B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 19);\n    i0.ɵɵtext(14, \" \\u041F\\u043E\\u0434\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\\u0438: JPG, PNG, GIF, WebP\");\n    i0.ɵɵelement(15, \"br\");\n    i0.ɵɵtext(16, \" \\u041C\\u0430\\u043A\\u0441\\u0438\\u043C\\u0430\\u043B\\u0435\\u043D \\u0440\\u0430\\u0437\\u043C\\u0435\\u0440: 5MB \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.placeholder);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"accept\", ctx_r5.accept);\n  }\n}\nfunction ImageUploadComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵlistener(\"dragover\", function ImageUploadComponent_div_2_Template_div_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onDragOver($event));\n    })(\"dragleave\", function ImageUploadComponent_div_2_Template_div_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onDragLeave($event));\n    })(\"drop\", function ImageUploadComponent_div_2_Template_div_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onDrop($event));\n    });\n    i0.ɵɵtemplate(1, ImageUploadComponent_div_2_div_1_Template, 4, 0, \"div\", 8);\n    i0.ɵɵtemplate(2, ImageUploadComponent_div_2_div_2_Template, 17, 2, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"drag-over\", ctx_r1.dragOver)(\"uploading\", ctx_r1.isUploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isUploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isUploading);\n  }\n}\nexport let ImageUploadComponent = /*#__PURE__*/(() => {\n  class ImageUploadComponent {\n    constructor(fileUploadService, snackBar) {\n      this.fileUploadService = fileUploadService;\n      this.snackBar = snackBar;\n      this.placeholder = 'Качете снимка';\n      this.accept = 'image/*';\n      this.imageUploaded = new EventEmitter();\n      this.imageRemoved = new EventEmitter();\n      this.isUploading = false;\n      this.dragOver = false;\n    }\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files && input.files.length > 0) {\n        this.uploadFile(input.files[0]);\n      }\n    }\n    onDragOver(event) {\n      event.preventDefault();\n      this.dragOver = true;\n    }\n    onDragLeave(event) {\n      event.preventDefault();\n      this.dragOver = false;\n    }\n    onDrop(event) {\n      event.preventDefault();\n      this.dragOver = false;\n      if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {\n        this.uploadFile(event.dataTransfer.files[0]);\n      }\n    }\n    uploadFile(file) {\n      // Validate file\n      const validation = this.fileUploadService.validateImageFile(file);\n      if (!validation.valid) {\n        this.snackBar.open(validation.error, 'Затвори', {\n          duration: 3000\n        });\n        return;\n      }\n      this.isUploading = true;\n      this.fileUploadService.uploadImage(file).subscribe({\n        next: response => {\n          const fullUrl = this.fileUploadService.getImageUrl(response.url);\n          this.imageUploaded.emit(fullUrl);\n          this.snackBar.open('Снимката е качена успешно', 'Затвори', {\n            duration: 3000\n          });\n          this.isUploading = false;\n        },\n        error: error => {\n          console.error('Error uploading image:', error);\n          this.snackBar.open(error.message || 'Грешка при качването на снимката', 'Затвори', {\n            duration: 3000\n          });\n          this.isUploading = false;\n        }\n      });\n    }\n    removeImage() {\n      this.imageRemoved.emit();\n    }\n    getImageUrl() {\n      if (!this.currentImageUrl) return '';\n      return this.fileUploadService.getImageUrl(this.currentImageUrl);\n    }\n    static {\n      this.ɵfac = function ImageUploadComponent_Factory(t) {\n        return new (t || ImageUploadComponent)(i0.ɵɵdirectiveInject(i1.FileUploadService), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ImageUploadComponent,\n        selectors: [[\"app-image-upload\"]],\n        inputs: {\n          currentImageUrl: \"currentImageUrl\",\n          placeholder: \"placeholder\",\n          accept: \"accept\"\n        },\n        outputs: {\n          imageUploaded: \"imageUploaded\",\n          imageRemoved: \"imageRemoved\"\n        },\n        decls: 3,\n        vars: 2,\n        consts: [[1, \"image-upload-container\"], [\"class\", \"current-image\", 4, \"ngIf\"], [\"class\", \"upload-area\", 3, \"drag-over\", \"uploading\", \"dragover\", \"dragleave\", \"drop\", 4, \"ngIf\"], [1, \"current-image\"], [1, \"uploaded-image\", 3, \"src\", \"alt\"], [1, \"image-overlay\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 1, \"remove-button\", 3, \"click\"], [1, \"upload-area\", 3, \"dragover\", \"dragleave\", \"drop\"], [\"class\", \"upload-loading\", 4, \"ngIf\"], [\"class\", \"upload-prompt\", 4, \"ngIf\"], [1, \"upload-loading\"], [\"mode\", \"indeterminate\", \"diameter\", \"40\"], [1, \"upload-prompt\"], [1, \"upload-icon\"], [1, \"upload-text\"], [1, \"upload-hint\"], [\"type\", \"file\", 1, \"file-input\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"file-requirements\"]],\n        template: function ImageUploadComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, ImageUploadComponent_div_1_Template, 6, 2, \"div\", 1);\n            i0.ɵɵtemplate(2, ImageUploadComponent_div_2_Template, 3, 6, \"div\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentImageUrl && !ctx.isUploading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.currentImageUrl || ctx.isUploading);\n          }\n        },\n        styles: [\".image-upload-container[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.current-image[_ngcontent-%COMP%]{position:relative;display:inline-block;border-radius:8px;overflow:hidden;box-shadow:0 2px 8px #0000001a}.uploaded-image[_ngcontent-%COMP%]{max-width:100%;max-height:300px;width:auto;height:auto;display:block}.image-overlay[_ngcontent-%COMP%]{position:absolute;top:0;right:0;background:rgba(0,0,0,.5);border-radius:0 0 0 8px}.remove-button[_ngcontent-%COMP%]{color:#fff}.upload-area[_ngcontent-%COMP%]{border:2px dashed #d2a6d0;border-radius:8px;padding:40px 20px;text-align:center;background-color:#fafafa;transition:all .3s ease;cursor:pointer}.upload-area[_ngcontent-%COMP%]:hover{border-color:#67455c;background-color:#f5f5f5}.upload-area.drag-over[_ngcontent-%COMP%]{border-color:#67455c;background-color:#e6dbec;transform:scale(1.02)}.upload-area.uploading[_ngcontent-%COMP%]{border-color:#d2a6d0;background-color:#f9f9f9;cursor:not-allowed}.upload-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px}.upload-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:14px}.upload-prompt[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.upload-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;color:#d2a6d0}.upload-text[_ngcontent-%COMP%]{font-size:18px;font-weight:500;color:#3f2f4e;margin:0}.upload-hint[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.file-input[_ngcontent-%COMP%]{display:none}.file-requirements[_ngcontent-%COMP%]{font-size:12px;color:#999;margin:8px 0 0;line-height:1.4}@media (max-width: 768px){.upload-area[_ngcontent-%COMP%]{padding:30px 15px}.upload-icon[_ngcontent-%COMP%]{font-size:36px;width:36px;height:36px}.upload-text[_ngcontent-%COMP%]{font-size:16px}.uploaded-image[_ngcontent-%COMP%]{max-height:200px}}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.upload-area.drag-over[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1s infinite}\"]\n      });\n    }\n  }\n  return ImageUploadComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}