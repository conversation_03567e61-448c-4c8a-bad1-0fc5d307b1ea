.article-editor-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.editor-card {
  margin-bottom: 20px;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.content-editor {
  margin-bottom: 16px;
}

.editor-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #3f2f4e;
  margin-bottom: 8px;
}

.featured-image-section {
  margin-bottom: 16px;
}

.seo-section {
  margin: 20px 0;
  padding: 16px;
  background-color: #f8f9fa;
}

.options-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 20px 0;
}

.options-section mat-checkbox {
  margin-bottom: 8px;
}

/* Quill Editor Styling */
::ng-deep .ql-editor {
  min-height: 300px;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  line-height: 1.6;
}

::ng-deep .ql-toolbar {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  background-color: #f8f9fa;
}

::ng-deep .ql-container {
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
}

::ng-deep .ql-editor.ql-blank::before {
  color: #999;
  font-style: italic;
}

/* Material Design Integration */
::ng-deep .ql-toolbar .ql-stroke {
  stroke: #3f2f4e;
}

::ng-deep .ql-toolbar .ql-fill {
  fill: #3f2f4e;
}

::ng-deep .ql-toolbar button:hover .ql-stroke {
  stroke: #d2a6d0;
}

::ng-deep .ql-toolbar button:hover .ql-fill {
  fill: #d2a6d0;
}

::ng-deep .ql-toolbar button.ql-active .ql-stroke {
  stroke: #67455c;
}

::ng-deep .ql-toolbar button.ql-active .ql-fill {
  fill: #67455c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .article-editor-container {
    padding: 10px;
  }

  .editor-card {
    margin: 0;
  }

  ::ng-deep .ql-toolbar {
    padding: 8px;
  }

  ::ng-deep .ql-editor {
    min-height: 200px;
  }
}

/* Action Buttons */
mat-card-actions {
  padding: 16px 24px;
  gap: 8px;
}

mat-card-actions button {
  margin-left: 8px;
}

mat-card-actions button:first-child {
  margin-left: 0;
}

/* Form Validation Styling */
.mat-form-field.ng-invalid .mat-form-field-outline-thick {
  color: #f44336;
}

.mat-form-field.ng-invalid .mat-form-field-label {
  color: #f44336;
}

/* Custom scrollbar for editor */
::ng-deep .ql-editor::-webkit-scrollbar {
  width: 8px;
}

::ng-deep .ql-editor::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::ng-deep .ql-editor::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::ng-deep .ql-editor::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
