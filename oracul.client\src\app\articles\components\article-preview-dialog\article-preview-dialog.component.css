.preview-dialog {
  max-width: 800px;
  width: 100%;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.dialog-header h2 {
  margin: 0;
  color: #3f2f4e;
}

.close-button {
  color: #666;
}

.dialog-content {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.article-preview {
  padding: 24px;
}

.article-image {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
}

.featured-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
}

.article-header {
  margin-bottom: 24px;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.category {
  background-color: #e6dbec;
  color: #3f2f4e;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.read-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.read-time mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.article-title {
  font-size: 32px;
  font-weight: 700;
  color: #3f2f4e;
  line-height: 1.2;
  margin: 0 0 16px 0;
}

.article-info {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #666;
  font-size: 14px;
}

.author {
  font-weight: 500;
}

.article-excerpt {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-left: 4px solid #d2a6d0;
  border-radius: 4px;
}

.article-excerpt p {
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
  color: #555;
  font-style: italic;
}

.article-content {
  margin-bottom: 32px;
}

.content-html {
  font-size: 16px;
  line-height: 1.7;
  color: #333;
}

/* Style the HTML content */
.content-html h1,
.content-html h2,
.content-html h3,
.content-html h4,
.content-html h5,
.content-html h6 {
  color: #3f2f4e;
  margin-top: 24px;
  margin-bottom: 12px;
}

.content-html h1 { font-size: 28px; }
.content-html h2 { font-size: 24px; }
.content-html h3 { font-size: 20px; }

.content-html p {
  margin-bottom: 16px;
}

.content-html ul,
.content-html ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.content-html li {
  margin-bottom: 8px;
}

.content-html blockquote {
  border-left: 4px solid #d2a6d0;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
  color: #666;
}

.content-html img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 16px 0;
}

.content-html code {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.content-html pre {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

.article-tags {
  border-top: 1px solid #e0e0e0;
  padding-top: 24px;
}

.article-tags h4 {
  margin: 0 0 12px 0;
  color: #3f2f4e;
  font-size: 16px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-chip {
  background-color: #e6dbec;
  color: #3f2f4e;
}

.dialog-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  gap: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .preview-dialog {
    max-width: 100vw;
    height: 100vh;
  }
  
  .dialog-content {
    max-height: calc(100vh - 120px);
  }
  
  .article-preview {
    padding: 16px;
  }
  
  .article-title {
    font-size: 24px;
  }
  
  .featured-image {
    height: 200px;
  }
}
