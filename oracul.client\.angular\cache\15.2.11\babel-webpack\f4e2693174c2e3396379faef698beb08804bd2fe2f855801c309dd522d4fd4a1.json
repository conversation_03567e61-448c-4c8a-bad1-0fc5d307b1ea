{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Quill Editor\nimport { QuillModule } from 'ngx-quill';\n// Angular Material Modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDividerModule } from '@angular/material/divider';\n// Components\nimport { ArticleViewComponent } from './article-view/article-view.component';\nimport { ArticleEditorComponent } from './article-editor/article-editor.component';\nimport { ArticleManagementComponent } from './article-management/article-management.component';\n// Services\nimport { ArticleService } from '../shared/services/article.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-quill\";\nconst routes = [{\n  path: 'manage',\n  component: ArticleManagementComponent\n}, {\n  path: 'editor',\n  component: ArticleEditorComponent\n}, {\n  path: 'editor/:id',\n  component: ArticleEditorComponent\n}, {\n  path: ':slug',\n  component: ArticleViewComponent\n}];\nexport let ArticlesModule = /*#__PURE__*/(() => {\n  class ArticlesModule {\n    static {\n      this.ɵfac = function ArticlesModule_Factory(t) {\n        return new (t || ArticlesModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ArticlesModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [ArticleService],\n        imports: [CommonModule, RouterModule.forChild(routes), ReactiveFormsModule, FormsModule, QuillModule.forRoot(), MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, MatProgressSpinnerModule, MatProgressBarModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatCheckboxModule, MatTableModule, MatPaginatorModule, MatSortModule, MatDialogModule, MatSnackBarModule, MatTabsModule, MatMenuModule, MatTooltipModule, MatDividerModule]\n      });\n    }\n  }\n  return ArticlesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}