using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Oracul.Server.Models;
using Oracul.Server.Services;
using System.Text;
using Xunit;

namespace Oracul.Tests
{
    public class BlobStorageServiceTests
    {
        private readonly Mock<ILogger<BlobStorageService>> _mockLogger;
        private readonly Mock<IOptions<BlobStorageSettings>> _mockOptions;
        private readonly BlobStorageSettings _settings;
        private readonly BlobStorageService _service;

        public BlobStorageServiceTests()
        {
            _mockLogger = new Mock<ILogger<BlobStorageService>>();
            _mockOptions = new Mock<IOptions<BlobStorageSettings>>();

            _settings = new BlobStorageSettings
            {
                ConnectionString = "UseDevelopmentStorage=true",
                ContainerName = "test-container",
                BaseUrl = "https://test.blob.core.windows.net/",
                MaxFileSizeBytes = 10 * 1024 * 1024, // 10MB
                AllowedImageTypes = new[] { "image/jpeg", "image/png", "image/gif" },
                AllowedDocumentTypes = new[] { "application/pdf", "application/msword" }
            };

            _mockOptions.Setup(x => x.Value).Returns(_settings);
            _service = new BlobStorageService(_mockOptions.Object, _mockLogger.Object);
        }

        // Note: These tests require Azure Storage Emulator or actual Azure Storage connection
        // For unit testing, we focus on the validation and utility methods that don't require Azure SDK mocking

        [Theory]
        [InlineData("test.jpg", "image/jpeg", new[] { "image/jpeg", "image/png" }, 1024, 2048, true)]
        [InlineData("test.pdf", "application/pdf", new[] { "application/pdf" }, 1024, 2048, true)]
        [InlineData("test.exe", "application/exe", new[] { "image/jpeg", "image/png" }, 1024, 2048, false)]
        [InlineData("test.jpg", "image/jpeg", new[] { "image/jpeg", "image/png" }, 2048, 1024, false)]
        public void ValidateFile_VariousInputs_ReturnsExpectedResult(
            string fileName, string contentType, string[] allowedTypes, 
            long fileSize, long maxSize, bool expectedValid)
        {
            // Act
            var result = _service.ValidateFile(fileName, contentType, fileSize, allowedTypes, maxSize);

            // Assert
            Assert.Equal(expectedValid, result.IsValid);
            if (!expectedValid)
            {
                Assert.NotEmpty(result.Errors);
            }
        }

        [Fact]
        public void GenerateUniqueFileName_ValidInput_ReturnsUniqueFileName()
        {
            // Arrange
            var originalFileName = "test file.jpg";

            // Act
            var result = _service.GenerateUniqueFileName(originalFileName);

            // Assert
            Assert.NotEqual(originalFileName, result);
            Assert.Contains("test_file", result);
            Assert.EndsWith(".jpg", result);
            Assert.Contains("_", result); // Should contain timestamp separator
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public void GenerateUniqueFileName_InvalidInput_ThrowsArgumentException(string fileName)
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _service.GenerateUniqueFileName(fileName));
        }



        [Fact]
        public void GetFileUrlAsync_ValidPath_ReturnsCorrectUrl()
        {
            // Arrange
            var blobPath = "profiles/123/test.jpg";
            var expectedUrl = $"{_settings.BaseUrl}{_settings.ContainerName}/{blobPath}";

            // Act
            var result = _service.GetFileUrlAsync(blobPath);

            // Assert
            Assert.Equal(expectedUrl, result);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public void GetFileUrlAsync_InvalidPath_ThrowsArgumentException(string blobPath)
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _service.GetFileUrlAsync(blobPath));
        }

        [Fact]
        public async Task DownloadFileAsync_ValidPath_ReturnsStream()
        {
            // Arrange
            var blobPath = "profiles/123/test.jpg";
            var testContent = "test file content";
            var contentStream = new MemoryStream(Encoding.UTF8.GetBytes(testContent));
            
            var mockDownloadResult = BlobsModelFactory.BlobDownloadStreamingResult(contentStream);
            var mockResponse = Response.FromValue(mockDownloadResult, Mock.Of<Response>());

            _mockContainerClient.Setup(x => x.GetBlobClient(blobPath))
                .Returns(_mockBlobClient.Object);

            _mockBlobClient.Setup(x => x.DownloadStreamingAsync(It.IsAny<BlobDownloadOptions>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockResponse);

            // Act
            var result = await _service.DownloadFileAsync(blobPath);

            // Assert
            Assert.NotNull(result);
            
            // Read the stream to verify content
            using var reader = new StreamReader(result);
            var content = await reader.ReadToEndAsync();
            Assert.Equal(testContent, content);
        }
    }
}
