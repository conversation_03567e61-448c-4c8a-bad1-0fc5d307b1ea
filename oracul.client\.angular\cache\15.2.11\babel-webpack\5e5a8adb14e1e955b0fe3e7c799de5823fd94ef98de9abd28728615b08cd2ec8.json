{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i3 from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { mixinInitialized, mixinDisabled, AnimationDurations, AnimationCurves, MatCommonModule } from '@angular/material/core';\nimport { Subject, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes, query, animateChild } from '@angular/animations';\nimport * as i4 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nconst _c0 = [\"mat-sort-header\", \"\"];\nfunction MatSortHeader_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"@arrowPosition.start\", function MatSortHeader_div_3_Template_div_animation_arrowPosition_start_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._disableViewStateAnimation = true);\n    })(\"@arrowPosition.done\", function MatSortHeader_div_3_Template_div_animation_arrowPosition_done_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3._disableViewStateAnimation = false);\n    });\n    i0.ɵɵelement(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"div\", 5);\n    i0.ɵɵelement(3, \"div\", 6)(4, \"div\", 7)(5, \"div\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@arrowOpacity\", ctx_r0._getArrowViewState())(\"@arrowPosition\", ctx_r0._getArrowViewState())(\"@allowChildren\", ctx_r0._getArrowDirectionState());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"@indicator\", ctx_r0._getArrowDirectionState());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@leftPointer\", ctx_r0._getArrowDirectionState());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@rightPointer\", ctx_r0._getArrowDirectionState());\n  }\n}\nconst _c1 = [\"*\"];\nfunction getSortDuplicateSortableIdError(id) {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to MatSort.\n/** @docs-private */\nconst _MatSortBase = mixinInitialized(mixinDisabled(class {}));\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort extends _MatSortBase {\n  /** The sort direction of the currently active MatSortable. */\n  get direction() {\n    return this._direction;\n  }\n  set direction(direction) {\n    if (direction && direction !== 'asc' && direction !== 'desc' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortInvalidDirectionError(direction);\n    }\n    this._direction = direction;\n  }\n  /**\n   * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n   * May be overridden by the MatSortable's disable clear input.\n   */\n  get disableClear() {\n    return this._disableClear;\n  }\n  set disableClear(v) {\n    this._disableClear = coerceBooleanProperty(v);\n  }\n  constructor(_defaultOptions) {\n    super();\n    this._defaultOptions = _defaultOptions;\n    /** Collection of all registered sortables that this directive manages. */\n    this.sortables = new Map();\n    /** Used to notify any child components listening to state changes. */\n    this._stateChanges = new Subject();\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    this.start = 'asc';\n    this._direction = '';\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    this.sortChange = new EventEmitter();\n  }\n  /**\n   * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n   * collection of MatSortables.\n   */\n  register(sortable) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!sortable.id) {\n        throw getSortHeaderMissingIdError();\n      }\n      if (this.sortables.has(sortable.id)) {\n        throw getSortDuplicateSortableIdError(sortable.id);\n      }\n    }\n    this.sortables.set(sortable.id, sortable);\n  }\n  /**\n   * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n   * collection of contained MatSortables.\n   */\n  deregister(sortable) {\n    this.sortables.delete(sortable.id);\n  }\n  /** Sets the active sort id and determines the new sort direction. */\n  sort(sortable) {\n    if (this.active != sortable.id) {\n      this.active = sortable.id;\n      this.direction = sortable.start ? sortable.start : this.start;\n    } else {\n      this.direction = this.getNextSortDirection(sortable);\n    }\n    this.sortChange.emit({\n      active: this.active,\n      direction: this.direction\n    });\n  }\n  /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n  getNextSortDirection(sortable) {\n    if (!sortable) {\n      return '';\n    }\n    // Get the sort direction cycle with the potential sortable overrides.\n    const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n    let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n    // Get and return the next direction in the cycle\n    let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n    if (nextDirectionIndex >= sortDirectionCycle.length) {\n      nextDirectionIndex = 0;\n    }\n    return sortDirectionCycle[nextDirectionIndex];\n  }\n  ngOnInit() {\n    this._markInitialized();\n  }\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n}\nMatSort.ɵfac = function MatSort_Factory(t) {\n  return new (t || MatSort)(i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n};\nMatSort.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatSort,\n  selectors: [[\"\", \"matSort\", \"\"]],\n  hostAttrs: [1, \"mat-sort\"],\n  inputs: {\n    disabled: [\"matSortDisabled\", \"disabled\"],\n    active: [\"matSortActive\", \"active\"],\n    start: [\"matSortStart\", \"start\"],\n    direction: [\"matSortDirection\", \"direction\"],\n    disableClear: [\"matSortDisableClear\", \"disableClear\"]\n  },\n  outputs: {\n    sortChange: \"matSortChange\"\n  },\n  exportAs: [\"matSort\"],\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSort, [{\n    type: Directive,\n    args: [{\n      selector: '[matSort]',\n      exportAs: 'matSort',\n      host: {\n        'class': 'mat-sort'\n      },\n      inputs: ['disabled: matSortDisabled']\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_SORT_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    active: [{\n      type: Input,\n      args: ['matSortActive']\n    }],\n    start: [{\n      type: Input,\n      args: ['matSortStart']\n    }],\n    direction: [{\n      type: Input,\n      args: ['matSortDirection']\n    }],\n    disableClear: [{\n      type: Input,\n      args: ['matSortDisableClear']\n    }],\n    sortChange: [{\n      type: Output,\n      args: ['matSortChange']\n    }]\n  });\n})();\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n  let sortOrder = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n  return sortOrder;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst SORT_ANIMATION_TRANSITION = AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nconst matSortAnimations = {\n  /** Animation that moves the sort indicator. */\n  indicator: trigger('indicator', [state('active-asc, asc', style({\n    transform: 'translateY(0px)'\n  })),\n  // 10px is the height of the sort indicator, minus the width of the pointers\n  state('active-desc, desc', style({\n    transform: 'translateY(10px)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: trigger('leftPointer', [state('active-asc, asc', style({\n    transform: 'rotate(-45deg)'\n  })), state('active-desc, desc', style({\n    transform: 'rotate(45deg)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: trigger('rightPointer', [state('active-asc, asc', style({\n    transform: 'rotate(45deg)'\n  })), state('active-desc, desc', style({\n    transform: 'rotate(-45deg)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: trigger('arrowOpacity', [state('desc-to-active, asc-to-active, active', style({\n    opacity: 1\n  })), state('desc-to-hint, asc-to-hint, hint', style({\n    opacity: 0.54\n  })), state('hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void', style({\n    opacity: 0\n  })),\n  // Transition between all states except for immediate transitions\n  transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')), transition('* <=> *', animate(SORT_ANIMATION_TRANSITION))]),\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: trigger('arrowPosition', [\n  // Hidden Above => Hint Center\n  transition('* => desc-to-hint, * => desc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(-25%)'\n  }), style({\n    transform: 'translateY(0)'\n  })]))),\n  // Hint Center => Hidden Below\n  transition('* => hint-to-desc, * => active-to-desc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(0)'\n  }), style({\n    transform: 'translateY(25%)'\n  })]))),\n  // Hidden Below => Hint Center\n  transition('* => asc-to-hint, * => asc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(25%)'\n  }), style({\n    transform: 'translateY(0)'\n  })]))),\n  // Hint Center => Hidden Above\n  transition('* => hint-to-asc, * => active-to-asc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(0)'\n  }), style({\n    transform: 'translateY(-25%)'\n  })]))), state('desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active', style({\n    transform: 'translateY(0)'\n  })), state('hint-to-desc, active-to-desc, desc', style({\n    transform: 'translateY(-25%)'\n  })), state('hint-to-asc, active-to-asc, asc', style({\n    transform: 'translateY(25%)'\n  }))]),\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: trigger('allowChildren', [transition('* <=> *', [query('@*', animateChild(), {\n    optional: true\n  })])])\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n  constructor() {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    this.changes = new Subject();\n  }\n}\nMatSortHeaderIntl.ɵfac = function MatSortHeaderIntl_Factory(t) {\n  return new (t || MatSortHeaderIntl)();\n};\nMatSortHeaderIntl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MatSortHeaderIntl,\n  factory: MatSortHeaderIntl.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeaderIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n/** @docs-private */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to the sort header.\n/** @docs-private */\nconst _MatSortHeaderBase = mixinDisabled(class {});\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader extends _MatSortHeaderBase {\n  /**\n   * Description applied to MatSortHeader's button element with aria-describedby. This text should\n   * describe the action that will occur when the user clicks the sort header.\n   */\n  get sortActionDescription() {\n    return this._sortActionDescription;\n  }\n  set sortActionDescription(value) {\n    this._updateSortActionDescription(value);\n  }\n  /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n  get disableClear() {\n    return this._disableClear;\n  }\n  set disableClear(v) {\n    this._disableClear = coerceBooleanProperty(v);\n  }\n  constructor(\n  /**\n   * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n   * @breaking-change 13.0.0\n   */\n  _intl, _changeDetectorRef,\n  // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n  // tslint:disable-next-line: lightweight-tokens\n  _sort, _columnDef, _focusMonitor, _elementRef, /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n  _ariaDescriber, defaultOptions) {\n    // Note that we use a string token for the `_columnDef`, because the value is provided both by\n    // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n    // and we want to avoid having the sort header depending on the CDK table because\n    // of this single reference.\n    super();\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._sort = _sort;\n    this._columnDef = _columnDef;\n    this._focusMonitor = _focusMonitor;\n    this._elementRef = _elementRef;\n    this._ariaDescriber = _ariaDescriber;\n    /**\n     * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n     * provide an affordance that the header is sortable by showing on focus and hover.\n     */\n    this._showIndicatorHint = false;\n    /**\n     * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n     * position through the animation. If animations are currently disabled, the fromState is removed\n     * so that there is no animation displayed.\n     */\n    this._viewState = {};\n    /** The direction the arrow should be facing according to the current state. */\n    this._arrowDirection = '';\n    /**\n     * Whether the view state animation should show the transition between the `from` and `to` states.\n     */\n    this._disableViewStateAnimation = false;\n    /** Sets the position of the arrow that displays when sorted. */\n    this.arrowPosition = 'after';\n    // Default the action description to \"Sort\" because it's better than nothing.\n    // Without a description, the button's label comes from the sort header text content,\n    // which doesn't give any indication that it performs a sorting operation.\n    this._sortActionDescription = 'Sort';\n    if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortHeaderNotContainedWithinSortError();\n    }\n    if (defaultOptions?.arrowPosition) {\n      this.arrowPosition = defaultOptions?.arrowPosition;\n    }\n    this._handleStateChanges();\n  }\n  ngOnInit() {\n    if (!this.id && this._columnDef) {\n      this.id = this._columnDef.name;\n    }\n    // Initialize the direction of the arrow and set the view state to be immediately that state.\n    this._updateArrowDirection();\n    this._setAnimationTransitionState({\n      toState: this._isSorted() ? 'active' : this._arrowDirection\n    });\n    this._sort.register(this);\n    this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n    this._updateSortActionDescription(this._sortActionDescription);\n  }\n  ngAfterViewInit() {\n    // We use the focus monitor because we also want to style\n    // things differently based on the focus origin.\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n      const newState = !!origin;\n      if (newState !== this._showIndicatorHint) {\n        this._setIndicatorHintVisible(newState);\n        this._changeDetectorRef.markForCheck();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._sort.deregister(this);\n    this._rerenderSubscription.unsubscribe();\n  }\n  /**\n   * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n   * user showing what the active sort will become. If set to false, the arrow will fade away.\n   */\n  _setIndicatorHintVisible(visible) {\n    // No-op if the sort header is disabled - should not make the hint visible.\n    if (this._isDisabled() && visible) {\n      return;\n    }\n    this._showIndicatorHint = visible;\n    if (!this._isSorted()) {\n      this._updateArrowDirection();\n      if (this._showIndicatorHint) {\n        this._setAnimationTransitionState({\n          fromState: this._arrowDirection,\n          toState: 'hint'\n        });\n      } else {\n        this._setAnimationTransitionState({\n          fromState: 'hint',\n          toState: this._arrowDirection\n        });\n      }\n    }\n  }\n  /**\n   * Sets the animation transition view state for the arrow's position and opacity. If the\n   * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n   * no animation appears.\n   */\n  _setAnimationTransitionState(viewState) {\n    this._viewState = viewState || {};\n    // If the animation for arrow position state (opacity/translation) should be disabled,\n    // remove the fromState so that it jumps right to the toState.\n    if (this._disableViewStateAnimation) {\n      this._viewState = {\n        toState: viewState.toState\n      };\n    }\n  }\n  /** Triggers the sort on this sort header and removes the indicator hint. */\n  _toggleOnInteraction() {\n    this._sort.sort(this);\n    // Do not show the animation if the header was already shown in the right position.\n    if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n      this._disableViewStateAnimation = true;\n    }\n  }\n  _handleClick() {\n    if (!this._isDisabled()) {\n      this._sort.sort(this);\n    }\n  }\n  _handleKeydown(event) {\n    if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n      event.preventDefault();\n      this._toggleOnInteraction();\n    }\n  }\n  /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n  _isSorted() {\n    return this._sort.active == this.id && (this._sort.direction === 'asc' || this._sort.direction === 'desc');\n  }\n  /** Returns the animation state for the arrow direction (indicator and pointers). */\n  _getArrowDirectionState() {\n    return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n  }\n  /** Returns the arrow position state (opacity, translation). */\n  _getArrowViewState() {\n    const fromState = this._viewState.fromState;\n    return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n  }\n  /**\n   * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n   * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n   * active sorted direction. The reason this is updated through a function is because the direction\n   * should only be changed at specific times - when deactivated but the hint is displayed and when\n   * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n   * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n   * preserving its direction, even though the next sort direction is actually different and should\n   * only be changed once the arrow displays again (hint or activation).\n   */\n  _updateArrowDirection() {\n    this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n  }\n  _isDisabled() {\n    return this._sort.disabled || this.disabled;\n  }\n  /**\n   * Gets the aria-sort attribute that should be applied to this sort header. If this header\n   * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n   * says that the aria-sort property should only be present on one header at a time, so removing\n   * ensures this is true.\n   */\n  _getAriaSortAttribute() {\n    if (!this._isSorted()) {\n      return 'none';\n    }\n    return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n  }\n  /** Whether the arrow inside the sort header should be rendered. */\n  _renderArrow() {\n    return !this._isDisabled() || this._isSorted();\n  }\n  _updateSortActionDescription(newDescription) {\n    // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n    // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n    // for every *cell* in the table, creating a lot of unnecessary noise.\n    // If _sortButton is undefined, the component hasn't been initialized yet so there's\n    // nothing to update in the DOM.\n    if (this._sortButton) {\n      // removeDescription will no-op if there is no existing message.\n      // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      this._ariaDescriber?.describe(this._sortButton, newDescription);\n    }\n    this._sortActionDescription = newDescription;\n  }\n  /** Handles changes in the sorting state. */\n  _handleStateChanges() {\n    this._rerenderSubscription = merge(this._sort.sortChange, this._sort._stateChanges, this._intl.changes).subscribe(() => {\n      if (this._isSorted()) {\n        this._updateArrowDirection();\n        // Do not show the animation if the header was already shown in the right position.\n        if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n          this._disableViewStateAnimation = true;\n        }\n        this._setAnimationTransitionState({\n          fromState: this._arrowDirection,\n          toState: 'active'\n        });\n        this._showIndicatorHint = false;\n      }\n      // If this header was recently active and now no longer sorted, animate away the arrow.\n      if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n        this._disableViewStateAnimation = false;\n        this._setAnimationTransitionState({\n          fromState: 'active',\n          toState: this._arrowDirection\n        });\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n}\nMatSortHeader.ɵfac = function MatSortHeader_Factory(t) {\n  return new (t || MatSortHeader)(i0.ɵɵdirectiveInject(MatSortHeaderIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MatSort, 8), i0.ɵɵdirectiveInject('MAT_SORT_HEADER_COLUMN_DEF', 8), i0.ɵɵdirectiveInject(i3.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.AriaDescriber, 8), i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n};\nMatSortHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSortHeader,\n  selectors: [[\"\", \"mat-sort-header\", \"\"]],\n  hostAttrs: [1, \"mat-sort-header\"],\n  hostVars: 3,\n  hostBindings: function MatSortHeader_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatSortHeader_click_HostBindingHandler() {\n        return ctx._handleClick();\n      })(\"keydown\", function MatSortHeader_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"mouseenter\", function MatSortHeader_mouseenter_HostBindingHandler() {\n        return ctx._setIndicatorHintVisible(true);\n      })(\"mouseleave\", function MatSortHeader_mouseleave_HostBindingHandler() {\n        return ctx._setIndicatorHintVisible(false);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-sort\", ctx._getAriaSortAttribute());\n      i0.ɵɵclassProp(\"mat-sort-header-disabled\", ctx._isDisabled());\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    id: [\"mat-sort-header\", \"id\"],\n    arrowPosition: \"arrowPosition\",\n    start: \"start\",\n    sortActionDescription: \"sortActionDescription\",\n    disableClear: \"disableClear\"\n  },\n  exportAs: [\"matSortHeader\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c0,\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 7,\n  consts: [[1, \"mat-sort-header-container\", \"mat-focus-indicator\"], [1, \"mat-sort-header-content\"], [\"class\", \"mat-sort-header-arrow\", 4, \"ngIf\"], [1, \"mat-sort-header-arrow\"], [1, \"mat-sort-header-stem\"], [1, \"mat-sort-header-indicator\"], [1, \"mat-sort-header-pointer-left\"], [1, \"mat-sort-header-pointer-right\"], [1, \"mat-sort-header-pointer-middle\"]],\n  template: function MatSortHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(3, MatSortHeader_div_3_Template, 6, 6, \"div\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-sort-header-sorted\", ctx._isSorted())(\"mat-sort-header-position-before\", ctx.arrowPosition === \"before\");\n      i0.ɵɵattribute(\"tabindex\", ctx._isDisabled() ? null : 0)(\"role\", ctx._isDisabled() ? null : \"button\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx._renderArrow());\n    }\n  },\n  dependencies: [i4.NgIf],\n  styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeader, [{\n    type: Component,\n    args: [{\n      selector: '[mat-sort-header]',\n      exportAs: 'matSortHeader',\n      host: {\n        'class': 'mat-sort-header',\n        '(click)': '_handleClick()',\n        '(keydown)': '_handleKeydown($event)',\n        '(mouseenter)': '_setIndicatorHintVisible(true)',\n        '(mouseleave)': '_setIndicatorHintVisible(false)',\n        '[attr.aria-sort]': '_getAriaSortAttribute()',\n        '[class.mat-sort-header-disabled]': '_isDisabled()'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['disabled'],\n      animations: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren],\n      template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  <div class=\\\"mat-sort-header-arrow\\\"\\n       *ngIf=\\\"_renderArrow()\\\"\\n       [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n       [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n       [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n       (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n       (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n    <div class=\\\"mat-sort-header-stem\\\"></div>\\n    <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n      <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatSortHeaderIntl\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatSort,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: ['MAT_SORT_HEADER_COLUMN_DEF']\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: i3.FocusMonitor\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.AriaDescriber,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_SORT_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    id: [{\n      type: Input,\n      args: ['mat-sort-header']\n    }],\n    arrowPosition: [{\n      type: Input\n    }],\n    start: [{\n      type: Input\n    }],\n    sortActionDescription: [{\n      type: Input\n    }],\n    disableClear: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSortModule {}\nMatSortModule.ɵfac = function MatSortModule_Factory(t) {\n  return new (t || MatSortModule)();\n};\nMatSortModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatSortModule\n});\nMatSortModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n  imports: [CommonModule, MatCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule],\n      exports: [MatSort, MatSortHeader],\n      declarations: [MatSort, MatSortHeader],\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "EventEmitter", "Directive", "Optional", "Inject", "Input", "Output", "Injectable", "SkipSelf", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgModule", "i3", "coerceBooleanProperty", "SPACE", "ENTER", "mixinInitialized", "mixinDisabled", "AnimationDurations", "AnimationCurves", "MatCommonModule", "Subject", "merge", "trigger", "state", "style", "transition", "animate", "keyframes", "query", "animate<PERSON><PERSON><PERSON>", "i4", "CommonModule", "getSortDuplicateSortableIdError", "id", "Error", "getSortHeaderNotContainedWithinSortError", "getSortHeaderMissingIdError", "getSortInvalidDirectionError", "direction", "MAT_SORT_DEFAULT_OPTIONS", "_MatSortBase", "MatSort", "_direction", "ngDevMode", "disableClear", "_disableClear", "v", "constructor", "_defaultOptions", "sortables", "Map", "_stateChanges", "start", "sortChange", "register", "sortable", "has", "set", "deregister", "delete", "sort", "active", "getNextSortDirection", "emit", "sortDirectionCycle", "getSortDirectionCycle", "nextDirectionIndex", "indexOf", "length", "ngOnInit", "_markInitialized", "ngOnChanges", "next", "ngOnDestroy", "complete", "ɵfac", "ɵdir", "type", "args", "selector", "exportAs", "host", "inputs", "undefined", "decorators", "sortOrder", "reverse", "push", "SORT_ANIMATION_TRANSITION", "ENTERING", "STANDARD_CURVE", "matSortAnimations", "indicator", "transform", "leftPointer", "rightPointer", "arrowOpacity", "opacity", "arrowPosition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional", "MatSortHeaderIntl", "changes", "ɵprov", "providedIn", "MAT_SORT_HEADER_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_SORT_HEADER_INTL_PROVIDER", "provide", "deps", "useFactory", "_MatSortHeaderBase", "Mat<PERSON>ort<PERSON><PERSON>er", "sortActionDescription", "_sortActionDescription", "value", "_updateSortActionDescription", "_intl", "_changeDetectorRef", "_sort", "_columnDef", "_focusMonitor", "_elementRef", "_ariaDescriber", "defaultOptions", "_showIndicatorHint", "_viewState", "_arrowDirection", "_disableViewStateAnimation", "_handleStateChanges", "name", "_updateArrowDirection", "_setAnimationTransitionState", "toState", "_isSorted", "_sortButton", "nativeElement", "querySelector", "ngAfterViewInit", "monitor", "subscribe", "origin", "newState", "_setIndicatorHintVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stopMonitoring", "_rerenderSubscription", "unsubscribe", "visible", "_isDisabled", "fromState", "viewState", "_toggleOnInteraction", "_handleClick", "_handleKeydown", "event", "keyCode", "preventDefault", "_getArrowDirectionState", "_getArrowViewState", "disabled", "_getAriaSortAttribute", "_renderArrow", "newDescription", "removeDescription", "describe", "ChangeDetectorRef", "FocusMonitor", "ElementRef", "AriaDescriber", "ɵcmp", "NgIf", "encapsulation", "None", "changeDetection", "OnPush", "animations", "template", "styles", "MatSortModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "providers"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/@angular/material/fesm2020/sort.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i3 from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { mixinInitialized, mixinDisabled, AnimationDurations, AnimationCurves, MatCommonModule } from '@angular/material/core';\nimport { Subject, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes, query, animateChild } from '@angular/animations';\nimport * as i4 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nfunction getSortDuplicateSortableIdError(id) {\n    return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n    return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n    return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n    return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to MatSort.\n/** @docs-private */\nconst _MatSortBase = mixinInitialized(mixinDisabled(class {\n}));\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort extends _MatSortBase {\n    /** The sort direction of the currently active MatSortable. */\n    get direction() {\n        return this._direction;\n    }\n    set direction(direction) {\n        if (direction &&\n            direction !== 'asc' &&\n            direction !== 'desc' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortInvalidDirectionError(direction);\n        }\n        this._direction = direction;\n    }\n    /**\n     * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n     * May be overridden by the MatSortable's disable clear input.\n     */\n    get disableClear() {\n        return this._disableClear;\n    }\n    set disableClear(v) {\n        this._disableClear = coerceBooleanProperty(v);\n    }\n    constructor(_defaultOptions) {\n        super();\n        this._defaultOptions = _defaultOptions;\n        /** Collection of all registered sortables that this directive manages. */\n        this.sortables = new Map();\n        /** Used to notify any child components listening to state changes. */\n        this._stateChanges = new Subject();\n        /**\n         * The direction to set when an MatSortable is initially sorted.\n         * May be overridden by the MatSortable's sort start.\n         */\n        this.start = 'asc';\n        this._direction = '';\n        /** Event emitted when the user changes either the active sort or sort direction. */\n        this.sortChange = new EventEmitter();\n    }\n    /**\n     * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n     * collection of MatSortables.\n     */\n    register(sortable) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!sortable.id) {\n                throw getSortHeaderMissingIdError();\n            }\n            if (this.sortables.has(sortable.id)) {\n                throw getSortDuplicateSortableIdError(sortable.id);\n            }\n        }\n        this.sortables.set(sortable.id, sortable);\n    }\n    /**\n     * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n     * collection of contained MatSortables.\n     */\n    deregister(sortable) {\n        this.sortables.delete(sortable.id);\n    }\n    /** Sets the active sort id and determines the new sort direction. */\n    sort(sortable) {\n        if (this.active != sortable.id) {\n            this.active = sortable.id;\n            this.direction = sortable.start ? sortable.start : this.start;\n        }\n        else {\n            this.direction = this.getNextSortDirection(sortable);\n        }\n        this.sortChange.emit({ active: this.active, direction: this.direction });\n    }\n    /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n    getNextSortDirection(sortable) {\n        if (!sortable) {\n            return '';\n        }\n        // Get the sort direction cycle with the potential sortable overrides.\n        const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n        let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n        // Get and return the next direction in the cycle\n        let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n        if (nextDirectionIndex >= sortDirectionCycle.length) {\n            nextDirectionIndex = 0;\n        }\n        return sortDirectionCycle[nextDirectionIndex];\n    }\n    ngOnInit() {\n        this._markInitialized();\n    }\n    ngOnChanges() {\n        this._stateChanges.next();\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n}\nMatSort.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSort, deps: [{ token: MAT_SORT_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatSort.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSort, selector: \"[matSort]\", inputs: { disabled: [\"matSortDisabled\", \"disabled\"], active: [\"matSortActive\", \"active\"], start: [\"matSortStart\", \"start\"], direction: [\"matSortDirection\", \"direction\"], disableClear: [\"matSortDisableClear\", \"disableClear\"] }, outputs: { sortChange: \"matSortChange\" }, host: { classAttribute: \"mat-sort\" }, exportAs: [\"matSort\"], usesInheritance: true, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSort, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSort]',\n                    exportAs: 'matSort',\n                    host: { 'class': 'mat-sort' },\n                    inputs: ['disabled: matSortDisabled'],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SORT_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { active: [{\n                type: Input,\n                args: ['matSortActive']\n            }], start: [{\n                type: Input,\n                args: ['matSortStart']\n            }], direction: [{\n                type: Input,\n                args: ['matSortDirection']\n            }], disableClear: [{\n                type: Input,\n                args: ['matSortDisableClear']\n            }], sortChange: [{\n                type: Output,\n                args: ['matSortChange']\n            }] } });\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n    let sortOrder = ['asc', 'desc'];\n    if (start == 'desc') {\n        sortOrder.reverse();\n    }\n    if (!disableClear) {\n        sortOrder.push('');\n    }\n    return sortOrder;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst SORT_ANIMATION_TRANSITION = AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nconst matSortAnimations = {\n    /** Animation that moves the sort indicator. */\n    indicator: trigger('indicator', [\n        state('active-asc, asc', style({ transform: 'translateY(0px)' })),\n        // 10px is the height of the sort indicator, minus the width of the pointers\n        state('active-desc, desc', style({ transform: 'translateY(10px)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n    leftPointer: trigger('leftPointer', [\n        state('active-asc, asc', style({ transform: 'rotate(-45deg)' })),\n        state('active-desc, desc', style({ transform: 'rotate(45deg)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n    rightPointer: trigger('rightPointer', [\n        state('active-asc, asc', style({ transform: 'rotate(45deg)' })),\n        state('active-desc, desc', style({ transform: 'rotate(-45deg)' })),\n        transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /** Animation that controls the arrow opacity. */\n    arrowOpacity: trigger('arrowOpacity', [\n        state('desc-to-active, asc-to-active, active', style({ opacity: 1 })),\n        state('desc-to-hint, asc-to-hint, hint', style({ opacity: 0.54 })),\n        state('hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void', style({ opacity: 0 })),\n        // Transition between all states except for immediate transitions\n        transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n        transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n    ]),\n    /**\n     * Animation for the translation of the arrow as a whole. States are separated into two\n     * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n     * peek, and active. The other states define a specific animation (source-to-destination)\n     * and are determined as a function of their prev user-perceived state and what the next state\n     * should be.\n     */\n    arrowPosition: trigger('arrowPosition', [\n        // Hidden Above => Hint Center\n        transition('* => desc-to-hint, * => desc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(-25%)' }), style({ transform: 'translateY(0)' })]))),\n        // Hint Center => Hidden Below\n        transition('* => hint-to-desc, * => active-to-desc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(0)' }), style({ transform: 'translateY(25%)' })]))),\n        // Hidden Below => Hint Center\n        transition('* => asc-to-hint, * => asc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(25%)' }), style({ transform: 'translateY(0)' })]))),\n        // Hint Center => Hidden Above\n        transition('* => hint-to-asc, * => active-to-asc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({ transform: 'translateY(0)' }), style({ transform: 'translateY(-25%)' })]))),\n        state('desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active', style({ transform: 'translateY(0)' })),\n        state('hint-to-desc, active-to-desc, desc', style({ transform: 'translateY(-25%)' })),\n        state('hint-to-asc, active-to-asc, asc', style({ transform: 'translateY(25%)' })),\n    ]),\n    /** Necessary trigger that calls animate on children animations. */\n    allowChildren: trigger('allowChildren', [\n        transition('* <=> *', [query('@*', animateChild(), { optional: true })]),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n    constructor() {\n        /**\n         * Stream that emits whenever the labels here are changed. Use this to notify\n         * components if the labels have changed after initialization.\n         */\n        this.changes = new Subject();\n    }\n}\nMatSortHeaderIntl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortHeaderIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nMatSortHeaderIntl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortHeaderIntl, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortHeaderIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** @docs-private */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatSortHeaderIntl();\n}\n/** @docs-private */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n    // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n    provide: MatSortHeaderIntl,\n    deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n    useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY,\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to the sort header.\n/** @docs-private */\nconst _MatSortHeaderBase = mixinDisabled(class {\n});\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader extends _MatSortHeaderBase {\n    /**\n     * Description applied to MatSortHeader's button element with aria-describedby. This text should\n     * describe the action that will occur when the user clicks the sort header.\n     */\n    get sortActionDescription() {\n        return this._sortActionDescription;\n    }\n    set sortActionDescription(value) {\n        this._updateSortActionDescription(value);\n    }\n    /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n    get disableClear() {\n        return this._disableClear;\n    }\n    set disableClear(v) {\n        this._disableClear = coerceBooleanProperty(v);\n    }\n    constructor(\n    /**\n     * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n     * @breaking-change 13.0.0\n     */\n    _intl, _changeDetectorRef, \n    // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n    // tslint:disable-next-line: lightweight-tokens\n    _sort, _columnDef, _focusMonitor, _elementRef, \n    /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n    _ariaDescriber, defaultOptions) {\n        // Note that we use a string token for the `_columnDef`, because the value is provided both by\n        // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n        // and we want to avoid having the sort header depending on the CDK table because\n        // of this single reference.\n        super();\n        this._intl = _intl;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._sort = _sort;\n        this._columnDef = _columnDef;\n        this._focusMonitor = _focusMonitor;\n        this._elementRef = _elementRef;\n        this._ariaDescriber = _ariaDescriber;\n        /**\n         * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n         * provide an affordance that the header is sortable by showing on focus and hover.\n         */\n        this._showIndicatorHint = false;\n        /**\n         * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n         * position through the animation. If animations are currently disabled, the fromState is removed\n         * so that there is no animation displayed.\n         */\n        this._viewState = {};\n        /** The direction the arrow should be facing according to the current state. */\n        this._arrowDirection = '';\n        /**\n         * Whether the view state animation should show the transition between the `from` and `to` states.\n         */\n        this._disableViewStateAnimation = false;\n        /** Sets the position of the arrow that displays when sorted. */\n        this.arrowPosition = 'after';\n        // Default the action description to \"Sort\" because it's better than nothing.\n        // Without a description, the button's label comes from the sort header text content,\n        // which doesn't give any indication that it performs a sorting operation.\n        this._sortActionDescription = 'Sort';\n        if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortHeaderNotContainedWithinSortError();\n        }\n        if (defaultOptions?.arrowPosition) {\n            this.arrowPosition = defaultOptions?.arrowPosition;\n        }\n        this._handleStateChanges();\n    }\n    ngOnInit() {\n        if (!this.id && this._columnDef) {\n            this.id = this._columnDef.name;\n        }\n        // Initialize the direction of the arrow and set the view state to be immediately that state.\n        this._updateArrowDirection();\n        this._setAnimationTransitionState({\n            toState: this._isSorted() ? 'active' : this._arrowDirection,\n        });\n        this._sort.register(this);\n        this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n        this._updateSortActionDescription(this._sortActionDescription);\n    }\n    ngAfterViewInit() {\n        // We use the focus monitor because we also want to style\n        // things differently based on the focus origin.\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n            const newState = !!origin;\n            if (newState !== this._showIndicatorHint) {\n                this._setIndicatorHintVisible(newState);\n                this._changeDetectorRef.markForCheck();\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._sort.deregister(this);\n        this._rerenderSubscription.unsubscribe();\n    }\n    /**\n     * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n     * user showing what the active sort will become. If set to false, the arrow will fade away.\n     */\n    _setIndicatorHintVisible(visible) {\n        // No-op if the sort header is disabled - should not make the hint visible.\n        if (this._isDisabled() && visible) {\n            return;\n        }\n        this._showIndicatorHint = visible;\n        if (!this._isSorted()) {\n            this._updateArrowDirection();\n            if (this._showIndicatorHint) {\n                this._setAnimationTransitionState({ fromState: this._arrowDirection, toState: 'hint' });\n            }\n            else {\n                this._setAnimationTransitionState({ fromState: 'hint', toState: this._arrowDirection });\n            }\n        }\n    }\n    /**\n     * Sets the animation transition view state for the arrow's position and opacity. If the\n     * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n     * no animation appears.\n     */\n    _setAnimationTransitionState(viewState) {\n        this._viewState = viewState || {};\n        // If the animation for arrow position state (opacity/translation) should be disabled,\n        // remove the fromState so that it jumps right to the toState.\n        if (this._disableViewStateAnimation) {\n            this._viewState = { toState: viewState.toState };\n        }\n    }\n    /** Triggers the sort on this sort header and removes the indicator hint. */\n    _toggleOnInteraction() {\n        this._sort.sort(this);\n        // Do not show the animation if the header was already shown in the right position.\n        if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n            this._disableViewStateAnimation = true;\n        }\n    }\n    _handleClick() {\n        if (!this._isDisabled()) {\n            this._sort.sort(this);\n        }\n    }\n    _handleKeydown(event) {\n        if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n            event.preventDefault();\n            this._toggleOnInteraction();\n        }\n    }\n    /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n    _isSorted() {\n        return (this._sort.active == this.id &&\n            (this._sort.direction === 'asc' || this._sort.direction === 'desc'));\n    }\n    /** Returns the animation state for the arrow direction (indicator and pointers). */\n    _getArrowDirectionState() {\n        return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n    }\n    /** Returns the arrow position state (opacity, translation). */\n    _getArrowViewState() {\n        const fromState = this._viewState.fromState;\n        return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n    }\n    /**\n     * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n     * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n     * active sorted direction. The reason this is updated through a function is because the direction\n     * should only be changed at specific times - when deactivated but the hint is displayed and when\n     * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n     * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n     * preserving its direction, even though the next sort direction is actually different and should\n     * only be changed once the arrow displays again (hint or activation).\n     */\n    _updateArrowDirection() {\n        this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n    }\n    _isDisabled() {\n        return this._sort.disabled || this.disabled;\n    }\n    /**\n     * Gets the aria-sort attribute that should be applied to this sort header. If this header\n     * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n     * says that the aria-sort property should only be present on one header at a time, so removing\n     * ensures this is true.\n     */\n    _getAriaSortAttribute() {\n        if (!this._isSorted()) {\n            return 'none';\n        }\n        return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n    }\n    /** Whether the arrow inside the sort header should be rendered. */\n    _renderArrow() {\n        return !this._isDisabled() || this._isSorted();\n    }\n    _updateSortActionDescription(newDescription) {\n        // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n        // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n        // for every *cell* in the table, creating a lot of unnecessary noise.\n        // If _sortButton is undefined, the component hasn't been initialized yet so there's\n        // nothing to update in the DOM.\n        if (this._sortButton) {\n            // removeDescription will no-op if there is no existing message.\n            // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n            this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n            this._ariaDescriber?.describe(this._sortButton, newDescription);\n        }\n        this._sortActionDescription = newDescription;\n    }\n    /** Handles changes in the sorting state. */\n    _handleStateChanges() {\n        this._rerenderSubscription = merge(this._sort.sortChange, this._sort._stateChanges, this._intl.changes).subscribe(() => {\n            if (this._isSorted()) {\n                this._updateArrowDirection();\n                // Do not show the animation if the header was already shown in the right position.\n                if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n                    this._disableViewStateAnimation = true;\n                }\n                this._setAnimationTransitionState({ fromState: this._arrowDirection, toState: 'active' });\n                this._showIndicatorHint = false;\n            }\n            // If this header was recently active and now no longer sorted, animate away the arrow.\n            if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n                this._disableViewStateAnimation = false;\n                this._setAnimationTransitionState({ fromState: 'active', toState: this._arrowDirection });\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n}\nMatSortHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortHeader, deps: [{ token: MatSortHeaderIntl }, { token: i0.ChangeDetectorRef }, { token: MatSort, optional: true }, { token: 'MAT_SORT_HEADER_COLUMN_DEF', optional: true }, { token: i3.FocusMonitor }, { token: i0.ElementRef }, { token: i3.AriaDescriber, optional: true }, { token: MAT_SORT_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatSortHeader.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: MatSortHeader, selector: \"[mat-sort-header]\", inputs: { disabled: \"disabled\", id: [\"mat-sort-header\", \"id\"], arrowPosition: \"arrowPosition\", start: \"start\", sortActionDescription: \"sortActionDescription\", disableClear: \"disableClear\" }, host: { listeners: { \"click\": \"_handleClick()\", \"keydown\": \"_handleKeydown($event)\", \"mouseenter\": \"_setIndicatorHintVisible(true)\", \"mouseleave\": \"_setIndicatorHintVisible(false)\" }, properties: { \"attr.aria-sort\": \"_getAriaSortAttribute()\", \"class.mat-sort-header-disabled\": \"_isDisabled()\" }, classAttribute: \"mat-sort-header\" }, exportAs: [\"matSortHeader\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  <div class=\\\"mat-sort-header-arrow\\\"\\n       *ngIf=\\\"_renderArrow()\\\"\\n       [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n       [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n       [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n       (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n       (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n    <div class=\\\"mat-sort-header-stem\\\"></div>\\n    <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n      <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"], dependencies: [{ kind: \"directive\", type: i4.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], animations: [\n        matSortAnimations.indicator,\n        matSortAnimations.leftPointer,\n        matSortAnimations.rightPointer,\n        matSortAnimations.arrowOpacity,\n        matSortAnimations.arrowPosition,\n        matSortAnimations.allowChildren,\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortHeader, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-sort-header]', exportAs: 'matSortHeader', host: {\n                        'class': 'mat-sort-header',\n                        '(click)': '_handleClick()',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(mouseenter)': '_setIndicatorHintVisible(true)',\n                        '(mouseleave)': '_setIndicatorHintVisible(false)',\n                        '[attr.aria-sort]': '_getAriaSortAttribute()',\n                        '[class.mat-sort-header-disabled]': '_isDisabled()',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['disabled'], animations: [\n                        matSortAnimations.indicator,\n                        matSortAnimations.leftPointer,\n                        matSortAnimations.rightPointer,\n                        matSortAnimations.arrowOpacity,\n                        matSortAnimations.arrowPosition,\n                        matSortAnimations.allowChildren,\n                    ], template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  <div class=\\\"mat-sort-header-arrow\\\"\\n       *ngIf=\\\"_renderArrow()\\\"\\n       [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n       [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n       [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n       (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n       (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n    <div class=\\\"mat-sort-header-stem\\\"></div>\\n    <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n      <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n      <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"] }]\n        }], ctorParameters: function () { return [{ type: MatSortHeaderIntl }, { type: i0.ChangeDetectorRef }, { type: MatSort, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: ['MAT_SORT_HEADER_COLUMN_DEF']\n                }, {\n                    type: Optional\n                }] }, { type: i3.FocusMonitor }, { type: i0.ElementRef }, { type: i3.AriaDescriber, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SORT_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { id: [{\n                type: Input,\n                args: ['mat-sort-header']\n            }], arrowPosition: [{\n                type: Input\n            }], start: [{\n                type: Input\n            }], sortActionDescription: [{\n                type: Input\n            }], disableClear: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSortModule {\n}\nMatSortModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatSortModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortModule, declarations: [MatSort, MatSortHeader], imports: [CommonModule, MatCommonModule], exports: [MatSort, MatSortHeader] });\nMatSortModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortModule, providers: [MAT_SORT_HEADER_INTL_PROVIDER], imports: [CommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: MatSortModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule],\n                    exports: [MatSort, MatSortHeader],\n                    declarations: [MatSort, MatSortHeader],\n                    providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,QAAQ,eAAe;AAC/L,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC9H,SAASC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACrC,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,QAAQ,qBAAqB;AAChH,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;EAAA;IAAA,YAiI+FlC,EAAE;IAAFA,EAAE,4BAmZs+E;IAnZx+EA,EAAE;MAAFA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,iDAmZ05E,IAAI;IAAA,EAAE;MAnZl6EA,EAAE;MAAA,eAAFA,EAAE;MAAA,OAAFA,EAAE,iDAmZ89E,KAAK;IAAA,EAAnE;IAnZl6EA,EAAE,uBAmZshF;IAnZxhFA,EAAE,4BAmZgnF;IAnZlnFA,EAAE,uBAmZutF;IAnZztFA,EAAE,eAmZw4F;EAAA;EAAA;IAAA,eAnZ14FA,EAAE;IAAFA,EAAE,yDAmZkvE;IAnZpvEA,EAAE,aAmZ+mF;IAnZjnFA,EAAE,2DAmZ+mF;IAnZjnFA,EAAE,aAmZgtF;IAnZltFA,EAAE,6DAmZgtF;IAnZltFA,EAAE,aAmZyzF;IAnZ3zFA,EAAE,8DAmZyzF;EAAA;AAAA;AAAA;AAnhB15F,SAASmC,+BAA+B,CAACC,EAAE,EAAE;EACzC,OAAOC,KAAK,CAAE,kDAAiDD,EAAG,IAAG,CAAC;AAC1E;AACA;AACA,SAASE,wCAAwC,GAAG;EAChD,OAAOD,KAAK,CAAE,kFAAiF,CAAC;AACpG;AACA;AACA,SAASE,2BAA2B,GAAG;EACnC,OAAOF,KAAK,CAAE,kDAAiD,CAAC;AACpE;AACA;AACA,SAASG,4BAA4B,CAACC,SAAS,EAAE;EAC7C,OAAOJ,KAAK,CAAE,GAAEI,SAAU,mDAAkD,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,IAAIzC,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA;AACA,MAAM0C,YAAY,GAAGzB,gBAAgB,CAACC,aAAa,CAAC,MAAM,EACzD,CAAC,CAAC;AACH;AACA,MAAMyB,OAAO,SAASD,YAAY,CAAC;EAC/B;EACA,IAAIF,SAAS,GAAG;IACZ,OAAO,IAAI,CAACI,UAAU;EAC1B;EACA,IAAIJ,SAAS,CAACA,SAAS,EAAE;IACrB,IAAIA,SAAS,IACTA,SAAS,KAAK,KAAK,IACnBA,SAAS,KAAK,MAAM,KACnB,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMN,4BAA4B,CAACC,SAAS,CAAC;IACjD;IACA,IAAI,CAACI,UAAU,GAAGJ,SAAS;EAC/B;EACA;AACJ;AACA;AACA;EACI,IAAIM,YAAY,GAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAY,CAACE,CAAC,EAAE;IAChB,IAAI,CAACD,aAAa,GAAGjC,qBAAqB,CAACkC,CAAC,CAAC;EACjD;EACAC,WAAW,CAACC,eAAe,EAAE;IACzB,KAAK,EAAE;IACP,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;IACA,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,EAAE;IAC1B;IACA,IAAI,CAACC,aAAa,GAAG,IAAI/B,OAAO,EAAE;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACgC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACV,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACW,UAAU,GAAG,IAAItD,YAAY,EAAE;EACxC;EACA;AACJ;AACA;AACA;EACIuD,QAAQ,CAACC,QAAQ,EAAE;IACf,IAAI,OAAOZ,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAACY,QAAQ,CAACtB,EAAE,EAAE;QACd,MAAMG,2BAA2B,EAAE;MACvC;MACA,IAAI,IAAI,CAACa,SAAS,CAACO,GAAG,CAACD,QAAQ,CAACtB,EAAE,CAAC,EAAE;QACjC,MAAMD,+BAA+B,CAACuB,QAAQ,CAACtB,EAAE,CAAC;MACtD;IACJ;IACA,IAAI,CAACgB,SAAS,CAACQ,GAAG,CAACF,QAAQ,CAACtB,EAAE,EAAEsB,QAAQ,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIG,UAAU,CAACH,QAAQ,EAAE;IACjB,IAAI,CAACN,SAAS,CAACU,MAAM,CAACJ,QAAQ,CAACtB,EAAE,CAAC;EACtC;EACA;EACA2B,IAAI,CAACL,QAAQ,EAAE;IACX,IAAI,IAAI,CAACM,MAAM,IAAIN,QAAQ,CAACtB,EAAE,EAAE;MAC5B,IAAI,CAAC4B,MAAM,GAAGN,QAAQ,CAACtB,EAAE;MACzB,IAAI,CAACK,SAAS,GAAGiB,QAAQ,CAACH,KAAK,GAAGG,QAAQ,CAACH,KAAK,GAAG,IAAI,CAACA,KAAK;IACjE,CAAC,MACI;MACD,IAAI,CAACd,SAAS,GAAG,IAAI,CAACwB,oBAAoB,CAACP,QAAQ,CAAC;IACxD;IACA,IAAI,CAACF,UAAU,CAACU,IAAI,CAAC;MAAEF,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEvB,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,CAAC;EAC5E;EACA;EACAwB,oBAAoB,CAACP,QAAQ,EAAE;IAC3B,IAAI,CAACA,QAAQ,EAAE;MACX,OAAO,EAAE;IACb;IACA;IACA,MAAMX,YAAY,GAAGW,QAAQ,EAAEX,YAAY,IAAI,IAAI,CAACA,YAAY,IAAI,CAAC,CAAC,IAAI,CAACI,eAAe,EAAEJ,YAAY;IACxG,IAAIoB,kBAAkB,GAAGC,qBAAqB,CAACV,QAAQ,CAACH,KAAK,IAAI,IAAI,CAACA,KAAK,EAAER,YAAY,CAAC;IAC1F;IACA,IAAIsB,kBAAkB,GAAGF,kBAAkB,CAACG,OAAO,CAAC,IAAI,CAAC7B,SAAS,CAAC,GAAG,CAAC;IACvE,IAAI4B,kBAAkB,IAAIF,kBAAkB,CAACI,MAAM,EAAE;MACjDF,kBAAkB,GAAG,CAAC;IAC1B;IACA,OAAOF,kBAAkB,CAACE,kBAAkB,CAAC;EACjD;EACAG,QAAQ,GAAG;IACP,IAAI,CAACC,gBAAgB,EAAE;EAC3B;EACAC,WAAW,GAAG;IACV,IAAI,CAACpB,aAAa,CAACqB,IAAI,EAAE;EAC7B;EACAC,WAAW,GAAG;IACV,IAAI,CAACtB,aAAa,CAACuB,QAAQ,EAAE;EACjC;AACJ;AACAjC,OAAO,CAACkC,IAAI;EAAA,iBAA6FlC,OAAO,EAAjB5C,EAAE,mBAAiC0C,wBAAwB;AAAA,CAA4D;AACtNE,OAAO,CAACmC,IAAI,kBADmF/E,EAAE;EAAA,MACJ4C,OAAO;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WADL5C,EAAE,6BAAFA,EAAE;AAAA,EACia;AAClgB;EAAA,mDAF+FA,EAAE,mBAED4C,OAAO,EAAc,CAAC;IAC1GoC,IAAI,EAAE7E,SAAS;IACf8E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBC,QAAQ,EAAE,SAAS;MACnBC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAW,CAAC;MAC7BC,MAAM,EAAE,CAAC,2BAA2B;IACxC,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEL,IAAI,EAAEM,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DP,IAAI,EAAE5E;MACV,CAAC,EAAE;QACC4E,IAAI,EAAE3E,MAAM;QACZ4E,IAAI,EAAE,CAACvC,wBAAwB;MACnC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsB,MAAM,EAAE,CAAC;MACrCgB,IAAI,EAAE1E,KAAK;MACX2E,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE1B,KAAK,EAAE,CAAC;MACRyB,IAAI,EAAE1E,KAAK;MACX2E,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAExC,SAAS,EAAE,CAAC;MACZuC,IAAI,EAAE1E,KAAK;MACX2E,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAElC,YAAY,EAAE,CAAC;MACfiC,IAAI,EAAE1E,KAAK;MACX2E,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEzB,UAAU,EAAE,CAAC;MACbwB,IAAI,EAAEzE,MAAM;MACZ0E,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASb,qBAAqB,CAACb,KAAK,EAAER,YAAY,EAAE;EAChD,IAAIyC,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;EAC/B,IAAIjC,KAAK,IAAI,MAAM,EAAE;IACjBiC,SAAS,CAACC,OAAO,EAAE;EACvB;EACA,IAAI,CAAC1C,YAAY,EAAE;IACfyC,SAAS,CAACE,IAAI,CAAC,EAAE,CAAC;EACtB;EACA,OAAOF,SAAS;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,yBAAyB,GAAGvE,kBAAkB,CAACwE,QAAQ,GAAG,GAAG,GAAGvE,eAAe,CAACwE,cAAc;AACpG;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG;EACtB;EACAC,SAAS,EAAEtE,OAAO,CAAC,WAAW,EAAE,CAC5BC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC;EACjE;EACAtE,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,EACpEpE,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAAC8D,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAM,WAAW,EAAExE,OAAO,CAAC,aAAa,EAAE,CAChCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EAChEtE,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EACjEpE,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAAC8D,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAO,YAAY,EAAEzE,OAAO,CAAC,cAAc,EAAE,CAClCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EAC/DtE,KAAK,CAAC,mBAAmB,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EAClEpE,UAAU,CAAC,4BAA4B,EAAEC,OAAO,CAAC8D,yBAAyB,CAAC,CAAC,CAC/E,CAAC;EACF;EACAQ,YAAY,EAAE1E,OAAO,CAAC,cAAc,EAAE,CAClCC,KAAK,CAAC,uCAAuC,EAAEC,KAAK,CAAC;IAAEyE,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EACrE1E,KAAK,CAAC,iCAAiC,EAAEC,KAAK,CAAC;IAAEyE,OAAO,EAAE;EAAK,CAAC,CAAC,CAAC,EAClE1E,KAAK,CAAC,2EAA2E,EAAEC,KAAK,CAAC;IAAEyE,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC;EACzG;EACAxE,UAAU,CAAC,wDAAwD,EAAEC,OAAO,CAAC,KAAK,CAAC,CAAC,EACpFD,UAAU,CAAC,SAAS,EAAEC,OAAO,CAAC8D,yBAAyB,CAAC,CAAC,CAC5D,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;EACIU,aAAa,EAAE5E,OAAO,CAAC,eAAe,EAAE;EACpC;EACAG,UAAU,CAAC,wCAAwC,EAAEC,OAAO,CAAC8D,yBAAyB,EAAE7D,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAmB,CAAC,CAAC,EAAErE,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtL;EACApE,UAAU,CAAC,wCAAwC,EAAEC,OAAO,CAAC8D,yBAAyB,EAAE7D,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAgB,CAAC,CAAC,EAAErE,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrL;EACApE,UAAU,CAAC,sCAAsC,EAAEC,OAAO,CAAC8D,yBAAyB,EAAE7D,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAkB,CAAC,CAAC,EAAErE,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnL;EACApE,UAAU,CAAC,sCAAsC,EAAEC,OAAO,CAAC8D,yBAAyB,EAAE7D,SAAS,CAAC,CAACH,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAgB,CAAC,CAAC,EAAErE,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpLtE,KAAK,CAAC,wEAAwE,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAgB,CAAC,CAAC,CAAC,EACtHtE,KAAK,CAAC,oCAAoC,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAmB,CAAC,CAAC,CAAC,EACrFtE,KAAK,CAAC,iCAAiC,EAAEC,KAAK,CAAC;IAAEqE,SAAS,EAAE;EAAkB,CAAC,CAAC,CAAC,CACpF,CAAC;EACF;EACAM,aAAa,EAAE7E,OAAO,CAAC,eAAe,EAAE,CACpCG,UAAU,CAAC,SAAS,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEC,YAAY,EAAE,EAAE;IAAEuE,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC,CAAC,CAC3E;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBtD,WAAW,GAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACuD,OAAO,GAAG,IAAIlF,OAAO,EAAE;EAChC;AACJ;AACAiF,iBAAiB,CAAC1B,IAAI;EAAA,iBAA6F0B,iBAAiB;AAAA,CAAoD;AACxLA,iBAAiB,CAACE,KAAK,kBAnIwE1G,EAAE;EAAA,OAmIsBwG,iBAAiB;EAAA,SAAjBA,iBAAiB;EAAA,YAAc;AAAM,EAAG;AAC/J;EAAA,mDApI+FxG,EAAE,mBAoIDwG,iBAAiB,EAAc,CAAC;IACpHxB,IAAI,EAAExE,UAAU;IAChByE,IAAI,EAAE,CAAC;MAAE0B,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAASC,qCAAqC,CAACC,UAAU,EAAE;EACvD,OAAOA,UAAU,IAAI,IAAIL,iBAAiB,EAAE;AAChD;AACA;AACA,MAAMM,6BAA6B,GAAG;EAClC;EACAC,OAAO,EAAEP,iBAAiB;EAC1BQ,IAAI,EAAE,CAAC,CAAC,IAAI5G,QAAQ,EAAE,EAAE,IAAIK,QAAQ,EAAE,EAAE+F,iBAAiB,CAAC,CAAC;EAC3DS,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,kBAAkB,GAAG/F,aAAa,CAAC,MAAM,EAC9C,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgG,aAAa,SAASD,kBAAkB,CAAC;EAC3C;AACJ;AACA;AACA;EACI,IAAIE,qBAAqB,GAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqB,CAACE,KAAK,EAAE;IAC7B,IAAI,CAACC,4BAA4B,CAACD,KAAK,CAAC;EAC5C;EACA;EACA,IAAIvE,YAAY,GAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAY,CAACE,CAAC,EAAE;IAChB,IAAI,CAACD,aAAa,GAAGjC,qBAAqB,CAACkC,CAAC,CAAC;EACjD;EACAC,WAAW;EACX;AACJ;AACA;AACA;EACIsE,KAAK,EAAEC,kBAAkB;EACzB;EACA;EACAC,KAAK,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAC7C;EACAC,cAAc,EAAEC,cAAc,EAAE;IAC5B;IACA;IACA;IACA;IACA,KAAK,EAAE;IACP,IAAI,CAACP,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACE,kBAAkB,GAAG,KAAK;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;AACR;AACA;IACQ,IAAI,CAACC,0BAA0B,GAAG,KAAK;IACvC;IACA,IAAI,CAAC9B,aAAa,GAAG,OAAO;IAC5B;IACA;IACA;IACA,IAAI,CAACgB,sBAAsB,GAAG,MAAM;IACpC,IAAI,CAACK,KAAK,KAAK,OAAO5E,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC3D,MAAMR,wCAAwC,EAAE;IACpD;IACA,IAAIyF,cAAc,EAAE1B,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,GAAG0B,cAAc,EAAE1B,aAAa;IACtD;IACA,IAAI,CAAC+B,mBAAmB,EAAE;EAC9B;EACA5D,QAAQ,GAAG;IACP,IAAI,CAAC,IAAI,CAACpC,EAAE,IAAI,IAAI,CAACuF,UAAU,EAAE;MAC7B,IAAI,CAACvF,EAAE,GAAG,IAAI,CAACuF,UAAU,CAACU,IAAI;IAClC;IACA;IACA,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,4BAA4B,CAAC;MAC9BC,OAAO,EAAE,IAAI,CAACC,SAAS,EAAE,GAAG,QAAQ,GAAG,IAAI,CAACP;IAChD,CAAC,CAAC;IACF,IAAI,CAACR,KAAK,CAACjE,QAAQ,CAAC,IAAI,CAAC;IACzB,IAAI,CAACiF,WAAW,GAAG,IAAI,CAACb,WAAW,CAACc,aAAa,CAACC,aAAa,CAAC,4BAA4B,CAAC;IAC7F,IAAI,CAACrB,4BAA4B,CAAC,IAAI,CAACF,sBAAsB,CAAC;EAClE;EACAwB,eAAe,GAAG;IACd;IACA;IACA,IAAI,CAACjB,aAAa,CAACkB,OAAO,CAAC,IAAI,CAACjB,WAAW,EAAE,IAAI,CAAC,CAACkB,SAAS,CAACC,MAAM,IAAI;MACnE,MAAMC,QAAQ,GAAG,CAAC,CAACD,MAAM;MACzB,IAAIC,QAAQ,KAAK,IAAI,CAACjB,kBAAkB,EAAE;QACtC,IAAI,CAACkB,wBAAwB,CAACD,QAAQ,CAAC;QACvC,IAAI,CAACxB,kBAAkB,CAAC0B,YAAY,EAAE;MAC1C;IACJ,CAAC,CAAC;EACN;EACAvE,WAAW,GAAG;IACV,IAAI,CAACgD,aAAa,CAACwB,cAAc,CAAC,IAAI,CAACvB,WAAW,CAAC;IACnD,IAAI,CAACH,KAAK,CAAC7D,UAAU,CAAC,IAAI,CAAC;IAC3B,IAAI,CAACwF,qBAAqB,CAACC,WAAW,EAAE;EAC5C;EACA;AACJ;AACA;AACA;EACIJ,wBAAwB,CAACK,OAAO,EAAE;IAC9B;IACA,IAAI,IAAI,CAACC,WAAW,EAAE,IAAID,OAAO,EAAE;MAC/B;IACJ;IACA,IAAI,CAACvB,kBAAkB,GAAGuB,OAAO;IACjC,IAAI,CAAC,IAAI,CAACd,SAAS,EAAE,EAAE;MACnB,IAAI,CAACH,qBAAqB,EAAE;MAC5B,IAAI,IAAI,CAACN,kBAAkB,EAAE;QACzB,IAAI,CAACO,4BAA4B,CAAC;UAAEkB,SAAS,EAAE,IAAI,CAACvB,eAAe;UAAEM,OAAO,EAAE;QAAO,CAAC,CAAC;MAC3F,CAAC,MACI;QACD,IAAI,CAACD,4BAA4B,CAAC;UAAEkB,SAAS,EAAE,MAAM;UAAEjB,OAAO,EAAE,IAAI,CAACN;QAAgB,CAAC,CAAC;MAC3F;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIK,4BAA4B,CAACmB,SAAS,EAAE;IACpC,IAAI,CAACzB,UAAU,GAAGyB,SAAS,IAAI,CAAC,CAAC;IACjC;IACA;IACA,IAAI,IAAI,CAACvB,0BAA0B,EAAE;MACjC,IAAI,CAACF,UAAU,GAAG;QAAEO,OAAO,EAAEkB,SAAS,CAAClB;MAAQ,CAAC;IACpD;EACJ;EACA;EACAmB,oBAAoB,GAAG;IACnB,IAAI,CAACjC,KAAK,CAAC3D,IAAI,CAAC,IAAI,CAAC;IACrB;IACA,IAAI,IAAI,CAACkE,UAAU,CAACO,OAAO,KAAK,MAAM,IAAI,IAAI,CAACP,UAAU,CAACO,OAAO,KAAK,QAAQ,EAAE;MAC5E,IAAI,CAACL,0BAA0B,GAAG,IAAI;IAC1C;EACJ;EACAyB,YAAY,GAAG;IACX,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE,EAAE;MACrB,IAAI,CAAC9B,KAAK,CAAC3D,IAAI,CAAC,IAAI,CAAC;IACzB;EACJ;EACA8F,cAAc,CAACC,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACN,WAAW,EAAE,KAAKM,KAAK,CAACC,OAAO,KAAK/I,KAAK,IAAI8I,KAAK,CAACC,OAAO,KAAK9I,KAAK,CAAC,EAAE;MAC7E6I,KAAK,CAACE,cAAc,EAAE;MACtB,IAAI,CAACL,oBAAoB,EAAE;IAC/B;EACJ;EACA;EACAlB,SAAS,GAAG;IACR,OAAQ,IAAI,CAACf,KAAK,CAAC1D,MAAM,IAAI,IAAI,CAAC5B,EAAE,KAC/B,IAAI,CAACsF,KAAK,CAACjF,SAAS,KAAK,KAAK,IAAI,IAAI,CAACiF,KAAK,CAACjF,SAAS,KAAK,MAAM,CAAC;EAC3E;EACA;EACAwH,uBAAuB,GAAG;IACtB,OAAQ,GAAE,IAAI,CAACxB,SAAS,EAAE,GAAG,SAAS,GAAG,EAAG,GAAE,IAAI,CAACP,eAAgB,EAAC;EACxE;EACA;EACAgC,kBAAkB,GAAG;IACjB,MAAMT,SAAS,GAAG,IAAI,CAACxB,UAAU,CAACwB,SAAS;IAC3C,OAAO,CAACA,SAAS,GAAI,GAAEA,SAAU,MAAK,GAAG,EAAE,IAAI,IAAI,CAACxB,UAAU,CAACO,OAAO;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,qBAAqB,GAAG;IACpB,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACO,SAAS,EAAE,GAAG,IAAI,CAACf,KAAK,CAACjF,SAAS,GAAG,IAAI,CAACc,KAAK,IAAI,IAAI,CAACmE,KAAK,CAACnE,KAAK;EACnG;EACAiG,WAAW,GAAG;IACV,OAAO,IAAI,CAAC9B,KAAK,CAACyC,QAAQ,IAAI,IAAI,CAACA,QAAQ;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,qBAAqB,GAAG;IACpB,IAAI,CAAC,IAAI,CAAC3B,SAAS,EAAE,EAAE;MACnB,OAAO,MAAM;IACjB;IACA,OAAO,IAAI,CAACf,KAAK,CAACjF,SAAS,IAAI,KAAK,GAAG,WAAW,GAAG,YAAY;EACrE;EACA;EACA4H,YAAY,GAAG;IACX,OAAO,CAAC,IAAI,CAACb,WAAW,EAAE,IAAI,IAAI,CAACf,SAAS,EAAE;EAClD;EACAlB,4BAA4B,CAAC+C,cAAc,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC5B,WAAW,EAAE;MAClB;MACA;MACA,IAAI,CAACZ,cAAc,EAAEyC,iBAAiB,CAAC,IAAI,CAAC7B,WAAW,EAAE,IAAI,CAACrB,sBAAsB,CAAC;MACrF,IAAI,CAACS,cAAc,EAAE0C,QAAQ,CAAC,IAAI,CAAC9B,WAAW,EAAE4B,cAAc,CAAC;IACnE;IACA,IAAI,CAACjD,sBAAsB,GAAGiD,cAAc;EAChD;EACA;EACAlC,mBAAmB,GAAG;IAClB,IAAI,CAACiB,qBAAqB,GAAG7H,KAAK,CAAC,IAAI,CAACkG,KAAK,CAAClE,UAAU,EAAE,IAAI,CAACkE,KAAK,CAACpE,aAAa,EAAE,IAAI,CAACkE,KAAK,CAACf,OAAO,CAAC,CAACsC,SAAS,CAAC,MAAM;MACpH,IAAI,IAAI,CAACN,SAAS,EAAE,EAAE;QAClB,IAAI,CAACH,qBAAqB,EAAE;QAC5B;QACA,IAAI,IAAI,CAACL,UAAU,CAACO,OAAO,KAAK,MAAM,IAAI,IAAI,CAACP,UAAU,CAACO,OAAO,KAAK,QAAQ,EAAE;UAC5E,IAAI,CAACL,0BAA0B,GAAG,IAAI;QAC1C;QACA,IAAI,CAACI,4BAA4B,CAAC;UAAEkB,SAAS,EAAE,IAAI,CAACvB,eAAe;UAAEM,OAAO,EAAE;QAAS,CAAC,CAAC;QACzF,IAAI,CAACR,kBAAkB,GAAG,KAAK;MACnC;MACA;MACA,IAAI,CAAC,IAAI,CAACS,SAAS,EAAE,IAAI,IAAI,CAACR,UAAU,IAAI,IAAI,CAACA,UAAU,CAACO,OAAO,KAAK,QAAQ,EAAE;QAC9E,IAAI,CAACL,0BAA0B,GAAG,KAAK;QACvC,IAAI,CAACI,4BAA4B,CAAC;UAAEkB,SAAS,EAAE,QAAQ;UAAEjB,OAAO,EAAE,IAAI,CAACN;QAAgB,CAAC,CAAC;MAC7F;MACA,IAAI,CAACT,kBAAkB,CAAC0B,YAAY,EAAE;IAC1C,CAAC,CAAC;EACN;AACJ;AACAhC,aAAa,CAACrC,IAAI;EAAA,iBAA6FqC,aAAa,EAlZ7BnH,EAAE,mBAkZ6CwG,iBAAiB,GAlZhExG,EAAE,mBAkZ2EA,EAAE,CAACyK,iBAAiB,GAlZjGzK,EAAE,mBAkZ4G4C,OAAO,MAlZrH5C,EAAE,mBAkZgJ,4BAA4B,MAlZ9KA,EAAE,mBAkZyMc,EAAE,CAAC4J,YAAY,GAlZ1N1K,EAAE,mBAkZqOA,EAAE,CAAC2K,UAAU,GAlZpP3K,EAAE,mBAkZ+Pc,EAAE,CAAC8J,aAAa,MAlZjR5K,EAAE,mBAkZ4S0C,wBAAwB;AAAA,CAA4D;AACjeyE,aAAa,CAAC0D,IAAI,kBAnZ6E7K,EAAE;EAAA,MAmZEmH,aAAa;EAAA;EAAA;EAAA;EAAA;IAAA;MAnZjBnH,EAAE;QAAA,OAmZE,kBAAc;MAAA;QAAA,OAAd,0BAAsB;MAAA;QAAA,OAAtB,6BAAyB,IAAI,CAAC;MAAA;QAAA,OAA9B,6BAAyB,KAAK,CAAC;MAAA;IAAA;IAAA;MAnZnCA,EAAE;MAAFA,EAAE;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,4BAmZilD;MAnZnlDA,EAAE,gBAmZwiE;MAnZ1iEA,EAAE,eAmZkjE;MAnZpjEA,EAAE,4DAmZk5F;MAnZp5FA,EAAE,eAmZ05F;IAAA;IAAA;MAnZ55FA,EAAE,uDAmZ05C;MAnZ55CA,EAAE,sDAmZ0hD;MAnZ5hDA,EAAE,aAmZ+rE;MAnZjsEA,EAAE,uCAmZ+rE;IAAA;EAAA;EAAA,eAA0lFiC,EAAE,CAAC6I,IAAI;EAAA;EAAA;EAAA;IAAA,WAA+E,CACx8JhF,iBAAiB,CAACC,SAAS,EAC3BD,iBAAiB,CAACG,WAAW,EAC7BH,iBAAiB,CAACI,YAAY,EAC9BJ,iBAAiB,CAACK,YAAY,EAC9BL,iBAAiB,CAACO,aAAa,EAC/BP,iBAAiB,CAACQ,aAAa;EAClC;EAAA;AAAA,EAAiG;AACtG;EAAA,mDA3Z+FtG,EAAE,mBA2ZDmH,aAAa,EAAc,CAAC;IAChHnC,IAAI,EAAEtE,SAAS;IACfuE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEC,QAAQ,EAAE,eAAe;MAAEC,IAAI,EAAE;QAC7D,OAAO,EAAE,iBAAiB;QAC1B,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE,wBAAwB;QACrC,cAAc,EAAE,gCAAgC;QAChD,cAAc,EAAE,iCAAiC;QACjD,kBAAkB,EAAE,yBAAyB;QAC7C,kCAAkC,EAAE;MACxC,CAAC;MAAE2F,aAAa,EAAEpK,iBAAiB,CAACqK,IAAI;MAAEC,eAAe,EAAErK,uBAAuB,CAACsK,MAAM;MAAE7F,MAAM,EAAE,CAAC,UAAU,CAAC;MAAE8F,UAAU,EAAE,CACzHrF,iBAAiB,CAACC,SAAS,EAC3BD,iBAAiB,CAACG,WAAW,EAC7BH,iBAAiB,CAACI,YAAY,EAC9BJ,iBAAiB,CAACK,YAAY,EAC9BL,iBAAiB,CAACO,aAAa,EAC/BP,iBAAiB,CAACQ,aAAa,CAClC;MAAE8E,QAAQ,EAAE,qxEAAqxE;MAAEC,MAAM,EAAE,CAAC,o0DAAo0D;IAAE,CAAC;EAChoI,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErG,IAAI,EAAEwB;IAAkB,CAAC,EAAE;MAAExB,IAAI,EAAEhF,EAAE,CAACyK;IAAkB,CAAC,EAAE;MAAEzF,IAAI,EAAEpC,OAAO;MAAE2C,UAAU,EAAE,CAAC;QACzHP,IAAI,EAAE5E;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4E,IAAI,EAAEM,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCP,IAAI,EAAE3E,MAAM;QACZ4E,IAAI,EAAE,CAAC,4BAA4B;MACvC,CAAC,EAAE;QACCD,IAAI,EAAE5E;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4E,IAAI,EAAElE,EAAE,CAAC4J;IAAa,CAAC,EAAE;MAAE1F,IAAI,EAAEhF,EAAE,CAAC2K;IAAW,CAAC,EAAE;MAAE3F,IAAI,EAAElE,EAAE,CAAC8J,aAAa;MAAErF,UAAU,EAAE,CAAC;QAC7FP,IAAI,EAAE5E;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4E,IAAI,EAAEM,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCP,IAAI,EAAE5E;MACV,CAAC,EAAE;QACC4E,IAAI,EAAE3E,MAAM;QACZ4E,IAAI,EAAE,CAACvC,wBAAwB;MACnC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEN,EAAE,EAAE,CAAC;MACjC4C,IAAI,EAAE1E,KAAK;MACX2E,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEoB,aAAa,EAAE,CAAC;MAChBrB,IAAI,EAAE1E;IACV,CAAC,CAAC;IAAEiD,KAAK,EAAE,CAAC;MACRyB,IAAI,EAAE1E;IACV,CAAC,CAAC;IAAE8G,qBAAqB,EAAE,CAAC;MACxBpC,IAAI,EAAE1E;IACV,CAAC,CAAC;IAAEyC,YAAY,EAAE,CAAC;MACfiC,IAAI,EAAE1E;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgL,aAAa,CAAC;AAEpBA,aAAa,CAACxG,IAAI;EAAA,iBAA6FwG,aAAa;AAAA,CAAkD;AAC9KA,aAAa,CAACC,IAAI,kBAld6EvL,EAAE;EAAA,MAkdesL;AAAa,EAAwH;AACrPA,aAAa,CAACE,IAAI,kBAnd6ExL,EAAE;EAAA,WAmdyC,CAAC8G,6BAA6B,CAAC;EAAA,UAAY5E,YAAY,EAAEZ,eAAe;AAAA,EAAI;AACtN;EAAA,mDApd+FtB,EAAE,mBAodDsL,aAAa,EAAc,CAAC;IAChHtG,IAAI,EAAEnE,QAAQ;IACdoE,IAAI,EAAE,CAAC;MACCwG,OAAO,EAAE,CAACvJ,YAAY,EAAEZ,eAAe,CAAC;MACxCoK,OAAO,EAAE,CAAC9I,OAAO,EAAEuE,aAAa,CAAC;MACjCwE,YAAY,EAAE,CAAC/I,OAAO,EAAEuE,aAAa,CAAC;MACtCyE,SAAS,EAAE,CAAC9E,6BAA6B;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASpE,wBAAwB,EAAEoE,6BAA6B,EAAEF,qCAAqC,EAAEhE,OAAO,EAAEuE,aAAa,EAAEX,iBAAiB,EAAE8E,aAAa,EAAExF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}