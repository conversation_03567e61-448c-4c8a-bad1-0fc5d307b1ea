# Quick verification of registration data
Write-Host "Quick Registration Data Verification" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Get connection string
$appsettings = Get-Content "Oracul.Server\appsettings.json" | ConvertFrom-Json
$connectionString = $appsettings.ConnectionStrings.DefaultConnection

# Function to run query
function Run-Query {
    param([string]$Query)
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        $command = New-Object System.Data.SqlClient.SqlCommand($Query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataset = New-Object System.Data.DataSet
        $adapter.Fill($dataset)
        $connection.Close()
        return $dataset.Tables[0]
    }
    catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

Write-Host "`n1. Recent Users (last 5):" -ForegroundColor Cyan
$users = Run-Query "SELECT TOP 5 Id, FirstName, LastName, Email, PhoneNumber, IsActive, EmailConfirmed FROM Users ORDER BY CreatedAt DESC"
if ($users) { $users | Format-Table -AutoSize }

Write-Host "`n2. User Roles:" -ForegroundColor Cyan
$roles = Run-Query "SELECT u.Email, r.Name AS Role FROM Users u INNER JOIN UserRoles ur ON u.Id = ur.UserId INNER JOIN Roles r ON ur.RoleId = r.Id ORDER BY u.CreatedAt DESC"
if ($roles) { $roles | Format-Table -AutoSize }

Write-Host "`n3. Oracle Profiles (last 3):" -ForegroundColor Cyan
$profiles = Run-Query "SELECT TOP 3 u.Email, up.ProfessionalTitle, up.Headline, up.IsPublic FROM UserProfiles up INNER JOIN Users u ON up.UserId = u.Id ORDER BY up.CreatedAt DESC"
if ($profiles) { $profiles | Format-Table -AutoSize }

Write-Host "`n4. Profile Skills (last 10):" -ForegroundColor Cyan
$skills = Run-Query "SELECT TOP 10 u.Email, ps.Name AS Skill, ps.Category FROM ProfileSkills ps INNER JOIN UserProfiles up ON ps.UserProfileId = up.Id INNER JOIN Users u ON up.UserId = u.Id ORDER BY ps.CreatedAt DESC"
if ($skills) { $skills | Format-Table -AutoSize }

Write-Host "`n5. Summary:" -ForegroundColor Yellow
$summary = Run-Query @"
SELECT 'Total Users' AS Item, COUNT(*) AS Count FROM Users
UNION ALL
SELECT 'Oracle Users', COUNT(*) FROM Users u INNER JOIN UserRoles ur ON u.Id = ur.UserId INNER JOIN Roles r ON ur.RoleId = r.Id WHERE r.Name = 'Oracle'
UNION ALL
SELECT 'User Profiles', COUNT(*) FROM UserProfiles
UNION ALL
SELECT 'Profile Skills', COUNT(*) FROM ProfileSkills
"@
if ($summary) { $summary | Format-Table -AutoSize }

Write-Host "`nVerification complete! ✅" -ForegroundColor Green
