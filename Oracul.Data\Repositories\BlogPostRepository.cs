using Microsoft.EntityFrameworkCore;
using Oracul.Data.Data;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;

namespace Oracul.Data.Repositories
{
    /// <summary>
    /// Blog post repository implementation with article-specific operations
    /// </summary>
    public class BlogPostRepository : Repository<BlogPost>, IBlogPostRepository
    {
        public BlogPostRepository(OraculDbContext context) : base(context)
        {
        }

        public async Task<BlogPost?> GetBySlugAsync(string slug)
        {
            return await _dbSet
                .Include(bp => bp.UserProfile)
                    .ThenInclude(up => up.User)
                .Include(bp => bp.BlogPostTags)
                .FirstOrDefaultAsync(bp => bp.Slug == slug);
        }

        public async Task<IEnumerable<BlogPost>> GetFeaturedArticlesAsync(int count)
        {
            return await _dbSet
                .Include(bp => bp.UserProfile)
                    .ThenInclude(up => up.User)
                .Include(bp => bp.BlogPostTags)
                .Where(bp => bp.PublishedAt <= DateTime.UtcNow)
                .OrderByDescending(bp => bp.ReadCount)
                .ThenByDescending(bp => bp.PublishedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<(IEnumerable<BlogPost> Articles, int TotalCount)> SearchArticlesAsync(
            string? searchTerm,
            string? category,
            int? authorId,
            int page,
            int pageSize,
            string sortBy,
            string sortOrder)
        {
            var query = _dbSet
                .Include(bp => bp.UserProfile)
                    .ThenInclude(up => up.User)
                .Include(bp => bp.BlogPostTags)
                .Where(bp => bp.PublishedAt <= DateTime.UtcNow);

            // Apply search term filter
            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(bp =>
                    bp.Title.ToLower().Contains(lowerSearchTerm) ||
                    bp.Excerpt.ToLower().Contains(lowerSearchTerm) ||
                    (bp.Content != null && bp.Content.ToLower().Contains(lowerSearchTerm)) ||
                    bp.BlogPostTags.Any(tag => tag.Tag.ToLower().Contains(lowerSearchTerm))
                );
            }

            // Apply category filter
            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(bp => bp.BlogPostTags.Any(tag => tag.Tag.ToLower() == category.ToLower()));
            }

            // Apply author filter
            if (authorId.HasValue)
            {
                query = query.Where(bp => bp.UserProfileId == authorId.Value);
            }

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply sorting
            query = sortBy.ToLower() switch
            {
                "title" => sortOrder.ToLower() == "desc" 
                    ? query.OrderByDescending(bp => bp.Title)
                    : query.OrderBy(bp => bp.Title),
                "readcount" => sortOrder.ToLower() == "desc"
                    ? query.OrderByDescending(bp => bp.ReadCount)
                    : query.OrderBy(bp => bp.ReadCount),
                _ => sortOrder.ToLower() == "desc"
                    ? query.OrderByDescending(bp => bp.PublishedAt)
                    : query.OrderBy(bp => bp.PublishedAt)
            };

            // Apply pagination
            var articles = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (articles, totalCount);
        }

        public async Task<(IEnumerable<BlogPost> Articles, int TotalCount)> GetByCategoryAsync(
            string category, 
            int page, 
            int pageSize)
        {
            var query = _dbSet
                .Include(bp => bp.UserProfile)
                    .ThenInclude(up => up.User)
                .Include(bp => bp.BlogPostTags)
                .Where(bp => bp.PublishedAt <= DateTime.UtcNow &&
                           bp.BlogPostTags.Any(tag => tag.Tag.ToLower() == category.ToLower()));

            var totalCount = await query.CountAsync();

            var articles = await query
                .OrderByDescending(bp => bp.PublishedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (articles, totalCount);
        }

        public async Task<(IEnumerable<BlogPost> Articles, int TotalCount)> GetByAuthorAsync(
            int authorId, 
            int page, 
            int pageSize)
        {
            var query = _dbSet
                .Include(bp => bp.UserProfile)
                    .ThenInclude(up => up.User)
                .Include(bp => bp.BlogPostTags)
                .Where(bp => bp.UserProfileId == authorId && bp.PublishedAt <= DateTime.UtcNow);

            var totalCount = await query.CountAsync();

            var articles = await query
                .OrderByDescending(bp => bp.PublishedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (articles, totalCount);
        }

        public async Task<IEnumerable<BlogPost>> GetRecentArticlesAsync(int count)
        {
            return await _dbSet
                .Include(bp => bp.UserProfile)
                    .ThenInclude(up => up.User)
                .Include(bp => bp.BlogPostTags)
                .Where(bp => bp.PublishedAt <= DateTime.UtcNow)
                .OrderByDescending(bp => bp.PublishedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<BlogPost>> GetPopularArticlesAsync(int count)
        {
            return await _dbSet
                .Include(bp => bp.UserProfile)
                    .ThenInclude(up => up.User)
                .Include(bp => bp.BlogPostTags)
                .Where(bp => bp.PublishedAt <= DateTime.UtcNow)
                .OrderByDescending(bp => bp.ReadCount)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<string>> GetCategoriesAsync()
        {
            return await _context.BlogPostTags
                .Select(tag => tag.Tag)
                .Distinct()
                .OrderBy(tag => tag)
                .ToListAsync();
        }

        public async Task<bool> IsSlugAvailableAsync(string slug, int? excludeArticleId = null)
        {
            var query = _dbSet.Where(bp => bp.Slug == slug);
            
            if (excludeArticleId.HasValue)
            {
                query = query.Where(bp => bp.Id != excludeArticleId.Value);
            }
            
            return !await query.AnyAsync();
        }

        public async Task IncrementReadCountAsync(int articleId)
        {
            var article = await _dbSet.FindAsync(articleId);
            if (article != null)
            {
                article.ReadCount++;
                _context.Update(article);
            }
        }
    }
}
