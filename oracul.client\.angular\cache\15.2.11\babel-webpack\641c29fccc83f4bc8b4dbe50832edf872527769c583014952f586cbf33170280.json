{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../shared/services/file-upload.service\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nfunction ImageUploadComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"img\", 4);\n    i0.ɵɵelementStart(2, \"div\", 5)(3, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function ImageUploadComponent_div_1_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeImage());\n    });\n    i0.ɵɵelementStart(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r0.getImageUrl(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.placeholder);\n  }\n}\nfunction ImageUploadComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 11);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u041A\\u0430\\u0447\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u043D\\u0438\\u043C\\u043A\\u0430\\u0442\\u0430...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImageUploadComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"mat-icon\", 13);\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 15);\n    i0.ɵɵtext(6, \"\\u041F\\u043B\\u044A\\u0437\\u043D\\u0435\\u0442\\u0435 \\u0438 \\u043F\\u0443\\u0441\\u043D\\u0435\\u0442\\u0435 \\u0444\\u0430\\u0439\\u043B \\u0442\\u0443\\u043A \\u0438\\u043B\\u0438\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 16, 17);\n    i0.ɵɵlistener(\"change\", function ImageUploadComponent_div_2_div_2_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ImageUploadComponent_div_2_div_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const _r6 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(_r6.click());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"add_photo_alternate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" \\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0444\\u0430\\u0439\\u043B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 19);\n    i0.ɵɵtext(14, \" \\u041F\\u043E\\u0434\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\\u0438: JPG, PNG, GIF, WebP\");\n    i0.ɵɵelement(15, \"br\");\n    i0.ɵɵtext(16, \" \\u041C\\u0430\\u043A\\u0441\\u0438\\u043C\\u0430\\u043B\\u0435\\u043D \\u0440\\u0430\\u0437\\u043C\\u0435\\u0440: 5MB \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.placeholder);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"accept\", ctx_r5.accept);\n  }\n}\nfunction ImageUploadComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵlistener(\"dragover\", function ImageUploadComponent_div_2_Template_div_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onDragOver($event));\n    })(\"dragleave\", function ImageUploadComponent_div_2_Template_div_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onDragLeave($event));\n    })(\"drop\", function ImageUploadComponent_div_2_Template_div_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onDrop($event));\n    });\n    i0.ɵɵtemplate(1, ImageUploadComponent_div_2_div_1_Template, 4, 0, \"div\", 8);\n    i0.ɵɵtemplate(2, ImageUploadComponent_div_2_div_2_Template, 17, 2, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"drag-over\", ctx_r1.dragOver)(\"uploading\", ctx_r1.isUploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isUploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isUploading);\n  }\n}\nexport class ImageUploadComponent {\n  constructor(fileUploadService, snackBar) {\n    this.fileUploadService = fileUploadService;\n    this.snackBar = snackBar;\n    this.placeholder = 'Качете снимка';\n    this.accept = 'image/*';\n    this.imageUploaded = new EventEmitter();\n    this.imageRemoved = new EventEmitter();\n    this.isUploading = false;\n    this.dragOver = false;\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.uploadFile(input.files[0]);\n    }\n  }\n  onDragOver(event) {\n    event.preventDefault();\n    this.dragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    this.dragOver = false;\n  }\n  onDrop(event) {\n    event.preventDefault();\n    this.dragOver = false;\n    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {\n      this.uploadFile(event.dataTransfer.files[0]);\n    }\n  }\n  uploadFile(file) {\n    // Validate file\n    const validation = this.fileUploadService.validateImageFile(file);\n    if (!validation.valid) {\n      this.snackBar.open(validation.error, 'Затвори', {\n        duration: 3000\n      });\n      return;\n    }\n    this.isUploading = true;\n    this.fileUploadService.uploadImage(file).subscribe({\n      next: response => {\n        const fullUrl = this.fileUploadService.getImageUrl(response.url);\n        this.imageUploaded.emit(fullUrl);\n        this.snackBar.open('Снимката е качена успешно', 'Затвори', {\n          duration: 3000\n        });\n        this.isUploading = false;\n      },\n      error: error => {\n        console.error('Error uploading image:', error);\n        this.snackBar.open(error.message || 'Грешка при качването на снимката', 'Затвори', {\n          duration: 3000\n        });\n        this.isUploading = false;\n      }\n    });\n  }\n  removeImage() {\n    this.imageRemoved.emit();\n  }\n  getImageUrl() {\n    if (!this.currentImageUrl) return '';\n    return this.fileUploadService.getImageUrl(this.currentImageUrl);\n  }\n  static {\n    this.ɵfac = function ImageUploadComponent_Factory(t) {\n      return new (t || ImageUploadComponent)(i0.ɵɵdirectiveInject(i1.FileUploadService), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageUploadComponent,\n      selectors: [[\"app-image-upload\"]],\n      inputs: {\n        currentImageUrl: \"currentImageUrl\",\n        placeholder: \"placeholder\",\n        accept: \"accept\"\n      },\n      outputs: {\n        imageUploaded: \"imageUploaded\",\n        imageRemoved: \"imageRemoved\"\n      },\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"image-upload-container\"], [\"class\", \"current-image\", 4, \"ngIf\"], [\"class\", \"upload-area\", 3, \"drag-over\", \"uploading\", \"dragover\", \"dragleave\", \"drop\", 4, \"ngIf\"], [1, \"current-image\"], [1, \"uploaded-image\", 3, \"src\", \"alt\"], [1, \"image-overlay\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 1, \"remove-button\", 3, \"click\"], [1, \"upload-area\", 3, \"dragover\", \"dragleave\", \"drop\"], [\"class\", \"upload-loading\", 4, \"ngIf\"], [\"class\", \"upload-prompt\", 4, \"ngIf\"], [1, \"upload-loading\"], [\"mode\", \"indeterminate\", \"diameter\", \"40\"], [1, \"upload-prompt\"], [1, \"upload-icon\"], [1, \"upload-text\"], [1, \"upload-hint\"], [\"type\", \"file\", 1, \"file-input\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"file-requirements\"]],\n      template: function ImageUploadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ImageUploadComponent_div_1_Template, 6, 2, \"div\", 1);\n          i0.ɵɵtemplate(2, ImageUploadComponent_div_2_Template, 3, 6, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentImageUrl && !ctx.isUploading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentImageUrl || ctx.isUploading);\n        }\n      },\n      dependencies: [i3.NgIf, i4.MatButton, i4.MatIconButton, i5.MatIcon, i6.MatProgressSpinner],\n      styles: [\".image-upload-container[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n\\r\\n.current-image[_ngcontent-%COMP%] {\\r\\n  position: relative;\\r\\n  display: inline-block;\\r\\n  border-radius: 8px;\\r\\n  overflow: hidden;\\r\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.uploaded-image[_ngcontent-%COMP%] {\\r\\n  max-width: 100%;\\r\\n  max-height: 300px;\\r\\n  width: auto;\\r\\n  height: auto;\\r\\n  display: block;\\r\\n}\\r\\n\\r\\n.image-overlay[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  right: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  border-radius: 0 0 0 8px;\\r\\n}\\r\\n\\r\\n.remove-button[_ngcontent-%COMP%] {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.upload-area[_ngcontent-%COMP%] {\\r\\n  border: 2px dashed #d2a6d0;\\r\\n  border-radius: 8px;\\r\\n  padding: 40px 20px;\\r\\n  text-align: center;\\r\\n  background-color: #fafafa;\\r\\n  transition: all 0.3s ease;\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.upload-area[_ngcontent-%COMP%]:hover {\\r\\n  border-color: #67455c;\\r\\n  background-color: #f5f5f5;\\r\\n}\\r\\n\\r\\n.upload-area.drag-over[_ngcontent-%COMP%] {\\r\\n  border-color: #67455c;\\r\\n  background-color: #e6dbec;\\r\\n  transform: scale(1.02);\\r\\n}\\r\\n\\r\\n.upload-area.uploading[_ngcontent-%COMP%] {\\r\\n  border-color: #d2a6d0;\\r\\n  background-color: #f9f9f9;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.upload-loading[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n}\\r\\n\\r\\n.upload-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  color: #666;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.upload-prompt[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n\\r\\n.upload-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 48px;\\r\\n  width: 48px;\\r\\n  height: 48px;\\r\\n  color: #d2a6d0;\\r\\n}\\r\\n\\r\\n.upload-text[_ngcontent-%COMP%] {\\r\\n  font-size: 18px;\\r\\n  font-weight: 500;\\r\\n  color: #3f2f4e;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n.upload-hint[_ngcontent-%COMP%] {\\r\\n  font-size: 14px;\\r\\n  color: #666;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n.file-input[_ngcontent-%COMP%] {\\r\\n  display: none;\\r\\n}\\r\\n\\r\\n.file-requirements[_ngcontent-%COMP%] {\\r\\n  font-size: 12px;\\r\\n  color: #999;\\r\\n  margin: 8px 0 0 0;\\r\\n  line-height: 1.4;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .upload-area[_ngcontent-%COMP%] {\\r\\n    padding: 30px 15px;\\r\\n  }\\r\\n  \\r\\n  .upload-icon[_ngcontent-%COMP%] {\\r\\n    font-size: 36px;\\r\\n    width: 36px;\\r\\n    height: 36px;\\r\\n  }\\r\\n  \\r\\n  .upload-text[_ngcontent-%COMP%] {\\r\\n    font-size: 16px;\\r\\n  }\\r\\n  \\r\\n  .uploaded-image[_ngcontent-%COMP%] {\\r\\n    max-height: 200px;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@keyframes _ngcontent-%COMP%_pulse {\\r\\n  0% {\\r\\n    transform: scale(1);\\r\\n  }\\r\\n  50% {\\r\\n    transform: scale(1.05);\\r\\n  }\\r\\n  100% {\\r\\n    transform: scale(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.upload-area.drag-over[_ngcontent-%COMP%] {\\r\\n  animation: _ngcontent-%COMP%_pulse 1s infinite;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;;;;;;;;;;;ICEpEC,8BAAmE;IACjEA,yBAAsE;IACtEA,8BAA2B;IACYA;MAAAA;MAAA;MAAA,OAASA,mCAAa;IAAA,EAAC;IAC1DA,gCAAU;IAAAA,sBAAM;IAAAA,iBAAW;;;;IAH1BA,eAAqB;IAArBA,4DAAqB;;;;;IAkB1BA,+BAAgD;IAC9CA,2CAAgF;IAChFA,yBAAG;IAAAA,2HAAsB;IAAAA,iBAAI;;;;;;IAI/BA,+BAAgD;IAChBA,4BAAY;IAAAA,iBAAW;IACrDA,6BAAuB;IAAAA,YAAiB;IAAAA,iBAAI;IAC5CA,6BAAuB;IAAAA,iLAA+B;IAAAA,iBAAI;IAE1DA,qCAIkB;IAFXA;MAAAA;MAAA;MAAA,OAAUA,4CAAsB;IAAA,EAAC;IAFxCA,iBAIkB;IAElBA,kCAAsE;IAA5BA;MAAAA;MAAA;MAAA,OAASA,0BAAiB;IAAA,EAAC;IACnEA,iCAAU;IAAAA,oCAAmB;IAAAA,iBAAW;IACxCA,4FACF;IAAAA,iBAAS;IAETA,8BAA6B;IAC3BA,8IAAuC;IAAAA,sBAAI;IAC3CA,yHACF;IAAAA,iBAAI;;;;IAjBmBA,eAAiB;IAAjBA,wCAAiB;IAIjCA,eAAiB;IAAjBA,sCAAiB;;;;;;IArB5BA,8BAM6B;IAFxBA;MAAAA;MAAA;MAAA,OAAYA,yCAAkB;IAAA,EAAC;MAAAA;MAAA;MAAA,OAClBA,0CAAmB;IAAA,EADD;MAAAA;MAAA;MAAA,OAEvBA,qCAAc;IAAA,EAFS;IAKlCA,2EAGM;IAGNA,4EAoBM;IACRA,iBAAM;;;;IAlCDA,4CAA4B;IAOzBA,eAAiB;IAAjBA,yCAAiB;IAMjBA,eAAkB;IAAlBA,0CAAkB;;;ADlB5B,OAAM,MAAOC,oBAAoB;EAU/BC,YACUC,iBAAoC,EACpCC,QAAqB;IADrB,sBAAiB,GAAjBD,iBAAiB;IACjB,aAAQ,GAARC,QAAQ;IAVT,gBAAW,GAAW,eAAe;IACrC,WAAM,GAAW,SAAS;IACzB,kBAAa,GAAG,IAAIL,YAAY,EAAU;IAC1C,iBAAY,GAAG,IAAIA,YAAY,EAAQ;IAEjD,gBAAW,GAAG,KAAK;IACnB,aAAQ,GAAG,KAAK;EAKb;EAEHM,cAAc,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACC,UAAU,CAACJ,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEnC;EAEAG,UAAU,CAACN,KAAgB;IACzBA,KAAK,CAACO,cAAc,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACtB;EAEAC,WAAW,CAACT,KAAgB;IAC1BA,KAAK,CAACO,cAAc,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACvB;EAEAE,MAAM,CAACV,KAAgB;IACrBA,KAAK,CAACO,cAAc,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IAErB,IAAIR,KAAK,CAACW,YAAY,EAAER,KAAK,IAAIH,KAAK,CAACW,YAAY,CAACR,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACpE,IAAI,CAACC,UAAU,CAACL,KAAK,CAACW,YAAY,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEhD;EAEQE,UAAU,CAACO,IAAU;IAC3B;IACA,MAAMC,UAAU,GAAG,IAAI,CAAChB,iBAAiB,CAACiB,iBAAiB,CAACF,IAAI,CAAC;IACjE,IAAI,CAACC,UAAU,CAACE,KAAK,EAAE;MACrB,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAACH,UAAU,CAACI,KAAM,EAAE,SAAS,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACpE;;IAGF,IAAI,CAACC,WAAW,GAAG,IAAI;IAEvB,IAAI,CAACtB,iBAAiB,CAACuB,WAAW,CAACR,IAAI,CAAC,CAACS,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,MAAMC,OAAO,GAAG,IAAI,CAAC3B,iBAAiB,CAAC4B,WAAW,CAACF,QAAQ,CAACG,GAAG,CAAC;QAChE,IAAI,CAACC,aAAa,CAACC,IAAI,CAACJ,OAAO,CAAC;QAChC,IAAI,CAAC1B,QAAQ,CAACkB,IAAI,CAAC,2BAA2B,EAAE,SAAS,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC9E,IAAI,CAACC,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACfY,OAAO,CAACZ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACnB,QAAQ,CAACkB,IAAI,CAACC,KAAK,CAACa,OAAO,IAAI,kCAAkC,EAAE,SAAS,EAAE;UAAEZ,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtG,IAAI,CAACC,WAAW,GAAG,KAAK;MAC1B;KACD,CAAC;EACJ;EAEAY,WAAW;IACT,IAAI,CAACC,YAAY,CAACJ,IAAI,EAAE;EAC1B;EAEAH,WAAW;IACT,IAAI,CAAC,IAAI,CAACQ,eAAe,EAAE,OAAO,EAAE;IACpC,OAAO,IAAI,CAACpC,iBAAiB,CAAC4B,WAAW,CAAC,IAAI,CAACQ,eAAe,CAAC;EACjE;;;uBAzEWtC,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAuC;MAAAC;QAAAF;QAAAG;QAAAC;MAAA;MAAAC;QAAAX;QAAAK;MAAA;MAAAO;MAAAC;MAAAC;MAAAC;QAAA;UCTjChD,8BAAoC;UAElCA,qEAOM;UAGNA,qEAoCM;UACRA,iBAAM;;;UA/CEA,eAAqC;UAArCA,8DAAqC;UAUrCA,eAAqC;UAArCA,8DAAqC", "names": ["EventEmitter", "i0", "ImageUploadComponent", "constructor", "fileUploadService", "snackBar", "onFileSelected", "event", "input", "target", "files", "length", "uploadFile", "onDragOver", "preventDefault", "dragOver", "onDragLeave", "onDrop", "dataTransfer", "file", "validation", "validateImageFile", "valid", "open", "error", "duration", "isUploading", "uploadImage", "subscribe", "next", "response", "fullUrl", "getImageUrl", "url", "imageUploaded", "emit", "console", "message", "removeImage", "imageRemoved", "currentImageUrl", "selectors", "inputs", "placeholder", "accept", "outputs", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\components\\image-upload\\image-upload.component.ts", "C:\\Projects\\Harmonia\\oracul.client\\src\\app\\articles\\components\\image-upload\\image-upload.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { FileUploadService } from '../../../shared/services/file-upload.service';\r\n\r\n@Component({\r\n  selector: 'app-image-upload',\r\n  templateUrl: './image-upload.component.html',\r\n  styleUrls: ['./image-upload.component.css']\r\n})\r\nexport class ImageUploadComponent {\r\n  @Input() currentImageUrl?: string;\r\n  @Input() placeholder: string = 'Качете снимка';\r\n  @Input() accept: string = 'image/*';\r\n  @Output() imageUploaded = new EventEmitter<string>();\r\n  @Output() imageRemoved = new EventEmitter<void>();\r\n\r\n  isUploading = false;\r\n  dragOver = false;\r\n\r\n  constructor(\r\n    private fileUploadService: FileUploadService,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.uploadFile(input.files[0]);\r\n    }\r\n  }\r\n\r\n  onDragOver(event: DragEvent): void {\r\n    event.preventDefault();\r\n    this.dragOver = true;\r\n  }\r\n\r\n  onDragLeave(event: DragEvent): void {\r\n    event.preventDefault();\r\n    this.dragOver = false;\r\n  }\r\n\r\n  onDrop(event: DragEvent): void {\r\n    event.preventDefault();\r\n    this.dragOver = false;\r\n\r\n    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {\r\n      this.uploadFile(event.dataTransfer.files[0]);\r\n    }\r\n  }\r\n\r\n  private uploadFile(file: File): void {\r\n    // Validate file\r\n    const validation = this.fileUploadService.validateImageFile(file);\r\n    if (!validation.valid) {\r\n      this.snackBar.open(validation.error!, 'Затвори', { duration: 3000 });\r\n      return;\r\n    }\r\n\r\n    this.isUploading = true;\r\n\r\n    this.fileUploadService.uploadImage(file).subscribe({\r\n      next: (response) => {\r\n        const fullUrl = this.fileUploadService.getImageUrl(response.url);\r\n        this.imageUploaded.emit(fullUrl);\r\n        this.snackBar.open('Снимката е качена успешно', 'Затвори', { duration: 3000 });\r\n        this.isUploading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error uploading image:', error);\r\n        this.snackBar.open(error.message || 'Грешка при качването на снимката', 'Затвори', { duration: 3000 });\r\n        this.isUploading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  removeImage(): void {\r\n    this.imageRemoved.emit();\r\n  }\r\n\r\n  getImageUrl(): string {\r\n    if (!this.currentImageUrl) return '';\r\n    return this.fileUploadService.getImageUrl(this.currentImageUrl);\r\n  }\r\n}\r\n", "<div class=\"image-upload-container\">\r\n  <!-- Current Image Display -->\r\n  <div *ngIf=\"currentImageUrl && !isUploading\" class=\"current-image\">\r\n    <img [src]=\"getImageUrl()\" [alt]=\"placeholder\" class=\"uploaded-image\">\r\n    <div class=\"image-overlay\">\r\n      <button mat-icon-button color=\"warn\" (click)=\"removeImage()\" class=\"remove-button\">\r\n        <mat-icon>delete</mat-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Upload Area -->\r\n  <div *ngIf=\"!currentImageUrl || isUploading\" \r\n       class=\"upload-area\"\r\n       [class.drag-over]=\"dragOver\"\r\n       [class.uploading]=\"isUploading\"\r\n       (dragover)=\"onDragOver($event)\"\r\n       (dragleave)=\"onDragLeave($event)\"\r\n       (drop)=\"onDrop($event)\">\r\n    \r\n    <!-- Loading State -->\r\n    <div *ngIf=\"isUploading\" class=\"upload-loading\">\r\n      <mat-progress-spinner mode=\"indeterminate\" diameter=\"40\"></mat-progress-spinner>\r\n      <p>Качване на снимката...</p>\r\n    </div>\r\n\r\n    <!-- Upload Prompt -->\r\n    <div *ngIf=\"!isUploading\" class=\"upload-prompt\">\r\n      <mat-icon class=\"upload-icon\">cloud_upload</mat-icon>\r\n      <p class=\"upload-text\">{{ placeholder }}</p>\r\n      <p class=\"upload-hint\">Плъзнете и пуснете файл тук или</p>\r\n      \r\n      <input type=\"file\" \r\n             [accept]=\"accept\" \r\n             (change)=\"onFileSelected($event)\"\r\n             class=\"file-input\"\r\n             #fileInput>\r\n      \r\n      <button mat-raised-button color=\"primary\" (click)=\"fileInput.click()\">\r\n        <mat-icon>add_photo_alternate</mat-icon>\r\n        Изберете файл\r\n      </button>\r\n      \r\n      <p class=\"file-requirements\">\r\n        Поддържани формати: JPG, PNG, GIF, WebP<br>\r\n        Максимален размер: 5MB\r\n      </p>\r\n    </div>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}