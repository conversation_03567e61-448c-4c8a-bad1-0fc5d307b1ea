{"ast": null, "code": "import { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./token.service\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, router, tokenService) {\n      this.http = http;\n      this.router = router;\n      this.tokenService = tokenService;\n      this.API_URL = `${environment.apiUrl}/auth`;\n      this.TOKEN_KEY = 'access_token';\n      this.REFRESH_TOKEN_KEY = 'refresh_token';\n      this.USER_KEY = 'user_info';\n      this.REMEMBER_ME_KEY = 'remember_me';\n      this.currentUserSubject = new BehaviorSubject(null);\n      this.currentUser$ = this.currentUserSubject.asObservable();\n      this.isAuthenticatedSubject = new BehaviorSubject(false);\n      this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n      this.initializeAuth();\n    }\n    initializeAuth() {\n      const token = this.tokenService.getToken();\n      const user = this.tokenService.getStoredUser();\n      if (token && user) {\n        // Check if token is expired\n        if (this.tokenService.isTokenExpired(token)) {\n          // Try to refresh the token\n          this.refreshToken().subscribe({\n            next: () => {\n              this.currentUserSubject.next(user);\n              this.isAuthenticatedSubject.next(true);\n              this.startTokenExpirationCheck();\n            },\n            error: () => {\n              // Refresh failed, clear auth data\n              this.clearAuthData();\n            }\n          });\n        } else {\n          this.currentUserSubject.next(user);\n          this.isAuthenticatedSubject.next(true);\n          this.startTokenExpirationCheck();\n        }\n      }\n    }\n    login(credentials) {\n      return this.http.post(`${this.API_URL}/login`, credentials).pipe(tap(response => {\n        if (response.success && response.accessToken && response.user) {\n          // Store remember me preference\n          const rememberMe = credentials.rememberMe || false;\n          this.tokenService.setRememberMe(rememberMe);\n          this.tokenService.setToken(response.accessToken, rememberMe);\n          if (response.refreshToken) {\n            this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n          }\n          this.tokenService.setUser(response.user, rememberMe);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        }\n      }), catchError(this.handleError));\n    }\n    register(userData) {\n      // Determine endpoint based on data type\n      const endpoint = this.isOracleRegistration(userData) ? 'register-oracle' : 'register';\n      return this.http.post(`${this.API_URL}/${endpoint}`, userData).pipe(tap(response => {\n        if (response.success && response.accessToken && response.user) {\n          // For registration, default to remember me = false\n          const rememberMe = false;\n          this.tokenService.setRememberMe(rememberMe);\n          this.tokenService.setToken(response.accessToken, rememberMe);\n          if (response.refreshToken) {\n            this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n          }\n          this.tokenService.setUser(response.user, rememberMe);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        }\n      }), catchError(this.handleError));\n    }\n    isOracleRegistration(userData) {\n      return 'professionalTitle' in userData;\n    }\n    logout() {\n      return this.http.post(`${this.API_URL}/logout`, {}).pipe(tap(() => {\n        this.clearAuthData();\n      }), catchError(() => {\n        // Even if logout fails on server, clear local data\n        this.clearAuthData();\n        return throwError('Logout failed');\n      }));\n    }\n    refreshToken() {\n      const refreshToken = this.tokenService.getRefreshToken();\n      if (!refreshToken) {\n        return throwError('No refresh token available');\n      }\n      return this.http.post(`${this.API_URL}/refresh-token`, {\n        refreshToken\n      }).pipe(tap(response => {\n        if (response.success && response.accessToken) {\n          const rememberMe = this.tokenService.getRememberMe();\n          this.tokenService.setToken(response.accessToken, rememberMe);\n          if (response.refreshToken) {\n            this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n          }\n          if (response.user) {\n            this.tokenService.setUser(response.user, rememberMe);\n            this.currentUserSubject.next(response.user);\n          }\n        }\n      }), catchError(error => {\n        this.clearAuthData();\n        return throwError(error);\n      }));\n    }\n    getCurrentUser() {\n      return this.http.get(`${this.API_URL}/me`).pipe(tap(user => {\n        const rememberMe = this.tokenService.getRememberMe();\n        this.tokenService.setUser(user, rememberMe);\n        this.currentUserSubject.next(user);\n      }), catchError(this.handleError));\n    }\n    changePassword(request) {\n      return this.http.post(`${this.API_URL}/change-password`, request).pipe(catchError(this.handleError));\n    }\n    forgotPassword(request) {\n      return this.http.post(`${this.API_URL}/forgot-password`, request).pipe(catchError(this.handleError));\n    }\n    resetPassword(request) {\n      return this.http.post(`${this.API_URL}/reset-password`, request).pipe(catchError(this.handleError));\n    }\n    loginWithOAuth(request) {\n      return this.http.post(`${this.API_URL}/oauth-login`, request).pipe(tap(response => {\n        if (response.success && response.accessToken && response.user) {\n          // For OAuth, default to remember me = true for better UX\n          const rememberMe = true;\n          this.tokenService.setRememberMe(rememberMe);\n          this.tokenService.setToken(response.accessToken, rememberMe);\n          if (response.refreshToken) {\n            this.tokenService.setRefreshToken(response.refreshToken, rememberMe);\n          }\n          this.tokenService.setUser(response.user, rememberMe);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        }\n      }), catchError(this.handleError));\n    }\n    // Token management - delegated to TokenService\n    getToken() {\n      return this.tokenService.getToken();\n    }\n    checkTokenExpiration() {\n      const token = this.tokenService.getToken();\n      if (token && this.tokenService.isTokenExpired(token)) {\n        // Try to refresh the token\n        this.refreshToken().subscribe({\n          next: () => {\n            // Token refreshed successfully\n          },\n          error: () => {\n            // Refresh failed, logout user\n            this.clearAuthData();\n          }\n        });\n      }\n    }\n    startTokenExpirationCheck() {\n      // Check token expiration every 5 minutes\n      setInterval(() => {\n        this.checkTokenExpiration();\n      }, 5 * 60 * 1000);\n    }\n    clearAuthData() {\n      this.tokenService.clearAllTokens();\n      this.currentUserSubject.next(null);\n      this.isAuthenticatedSubject.next(false);\n      this.router.navigate(['/login']);\n    }\n    isAuthenticated() {\n      return this.tokenService.isAuthenticated();\n    }\n    hasRole(role) {\n      const user = this.currentUserSubject.value;\n      return user?.roles.includes(role) || false;\n    }\n    hasPermission(permission) {\n      const user = this.currentUserSubject.value;\n      return user?.permissions.includes(permission) || false;\n    }\n    handleError(error) {\n      let errorMessage = 'An error occurred';\n      if (error.error?.message) {\n        errorMessage = error.error.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      return throwError(errorMessage);\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.TokenService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}