import { 
  Directive, 
  ElementRef, 
  Input, 
  OnInit, 
  OnDestroy, 
  Renderer2,
  Output,
  EventEmitter
} from '@angular/core';
import { AvatarService } from '../services/avatar.service';

@Directive({
  selector: '[appLazyLoadImage]'
})
export class LazyLoadImageDirective implements OnInit, OnDestroy {
  @Input() appLazyLoadImage: string | null | undefined = null; // The image URL to load
  @Input() fallbackFirstName: string = '';
  @Input() fallbackLastName: string = '';
  @Input() useDefaultImage: boolean = false;
  @Input() placeholder: string = ''; // Optional placeholder image
  @Input() loadingClass: string = 'loading'; // CSS class for loading state
  @Input() errorClass: string = 'error'; // CSS class for error state
  @Input() loadedClass: string = 'loaded'; // CSS class for loaded state
  
  @Output() imageLoaded = new EventEmitter<boolean>();
  @Output() imageError = new EventEmitter<string>();

  private intersectionObserver?: IntersectionObserver;
  private isLoaded = false;
  private isLoading = false;

  constructor(
    private elementRef: ElementRef<HTMLImageElement>,
    private renderer: Renderer2,
    private avatarService: AvatarService
  ) {}

  ngOnInit(): void {
    this.setupIntersectionObserver();
    this.setPlaceholder();
  }

  ngOnDestroy(): void {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }

  private setupIntersectionObserver(): void {
    // Check if IntersectionObserver is supported
    if (!('IntersectionObserver' in window)) {
      // Fallback: load image immediately
      this.loadImage();
      return;
    }

    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this.isLoaded && !this.isLoading) {
            this.loadImage();
            // Stop observing once we start loading
            this.intersectionObserver?.unobserve(entry.target);
          }
        });
      },
      {
        // Start loading when image is 100px away from viewport
        rootMargin: '100px 0px',
        threshold: 0.01
      }
    );

    this.intersectionObserver.observe(this.elementRef.nativeElement);
  }

  private setPlaceholder(): void {
    const img = this.elementRef.nativeElement;
    
    if (this.placeholder) {
      this.renderer.setAttribute(img, 'src', this.placeholder);
    } else {
      // Set a minimal placeholder or generated avatar
      const placeholderUrl = this.avatarService.generateAvatarUrl(
        this.fallbackFirstName, 
        this.fallbackLastName
      );
      this.renderer.setAttribute(img, 'src', placeholderUrl);
    }
    
    this.renderer.addClass(img, this.loadingClass);
  }

  private loadImage(): void {
    if (this.isLoading || this.isLoaded) {
      return;
    }

    this.isLoading = true;
    const img = this.elementRef.nativeElement;

    // Use the enhanced avatar service for loading
    this.avatarService.loadImageWithFallback(
      this.appLazyLoadImage,
      this.fallbackFirstName,
      this.fallbackLastName,
      this.useDefaultImage
    ).subscribe({
      next: (result) => {
        this.isLoading = false;
        this.isLoaded = true;

        // Update image source
        this.renderer.setAttribute(img, 'src', result.url);
        
        // Update CSS classes
        this.renderer.removeClass(img, this.loadingClass);
        
        if (result.success && !result.fallbackUsed) {
          this.renderer.addClass(img, this.loadedClass);
          this.imageLoaded.emit(true);
        } else {
          this.renderer.addClass(img, this.errorClass);
          this.imageLoaded.emit(false);
          this.imageError.emit(result.error || 'Failed to load image');
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.isLoaded = true;
        
        // Set fallback image
        const fallbackUrl = this.useDefaultImage 
          ? this.avatarService.getDefaultProfileImage()
          : this.avatarService.generateAvatarUrl(this.fallbackFirstName, this.fallbackLastName);
        
        this.renderer.setAttribute(img, 'src', fallbackUrl);
        this.renderer.removeClass(img, this.loadingClass);
        this.renderer.addClass(img, this.errorClass);
        
        this.imageLoaded.emit(false);
        this.imageError.emit(error.message || 'Unknown error');
      }
    });
  }

  /**
   * Force load the image (useful for programmatic loading)
   */
  forceLoad(): void {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
    this.loadImage();
  }

  /**
   * Retry loading the image
   */
  retry(): void {
    this.isLoaded = false;
    this.isLoading = false;
    
    const img = this.elementRef.nativeElement;
    this.renderer.removeClass(img, this.errorClass);
    this.renderer.removeClass(img, this.loadedClass);
    
    this.setPlaceholder();
    this.loadImage();
  }
}
