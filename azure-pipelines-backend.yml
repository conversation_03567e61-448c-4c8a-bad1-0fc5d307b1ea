# Azure DevOps Pipeline for Oracul Backend (.NET 9.0 API)
# This pipeline builds, tests, and deploys the ASP.NET Core backend with Entity Framework Core

trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - Oracul.Server/*
    - Oracul.Data/*
    - Oracul.Server.Tests/*
    - Oracul.sln

pr:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - Oracul.Server/*
    - Oracul.Data/*
    - Oracul.Server.Tests/*
    - Oracul.sln

variables:
  buildConfiguration: 'Release'
  dotNetFramework: 'net9.0'
  dotNetVersion: '9.0.x'
  vmImageName: 'windows-latest'

  # Azure App Service Configuration
  azureSubscription: '6dd88dbc-9333-4643-834f-978cc8b01dc8' # Azure Subscription ID
  appServiceName: 'ora-be' # Backend App Service name
  resourceGroupName: 'egishe' # Resource group name

  # Database Configuration
  sqlServerName: 'axsion.database.windows.net' # SQL Server name
  databaseName: 'Oracle2DB'

  # Application Settings (Testing Environment - Sensitive Data in YAML)
  connectionString: 'Server=tcp:axsion.database.windows.net,1433;Initial Catalog=Oracle2DB;Persist Security Info=False;User ID=asm;Password=********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;'
  jwtSecretKey: '********3211'
  azureBlobConnectionString: 'DefaultEndpointsProtocol=https;AccountName=axsion;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net'
  frontendUrl: 'https://ora-fe-dvdegtdvdgajcfb9.northeurope-01.azurewebsites.net'
  backendUrl: 'https://fb-be-h5eqc6fwa9acd6ew.northeurope-01.azurewebsites.net'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: Build
    displayName: 'Build Backend'
    pool:
      vmImage: $(vmImageName)
    
    steps:
    - checkout: self
      fetchDepth: 0
    
    - task: UseDotNet@2
      displayName: 'Use .NET $(dotNetVersion)'
      inputs:
        packageType: 'sdk'
        version: $(dotNetVersion)
        includePreviewVersions: false
    
    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet packages'
      inputs:
        command: 'restore'
        projects: 'Oracul.sln'
        feedsToUse: 'select'
        verbosityRestore: 'Normal'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: 'Oracul.sln'
        arguments: '--configuration $(buildConfiguration) --no-restore'
    
    - task: DotNetCoreCLI@2
      displayName: 'Run unit tests'
      inputs:
        command: 'test'
        projects: 'Oracul.Server.Tests/Oracul.Server.Tests.csproj'
        arguments: '--configuration $(buildConfiguration) --no-build --collect:"XPlat Code Coverage" --logger trx --results-directory $(Agent.TempDirectory)'
        publishTestResults: true
    
    - task: PublishCodeCoverageResults@1
      displayName: 'Publish code coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'
      condition: succeededOrFailed()
    
    - task: DotNetCoreCLI@2
      displayName: 'Publish backend application'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: 'Oracul.Server/Oracul.Server.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/backend --no-build'
        zipAfterPublish: true
        modifyOutputPath: false
    
    - task: PublishBuildArtifacts@1
      displayName: 'Publish backend artifacts'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)/backend'
        ArtifactName: 'backend-drop'
        publishLocation: 'Container'

- stage: DeployProduction
  displayName: 'Deploy to Production'
  dependsOn: Build
  condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.SourceBranch'], 'refs/heads/develop')))
  jobs:
  - deployment: DeployBackendProduction
    displayName: 'Deploy Backend to Production'
    pool:
      vmImage: $(vmImageName)
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: backend-drop

          - task: AzureRmWebAppDeployment@4
            displayName: 'Deploy to Azure App Service (Production)'
            inputs:
              ConnectionType: 'AzureRM'
              azureSubscription: $(azureSubscription)
              appType: 'webApp'
              WebAppName: '$(appServiceName)'
              ResourceGroupName: $(resourceGroupName)
              packageForLinux: '$(Pipeline.Workspace)/backend-drop/*.zip'
              AppSettings: |
                -ConnectionStrings__DefaultConnection "$(connectionString)"
                -JwtSettings__SecretKey "$(jwtSecretKey)"
                -BlobStorage__ConnectionString "$(azureBlobConnectionString)"
                -AppSettings__ClientUrl "$(frontendUrl)"

          - task: UseDotNet@2
            displayName: 'Use .NET $(dotNetVersion) for migrations'
            inputs:
              packageType: 'sdk'
              version: $(dotNetVersion)
              includePreviewVersions: false

          - script: |
              dotnet tool install --global dotnet-ef --version 9.0.5
              cd $(Pipeline.Workspace)/backend-drop
              dotnet ef database update --connection "$(StagingConnectionString)" --verbose
            displayName: 'Run EF Core Migrations (Staging)'
            env:
              ConnectionStrings__DefaultConnection: $(StagingConnectionString)

          # Run Entity Framework Migrations
          - task: DotNetCoreCLI@2
            displayName: 'Run EF Core Migrations'
            inputs:
              command: 'custom'
              custom: 'ef'
              arguments: 'database update --project Oracul.Data --startup-project Oracul.Server --connection "$(connectionString)"'
              workingDirectory: '$(Pipeline.Workspace)/backend-drop'

          - script: |
              dotnet tool install --global dotnet-ef --version 9.0.5
              cd $(Pipeline.Workspace)/backend-drop
              dotnet ef database update --connection "$(ProductionConnectionString)" --verbose
            displayName: 'Run EF Core Migrations (Production)'
            env:
              ConnectionStrings__DefaultConnection: $(ProductionConnectionString)
