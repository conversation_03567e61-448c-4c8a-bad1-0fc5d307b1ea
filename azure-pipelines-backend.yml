# Azure DevOps Pipeline for Oracul Backend (.NET 9.0 API)
# This pipeline builds and deploys the ASP.NET Core backend with Entity Framework Core (no frontend components)

trigger:
  branches:
    include:
    - main
    - master
    - develop
  paths:
    include:
    - Oracul.Server/*
    - Oracul.Data/*
    - Oracul.sln

pr:
  branches:
    include:
    - main
    - master
    - develop
  paths:
    include:
    - Oracul.Server/*
    - Oracul.Data/*
    - Oracul.sln

variables:
  buildConfiguration: 'Release'
  dotNetFramework: 'net9.0'
  dotNetVersion: '9.0.x'
  vmImageName: 'windows-latest'

  # Azure App Service Configuration
  azureSubscription: 'Azure-Service-Connection' # Service connection name
  appServiceName: 'ora-be' # Backend App Service name
  resourceGroupName: 'egishe' # Resource group name

  # Database Configuration
  sqlServerName: 'axsion.database.windows.net' # SQL Server name
  databaseName: 'Oracle2DB'

  # Application Settings (Testing Environment - Sensitive Data in YAML)
  connectionString: 'Server=tcp:axsion.database.windows.net,1433;Initial Catalog=Oracle2DB;Persist Security Info=False;User ID=asm;Password=********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;'
  jwtSecretKey: '********3211'
  azureBlobConnectionString: 'DefaultEndpointsProtocol=https;AccountName=axsion;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net'
  frontendUrl: 'https://ora-fe-dvdegtdvdgajcfb9.northeurope-01.azurewebsites.net'
  backendUrl: 'https://fb-be-h5eqc6fwa9acd6ew.northeurope-01.azurewebsites.net'

stages:
- stage: Build
  displayName: 'Build Backend'
  jobs:
  - job: Build
    displayName: 'Build Backend'
    pool:
      vmImage: $(vmImageName)
    
    steps:
    - checkout: self
      fetchDepth: 0
    
    - task: UseDotNet@2
      displayName: 'Use .NET $(dotNetVersion)'
      inputs:
        packageType: 'sdk'
        version: $(dotNetVersion)
        includePreviewVersions: false
    
    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet packages'
      inputs:
        command: 'restore'
        projects: 'Oracul.sln'
        feedsToUse: 'select'
        verbosityRestore: 'Normal'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: 'Oracul.sln'
        arguments: '--configuration $(buildConfiguration) --no-restore'

    # Note: EF Core Migrations are temporarily disabled in pipeline
    # Database schema should be managed manually until migration history is resolved
    # See PIPELINE-SEPARATION-SUMMARY.md for database management instructions

    - task: DotNetCoreCLI@2
      displayName: 'Publish backend application'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: 'Oracul.Server/Oracul.Server.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/backend --no-build'
        zipAfterPublish: true
        modifyOutputPath: false
    
    - task: PublishBuildArtifacts@1
      displayName: 'Publish backend artifacts'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)/backend'
        ArtifactName: 'backend-drop'
        publishLocation: 'Container'

- stage: DeployProduction
  displayName: 'Deploy to Production'
  dependsOn: Build
  condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.SourceBranch'], 'refs/heads/master'), eq(variables['Build.SourceBranch'], 'refs/heads/develop')))
  jobs:
  - deployment: DeployBackendProduction
    displayName: 'Deploy Backend to Production'
    pool:
      vmImage: $(vmImageName)
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: backend-drop

          - task: AzureRmWebAppDeployment@4
            displayName: 'Deploy to Azure App Service (Production)'
            inputs:
              ConnectionType: 'AzureRM'
              azureSubscription: $(azureSubscription)
              appType: 'webApp'
              WebAppName: '$(appServiceName)'
              ResourceGroupName: $(resourceGroupName)
              packageForLinux: '$(Pipeline.Workspace)/backend-drop/*.zip'

          # Configure App Settings separately
          - task: AzureAppServiceSettings@1
            displayName: 'Configure App Settings'
            inputs:
              azureSubscription: $(azureSubscription)
              appName: '$(appServiceName)'
              resourceGroupName: $(resourceGroupName)
              appSettings: |
                [
                  {
                    "name": "ConnectionStrings__DefaultConnection",
                    "value": "$(connectionString)"
                  },
                  {
                    "name": "JwtSettings__SecretKey",
                    "value": "$(jwtSecretKey)"
                  },
                  {
                    "name": "BlobStorage__ConnectionString",
                    "value": "$(azureBlobConnectionString)"
                  },
                  {
                    "name": "AppSettings__ClientUrl",
                    "value": "$(frontendUrl)"
                  }
                ]


