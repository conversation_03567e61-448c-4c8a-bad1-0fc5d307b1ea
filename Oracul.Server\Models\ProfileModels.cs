using System.ComponentModel.DataAnnotations;

namespace Oracul.Server.Models
{
    /// <summary>
    /// DTO for user profile data transfer
    /// </summary>
    public class UserProfileDto
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public bool IsPublic { get; set; }
        public int ProfileCompletionPercentage { get; set; }

        // Header Section
        public string? ProfilePhotoUrl { get; set; }
        public string? CoverPhotoUrl { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? ProfessionalTitle { get; set; }
        public string? Headline { get; set; }
        public ProfileLocationDto? Location { get; set; }

        // Contact Information
        public ContactInformationDto? ContactInfo { get; set; }

        // Professional Summary
        public string? Summary { get; set; }

        // Skills & Expertise
        public List<ProfileSkillDto> Skills { get; set; } = new();

        // Blog Posts
        public List<BlogPostDto> BlogPosts { get; set; } = new();

        // Achievements & Certifications
        public List<AchievementDto> Achievements { get; set; } = new();
        public List<CertificationDto> Certifications { get; set; } = new();

        // Experience Timeline
        public List<WorkExperienceDto> Experiences { get; set; } = new();

        // Portfolio/Projects
        public List<PortfolioItemDto> PortfolioItems { get; set; } = new();

        // Social Links
        public List<SocialLinkDto> SocialLinks { get; set; } = new();

        // Analytics
        public int ProfileViews { get; set; }
        public DateTime? LastViewedAt { get; set; }

        // Timestamps
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class ProfileLocationDto
    {
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? DisplayLocation { get; set; }
    }

    public class ContactInformationDto
    {
        public BusinessAddressDto? BusinessAddress { get; set; }
        public List<PhoneNumberDto> PhoneNumbers { get; set; } = new();
        public string? Email { get; set; }
        public bool IsEmailPublic { get; set; }
        public string? Website { get; set; }
        public string? PortfolioUrl { get; set; }
    }

    public class BusinessAddressDto
    {
        public string? Street { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PostalCode { get; set; }
        public string? Country { get; set; }
        public bool IsPublic { get; set; }
    }

    public class PhoneNumberDto
    {
        public int? Id { get; set; }
        public string Number { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // mobile, business, home
        public bool IsPublic { get; set; }
        public bool IsPrimary { get; set; }
    }

    public class ProfileSkillDto
    {
        public int? Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Category { get; set; }
        public int Endorsements { get; set; }
        public bool IsEndorsedByCurrentUser { get; set; }
        public string? ProficiencyLevel { get; set; } // beginner, intermediate, advanced, expert
    }

    public class BlogPostDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public string? Content { get; set; }
        public DateTime PublishedAt { get; set; }
        public int ReadCount { get; set; }
        public List<string> Tags { get; set; } = new();
        public string? FeaturedImageUrl { get; set; }
        public string Slug { get; set; } = string.Empty;
    }

    // Article DTOs for the new Article API
    public class ArticleDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public string? Content { get; set; }
        public string Author { get; set; } = string.Empty;
        public DateTime PublishedAt { get; set; }
        public int ReadTime { get; set; }
        public string Category { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public int ReadCount { get; set; }
        public List<string> Tags { get; set; } = new();
        public string? FeaturedImageUrl { get; set; }
        public int UserProfileId { get; set; }
    }

    public class ArticlePreviewDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public DateTime PublishedAt { get; set; }
        public int ReadTime { get; set; }
        public string Category { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public int ReadCount { get; set; }
        public List<string> Tags { get; set; } = new();
        public string? FeaturedImageUrl { get; set; }
    }

    public class ArticleListResponse
    {
        public List<ArticlePreviewDto> Articles { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class ArticleSearchRequest
    {
        public string? SearchTerm { get; set; }
        public string? Category { get; set; }
        public int? AuthorId { get; set; }
        public int? Page { get; set; }
        public int? PageSize { get; set; }
        public string? SortBy { get; set; }
        public string? SortOrder { get; set; }
    }

    // Article management DTOs
    public class CreateArticleRequest
    {
        public string Title { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public string? Content { get; set; }
        public string? Category { get; set; }
        public List<string> Tags { get; set; } = new();
        public string? FeaturedImageUrl { get; set; }
        public string? MetaDescription { get; set; }
        public string? MetaKeywords { get; set; }
        public bool AllowComments { get; set; } = true;
        public bool IsFeatured { get; set; } = false;
        public bool SaveAsDraft { get; set; } = true;
    }

    public class UpdateArticleRequest
    {
        public string Title { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public string? Content { get; set; }
        public string? Category { get; set; }
        public List<string> Tags { get; set; } = new();
        public string? FeaturedImageUrl { get; set; }
        public string? MetaDescription { get; set; }
        public string? MetaKeywords { get; set; }
        public bool AllowComments { get; set; } = true;
        public bool IsFeatured { get; set; } = false;
    }

    public class PublishArticleRequest
    {
        public DateTime? PublishAt { get; set; } // If null, publish immediately
    }

    public class ArticleManagementDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public string? Content { get; set; }
        public string? Category { get; set; }
        public List<string> Tags { get; set; } = new();
        public string? FeaturedImageUrl { get; set; }
        public string? MetaDescription { get; set; }
        public string? MetaKeywords { get; set; }
        public bool AllowComments { get; set; }
        public bool IsFeatured { get; set; }
        public string Status { get; set; } = string.Empty; // Draft, Published, Scheduled, Archived
        public DateTime? PublishedAt { get; set; }
        public DateTime? LastSavedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string Slug { get; set; } = string.Empty;
        public int ReadCount { get; set; }
        public int EstimatedReadTime { get; set; }
        public int UserProfileId { get; set; }
        public string Author { get; set; } = string.Empty;
    }

    public class AchievementDto
    {
        public int? Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime AchievedAt { get; set; }
        public string? Organization { get; set; }
        public string? ImageUrl { get; set; }
        public string? VerificationUrl { get; set; }
    }

    public class CertificationDto
    {
        public int? Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string IssuingOrganization { get; set; } = string.Empty;
        public DateTime IssueDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public string? CredentialId { get; set; }
        public string? CredentialUrl { get; set; }
        public string? ImageUrl { get; set; }
    }

    public class WorkExperienceDto
    {
        public int? Id { get; set; }
        public string Company { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsCurrent { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Location { get; set; }
        public string? CompanyLogoUrl { get; set; }
        public List<string> Achievements { get; set; } = new();
    }

    public class PortfolioItemDto
    {
        public int? Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> ImageUrls { get; set; } = new();
        public string? ProjectUrl { get; set; }
        public string? GithubUrl { get; set; }
        public List<string> Technologies { get; set; } = new();
        public DateTime CompletedAt { get; set; }
        public string? ClientName { get; set; }
        public ClientTestimonialDto? Testimonial { get; set; }
        public string? Category { get; set; }
    }

    public class ClientTestimonialDto
    {
        public int? Id { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public string? ClientTitle { get; set; }
        public string? ClientCompany { get; set; }
        public string TestimonialText { get; set; } = string.Empty;
        public int? Rating { get; set; }
        public string? ClientPhotoUrl { get; set; }
        public DateTime GivenAt { get; set; }
    }

    public class SocialLinkDto
    {
        public int? Id { get; set; }
        public string Platform { get; set; } = string.Empty; // linkedin, twitter, github, behance, dribbble, instagram, facebook, youtube, other
        public string Url { get; set; } = string.Empty;
        public string? DisplayName { get; set; }
        public bool IsPublic { get; set; }
    }

    // Request DTOs
    public class CreateProfileRequest
    {
        [Required]
        [MaxLength(100)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? ProfessionalTitle { get; set; }

        [MaxLength(500)]
        public string? Headline { get; set; }

        public bool IsPublic { get; set; } = true;
    }

    public class UpdateProfileRequest
    {
        [MaxLength(100)]
        public string? Username { get; set; }

        [MaxLength(100)]
        public string? FirstName { get; set; }

        [MaxLength(100)]
        public string? LastName { get; set; }

        [MaxLength(500)]
        public string? ProfilePhotoUrl { get; set; }

        [MaxLength(500)]
        public string? CoverPhotoUrl { get; set; }

        [MaxLength(200)]
        public string? ProfessionalTitle { get; set; }

        [MaxLength(500)]
        public string? Headline { get; set; }

        public string? Summary { get; set; }

        public bool? IsPublic { get; set; }

        public ProfileLocationDto? Location { get; set; }

        public ContactInformationDto? ContactInfo { get; set; }
    }

    public class ProfileSearchRequest
    {
        public string? SearchTerm { get; set; }
        public string? Location { get; set; }
        public List<string> Skills { get; set; } = new();
        public string? SortBy { get; set; } = "relevance"; // relevance, views, recent, alphabetical
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class ProfileSearchResult
    {
        public List<UserProfileDto> Profiles { get; set; } = new();
        public int TotalCount { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int PageSize { get; set; }
    }

    public class SkillEndorsementRequest
    {
        [Required]
        public int SkillId { get; set; }

        [Required]
        public int EndorsedUserId { get; set; }
    }

    public class ProfileViewRequest
    {
        [Required]
        public int ProfileId { get; set; }

        public int? ViewerUserId { get; set; }
        public string? Referrer { get; set; }
        public string? UserAgent { get; set; }
    }

    public class ProfileAnalyticsDto
    {
        public int ProfileViews { get; set; }
        public int UniqueVisitors { get; set; }
        public int ViewsThisMonth { get; set; }
        public int ViewsThisWeek { get; set; }
        public List<string> TopReferrers { get; set; } = new();
        public int SkillEndorsements { get; set; }
        public int BlogPostViews { get; set; }
        public int ContactButtonClicks { get; set; }
    }

    // Blob Storage Models
    public class BlobUploadResult
    {
        public bool Success { get; set; }
        public string? BlobUrl { get; set; }
        public string? BlobPath { get; set; }
        public string? FileName { get; set; }
        public long FileSize { get; set; }
        public string? ContentType { get; set; }
        public DateTime UploadedAt { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class FileValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class BlobStorageSettings
    {
        public string ConnectionString { get; set; } = string.Empty;
        public string ContainerName { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = string.Empty;
        public long MaxFileSizeBytes { get; set; }
        public string[] AllowedImageTypes { get; set; } = Array.Empty<string>();
        public string[] AllowedDocumentTypes { get; set; } = Array.Empty<string>();
    }
}
