import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { environment } from '../../../../environments/environment';

export interface ArticlePreviewData {
  title: string;
  excerpt: string;
  content?: string;
  category?: string;
  tags: string[];
  featuredImageUrl?: string;
  author: string;
  estimatedReadTime: number;
}

@Component({
  selector: 'app-article-preview-dialog',
  templateUrl: './article-preview-dialog.component.html',
  styleUrls: ['./article-preview-dialog.component.css']
})
export class ArticlePreviewDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ArticlePreviewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ArticlePreviewData
  ) {}

  onClose(): void {
    this.dialogRef.close();
  }

  calculateReadTime(content?: string): number {
    if (!content) return 0;

    // Remove HTML tags for word count
    const textContent = content.replace(/<[^>]*>/g, '');
    const words = textContent.trim().split(/\s+/).length;

    // Average reading speed is 200-250 words per minute
    const wordsPerMinute = 225;
    const readTime = Math.ceil(words / wordsPerMinute);

    return Math.max(1, readTime); // Minimum 1 minute
  }

  getReadTime(): number {
    return this.data.estimatedReadTime || this.calculateReadTime(this.data.content);
  }

  getImageUrl(): string {
    if (!this.data.featuredImageUrl) {
      return 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop';
    }

    if (this.data.featuredImageUrl.startsWith('http')) {
      return this.data.featuredImageUrl;
    }

    // Assume it's a relative path from our API
    return `${environment.apiUrl.replace('/api', '')}${this.data.featuredImageUrl}`;
  }
}
