import { TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { ThemeService } from './core/theme/theme.service';

describe('AppComponent', () => {
  let component: AppComponent;
  let mockThemeService: jasmine.SpyObj<ThemeService>;

  beforeEach(() => {
    const themeServiceSpy = jasmine.createSpyObj('ThemeService', ['getCurrentTheme']);

    TestBed.configureTestingModule({
      providers: [
        { provide: ThemeService, useValue: themeServiceSpy }
      ]
    });

    mockThemeService = TestBed.inject(ThemeService) as jasmine.SpyObj<ThemeService>;

    // Create component manually without rendering template
    component = new AppComponent(mockThemeService);
  });

  it('should create the app', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize theme on init', () => {
    spyOn(component, 'ngOnInit').and.callThrough();

    component.ngOnInit();

    expect(component.ngOnInit).toHaveBeenCalled();
  });
});
