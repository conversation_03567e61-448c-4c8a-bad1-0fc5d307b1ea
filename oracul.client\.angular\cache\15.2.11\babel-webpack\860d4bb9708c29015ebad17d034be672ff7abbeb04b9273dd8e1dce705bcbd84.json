{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Harmonia/oracul.client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { defaultModules, QUILL_CONFIG_TOKEN } from 'ngx-quill/config';\nexport * from 'ngx-quill/config';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, Inject, EventEmitter, SecurityContext, PLATFORM_ID, Directive, Input, Output, ElementRef, ChangeDetectorRef, Renderer2, NgZone, forwardRef, Component, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, isPlatformServer, CommonModule } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { defer, isObservable, firstValueFrom, Subscription, fromEvent } from 'rxjs';\nimport { shareReplay, mergeMap, debounceTime } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nfunction QuillEditorComponent_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nfunction QuillEditorComponent_ng_container_0_pre_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"pre\", 2);\n  }\n}\nfunction QuillEditorComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, QuillEditorComponent_ng_container_0_div_1_Template, 1, 0, \"div\", 1);\n    i0.ɵɵtemplate(2, QuillEditorComponent_ng_container_0_pre_2_Template, 1, 0, \"pre\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.preserve);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preserve);\n  }\n}\nfunction QuillEditorComponent_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nfunction QuillEditorComponent_ng_container_2_pre_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"pre\", 2);\n  }\n}\nfunction QuillEditorComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, QuillEditorComponent_ng_container_2_div_1_Template, 1, 0, \"div\", 1);\n    i0.ɵɵtemplate(2, QuillEditorComponent_ng_container_2_pre_2_Template, 1, 0, \"pre\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.preserve);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.preserve);\n  }\n}\nconst _c0 = [[[\"\", \"quill-editor-toolbar\", \"\"]]];\nconst _c1 = [\"[quill-editor-toolbar]\"];\nfunction QuillViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 1);\n  }\n}\nfunction QuillViewComponent_pre_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"pre\", 1);\n  }\n}\nconst getFormat = (format, configFormat) => {\n  const passedFormat = format || configFormat;\n  return passedFormat || 'html';\n};\nclass QuillService {\n  constructor(injector, config) {\n    var _this = this;\n    this.config = config;\n    this.quill$ = defer( /*#__PURE__*/_asyncToGenerator(function* () {\n      if (!_this.Quill) {\n        // Quill adds events listeners on import https://github.com/quilljs/quill/blob/develop/core/emitter.js#L8\n        // We'd want to use the unpatched `addEventListener` method to have all event callbacks to be run outside of zone.\n        // We don't know yet if the `zone.js` is used or not, just save the value to restore it back further.\n        const maybePatchedAddEventListener = _this.document.addEventListener;\n        // There're 2 types of Angular applications:\n        // 1) zone-full (by default)\n        // 2) zone-less\n        // The developer can avoid importing the `zone.js` package and tells Angular that he/she is responsible for running\n        // the change detection by himself. This is done by \"nooping\" the zone through `CompilerOptions` when bootstrapping\n        // the root module. We fallback to `document.addEventListener` if `__zone_symbol__addEventListener` is not defined,\n        // this means the `zone.js` is not imported.\n        // The `__zone_symbol__addEventListener` is basically a native DOM API, which is not patched by zone.js, thus not even going\n        // through the `zone.js` task lifecycle. You can also access the native DOM API as follows `target[Zone.__symbol__('methodName')]`.\n        // eslint-disable-next-line @typescript-eslint/dot-notation\n        _this.document.addEventListener = _this.document['__zone_symbol__addEventListener'] || _this.document.addEventListener;\n        const quillImport = yield import('quill');\n        _this.document.addEventListener = maybePatchedAddEventListener;\n        _this.Quill = quillImport.default ? quillImport.default : quillImport;\n      }\n      // Only register custom options and modules once\n      _this.config.customOptions?.forEach(customOption => {\n        const newCustomOption = _this.Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        _this.Quill.register(newCustomOption, true, _this.config.suppressGlobalRegisterWarning);\n      });\n      return yield _this.registerCustomModules(_this.Quill, _this.config.customModules, _this.config.suppressGlobalRegisterWarning);\n    })).pipe(shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n    this.document = injector.get(DOCUMENT);\n    if (!this.config) {\n      this.config = {\n        modules: defaultModules\n      };\n    }\n  }\n  getQuill() {\n    return this.quill$;\n  }\n  /**\n   * Marked as internal so it won't be available for `ngx-quill` consumers, this is only\n   * internal method to be used within the library.\n   *\n   * @internal\n   */\n  registerCustomModules(Quill, customModules, suppressGlobalRegisterWarning) {\n    return _asyncToGenerator(function* () {\n      if (Array.isArray(customModules)) {\n        // eslint-disable-next-line prefer-const\n        for (let {\n          implementation,\n          path\n        } of customModules) {\n          // The `implementation` might be an observable that resolves the actual implementation,\n          // e.g. if it should be lazy loaded.\n          if (isObservable(implementation)) {\n            implementation = yield firstValueFrom(implementation);\n          }\n          Quill.register(path, implementation, suppressGlobalRegisterWarning);\n        }\n      }\n      // Return `Quill` constructor so we'll be able to re-use its return value except of using\n      // `map` operators, etc.\n      return Quill;\n    })();\n  }\n}\nQuillService.ɵfac = function QuillService_Factory(t) {\n  return new (t || QuillService)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(QUILL_CONFIG_TOKEN, 8));\n};\nQuillService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: QuillService,\n  factory: QuillService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.Injector\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [QUILL_CONFIG_TOKEN]\n      }]\n    }];\n  }, null);\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass QuillEditorBase {\n  constructor(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service) {\n    this.elementRef = elementRef;\n    this.cd = cd;\n    this.domSanitizer = domSanitizer;\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.zone = zone;\n    this.service = service;\n    this.required = false;\n    this.customToolbarPosition = 'top';\n    this.styles = null;\n    this.strict = true;\n    this.customOptions = [];\n    this.customModules = [];\n    this.preserveWhitespace = false;\n    this.trimOnValidation = false;\n    this.compareValues = false;\n    this.filterNull = false;\n    /*\n    https://github.com/KillerCodeMonkey/ngx-quill/issues/1257 - fix null value set\n           provide default empty value\n    by default null\n           e.g. defaultEmptyValue=\"\" - empty string\n           <quill-editor\n      defaultEmptyValue=\"\"\n      formControlName=\"message\"\n    ></quill-editor>\n    */\n    this.defaultEmptyValue = null;\n    this.onEditorCreated = new EventEmitter();\n    this.onEditorChanged = new EventEmitter();\n    this.onContentChanged = new EventEmitter();\n    this.onSelectionChanged = new EventEmitter();\n    this.onFocus = new EventEmitter();\n    this.onBlur = new EventEmitter();\n    this.disabled = false; // used to store initial value before ViewInit\n    this.preserve = false;\n    this.toolbarPosition = 'top';\n    this.subscription = null;\n    this.quillSubscription = null;\n    this.valueGetter = (quillEditor, editorElement) => {\n      let html = editorElement.querySelector('.ql-editor').innerHTML;\n      if (html === '<p><br></p>' || html === '<div><br></div>') {\n        html = this.defaultEmptyValue;\n      }\n      let modelValue = html;\n      const format = getFormat(this.format, this.service.config.format);\n      if (format === 'text') {\n        modelValue = quillEditor.getText();\n      } else if (format === 'object') {\n        modelValue = quillEditor.getContents();\n      } else if (format === 'json') {\n        try {\n          modelValue = JSON.stringify(quillEditor.getContents());\n        } catch (e) {\n          modelValue = quillEditor.getText();\n        }\n      }\n      return modelValue;\n    };\n    this.valueSetter = (quillEditor, value) => {\n      const format = getFormat(this.format, this.service.config.format);\n      if (format === 'html') {\n        const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : this.service.config.sanitize || false;\n        if (sanitize) {\n          value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n        }\n        return quillEditor.clipboard.convert(value);\n      } else if (format === 'json') {\n        try {\n          return JSON.parse(value);\n        } catch (e) {\n          return [{\n            insert: value\n          }];\n        }\n      }\n      return value;\n    };\n    this.selectionChangeHandler = (range, oldRange, source) => {\n      const shouldTriggerOnModelTouched = !range && !!this.onModelTouched;\n      // only emit changes when there's any listener\n      if (!this.onBlur.observed && !this.onFocus.observed && !this.onSelectionChanged.observed && !shouldTriggerOnModelTouched) {\n        return;\n      }\n      this.zone.run(() => {\n        if (range === null) {\n          this.onBlur.emit({\n            editor: this.quillEditor,\n            source\n          });\n        } else if (oldRange === null) {\n          this.onFocus.emit({\n            editor: this.quillEditor,\n            source\n          });\n        }\n        this.onSelectionChanged.emit({\n          editor: this.quillEditor,\n          oldRange,\n          range,\n          source\n        });\n        if (shouldTriggerOnModelTouched) {\n          this.onModelTouched();\n        }\n        this.cd.markForCheck();\n      });\n    };\n    this.textChangeHandler = (delta, oldDelta, source) => {\n      // only emit changes emitted by user interactions\n      const text = this.quillEditor.getText();\n      const content = this.quillEditor.getContents();\n      let html = this.editorElem.querySelector('.ql-editor').innerHTML;\n      if (html === '<p><br></p>' || html === '<div><br></div>') {\n        html = this.defaultEmptyValue;\n      }\n      const trackChanges = this.trackChanges || this.service.config.trackChanges;\n      const shouldTriggerOnModelChange = (source === 'user' || trackChanges && trackChanges === 'all') && !!this.onModelChange;\n      // only emit changes when there's any listener\n      if (!this.onContentChanged.observed && !shouldTriggerOnModelChange) {\n        return;\n      }\n      this.zone.run(() => {\n        if (shouldTriggerOnModelChange) {\n          this.onModelChange(this.valueGetter(this.quillEditor, this.editorElem));\n        }\n        this.onContentChanged.emit({\n          content,\n          delta,\n          editor: this.quillEditor,\n          html,\n          oldDelta,\n          source,\n          text\n        });\n        this.cd.markForCheck();\n      });\n    };\n    // eslint-disable-next-line max-len\n    this.editorChangeHandler = (event, current, old, source) => {\n      // only emit changes when there's any listener\n      if (!this.onEditorChanged.observed) {\n        return;\n      }\n      // only emit changes emitted by user interactions\n      if (event === 'text-change') {\n        const text = this.quillEditor.getText();\n        const content = this.quillEditor.getContents();\n        let html = this.editorElem.querySelector('.ql-editor').innerHTML;\n        if (html === '<p><br></p>' || html === '<div><br></div>') {\n          html = this.defaultEmptyValue;\n        }\n        this.zone.run(() => {\n          this.onEditorChanged.emit({\n            content,\n            delta: current,\n            editor: this.quillEditor,\n            event,\n            html,\n            oldDelta: old,\n            source,\n            text\n          });\n          this.cd.markForCheck();\n        });\n      } else {\n        this.zone.run(() => {\n          this.onEditorChanged.emit({\n            editor: this.quillEditor,\n            event,\n            oldRange: old,\n            range: current,\n            source\n          });\n          this.cd.markForCheck();\n        });\n      }\n    };\n    this.document = injector.get(DOCUMENT);\n  }\n  static normalizeClassNames(classes) {\n    const classList = classes.trim().split(' ');\n    return classList.reduce((prev, cur) => {\n      const trimmed = cur.trim();\n      if (trimmed) {\n        prev.push(trimmed);\n      }\n      return prev;\n    }, []);\n  }\n  ngOnInit() {\n    this.preserve = this.preserveWhitespace;\n    this.toolbarPosition = this.customToolbarPosition;\n  }\n  ngAfterViewInit() {\n    if (isPlatformServer(this.platformId)) {\n      return;\n    }\n    // The `quill-editor` component might be destroyed before the `quill` chunk is loaded and its code is executed\n    // this will lead to runtime exceptions, since the code will be executed on DOM nodes that don't exist within the tree.\n    this.quillSubscription = this.service.getQuill().pipe(mergeMap(Quill => {\n      const promises = [this.service.registerCustomModules(Quill, this.customModules)];\n      const beforeRender = this.beforeRender ?? this.service.config.beforeRender;\n      if (beforeRender) {\n        promises.push(beforeRender());\n      }\n      return Promise.all(promises).then(() => Quill);\n    })).subscribe(Quill => {\n      this.editorElem = this.elementRef.nativeElement.querySelector('[quill-editor-element]');\n      const toolbarElem = this.elementRef.nativeElement.querySelector('[quill-editor-toolbar]');\n      const modules = Object.assign({}, this.modules || this.service.config.modules);\n      if (toolbarElem) {\n        modules.toolbar = toolbarElem;\n      } else if (modules.toolbar === undefined) {\n        modules.toolbar = defaultModules.toolbar;\n      }\n      let placeholder = this.placeholder !== undefined ? this.placeholder : this.service.config.placeholder;\n      if (placeholder === undefined) {\n        placeholder = 'Insert text here ...';\n      }\n      if (this.styles) {\n        Object.keys(this.styles).forEach(key => {\n          this.renderer.setStyle(this.editorElem, key, this.styles[key]);\n        });\n      }\n      if (this.classes) {\n        this.addClasses(this.classes);\n      }\n      this.customOptions.forEach(customOption => {\n        const newCustomOption = Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        Quill.register(newCustomOption, true);\n      });\n      let bounds = this.bounds && this.bounds === 'self' ? this.editorElem : this.bounds;\n      if (!bounds) {\n        bounds = this.service.config.bounds ? this.service.config.bounds : this.document.body;\n      }\n      let debug = this.debug;\n      if (!debug && debug !== false && this.service.config.debug) {\n        debug = this.service.config.debug;\n      }\n      let readOnly = this.readOnly;\n      if (!readOnly && this.readOnly !== false) {\n        readOnly = this.service.config.readOnly !== undefined ? this.service.config.readOnly : false;\n      }\n      let defaultEmptyValue = this.defaultEmptyValue;\n      if (this.service.config.hasOwnProperty('defaultEmptyValue')) {\n        defaultEmptyValue = this.service.config.defaultEmptyValue;\n      }\n      let scrollingContainer = this.scrollingContainer;\n      if (!scrollingContainer && this.scrollingContainer !== null) {\n        scrollingContainer = this.service.config.scrollingContainer === null || this.service.config.scrollingContainer ? this.service.config.scrollingContainer : null;\n      }\n      let formats = this.formats;\n      if (!formats && formats === undefined) {\n        formats = this.service.config.formats ? [...this.service.config.formats] : this.service.config.formats === null ? null : undefined;\n      }\n      this.zone.runOutsideAngular(() => {\n        this.quillEditor = new Quill(this.editorElem, {\n          bounds,\n          debug: debug,\n          formats: formats,\n          modules,\n          placeholder,\n          readOnly,\n          defaultEmptyValue,\n          scrollingContainer: scrollingContainer,\n          strict: this.strict,\n          theme: this.theme || (this.service.config.theme ? this.service.config.theme : 'snow')\n        });\n        // Set optional link placeholder, Quill has no native API for it so using workaround\n        if (this.linkPlaceholder) {\n          const tooltip = this.quillEditor?.theme?.tooltip;\n          const input = tooltip?.root?.querySelector('input[data-link]');\n          if (input?.dataset) {\n            input.dataset.link = this.linkPlaceholder;\n          }\n        }\n      });\n      if (this.content) {\n        const format = getFormat(this.format, this.service.config.format);\n        if (format === 'text') {\n          this.quillEditor.setText(this.content, 'silent');\n        } else {\n          const newValue = this.valueSetter(this.quillEditor, this.content);\n          this.quillEditor.setContents(newValue, 'silent');\n        }\n        this.quillEditor.getModule('history').clear();\n      }\n      // initialize disabled status based on this.disabled as default value\n      this.setDisabledState();\n      this.addQuillEventListeners();\n      // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n      // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n      if (!this.onEditorCreated.observed && !this.onValidatorChanged) {\n        return;\n      }\n      // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n      // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n      // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n      requestAnimationFrame(() => {\n        if (this.onValidatorChanged) {\n          this.onValidatorChanged();\n        }\n        this.onEditorCreated.emit(this.quillEditor);\n        this.onEditorCreated.complete();\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.dispose();\n    this.quillSubscription?.unsubscribe();\n    this.quillSubscription = null;\n  }\n  ngOnChanges(changes) {\n    if (!this.quillEditor) {\n      return;\n    }\n    /* eslint-disable @typescript-eslint/dot-notation */\n    if (changes.readOnly) {\n      this.quillEditor.enable(!changes.readOnly.currentValue);\n    }\n    if (changes.placeholder) {\n      this.quillEditor.root.dataset.placeholder = changes.placeholder.currentValue;\n    }\n    if (changes.defaultEmptyValue) {\n      this.quillEditor.root.dataset.defaultEmptyValue = changes.defaultEmptyValue.currentValue;\n    }\n    if (changes.styles) {\n      const currentStyling = changes.styles.currentValue;\n      const previousStyling = changes.styles.previousValue;\n      if (previousStyling) {\n        Object.keys(previousStyling).forEach(key => {\n          this.renderer.removeStyle(this.editorElem, key);\n        });\n      }\n      if (currentStyling) {\n        Object.keys(currentStyling).forEach(key => {\n          this.renderer.setStyle(this.editorElem, key, this.styles[key]);\n        });\n      }\n    }\n    if (changes.classes) {\n      const currentClasses = changes.classes.currentValue;\n      const previousClasses = changes.classes.previousValue;\n      if (previousClasses) {\n        this.removeClasses(previousClasses);\n      }\n      if (currentClasses) {\n        this.addClasses(currentClasses);\n      }\n    }\n    // We'd want to re-apply event listeners if the `debounceTime` binding changes to apply the\n    // `debounceTime` operator or vice-versa remove it.\n    if (changes.debounceTime) {\n      this.addQuillEventListeners();\n    }\n    /* eslint-enable @typescript-eslint/dot-notation */\n  }\n\n  addClasses(classList) {\n    QuillEditorBase.normalizeClassNames(classList).forEach(c => {\n      this.renderer.addClass(this.editorElem, c);\n    });\n  }\n  removeClasses(classList) {\n    QuillEditorBase.normalizeClassNames(classList).forEach(c => {\n      this.renderer.removeClass(this.editorElem, c);\n    });\n  }\n  writeValue(currentValue) {\n    // optional fix for https://github.com/angular/angular/issues/14988\n    if (this.filterNull && currentValue === null) {\n      return;\n    }\n    this.content = currentValue;\n    if (!this.quillEditor) {\n      return;\n    }\n    const format = getFormat(this.format, this.service.config.format);\n    const newValue = this.valueSetter(this.quillEditor, currentValue);\n    if (this.compareValues) {\n      const currentEditorValue = this.quillEditor.getContents();\n      if (JSON.stringify(currentEditorValue) === JSON.stringify(newValue)) {\n        return;\n      }\n    }\n    if (currentValue) {\n      if (format === 'text') {\n        this.quillEditor.setText(currentValue);\n      } else {\n        this.quillEditor.setContents(newValue);\n      }\n      return;\n    }\n    this.quillEditor.setText('');\n  }\n  setDisabledState(isDisabled = this.disabled) {\n    // store initial value to set appropriate disabled status after ViewInit\n    this.disabled = isDisabled;\n    if (this.quillEditor) {\n      if (isDisabled) {\n        this.quillEditor.disable();\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'disabled', 'disabled');\n      } else {\n        if (!this.readOnly) {\n          this.quillEditor.enable();\n        }\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'disabled');\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  registerOnValidatorChange(fn) {\n    this.onValidatorChanged = fn;\n  }\n  validate() {\n    if (!this.quillEditor) {\n      return null;\n    }\n    const err = {};\n    let valid = true;\n    const text = this.quillEditor.getText();\n    // trim text if wanted + handle special case that an empty editor contains a new line\n    const textLength = this.trimOnValidation ? text.trim().length : text.length === 1 && text.trim().length === 0 ? 0 : text.length - 1;\n    const deltaOperations = this.quillEditor.getContents().ops;\n    const onlyEmptyOperation = deltaOperations && deltaOperations.length === 1 && ['\\n', ''].includes(deltaOperations[0].insert);\n    if (this.minLength && textLength && textLength < this.minLength) {\n      err.minLengthError = {\n        given: textLength,\n        minLength: this.minLength\n      };\n      valid = false;\n    }\n    if (this.maxLength && textLength > this.maxLength) {\n      err.maxLengthError = {\n        given: textLength,\n        maxLength: this.maxLength\n      };\n      valid = false;\n    }\n    if (this.required && !textLength && onlyEmptyOperation) {\n      err.requiredError = {\n        empty: true\n      };\n      valid = false;\n    }\n    return valid ? null : err;\n  }\n  addQuillEventListeners() {\n    this.dispose();\n    // We have to enter the `<root>` zone when adding event listeners, so `debounceTime` will spawn the\n    // `AsyncAction` there w/o triggering change detections. We still re-enter the Angular's zone through\n    // `zone.run` when we emit an event to the parent component.\n    this.zone.runOutsideAngular(() => {\n      this.subscription = new Subscription();\n      this.subscription.add(\n      // mark model as touched if editor lost focus\n      fromEvent(this.quillEditor, 'selection-change').subscribe(([range, oldRange, source]) => {\n        this.selectionChangeHandler(range, oldRange, source);\n      }));\n      // The `fromEvent` supports passing JQuery-style event targets, the editor has `on` and `off` methods which\n      // will be invoked upon subscription and teardown.\n      let textChange$ = fromEvent(this.quillEditor, 'text-change');\n      let editorChange$ = fromEvent(this.quillEditor, 'editor-change');\n      if (typeof this.debounceTime === 'number') {\n        textChange$ = textChange$.pipe(debounceTime(this.debounceTime));\n        editorChange$ = editorChange$.pipe(debounceTime(this.debounceTime));\n      }\n      this.subscription.add(\n      // update model if text changes\n      textChange$.subscribe(([delta, oldDelta, source]) => {\n        this.textChangeHandler(delta, oldDelta, source);\n      }));\n      this.subscription.add(\n      // triggered if selection or text changed\n      editorChange$.subscribe(([event, current, old, source]) => {\n        this.editorChangeHandler(event, current, old, source);\n      }));\n    });\n  }\n  dispose() {\n    if (this.subscription !== null) {\n      this.subscription.unsubscribe();\n      this.subscription = null;\n    }\n  }\n}\nQuillEditorBase.ɵfac = function QuillEditorBase_Factory(t) {\n  return new (t || QuillEditorBase)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(QuillService));\n};\nQuillEditorBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: QuillEditorBase,\n  inputs: {\n    format: \"format\",\n    theme: \"theme\",\n    modules: \"modules\",\n    debug: \"debug\",\n    readOnly: \"readOnly\",\n    placeholder: \"placeholder\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    required: \"required\",\n    formats: \"formats\",\n    customToolbarPosition: \"customToolbarPosition\",\n    sanitize: \"sanitize\",\n    beforeRender: \"beforeRender\",\n    styles: \"styles\",\n    strict: \"strict\",\n    scrollingContainer: \"scrollingContainer\",\n    bounds: \"bounds\",\n    customOptions: \"customOptions\",\n    customModules: \"customModules\",\n    trackChanges: \"trackChanges\",\n    preserveWhitespace: \"preserveWhitespace\",\n    classes: \"classes\",\n    trimOnValidation: \"trimOnValidation\",\n    linkPlaceholder: \"linkPlaceholder\",\n    compareValues: \"compareValues\",\n    filterNull: \"filterNull\",\n    debounceTime: \"debounceTime\",\n    defaultEmptyValue: \"defaultEmptyValue\",\n    valueGetter: \"valueGetter\",\n    valueSetter: \"valueSetter\"\n  },\n  outputs: {\n    onEditorCreated: \"onEditorCreated\",\n    onEditorChanged: \"onEditorChanged\",\n    onContentChanged: \"onContentChanged\",\n    onSelectionChanged: \"onSelectionChanged\",\n    onFocus: \"onFocus\",\n    onBlur: \"onBlur\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillEditorBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.Injector\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.DomSanitizer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.NgZone\n    }, {\n      type: QuillService\n    }];\n  }, {\n    format: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    modules: [{\n      type: Input\n    }],\n    debug: [{\n      type: Input\n    }],\n    readOnly: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    maxLength: [{\n      type: Input\n    }],\n    minLength: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    formats: [{\n      type: Input\n    }],\n    customToolbarPosition: [{\n      type: Input\n    }],\n    sanitize: [{\n      type: Input\n    }],\n    beforeRender: [{\n      type: Input\n    }],\n    styles: [{\n      type: Input\n    }],\n    strict: [{\n      type: Input\n    }],\n    scrollingContainer: [{\n      type: Input\n    }],\n    bounds: [{\n      type: Input\n    }],\n    customOptions: [{\n      type: Input\n    }],\n    customModules: [{\n      type: Input\n    }],\n    trackChanges: [{\n      type: Input\n    }],\n    preserveWhitespace: [{\n      type: Input\n    }],\n    classes: [{\n      type: Input\n    }],\n    trimOnValidation: [{\n      type: Input\n    }],\n    linkPlaceholder: [{\n      type: Input\n    }],\n    compareValues: [{\n      type: Input\n    }],\n    filterNull: [{\n      type: Input\n    }],\n    debounceTime: [{\n      type: Input\n    }],\n    defaultEmptyValue: [{\n      type: Input\n    }],\n    onEditorCreated: [{\n      type: Output\n    }],\n    onEditorChanged: [{\n      type: Output\n    }],\n    onContentChanged: [{\n      type: Output\n    }],\n    onSelectionChanged: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    valueGetter: [{\n      type: Input\n    }],\n    valueSetter: [{\n      type: Input\n    }]\n  });\n})();\nclass QuillEditorComponent extends QuillEditorBase {\n  constructor(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service) {\n    super(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service);\n  }\n}\nQuillEditorComponent.ɵfac = function QuillEditorComponent_Factory(t) {\n  return new (t || QuillEditorComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(ElementRef), i0.ɵɵdirectiveInject(ChangeDetectorRef), i0.ɵɵdirectiveInject(DomSanitizer), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(Renderer2), i0.ɵɵdirectiveInject(NgZone), i0.ɵɵdirectiveInject(QuillService));\n};\nQuillEditorComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: QuillEditorComponent,\n  selectors: [[\"quill-editor\"]],\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    multi: true,\n    provide: NG_VALUE_ACCESSOR,\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    useExisting: forwardRef(() => QuillEditorComponent)\n  }, {\n    multi: true,\n    provide: NG_VALIDATORS,\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    useExisting: forwardRef(() => QuillEditorComponent)\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n  ngContentSelectors: _c1,\n  decls: 3,\n  vars: 2,\n  consts: [[4, \"ngIf\"], [\"quill-editor-element\", \"\", 4, \"ngIf\"], [\"quill-editor-element\", \"\"]],\n  template: function QuillEditorComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵtemplate(0, QuillEditorComponent_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵtemplate(2, QuillEditorComponent_ng_container_2_Template, 3, 2, \"ng-container\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.toolbarPosition !== \"top\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.toolbarPosition === \"top\");\n    }\n  },\n  dependencies: [CommonModule, i3.NgIf],\n  styles: [\"[_nghost-%COMP%]{display:inline-block}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillEditorComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.Emulated,\n      providers: [{\n        multi: true,\n        provide: NG_VALUE_ACCESSOR,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }, {\n        multi: true,\n        provide: NG_VALIDATORS,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        useExisting: forwardRef(() => QuillEditorComponent)\n      }],\n      selector: 'quill-editor',\n      template: `\n  <ng-container *ngIf=\"toolbarPosition !== 'top'\">\n    <div quill-editor-element *ngIf=\"!preserve\"></div>\n    <pre quill-editor-element *ngIf=\"preserve\"></pre>\n  </ng-container>\n  <ng-content select=\"[quill-editor-toolbar]\"></ng-content>\n  <ng-container *ngIf=\"toolbarPosition === 'top'\">\n    <div quill-editor-element *ngIf=\"!preserve\"></div>\n    <pre quill-editor-element *ngIf=\"preserve\"></pre>\n  </ng-container>\n`,\n      standalone: true,\n      imports: [CommonModule],\n      styles: [\":host{display:inline-block}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.Injector\n    }, {\n      type: i0.ElementRef,\n      decorators: [{\n        type: Inject,\n        args: [ElementRef]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef,\n      decorators: [{\n        type: Inject,\n        args: [ChangeDetectorRef]\n      }]\n    }, {\n      type: i1.DomSanitizer,\n      decorators: [{\n        type: Inject,\n        args: [DomSanitizer]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.Renderer2,\n      decorators: [{\n        type: Inject,\n        args: [Renderer2]\n      }]\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Inject,\n        args: [NgZone]\n      }]\n    }, {\n      type: QuillService,\n      decorators: [{\n        type: Inject,\n        args: [QuillService]\n      }]\n    }];\n  }, null);\n})();\nclass QuillViewHTMLComponent {\n  constructor(sanitizer, service) {\n    this.sanitizer = sanitizer;\n    this.service = service;\n    this.content = '';\n    this.innerHTML = '';\n    this.themeClass = 'ql-snow';\n  }\n  ngOnChanges(changes) {\n    if (changes.theme) {\n      const theme = changes.theme.currentValue || (this.service.config.theme ? this.service.config.theme : 'snow');\n      this.themeClass = `ql-${theme} ngx-quill-view-html`;\n    } else if (!this.theme) {\n      const theme = this.service.config.theme ? this.service.config.theme : 'snow';\n      this.themeClass = `ql-${theme} ngx-quill-view-html`;\n    }\n    if (changes.content) {\n      const content = changes.content.currentValue;\n      const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : this.service.config.sanitize || false;\n      this.innerHTML = sanitize ? content : this.sanitizer.bypassSecurityTrustHtml(content);\n    }\n  }\n}\nQuillViewHTMLComponent.ɵfac = function QuillViewHTMLComponent_Factory(t) {\n  return new (t || QuillViewHTMLComponent)(i0.ɵɵdirectiveInject(DomSanitizer), i0.ɵɵdirectiveInject(QuillService));\n};\nQuillViewHTMLComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: QuillViewHTMLComponent,\n  selectors: [[\"quill-view-html\"]],\n  inputs: {\n    content: \"content\",\n    theme: \"theme\",\n    sanitize: \"sanitize\"\n  },\n  standalone: true,\n  features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n  decls: 2,\n  vars: 2,\n  consts: [[1, \"ql-container\", 3, \"ngClass\"], [1, \"ql-editor\", 3, \"innerHTML\"]],\n  template: function QuillViewHTMLComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelement(1, \"div\", 1);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", ctx.themeClass);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"innerHTML\", ctx.innerHTML, i0.ɵɵsanitizeHtml);\n    }\n  },\n  dependencies: [CommonModule, i3.NgClass],\n  styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillViewHTMLComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      selector: 'quill-view-html',\n      template: `\n  <div class=\"ql-container\" [ngClass]=\"themeClass\">\n    <div class=\"ql-editor\" [innerHTML]=\"innerHTML\">\n    </div>\n  </div>\n`,\n      standalone: true,\n      imports: [CommonModule],\n      styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i1.DomSanitizer,\n      decorators: [{\n        type: Inject,\n        args: [DomSanitizer]\n      }]\n    }, {\n      type: QuillService\n    }];\n  }, {\n    content: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    sanitize: [{\n      type: Input\n    }]\n  });\n})();\nclass QuillViewComponent {\n  constructor(elementRef, renderer, zone, service, domSanitizer, platformId) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.zone = zone;\n    this.service = service;\n    this.domSanitizer = domSanitizer;\n    this.platformId = platformId;\n    this.strict = true;\n    this.customModules = [];\n    this.customOptions = [];\n    this.preserveWhitespace = false;\n    this.onEditorCreated = new EventEmitter();\n    this.preserve = false;\n    this.quillSubscription = null;\n    this.valueSetter = (quillEditor, value) => {\n      const format = getFormat(this.format, this.service.config.format);\n      let content = value;\n      if (format === 'text') {\n        quillEditor.setText(content);\n      } else {\n        if (format === 'html') {\n          const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : this.service.config.sanitize || false;\n          if (sanitize) {\n            value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n          }\n          content = quillEditor.clipboard.convert(value);\n        } else if (format === 'json') {\n          try {\n            content = JSON.parse(value);\n          } catch (e) {\n            content = [{\n              insert: value\n            }];\n          }\n        }\n        quillEditor.setContents(content);\n      }\n    };\n  }\n  ngOnInit() {\n    this.preserve = this.preserveWhitespace;\n  }\n  ngOnChanges(changes) {\n    if (!this.quillEditor) {\n      return;\n    }\n    if (changes.content) {\n      this.valueSetter(this.quillEditor, changes.content.currentValue);\n    }\n  }\n  ngAfterViewInit() {\n    if (isPlatformServer(this.platformId)) {\n      return;\n    }\n    this.quillSubscription = this.service.getQuill().pipe(mergeMap(Quill => {\n      const promises = [this.service.registerCustomModules(Quill, this.customModules)];\n      const beforeRender = this.beforeRender ?? this.service.config.beforeRender;\n      if (beforeRender) {\n        promises.push(beforeRender());\n      }\n      return Promise.all(promises).then(() => Quill);\n    })).subscribe(Quill => {\n      const modules = Object.assign({}, this.modules || this.service.config.modules);\n      modules.toolbar = false;\n      this.customOptions.forEach(customOption => {\n        const newCustomOption = Quill.import(customOption.import);\n        newCustomOption.whitelist = customOption.whitelist;\n        Quill.register(newCustomOption, true);\n      });\n      let debug = this.debug;\n      if (!debug && debug !== false && this.service.config.debug) {\n        debug = this.service.config.debug;\n      }\n      let formats = this.formats;\n      if (!formats && formats === undefined) {\n        formats = this.service.config.formats ? Object.assign({}, this.service.config.formats) : this.service.config.formats === null ? null : undefined;\n      }\n      const theme = this.theme || (this.service.config.theme ? this.service.config.theme : 'snow');\n      this.editorElem = this.elementRef.nativeElement.querySelector('[quill-view-element]');\n      this.zone.runOutsideAngular(() => {\n        this.quillEditor = new Quill(this.editorElem, {\n          debug: debug,\n          formats: formats,\n          modules,\n          readOnly: true,\n          strict: this.strict,\n          theme\n        });\n      });\n      this.renderer.addClass(this.editorElem, 'ngx-quill-view');\n      if (this.content) {\n        this.valueSetter(this.quillEditor, this.content);\n      }\n      // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n      // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n      if (!this.onEditorCreated.observers.length) {\n        return;\n      }\n      // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n      // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n      // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n      requestAnimationFrame(() => {\n        this.onEditorCreated.emit(this.quillEditor);\n        this.onEditorCreated.complete();\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.quillSubscription?.unsubscribe();\n    this.quillSubscription = null;\n  }\n}\nQuillViewComponent.ɵfac = function QuillViewComponent_Factory(t) {\n  return new (t || QuillViewComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(QuillService), i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(PLATFORM_ID));\n};\nQuillViewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: QuillViewComponent,\n  selectors: [[\"quill-view\"]],\n  inputs: {\n    format: \"format\",\n    theme: \"theme\",\n    modules: \"modules\",\n    debug: \"debug\",\n    formats: \"formats\",\n    sanitize: \"sanitize\",\n    beforeRender: \"beforeRender\",\n    strict: \"strict\",\n    content: \"content\",\n    customModules: \"customModules\",\n    customOptions: \"customOptions\",\n    preserveWhitespace: \"preserveWhitespace\"\n  },\n  outputs: {\n    onEditorCreated: \"onEditorCreated\"\n  },\n  standalone: true,\n  features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n  decls: 2,\n  vars: 2,\n  consts: [[\"quill-view-element\", \"\", 4, \"ngIf\"], [\"quill-view-element\", \"\"]],\n  template: function QuillViewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, QuillViewComponent_div_0_Template, 1, 0, \"div\", 0);\n      i0.ɵɵtemplate(1, QuillViewComponent_pre_1_Template, 1, 0, \"pre\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.preserve);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.preserve);\n    }\n  },\n  dependencies: [CommonModule, i3.NgIf],\n  styles: [\".ql-container.ngx-quill-view{border:0}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillViewComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      selector: 'quill-view',\n      template: `\n<div quill-view-element *ngIf=\"!preserve\"></div>\n<pre quill-view-element *ngIf=\"preserve\"></pre>\n`,\n      standalone: true,\n      imports: [CommonModule],\n      styles: [\".ql-container.ngx-quill-view{border:0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.NgZone\n    }, {\n      type: QuillService\n    }, {\n      type: i1.DomSanitizer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, {\n    format: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    modules: [{\n      type: Input\n    }],\n    debug: [{\n      type: Input\n    }],\n    formats: [{\n      type: Input\n    }],\n    sanitize: [{\n      type: Input\n    }],\n    beforeRender: [{\n      type: Input\n    }],\n    strict: [{\n      type: Input\n    }],\n    content: [{\n      type: Input\n    }],\n    customModules: [{\n      type: Input\n    }],\n    customOptions: [{\n      type: Input\n    }],\n    preserveWhitespace: [{\n      type: Input\n    }],\n    onEditorCreated: [{\n      type: Output\n    }]\n  });\n})();\nclass QuillModule {\n  static forRoot(config) {\n    return {\n      ngModule: QuillModule,\n      providers: [{\n        provide: QUILL_CONFIG_TOKEN,\n        useValue: config\n      }]\n    };\n  }\n}\nQuillModule.ɵfac = function QuillModule_Factory(t) {\n  return new (t || QuillModule)();\n};\nQuillModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: QuillModule\n});\nQuillModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QuillModule, [{\n    type: NgModule,\n    args: [{\n      imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent],\n      exports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ngx-quill\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QuillEditorBase, QuillEditorComponent, QuillModule, QuillService, QuillViewComponent, QuillViewHTMLComponent };", "map": {"version": 3, "names": ["defaultModules", "QUILL_CONFIG_TOKEN", "i0", "Injectable", "Optional", "Inject", "EventEmitter", "SecurityContext", "PLATFORM_ID", "Directive", "Input", "Output", "ElementRef", "ChangeDetectorRef", "Renderer2", "NgZone", "forwardRef", "Component", "ViewEncapsulation", "NgModule", "i3", "DOCUMENT", "isPlatformServer", "CommonModule", "i1", "Dom<PERSON><PERSON><PERSON>zer", "defer", "isObservable", "firstValueFrom", "Subscription", "fromEvent", "shareReplay", "mergeMap", "debounceTime", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "getFormat", "format", "configFormat", "passedFormat", "QuillService", "constructor", "injector", "config", "quill$", "<PERSON><PERSON><PERSON>", "maybePatchedAddEventListener", "document", "addEventListener", "quillImport", "default", "customOptions", "for<PERSON>ach", "customOption", "newCustomOption", "import", "whitelist", "register", "suppressGlobalRegisterWarning", "registerCustomModules", "customModules", "pipe", "bufferSize", "refCount", "get", "modules", "getQuill", "Array", "isArray", "implementation", "path", "ɵfac", "Injector", "ɵprov", "type", "args", "providedIn", "undefined", "decorators", "QuillEditorBase", "elementRef", "cd", "domSani<PERSON>zer", "platformId", "renderer", "zone", "service", "required", "customToolbarPosition", "styles", "strict", "preserveWhitespace", "trimOnValidation", "compareValues", "filterNull", "defaultEmptyValue", "onEditorCreated", "onEditorChanged", "onContentChanged", "onSelectionChanged", "onFocus", "onBlur", "disabled", "preserve", "toolbarPosition", "subscription", "quillSubscription", "valueGetter", "quill<PERSON><PERSON>or", "editor<PERSON><PERSON>", "html", "querySelector", "innerHTML", "modelValue", "getText", "getContents", "JSON", "stringify", "e", "valueSetter", "value", "sanitize", "includes", "HTML", "clipboard", "convert", "parse", "insert", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "range", "oldRange", "source", "shouldTriggerOnModelTouched", "onModelTouched", "observed", "run", "emit", "editor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delta", "<PERSON><PERSON><PERSON><PERSON>", "text", "content", "editor<PERSON><PERSON>", "trackChanges", "shouldTriggerOnModelChange", "onModelChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "current", "old", "normalizeClassNames", "classes", "classList", "trim", "split", "reduce", "prev", "cur", "trimmed", "push", "ngOnInit", "ngAfterViewInit", "promises", "beforeRender", "Promise", "all", "then", "subscribe", "nativeElement", "toolbarElem", "Object", "assign", "toolbar", "placeholder", "keys", "key", "setStyle", "addClasses", "bounds", "body", "debug", "readOnly", "hasOwnProperty", "scrollingContainer", "formats", "runOutsideAngular", "theme", "linkPlaceholder", "tooltip", "input", "root", "dataset", "link", "setText", "newValue", "setContents", "getModule", "clear", "setDisabledState", "addQuillEventListeners", "onValidatorChanged", "requestAnimationFrame", "complete", "ngOnDestroy", "dispose", "unsubscribe", "ngOnChanges", "changes", "enable", "currentValue", "currentStyling", "previousStyling", "previousValue", "removeStyle", "currentClasses", "previousClasses", "removeClasses", "c", "addClass", "removeClass", "writeValue", "currentEditorValue", "isDisabled", "disable", "setAttribute", "removeAttribute", "registerOnChange", "fn", "registerOnTouched", "registerOnValidatorChange", "validate", "err", "valid", "textLength", "length", "deltaOperations", "ops", "onlyEmptyOperation", "<PERSON><PERSON><PERSON><PERSON>", "minLengthError", "given", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON>th<PERSON><PERSON><PERSON>", "requiredError", "empty", "add", "textChange$", "editorChange$", "ɵdir", "QuillEditorComponent", "ɵcmp", "multi", "provide", "useExisting", "NgIf", "encapsulation", "Emulated", "providers", "selector", "template", "standalone", "imports", "QuillViewHTMLComponent", "sanitizer", "themeClass", "bypassSecurityTrustHtml", "Ng<PERSON><PERSON>", "None", "QuillViewComponent", "observers", "QuillModule", "forRoot", "ngModule", "useValue", "ɵmod", "ɵinj", "exports"], "sources": ["C:/Projects/Harmonia/oracul.client/node_modules/ngx-quill/fesm2020/ngx-quill.mjs"], "sourcesContent": ["import { defaultModules, QUILL_CONFIG_TOKEN } from 'ngx-quill/config';\nexport * from 'ngx-quill/config';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, Inject, EventEmitter, SecurityContext, PLATFORM_ID, Directive, Input, Output, ElementRef, ChangeDetectorRef, Renderer2, NgZone, forwardRef, Component, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, isPlatformServer, CommonModule } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { defer, isObservable, firstValueFrom, Subscription, fromEvent } from 'rxjs';\nimport { shareReplay, mergeMap, debounceTime } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\n\nconst getFormat = (format, configFormat) => {\n    const passedFormat = format || configFormat;\n    return passedFormat || 'html';\n};\n\nclass QuillService {\n    constructor(injector, config) {\n        this.config = config;\n        this.quill$ = defer(async () => {\n            if (!this.Quill) {\n                // Quill adds events listeners on import https://github.com/quilljs/quill/blob/develop/core/emitter.js#L8\n                // We'd want to use the unpatched `addEventListener` method to have all event callbacks to be run outside of zone.\n                // We don't know yet if the `zone.js` is used or not, just save the value to restore it back further.\n                const maybePatchedAddEventListener = this.document.addEventListener;\n                // There're 2 types of Angular applications:\n                // 1) zone-full (by default)\n                // 2) zone-less\n                // The developer can avoid importing the `zone.js` package and tells Angular that he/she is responsible for running\n                // the change detection by himself. This is done by \"nooping\" the zone through `CompilerOptions` when bootstrapping\n                // the root module. We fallback to `document.addEventListener` if `__zone_symbol__addEventListener` is not defined,\n                // this means the `zone.js` is not imported.\n                // The `__zone_symbol__addEventListener` is basically a native DOM API, which is not patched by zone.js, thus not even going\n                // through the `zone.js` task lifecycle. You can also access the native DOM API as follows `target[Zone.__symbol__('methodName')]`.\n                // eslint-disable-next-line @typescript-eslint/dot-notation\n                this.document.addEventListener = this.document['__zone_symbol__addEventListener'] || this.document.addEventListener;\n                const quillImport = await import('quill');\n                this.document.addEventListener = maybePatchedAddEventListener;\n                this.Quill = (quillImport.default ? quillImport.default : quillImport);\n            }\n            // Only register custom options and modules once\n            this.config.customOptions?.forEach((customOption) => {\n                const newCustomOption = this.Quill.import(customOption.import);\n                newCustomOption.whitelist = customOption.whitelist;\n                this.Quill.register(newCustomOption, true, this.config.suppressGlobalRegisterWarning);\n            });\n            return await this.registerCustomModules(this.Quill, this.config.customModules, this.config.suppressGlobalRegisterWarning);\n        }).pipe(shareReplay({ bufferSize: 1, refCount: true }));\n        this.document = injector.get(DOCUMENT);\n        if (!this.config) {\n            this.config = { modules: defaultModules };\n        }\n    }\n    getQuill() {\n        return this.quill$;\n    }\n    /**\n     * Marked as internal so it won't be available for `ngx-quill` consumers, this is only\n     * internal method to be used within the library.\n     *\n     * @internal\n     */\n    async registerCustomModules(Quill, customModules, suppressGlobalRegisterWarning) {\n        if (Array.isArray(customModules)) {\n            // eslint-disable-next-line prefer-const\n            for (let { implementation, path } of customModules) {\n                // The `implementation` might be an observable that resolves the actual implementation,\n                // e.g. if it should be lazy loaded.\n                if (isObservable(implementation)) {\n                    implementation = await firstValueFrom(implementation);\n                }\n                Quill.register(path, implementation, suppressGlobalRegisterWarning);\n            }\n        }\n        // Return `Quill` constructor so we'll be able to re-use its return value except of using\n        // `map` operators, etc.\n        return Quill;\n    }\n}\nQuillService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillService, deps: [{ token: i0.Injector }, { token: QUILL_CONFIG_TOKEN, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nQuillService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Injector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [QUILL_CONFIG_TOKEN]\n                }] }]; } });\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass QuillEditorBase {\n    constructor(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service) {\n        this.elementRef = elementRef;\n        this.cd = cd;\n        this.domSanitizer = domSanitizer;\n        this.platformId = platformId;\n        this.renderer = renderer;\n        this.zone = zone;\n        this.service = service;\n        this.required = false;\n        this.customToolbarPosition = 'top';\n        this.styles = null;\n        this.strict = true;\n        this.customOptions = [];\n        this.customModules = [];\n        this.preserveWhitespace = false;\n        this.trimOnValidation = false;\n        this.compareValues = false;\n        this.filterNull = false;\n        /*\n        https://github.com/KillerCodeMonkey/ngx-quill/issues/1257 - fix null value set\n      \n        provide default empty value\n        by default null\n      \n        e.g. defaultEmptyValue=\"\" - empty string\n      \n        <quill-editor\n          defaultEmptyValue=\"\"\n          formControlName=\"message\"\n        ></quill-editor>\n        */\n        this.defaultEmptyValue = null;\n        this.onEditorCreated = new EventEmitter();\n        this.onEditorChanged = new EventEmitter();\n        this.onContentChanged = new EventEmitter();\n        this.onSelectionChanged = new EventEmitter();\n        this.onFocus = new EventEmitter();\n        this.onBlur = new EventEmitter();\n        this.disabled = false; // used to store initial value before ViewInit\n        this.preserve = false;\n        this.toolbarPosition = 'top';\n        this.subscription = null;\n        this.quillSubscription = null;\n        this.valueGetter = (quillEditor, editorElement) => {\n            let html = editorElement.querySelector('.ql-editor').innerHTML;\n            if (html === '<p><br></p>' || html === '<div><br></div>') {\n                html = this.defaultEmptyValue;\n            }\n            let modelValue = html;\n            const format = getFormat(this.format, this.service.config.format);\n            if (format === 'text') {\n                modelValue = quillEditor.getText();\n            }\n            else if (format === 'object') {\n                modelValue = quillEditor.getContents();\n            }\n            else if (format === 'json') {\n                try {\n                    modelValue = JSON.stringify(quillEditor.getContents());\n                }\n                catch (e) {\n                    modelValue = quillEditor.getText();\n                }\n            }\n            return modelValue;\n        };\n        this.valueSetter = (quillEditor, value) => {\n            const format = getFormat(this.format, this.service.config.format);\n            if (format === 'html') {\n                const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : (this.service.config.sanitize || false);\n                if (sanitize) {\n                    value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n                }\n                return quillEditor.clipboard.convert(value);\n            }\n            else if (format === 'json') {\n                try {\n                    return JSON.parse(value);\n                }\n                catch (e) {\n                    return [{ insert: value }];\n                }\n            }\n            return value;\n        };\n        this.selectionChangeHandler = (range, oldRange, source) => {\n            const shouldTriggerOnModelTouched = !range && !!this.onModelTouched;\n            // only emit changes when there's any listener\n            if (!this.onBlur.observed &&\n                !this.onFocus.observed &&\n                !this.onSelectionChanged.observed &&\n                !shouldTriggerOnModelTouched) {\n                return;\n            }\n            this.zone.run(() => {\n                if (range === null) {\n                    this.onBlur.emit({\n                        editor: this.quillEditor,\n                        source\n                    });\n                }\n                else if (oldRange === null) {\n                    this.onFocus.emit({\n                        editor: this.quillEditor,\n                        source\n                    });\n                }\n                this.onSelectionChanged.emit({\n                    editor: this.quillEditor,\n                    oldRange,\n                    range,\n                    source\n                });\n                if (shouldTriggerOnModelTouched) {\n                    this.onModelTouched();\n                }\n                this.cd.markForCheck();\n            });\n        };\n        this.textChangeHandler = (delta, oldDelta, source) => {\n            // only emit changes emitted by user interactions\n            const text = this.quillEditor.getText();\n            const content = this.quillEditor.getContents();\n            let html = this.editorElem.querySelector('.ql-editor').innerHTML;\n            if (html === '<p><br></p>' || html === '<div><br></div>') {\n                html = this.defaultEmptyValue;\n            }\n            const trackChanges = this.trackChanges || this.service.config.trackChanges;\n            const shouldTriggerOnModelChange = (source === 'user' || trackChanges && trackChanges === 'all') && !!this.onModelChange;\n            // only emit changes when there's any listener\n            if (!this.onContentChanged.observed && !shouldTriggerOnModelChange) {\n                return;\n            }\n            this.zone.run(() => {\n                if (shouldTriggerOnModelChange) {\n                    this.onModelChange(this.valueGetter(this.quillEditor, this.editorElem));\n                }\n                this.onContentChanged.emit({\n                    content,\n                    delta,\n                    editor: this.quillEditor,\n                    html,\n                    oldDelta,\n                    source,\n                    text\n                });\n                this.cd.markForCheck();\n            });\n        };\n        // eslint-disable-next-line max-len\n        this.editorChangeHandler = (event, current, old, source) => {\n            // only emit changes when there's any listener\n            if (!this.onEditorChanged.observed) {\n                return;\n            }\n            // only emit changes emitted by user interactions\n            if (event === 'text-change') {\n                const text = this.quillEditor.getText();\n                const content = this.quillEditor.getContents();\n                let html = this.editorElem.querySelector('.ql-editor').innerHTML;\n                if (html === '<p><br></p>' || html === '<div><br></div>') {\n                    html = this.defaultEmptyValue;\n                }\n                this.zone.run(() => {\n                    this.onEditorChanged.emit({\n                        content,\n                        delta: current,\n                        editor: this.quillEditor,\n                        event,\n                        html,\n                        oldDelta: old,\n                        source,\n                        text\n                    });\n                    this.cd.markForCheck();\n                });\n            }\n            else {\n                this.zone.run(() => {\n                    this.onEditorChanged.emit({\n                        editor: this.quillEditor,\n                        event,\n                        oldRange: old,\n                        range: current,\n                        source\n                    });\n                    this.cd.markForCheck();\n                });\n            }\n        };\n        this.document = injector.get(DOCUMENT);\n    }\n    static normalizeClassNames(classes) {\n        const classList = classes.trim().split(' ');\n        return classList.reduce((prev, cur) => {\n            const trimmed = cur.trim();\n            if (trimmed) {\n                prev.push(trimmed);\n            }\n            return prev;\n        }, []);\n    }\n    ngOnInit() {\n        this.preserve = this.preserveWhitespace;\n        this.toolbarPosition = this.customToolbarPosition;\n    }\n    ngAfterViewInit() {\n        if (isPlatformServer(this.platformId)) {\n            return;\n        }\n        // The `quill-editor` component might be destroyed before the `quill` chunk is loaded and its code is executed\n        // this will lead to runtime exceptions, since the code will be executed on DOM nodes that don't exist within the tree.\n        this.quillSubscription = this.service.getQuill().pipe(mergeMap((Quill) => {\n            const promises = [this.service.registerCustomModules(Quill, this.customModules)];\n            const beforeRender = this.beforeRender ?? this.service.config.beforeRender;\n            if (beforeRender) {\n                promises.push(beforeRender());\n            }\n            return Promise.all(promises).then(() => Quill);\n        })).subscribe(Quill => {\n            this.editorElem = this.elementRef.nativeElement.querySelector('[quill-editor-element]');\n            const toolbarElem = this.elementRef.nativeElement.querySelector('[quill-editor-toolbar]');\n            const modules = Object.assign({}, this.modules || this.service.config.modules);\n            if (toolbarElem) {\n                modules.toolbar = toolbarElem;\n            }\n            else if (modules.toolbar === undefined) {\n                modules.toolbar = defaultModules.toolbar;\n            }\n            let placeholder = this.placeholder !== undefined ? this.placeholder : this.service.config.placeholder;\n            if (placeholder === undefined) {\n                placeholder = 'Insert text here ...';\n            }\n            if (this.styles) {\n                Object.keys(this.styles).forEach((key) => {\n                    this.renderer.setStyle(this.editorElem, key, this.styles[key]);\n                });\n            }\n            if (this.classes) {\n                this.addClasses(this.classes);\n            }\n            this.customOptions.forEach((customOption) => {\n                const newCustomOption = Quill.import(customOption.import);\n                newCustomOption.whitelist = customOption.whitelist;\n                Quill.register(newCustomOption, true);\n            });\n            let bounds = this.bounds && this.bounds === 'self' ? this.editorElem : this.bounds;\n            if (!bounds) {\n                bounds = this.service.config.bounds ? this.service.config.bounds : this.document.body;\n            }\n            let debug = this.debug;\n            if (!debug && debug !== false && this.service.config.debug) {\n                debug = this.service.config.debug;\n            }\n            let readOnly = this.readOnly;\n            if (!readOnly && this.readOnly !== false) {\n                readOnly = this.service.config.readOnly !== undefined ? this.service.config.readOnly : false;\n            }\n            let defaultEmptyValue = this.defaultEmptyValue;\n            if (this.service.config.hasOwnProperty('defaultEmptyValue')) {\n                defaultEmptyValue = this.service.config.defaultEmptyValue;\n            }\n            let scrollingContainer = this.scrollingContainer;\n            if (!scrollingContainer && this.scrollingContainer !== null) {\n                scrollingContainer =\n                    this.service.config.scrollingContainer === null\n                        || this.service.config.scrollingContainer ? this.service.config.scrollingContainer : null;\n            }\n            let formats = this.formats;\n            if (!formats && formats === undefined) {\n                formats = this.service.config.formats ? [...this.service.config.formats] : (this.service.config.formats === null ? null : undefined);\n            }\n            this.zone.runOutsideAngular(() => {\n                this.quillEditor = new Quill(this.editorElem, {\n                    bounds,\n                    debug: debug,\n                    formats: formats,\n                    modules,\n                    placeholder,\n                    readOnly,\n                    defaultEmptyValue,\n                    scrollingContainer: scrollingContainer,\n                    strict: this.strict,\n                    theme: this.theme || (this.service.config.theme ? this.service.config.theme : 'snow')\n                });\n                // Set optional link placeholder, Quill has no native API for it so using workaround\n                if (this.linkPlaceholder) {\n                    const tooltip = this.quillEditor?.theme?.tooltip;\n                    const input = tooltip?.root?.querySelector('input[data-link]');\n                    if (input?.dataset) {\n                        input.dataset.link = this.linkPlaceholder;\n                    }\n                }\n            });\n            if (this.content) {\n                const format = getFormat(this.format, this.service.config.format);\n                if (format === 'text') {\n                    this.quillEditor.setText(this.content, 'silent');\n                }\n                else {\n                    const newValue = this.valueSetter(this.quillEditor, this.content);\n                    this.quillEditor.setContents(newValue, 'silent');\n                }\n                this.quillEditor.getModule('history').clear();\n            }\n            // initialize disabled status based on this.disabled as default value\n            this.setDisabledState();\n            this.addQuillEventListeners();\n            // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n            // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n            if (!this.onEditorCreated.observed && !this.onValidatorChanged) {\n                return;\n            }\n            // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n            // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n            // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n            requestAnimationFrame(() => {\n                if (this.onValidatorChanged) {\n                    this.onValidatorChanged();\n                }\n                this.onEditorCreated.emit(this.quillEditor);\n                this.onEditorCreated.complete();\n            });\n        });\n    }\n    ngOnDestroy() {\n        this.dispose();\n        this.quillSubscription?.unsubscribe();\n        this.quillSubscription = null;\n    }\n    ngOnChanges(changes) {\n        if (!this.quillEditor) {\n            return;\n        }\n        /* eslint-disable @typescript-eslint/dot-notation */\n        if (changes.readOnly) {\n            this.quillEditor.enable(!changes.readOnly.currentValue);\n        }\n        if (changes.placeholder) {\n            this.quillEditor.root.dataset.placeholder =\n                changes.placeholder.currentValue;\n        }\n        if (changes.defaultEmptyValue) {\n            this.quillEditor.root.dataset.defaultEmptyValue =\n                changes.defaultEmptyValue.currentValue;\n        }\n        if (changes.styles) {\n            const currentStyling = changes.styles.currentValue;\n            const previousStyling = changes.styles.previousValue;\n            if (previousStyling) {\n                Object.keys(previousStyling).forEach((key) => {\n                    this.renderer.removeStyle(this.editorElem, key);\n                });\n            }\n            if (currentStyling) {\n                Object.keys(currentStyling).forEach((key) => {\n                    this.renderer.setStyle(this.editorElem, key, this.styles[key]);\n                });\n            }\n        }\n        if (changes.classes) {\n            const currentClasses = changes.classes.currentValue;\n            const previousClasses = changes.classes.previousValue;\n            if (previousClasses) {\n                this.removeClasses(previousClasses);\n            }\n            if (currentClasses) {\n                this.addClasses(currentClasses);\n            }\n        }\n        // We'd want to re-apply event listeners if the `debounceTime` binding changes to apply the\n        // `debounceTime` operator or vice-versa remove it.\n        if (changes.debounceTime) {\n            this.addQuillEventListeners();\n        }\n        /* eslint-enable @typescript-eslint/dot-notation */\n    }\n    addClasses(classList) {\n        QuillEditorBase.normalizeClassNames(classList).forEach((c) => {\n            this.renderer.addClass(this.editorElem, c);\n        });\n    }\n    removeClasses(classList) {\n        QuillEditorBase.normalizeClassNames(classList).forEach((c) => {\n            this.renderer.removeClass(this.editorElem, c);\n        });\n    }\n    writeValue(currentValue) {\n        // optional fix for https://github.com/angular/angular/issues/14988\n        if (this.filterNull && currentValue === null) {\n            return;\n        }\n        this.content = currentValue;\n        if (!this.quillEditor) {\n            return;\n        }\n        const format = getFormat(this.format, this.service.config.format);\n        const newValue = this.valueSetter(this.quillEditor, currentValue);\n        if (this.compareValues) {\n            const currentEditorValue = this.quillEditor.getContents();\n            if (JSON.stringify(currentEditorValue) === JSON.stringify(newValue)) {\n                return;\n            }\n        }\n        if (currentValue) {\n            if (format === 'text') {\n                this.quillEditor.setText(currentValue);\n            }\n            else {\n                this.quillEditor.setContents(newValue);\n            }\n            return;\n        }\n        this.quillEditor.setText('');\n    }\n    setDisabledState(isDisabled = this.disabled) {\n        // store initial value to set appropriate disabled status after ViewInit\n        this.disabled = isDisabled;\n        if (this.quillEditor) {\n            if (isDisabled) {\n                this.quillEditor.disable();\n                this.renderer.setAttribute(this.elementRef.nativeElement, 'disabled', 'disabled');\n            }\n            else {\n                if (!this.readOnly) {\n                    this.quillEditor.enable();\n                }\n                this.renderer.removeAttribute(this.elementRef.nativeElement, 'disabled');\n            }\n        }\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    registerOnValidatorChange(fn) {\n        this.onValidatorChanged = fn;\n    }\n    validate() {\n        if (!this.quillEditor) {\n            return null;\n        }\n        const err = {};\n        let valid = true;\n        const text = this.quillEditor.getText();\n        // trim text if wanted + handle special case that an empty editor contains a new line\n        const textLength = this.trimOnValidation ? text.trim().length : (text.length === 1 && text.trim().length === 0 ? 0 : text.length - 1);\n        const deltaOperations = this.quillEditor.getContents().ops;\n        const onlyEmptyOperation = deltaOperations && deltaOperations.length === 1 && ['\\n', ''].includes(deltaOperations[0].insert);\n        if (this.minLength && textLength && textLength < this.minLength) {\n            err.minLengthError = {\n                given: textLength,\n                minLength: this.minLength\n            };\n            valid = false;\n        }\n        if (this.maxLength && textLength > this.maxLength) {\n            err.maxLengthError = {\n                given: textLength,\n                maxLength: this.maxLength\n            };\n            valid = false;\n        }\n        if (this.required && !textLength && onlyEmptyOperation) {\n            err.requiredError = {\n                empty: true\n            };\n            valid = false;\n        }\n        return valid ? null : err;\n    }\n    addQuillEventListeners() {\n        this.dispose();\n        // We have to enter the `<root>` zone when adding event listeners, so `debounceTime` will spawn the\n        // `AsyncAction` there w/o triggering change detections. We still re-enter the Angular's zone through\n        // `zone.run` when we emit an event to the parent component.\n        this.zone.runOutsideAngular(() => {\n            this.subscription = new Subscription();\n            this.subscription.add(\n            // mark model as touched if editor lost focus\n            fromEvent(this.quillEditor, 'selection-change').subscribe(([range, oldRange, source]) => {\n                this.selectionChangeHandler(range, oldRange, source);\n            }));\n            // The `fromEvent` supports passing JQuery-style event targets, the editor has `on` and `off` methods which\n            // will be invoked upon subscription and teardown.\n            let textChange$ = fromEvent(this.quillEditor, 'text-change');\n            let editorChange$ = fromEvent(this.quillEditor, 'editor-change');\n            if (typeof this.debounceTime === 'number') {\n                textChange$ = textChange$.pipe(debounceTime(this.debounceTime));\n                editorChange$ = editorChange$.pipe(debounceTime(this.debounceTime));\n            }\n            this.subscription.add(\n            // update model if text changes\n            textChange$.subscribe(([delta, oldDelta, source]) => {\n                this.textChangeHandler(delta, oldDelta, source);\n            }));\n            this.subscription.add(\n            // triggered if selection or text changed\n            editorChange$.subscribe(([event, current, old, source]) => {\n                this.editorChangeHandler(event, current, old, source);\n            }));\n        });\n    }\n    dispose() {\n        if (this.subscription !== null) {\n            this.subscription.unsubscribe();\n            this.subscription = null;\n        }\n    }\n}\nQuillEditorBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillEditorBase, deps: [{ token: i0.Injector }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.DomSanitizer }, { token: PLATFORM_ID }, { token: i0.Renderer2 }, { token: i0.NgZone }, { token: QuillService }], target: i0.ɵɵFactoryTarget.Directive });\nQuillEditorBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.0.0\", type: QuillEditorBase, inputs: { format: \"format\", theme: \"theme\", modules: \"modules\", debug: \"debug\", readOnly: \"readOnly\", placeholder: \"placeholder\", maxLength: \"maxLength\", minLength: \"minLength\", required: \"required\", formats: \"formats\", customToolbarPosition: \"customToolbarPosition\", sanitize: \"sanitize\", beforeRender: \"beforeRender\", styles: \"styles\", strict: \"strict\", scrollingContainer: \"scrollingContainer\", bounds: \"bounds\", customOptions: \"customOptions\", customModules: \"customModules\", trackChanges: \"trackChanges\", preserveWhitespace: \"preserveWhitespace\", classes: \"classes\", trimOnValidation: \"trimOnValidation\", linkPlaceholder: \"linkPlaceholder\", compareValues: \"compareValues\", filterNull: \"filterNull\", debounceTime: \"debounceTime\", defaultEmptyValue: \"defaultEmptyValue\", valueGetter: \"valueGetter\", valueSetter: \"valueSetter\" }, outputs: { onEditorCreated: \"onEditorCreated\", onEditorChanged: \"onEditorChanged\", onContentChanged: \"onContentChanged\", onSelectionChanged: \"onSelectionChanged\", onFocus: \"onFocus\", onBlur: \"onBlur\" }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillEditorBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.Injector }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.DomSanitizer }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.Renderer2 }, { type: i0.NgZone }, { type: QuillService }]; }, propDecorators: { format: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], modules: [{\n                type: Input\n            }], debug: [{\n                type: Input\n            }], readOnly: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], maxLength: [{\n                type: Input\n            }], minLength: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], formats: [{\n                type: Input\n            }], customToolbarPosition: [{\n                type: Input\n            }], sanitize: [{\n                type: Input\n            }], beforeRender: [{\n                type: Input\n            }], styles: [{\n                type: Input\n            }], strict: [{\n                type: Input\n            }], scrollingContainer: [{\n                type: Input\n            }], bounds: [{\n                type: Input\n            }], customOptions: [{\n                type: Input\n            }], customModules: [{\n                type: Input\n            }], trackChanges: [{\n                type: Input\n            }], preserveWhitespace: [{\n                type: Input\n            }], classes: [{\n                type: Input\n            }], trimOnValidation: [{\n                type: Input\n            }], linkPlaceholder: [{\n                type: Input\n            }], compareValues: [{\n                type: Input\n            }], filterNull: [{\n                type: Input\n            }], debounceTime: [{\n                type: Input\n            }], defaultEmptyValue: [{\n                type: Input\n            }], onEditorCreated: [{\n                type: Output\n            }], onEditorChanged: [{\n                type: Output\n            }], onContentChanged: [{\n                type: Output\n            }], onSelectionChanged: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], valueGetter: [{\n                type: Input\n            }], valueSetter: [{\n                type: Input\n            }] } });\nclass QuillEditorComponent extends QuillEditorBase {\n    constructor(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service) {\n        super(injector, elementRef, cd, domSanitizer, platformId, renderer, zone, service);\n    }\n}\nQuillEditorComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillEditorComponent, deps: [{ token: i0.Injector }, { token: ElementRef }, { token: ChangeDetectorRef }, { token: DomSanitizer }, { token: PLATFORM_ID }, { token: Renderer2 }, { token: NgZone }, { token: QuillService }], target: i0.ɵɵFactoryTarget.Component });\nQuillEditorComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.0.0\", type: QuillEditorComponent, isStandalone: true, selector: \"quill-editor\", providers: [\n        {\n            multi: true,\n            provide: NG_VALUE_ACCESSOR,\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            useExisting: forwardRef(() => QuillEditorComponent)\n        },\n        {\n            multi: true,\n            provide: NG_VALIDATORS,\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            useExisting: forwardRef(() => QuillEditorComponent)\n        }\n    ], usesInheritance: true, ngImport: i0, template: `\n  <ng-container *ngIf=\"toolbarPosition !== 'top'\">\n    <div quill-editor-element *ngIf=\"!preserve\"></div>\n    <pre quill-editor-element *ngIf=\"preserve\"></pre>\n  </ng-container>\n  <ng-content select=\"[quill-editor-toolbar]\"></ng-content>\n  <ng-container *ngIf=\"toolbarPosition === 'top'\">\n    <div quill-editor-element *ngIf=\"!preserve\"></div>\n    <pre quill-editor-element *ngIf=\"preserve\"></pre>\n  </ng-container>\n`, isInline: true, styles: [\":host{display:inline-block}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillEditorComponent, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.Emulated, providers: [\n                        {\n                            multi: true,\n                            provide: NG_VALUE_ACCESSOR,\n                            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n                            useExisting: forwardRef(() => QuillEditorComponent)\n                        },\n                        {\n                            multi: true,\n                            provide: NG_VALIDATORS,\n                            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n                            useExisting: forwardRef(() => QuillEditorComponent)\n                        }\n                    ], selector: 'quill-editor', template: `\n  <ng-container *ngIf=\"toolbarPosition !== 'top'\">\n    <div quill-editor-element *ngIf=\"!preserve\"></div>\n    <pre quill-editor-element *ngIf=\"preserve\"></pre>\n  </ng-container>\n  <ng-content select=\"[quill-editor-toolbar]\"></ng-content>\n  <ng-container *ngIf=\"toolbarPosition === 'top'\">\n    <div quill-editor-element *ngIf=\"!preserve\"></div>\n    <pre quill-editor-element *ngIf=\"preserve\"></pre>\n  </ng-container>\n`, standalone: true, imports: [CommonModule], styles: [\":host{display:inline-block}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.Injector }, { type: i0.ElementRef, decorators: [{\n                    type: Inject,\n                    args: [ElementRef]\n                }] }, { type: i0.ChangeDetectorRef, decorators: [{\n                    type: Inject,\n                    args: [ChangeDetectorRef]\n                }] }, { type: i1.DomSanitizer, decorators: [{\n                    type: Inject,\n                    args: [DomSanitizer]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.Renderer2, decorators: [{\n                    type: Inject,\n                    args: [Renderer2]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Inject,\n                    args: [NgZone]\n                }] }, { type: QuillService, decorators: [{\n                    type: Inject,\n                    args: [QuillService]\n                }] }]; } });\n\nclass QuillViewHTMLComponent {\n    constructor(sanitizer, service) {\n        this.sanitizer = sanitizer;\n        this.service = service;\n        this.content = '';\n        this.innerHTML = '';\n        this.themeClass = 'ql-snow';\n    }\n    ngOnChanges(changes) {\n        if (changes.theme) {\n            const theme = changes.theme.currentValue || (this.service.config.theme ? this.service.config.theme : 'snow');\n            this.themeClass = `ql-${theme} ngx-quill-view-html`;\n        }\n        else if (!this.theme) {\n            const theme = this.service.config.theme ? this.service.config.theme : 'snow';\n            this.themeClass = `ql-${theme} ngx-quill-view-html`;\n        }\n        if (changes.content) {\n            const content = changes.content.currentValue;\n            const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : (this.service.config.sanitize || false);\n            this.innerHTML = sanitize ? content : this.sanitizer.bypassSecurityTrustHtml(content);\n        }\n    }\n}\nQuillViewHTMLComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillViewHTMLComponent, deps: [{ token: DomSanitizer }, { token: QuillService }], target: i0.ɵɵFactoryTarget.Component });\nQuillViewHTMLComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.0.0\", type: QuillViewHTMLComponent, isStandalone: true, selector: \"quill-view-html\", inputs: { content: \"content\", theme: \"theme\", sanitize: \"sanitize\" }, usesOnChanges: true, ngImport: i0, template: `\n  <div class=\"ql-container\" [ngClass]=\"themeClass\">\n    <div class=\"ql-editor\" [innerHTML]=\"innerHTML\">\n    </div>\n  </div>\n`, isInline: true, styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i3.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillViewHTMLComponent, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.None, selector: 'quill-view-html', template: `\n  <div class=\"ql-container\" [ngClass]=\"themeClass\">\n    <div class=\"ql-editor\" [innerHTML]=\"innerHTML\">\n    </div>\n  </div>\n`, standalone: true, imports: [CommonModule], styles: [\".ql-container.ngx-quill-view-html{border:0}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i1.DomSanitizer, decorators: [{\n                    type: Inject,\n                    args: [DomSanitizer]\n                }] }, { type: QuillService }]; }, propDecorators: { content: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], sanitize: [{\n                type: Input\n            }] } });\n\nclass QuillViewComponent {\n    constructor(elementRef, renderer, zone, service, domSanitizer, platformId) {\n        this.elementRef = elementRef;\n        this.renderer = renderer;\n        this.zone = zone;\n        this.service = service;\n        this.domSanitizer = domSanitizer;\n        this.platformId = platformId;\n        this.strict = true;\n        this.customModules = [];\n        this.customOptions = [];\n        this.preserveWhitespace = false;\n        this.onEditorCreated = new EventEmitter();\n        this.preserve = false;\n        this.quillSubscription = null;\n        this.valueSetter = (quillEditor, value) => {\n            const format = getFormat(this.format, this.service.config.format);\n            let content = value;\n            if (format === 'text') {\n                quillEditor.setText(content);\n            }\n            else {\n                if (format === 'html') {\n                    const sanitize = [true, false].includes(this.sanitize) ? this.sanitize : (this.service.config.sanitize || false);\n                    if (sanitize) {\n                        value = this.domSanitizer.sanitize(SecurityContext.HTML, value);\n                    }\n                    content = quillEditor.clipboard.convert(value);\n                }\n                else if (format === 'json') {\n                    try {\n                        content = JSON.parse(value);\n                    }\n                    catch (e) {\n                        content = [{ insert: value }];\n                    }\n                }\n                quillEditor.setContents(content);\n            }\n        };\n    }\n    ngOnInit() {\n        this.preserve = this.preserveWhitespace;\n    }\n    ngOnChanges(changes) {\n        if (!this.quillEditor) {\n            return;\n        }\n        if (changes.content) {\n            this.valueSetter(this.quillEditor, changes.content.currentValue);\n        }\n    }\n    ngAfterViewInit() {\n        if (isPlatformServer(this.platformId)) {\n            return;\n        }\n        this.quillSubscription = this.service.getQuill().pipe(mergeMap((Quill) => {\n            const promises = [this.service.registerCustomModules(Quill, this.customModules)];\n            const beforeRender = this.beforeRender ?? this.service.config.beforeRender;\n            if (beforeRender) {\n                promises.push(beforeRender());\n            }\n            return Promise.all(promises).then(() => Quill);\n        })).subscribe(Quill => {\n            const modules = Object.assign({}, this.modules || this.service.config.modules);\n            modules.toolbar = false;\n            this.customOptions.forEach((customOption) => {\n                const newCustomOption = Quill.import(customOption.import);\n                newCustomOption.whitelist = customOption.whitelist;\n                Quill.register(newCustomOption, true);\n            });\n            let debug = this.debug;\n            if (!debug && debug !== false && this.service.config.debug) {\n                debug = this.service.config.debug;\n            }\n            let formats = this.formats;\n            if (!formats && formats === undefined) {\n                formats = this.service.config.formats ?\n                    Object.assign({}, this.service.config.formats) : (this.service.config.formats === null ? null : undefined);\n            }\n            const theme = this.theme || (this.service.config.theme ? this.service.config.theme : 'snow');\n            this.editorElem = this.elementRef.nativeElement.querySelector('[quill-view-element]');\n            this.zone.runOutsideAngular(() => {\n                this.quillEditor = new Quill(this.editorElem, {\n                    debug: debug,\n                    formats: formats,\n                    modules,\n                    readOnly: true,\n                    strict: this.strict,\n                    theme\n                });\n            });\n            this.renderer.addClass(this.editorElem, 'ngx-quill-view');\n            if (this.content) {\n                this.valueSetter(this.quillEditor, this.content);\n            }\n            // The `requestAnimationFrame` triggers change detection. There's no sense to invoke the `requestAnimationFrame` if anyone is\n            // listening to the `onEditorCreated` event inside the template, for instance `<quill-view (onEditorCreated)=\"...\">`.\n            if (!this.onEditorCreated.observers.length) {\n                return;\n            }\n            // The `requestAnimationFrame` will trigger change detection and `onEditorCreated` will also call `markDirty()`\n            // internally, since Angular wraps template event listeners into `listener` instruction. We're using the `requestAnimationFrame`\n            // to prevent the frame drop and avoid `ExpressionChangedAfterItHasBeenCheckedError` error.\n            requestAnimationFrame(() => {\n                this.onEditorCreated.emit(this.quillEditor);\n                this.onEditorCreated.complete();\n            });\n        });\n    }\n    ngOnDestroy() {\n        this.quillSubscription?.unsubscribe();\n        this.quillSubscription = null;\n    }\n}\nQuillViewComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillViewComponent, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.NgZone }, { token: QuillService }, { token: i1.DomSanitizer }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Component });\nQuillViewComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.0.0\", type: QuillViewComponent, isStandalone: true, selector: \"quill-view\", inputs: { format: \"format\", theme: \"theme\", modules: \"modules\", debug: \"debug\", formats: \"formats\", sanitize: \"sanitize\", beforeRender: \"beforeRender\", strict: \"strict\", content: \"content\", customModules: \"customModules\", customOptions: \"customOptions\", preserveWhitespace: \"preserveWhitespace\" }, outputs: { onEditorCreated: \"onEditorCreated\" }, usesOnChanges: true, ngImport: i0, template: `\n<div quill-view-element *ngIf=\"!preserve\"></div>\n<pre quill-view-element *ngIf=\"preserve\"></pre>\n`, isInline: true, styles: [\".ql-container.ngx-quill-view{border:0}\\n\"], dependencies: [{ kind: \"ngmodule\", type: CommonModule }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillViewComponent, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.None, selector: 'quill-view', template: `\n<div quill-view-element *ngIf=\"!preserve\"></div>\n<pre quill-view-element *ngIf=\"preserve\"></pre>\n`, standalone: true, imports: [CommonModule], styles: [\".ql-container.ngx-quill-view{border:0}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.NgZone }, { type: QuillService }, { type: i1.DomSanitizer }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; }, propDecorators: { format: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], modules: [{\n                type: Input\n            }], debug: [{\n                type: Input\n            }], formats: [{\n                type: Input\n            }], sanitize: [{\n                type: Input\n            }], beforeRender: [{\n                type: Input\n            }], strict: [{\n                type: Input\n            }], content: [{\n                type: Input\n            }], customModules: [{\n                type: Input\n            }], customOptions: [{\n                type: Input\n            }], preserveWhitespace: [{\n                type: Input\n            }], onEditorCreated: [{\n                type: Output\n            }] } });\n\nclass QuillModule {\n    static forRoot(config) {\n        return {\n            ngModule: QuillModule,\n            providers: [\n                {\n                    provide: QUILL_CONFIG_TOKEN,\n                    useValue: config\n                }\n            ]\n        };\n    }\n}\nQuillModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nQuillModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillModule, imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent], exports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent] });\nQuillModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillModule, imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.0\", ngImport: i0, type: QuillModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent],\n                    exports: [QuillEditorComponent, QuillViewComponent, QuillViewHTMLComponent],\n                }]\n        }] });\n\n/*\n * Public API Surface of ngx-quill\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { QuillEditorBase, QuillEditorComponent, QuillModule, QuillService, QuillViewComponent, QuillViewHTMLComponent };\n"], "mappings": ";AAAA,SAASA,cAAc,EAAEC,kBAAkB,QAAQ,kBAAkB;AACrE,cAAc,kBAAkB;AAChC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ,QAAQ,eAAe;AACxO,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,iBAAiB;AAC1E,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,KAAK,EAAEC,YAAY,EAAEC,cAAc,EAAEC,YAAY,EAAEC,SAAS,QAAQ,MAAM;AACnF,SAASC,WAAW,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,gBAAgB;AACpE,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,gBAAgB;AAAC;EAAA;IAsE6BjC,EAAE,uBAqnB3C;EAAA;AAAA;AAAA;EAAA;IArnByCA,EAAE,uBAsnB5C;EAAA;AAAA;AAAA;EAAA;IAtnB0CA,EAAE,2BAonB/C;IApnB6CA,EAAE,kFAqnB3C;IArnByCA,EAAE,kFAsnB5C;IAtnB0CA,EAAE,wBAunBhF;EAAA;EAAA;IAAA,eAvnB8EA,EAAE;IAAFA,EAAE,aAqnBnD;IArnBiDA,EAAE,qCAqnBnD;IArnBiDA,EAAE,aAsnBpD;IAtnBkDA,EAAE,oCAsnBpD;EAAA;AAAA;AAAA;EAAA;IAtnBkDA,EAAE,uBA0nB3C;EAAA;AAAA;AAAA;EAAA;IA1nByCA,EAAE,uBA2nB5C;EAAA;AAAA;AAAA;EAAA;IA3nB0CA,EAAE,2BAynB/C;IAznB6CA,EAAE,kFA0nB3C;IA1nByCA,EAAE,kFA2nB5C;IA3nB0CA,EAAE,wBA4nBhF;EAAA;EAAA;IAAA,eA5nB8EA,EAAE;IAAFA,EAAE,aA0nBnD;IA1nBiDA,EAAE,qCA0nBnD;IA1nBiDA,EAAE,aA2nBpD;IA3nBkDA,EAAE,oCA2nBpD;EAAA;AAAA;AAAA;AAAA;AAAA;EAAA;IA3nBkDA,EAAE,uBAs1BjD;EAAA;AAAA;AAAA;EAAA;IAt1B+CA,EAAE,uBAu1BlD;EAAA;AAAA;AA35B/C,MAAMkC,SAAS,GAAG,CAACC,MAAM,EAAEC,YAAY,KAAK;EACxC,MAAMC,YAAY,GAAGF,MAAM,IAAIC,YAAY;EAC3C,OAAOC,YAAY,IAAI,MAAM;AACjC,CAAC;AAED,MAAMC,YAAY,CAAC;EACfC,WAAW,CAACC,QAAQ,EAAEC,MAAM,EAAE;IAAA;IAC1B,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGlB,KAAK,iCAAC,aAAY;MAC5B,IAAI,CAAC,KAAI,CAACmB,KAAK,EAAE;QACb;QACA;QACA;QACA,MAAMC,4BAA4B,GAAG,KAAI,CAACC,QAAQ,CAACC,gBAAgB;QACnE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAI,CAACD,QAAQ,CAACC,gBAAgB,GAAG,KAAI,CAACD,QAAQ,CAAC,iCAAiC,CAAC,IAAI,KAAI,CAACA,QAAQ,CAACC,gBAAgB;QACnH,MAAMC,WAAW,SAAS,MAAM,CAAC,OAAO,CAAC;QACzC,KAAI,CAACF,QAAQ,CAACC,gBAAgB,GAAGF,4BAA4B;QAC7D,KAAI,CAACD,KAAK,GAAII,WAAW,CAACC,OAAO,GAAGD,WAAW,CAACC,OAAO,GAAGD,WAAY;MAC1E;MACA;MACA,KAAI,CAACN,MAAM,CAACQ,aAAa,EAAEC,OAAO,CAAEC,YAAY,IAAK;QACjD,MAAMC,eAAe,GAAG,KAAI,CAACT,KAAK,CAACU,MAAM,CAACF,YAAY,CAACE,MAAM,CAAC;QAC9DD,eAAe,CAACE,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClD,KAAI,CAACX,KAAK,CAACY,QAAQ,CAACH,eAAe,EAAE,IAAI,EAAE,KAAI,CAACX,MAAM,CAACe,6BAA6B,CAAC;MACzF,CAAC,CAAC;MACF,aAAa,KAAI,CAACC,qBAAqB,CAAC,KAAI,CAACd,KAAK,EAAE,KAAI,CAACF,MAAM,CAACiB,aAAa,EAAE,KAAI,CAACjB,MAAM,CAACe,6BAA6B,CAAC;IAC7H,CAAC,EAAC,CAACG,IAAI,CAAC9B,WAAW,CAAC;MAAE+B,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC;IACvD,IAAI,CAAChB,QAAQ,GAAGL,QAAQ,CAACsB,GAAG,CAAC3C,QAAQ,CAAC;IACtC,IAAI,CAAC,IAAI,CAACsB,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG;QAAEsB,OAAO,EAAEjE;MAAe,CAAC;IAC7C;EACJ;EACAkE,QAAQ,GAAG;IACP,OAAO,IAAI,CAACtB,MAAM;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACUe,qBAAqB,CAACd,KAAK,EAAEe,aAAa,EAAEF,6BAA6B,EAAE;IAAA;MAC7E,IAAIS,KAAK,CAACC,OAAO,CAACR,aAAa,CAAC,EAAE;QAC9B;QACA,KAAK,IAAI;UAAES,cAAc;UAAEC;QAAK,CAAC,IAAIV,aAAa,EAAE;UAChD;UACA;UACA,IAAIjC,YAAY,CAAC0C,cAAc,CAAC,EAAE;YAC9BA,cAAc,SAASzC,cAAc,CAACyC,cAAc,CAAC;UACzD;UACAxB,KAAK,CAACY,QAAQ,CAACa,IAAI,EAAED,cAAc,EAAEX,6BAA6B,CAAC;QACvE;MACJ;MACA;MACA;MACA,OAAOb,KAAK;IAAC;EACjB;AACJ;AACAL,YAAY,CAAC+B,IAAI;EAAA,iBAAwF/B,YAAY,EAAtBtC,EAAE,UAAsCA,EAAE,CAACsE,QAAQ,GAAnDtE,EAAE,UAA8DD,kBAAkB;AAAA,CAA6D;AAC9OuC,YAAY,CAACiC,KAAK,kBAD6EvE,EAAE;EAAA,OACYsC,YAAY;EAAA,SAAZA,YAAY;EAAA,YAAc;AAAM,EAAG;AAChJ;EAAA,mDAF+FtC,EAAE,mBAENsC,YAAY,EAAc,CAAC;IAC1GkC,IAAI,EAAEvE,UAAU;IAChBwE,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAExE,EAAE,CAACsE;IAAS,CAAC,EAAE;MAAEE,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrFJ,IAAI,EAAEtE;MACV,CAAC,EAAE;QACCsE,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAAC1E,kBAAkB;MAC7B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,MAAM8E,eAAe,CAAC;EAClBtC,WAAW,CAACC,QAAQ,EAAEsC,UAAU,EAAEC,EAAE,EAAEC,YAAY,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACrF,IAAI,CAACN,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACvC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACS,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC+B,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAIQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,eAAe,GAAG,IAAI1F,YAAY,EAAE;IACzC,IAAI,CAAC2F,eAAe,GAAG,IAAI3F,YAAY,EAAE;IACzC,IAAI,CAAC4F,gBAAgB,GAAG,IAAI5F,YAAY,EAAE;IAC1C,IAAI,CAAC6F,kBAAkB,GAAG,IAAI7F,YAAY,EAAE;IAC5C,IAAI,CAAC8F,OAAO,GAAG,IAAI9F,YAAY,EAAE;IACjC,IAAI,CAAC+F,MAAM,GAAG,IAAI/F,YAAY,EAAE;IAChC,IAAI,CAACgG,QAAQ,GAAG,KAAK,CAAC,CAAC;IACvB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,WAAW,GAAG,CAACC,WAAW,EAAEC,aAAa,KAAK;MAC/C,IAAIC,IAAI,GAAGD,aAAa,CAACE,aAAa,CAAC,YAAY,CAAC,CAACC,SAAS;MAC9D,IAAIF,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,iBAAiB,EAAE;QACtDA,IAAI,GAAG,IAAI,CAACf,iBAAiB;MACjC;MACA,IAAIkB,UAAU,GAAGH,IAAI;MACrB,MAAMzE,MAAM,GAAGD,SAAS,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACiD,OAAO,CAAC3C,MAAM,CAACN,MAAM,CAAC;MACjE,IAAIA,MAAM,KAAK,MAAM,EAAE;QACnB4E,UAAU,GAAGL,WAAW,CAACM,OAAO,EAAE;MACtC,CAAC,MACI,IAAI7E,MAAM,KAAK,QAAQ,EAAE;QAC1B4E,UAAU,GAAGL,WAAW,CAACO,WAAW,EAAE;MAC1C,CAAC,MACI,IAAI9E,MAAM,KAAK,MAAM,EAAE;QACxB,IAAI;UACA4E,UAAU,GAAGG,IAAI,CAACC,SAAS,CAACT,WAAW,CAACO,WAAW,EAAE,CAAC;QAC1D,CAAC,CACD,OAAOG,CAAC,EAAE;UACNL,UAAU,GAAGL,WAAW,CAACM,OAAO,EAAE;QACtC;MACJ;MACA,OAAOD,UAAU;IACrB,CAAC;IACD,IAAI,CAACM,WAAW,GAAG,CAACX,WAAW,EAAEY,KAAK,KAAK;MACvC,MAAMnF,MAAM,GAAGD,SAAS,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACiD,OAAO,CAAC3C,MAAM,CAACN,MAAM,CAAC;MACjE,IAAIA,MAAM,KAAK,MAAM,EAAE;QACnB,MAAMoF,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACD,QAAQ,CAAC,GAAG,IAAI,CAACA,QAAQ,GAAI,IAAI,CAACnC,OAAO,CAAC3C,MAAM,CAAC8E,QAAQ,IAAI,KAAM;QAChH,IAAIA,QAAQ,EAAE;UACVD,KAAK,GAAG,IAAI,CAACtC,YAAY,CAACuC,QAAQ,CAAClH,eAAe,CAACoH,IAAI,EAAEH,KAAK,CAAC;QACnE;QACA,OAAOZ,WAAW,CAACgB,SAAS,CAACC,OAAO,CAACL,KAAK,CAAC;MAC/C,CAAC,MACI,IAAInF,MAAM,KAAK,MAAM,EAAE;QACxB,IAAI;UACA,OAAO+E,IAAI,CAACU,KAAK,CAACN,KAAK,CAAC;QAC5B,CAAC,CACD,OAAOF,CAAC,EAAE;UACN,OAAO,CAAC;YAAES,MAAM,EAAEP;UAAM,CAAC,CAAC;QAC9B;MACJ;MACA,OAAOA,KAAK;IAChB,CAAC;IACD,IAAI,CAACQ,sBAAsB,GAAG,CAACC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,KAAK;MACvD,MAAMC,2BAA2B,GAAG,CAACH,KAAK,IAAI,CAAC,CAAC,IAAI,CAACI,cAAc;MACnE;MACA,IAAI,CAAC,IAAI,CAAChC,MAAM,CAACiC,QAAQ,IACrB,CAAC,IAAI,CAAClC,OAAO,CAACkC,QAAQ,IACtB,CAAC,IAAI,CAACnC,kBAAkB,CAACmC,QAAQ,IACjC,CAACF,2BAA2B,EAAE;QAC9B;MACJ;MACA,IAAI,CAAC/C,IAAI,CAACkD,GAAG,CAAC,MAAM;QAChB,IAAIN,KAAK,KAAK,IAAI,EAAE;UAChB,IAAI,CAAC5B,MAAM,CAACmC,IAAI,CAAC;YACbC,MAAM,EAAE,IAAI,CAAC7B,WAAW;YACxBuB;UACJ,CAAC,CAAC;QACN,CAAC,MACI,IAAID,QAAQ,KAAK,IAAI,EAAE;UACxB,IAAI,CAAC9B,OAAO,CAACoC,IAAI,CAAC;YACdC,MAAM,EAAE,IAAI,CAAC7B,WAAW;YACxBuB;UACJ,CAAC,CAAC;QACN;QACA,IAAI,CAAChC,kBAAkB,CAACqC,IAAI,CAAC;UACzBC,MAAM,EAAE,IAAI,CAAC7B,WAAW;UACxBsB,QAAQ;UACRD,KAAK;UACLE;QACJ,CAAC,CAAC;QACF,IAAIC,2BAA2B,EAAE;UAC7B,IAAI,CAACC,cAAc,EAAE;QACzB;QACA,IAAI,CAACpD,EAAE,CAACyD,YAAY,EAAE;MAC1B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,iBAAiB,GAAG,CAACC,KAAK,EAAEC,QAAQ,EAAEV,MAAM,KAAK;MAClD;MACA,MAAMW,IAAI,GAAG,IAAI,CAAClC,WAAW,CAACM,OAAO,EAAE;MACvC,MAAM6B,OAAO,GAAG,IAAI,CAACnC,WAAW,CAACO,WAAW,EAAE;MAC9C,IAAIL,IAAI,GAAG,IAAI,CAACkC,UAAU,CAACjC,aAAa,CAAC,YAAY,CAAC,CAACC,SAAS;MAChE,IAAIF,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,iBAAiB,EAAE;QACtDA,IAAI,GAAG,IAAI,CAACf,iBAAiB;MACjC;MACA,MAAMkD,YAAY,GAAG,IAAI,CAACA,YAAY,IAAI,IAAI,CAAC3D,OAAO,CAAC3C,MAAM,CAACsG,YAAY;MAC1E,MAAMC,0BAA0B,GAAG,CAACf,MAAM,KAAK,MAAM,IAAIc,YAAY,IAAIA,YAAY,KAAK,KAAK,KAAK,CAAC,CAAC,IAAI,CAACE,aAAa;MACxH;MACA,IAAI,CAAC,IAAI,CAACjD,gBAAgB,CAACoC,QAAQ,IAAI,CAACY,0BAA0B,EAAE;QAChE;MACJ;MACA,IAAI,CAAC7D,IAAI,CAACkD,GAAG,CAAC,MAAM;QAChB,IAAIW,0BAA0B,EAAE;UAC5B,IAAI,CAACC,aAAa,CAAC,IAAI,CAACxC,WAAW,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACoC,UAAU,CAAC,CAAC;QAC3E;QACA,IAAI,CAAC9C,gBAAgB,CAACsC,IAAI,CAAC;UACvBO,OAAO;UACPH,KAAK;UACLH,MAAM,EAAE,IAAI,CAAC7B,WAAW;UACxBE,IAAI;UACJ+B,QAAQ;UACRV,MAAM;UACNW;QACJ,CAAC,CAAC;QACF,IAAI,CAAC7D,EAAE,CAACyD,YAAY,EAAE;MAC1B,CAAC,CAAC;IACN,CAAC;IACD;IACA,IAAI,CAACU,mBAAmB,GAAG,CAACC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEpB,MAAM,KAAK;MACxD;MACA,IAAI,CAAC,IAAI,CAAClC,eAAe,CAACqC,QAAQ,EAAE;QAChC;MACJ;MACA;MACA,IAAIe,KAAK,KAAK,aAAa,EAAE;QACzB,MAAMP,IAAI,GAAG,IAAI,CAAClC,WAAW,CAACM,OAAO,EAAE;QACvC,MAAM6B,OAAO,GAAG,IAAI,CAACnC,WAAW,CAACO,WAAW,EAAE;QAC9C,IAAIL,IAAI,GAAG,IAAI,CAACkC,UAAU,CAACjC,aAAa,CAAC,YAAY,CAAC,CAACC,SAAS;QAChE,IAAIF,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,iBAAiB,EAAE;UACtDA,IAAI,GAAG,IAAI,CAACf,iBAAiB;QACjC;QACA,IAAI,CAACV,IAAI,CAACkD,GAAG,CAAC,MAAM;UAChB,IAAI,CAACtC,eAAe,CAACuC,IAAI,CAAC;YACtBO,OAAO;YACPH,KAAK,EAAEU,OAAO;YACdb,MAAM,EAAE,IAAI,CAAC7B,WAAW;YACxByC,KAAK;YACLvC,IAAI;YACJ+B,QAAQ,EAAEU,GAAG;YACbpB,MAAM;YACNW;UACJ,CAAC,CAAC;UACF,IAAI,CAAC7D,EAAE,CAACyD,YAAY,EAAE;QAC1B,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACrD,IAAI,CAACkD,GAAG,CAAC,MAAM;UAChB,IAAI,CAACtC,eAAe,CAACuC,IAAI,CAAC;YACtBC,MAAM,EAAE,IAAI,CAAC7B,WAAW;YACxByC,KAAK;YACLnB,QAAQ,EAAEqB,GAAG;YACbtB,KAAK,EAAEqB,OAAO;YACdnB;UACJ,CAAC,CAAC;UACF,IAAI,CAAClD,EAAE,CAACyD,YAAY,EAAE;QAC1B,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAAC3F,QAAQ,GAAGL,QAAQ,CAACsB,GAAG,CAAC3C,QAAQ,CAAC;EAC1C;EACA,OAAOmI,mBAAmB,CAACC,OAAO,EAAE;IAChC,MAAMC,SAAS,GAAGD,OAAO,CAACE,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC3C,OAAOF,SAAS,CAACG,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;MACnC,MAAMC,OAAO,GAAGD,GAAG,CAACJ,IAAI,EAAE;MAC1B,IAAIK,OAAO,EAAE;QACTF,IAAI,CAACG,IAAI,CAACD,OAAO,CAAC;MACtB;MACA,OAAOF,IAAI;IACf,CAAC,EAAE,EAAE,CAAC;EACV;EACAI,QAAQ,GAAG;IACP,IAAI,CAAC3D,QAAQ,GAAG,IAAI,CAACZ,kBAAkB;IACvC,IAAI,CAACa,eAAe,GAAG,IAAI,CAAChB,qBAAqB;EACrD;EACA2E,eAAe,GAAG;IACd,IAAI7I,gBAAgB,CAAC,IAAI,CAAC6D,UAAU,CAAC,EAAE;MACnC;IACJ;IACA;IACA;IACA,IAAI,CAACuB,iBAAiB,GAAG,IAAI,CAACpB,OAAO,CAACpB,QAAQ,EAAE,CAACL,IAAI,CAAC7B,QAAQ,CAAEa,KAAK,IAAK;MACtE,MAAMuH,QAAQ,GAAG,CAAC,IAAI,CAAC9E,OAAO,CAAC3B,qBAAqB,CAACd,KAAK,EAAE,IAAI,CAACe,aAAa,CAAC,CAAC;MAChF,MAAMyG,YAAY,GAAG,IAAI,CAACA,YAAY,IAAI,IAAI,CAAC/E,OAAO,CAAC3C,MAAM,CAAC0H,YAAY;MAC1E,IAAIA,YAAY,EAAE;QACdD,QAAQ,CAACH,IAAI,CAACI,YAAY,EAAE,CAAC;MACjC;MACA,OAAOC,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC,CAACI,IAAI,CAAC,MAAM3H,KAAK,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC4H,SAAS,CAAC5H,KAAK,IAAI;MACnB,IAAI,CAACmG,UAAU,GAAG,IAAI,CAAChE,UAAU,CAAC0F,aAAa,CAAC3D,aAAa,CAAC,wBAAwB,CAAC;MACvF,MAAM4D,WAAW,GAAG,IAAI,CAAC3F,UAAU,CAAC0F,aAAa,CAAC3D,aAAa,CAAC,wBAAwB,CAAC;MACzF,MAAM9C,OAAO,GAAG2G,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC5G,OAAO,IAAI,IAAI,CAACqB,OAAO,CAAC3C,MAAM,CAACsB,OAAO,CAAC;MAC9E,IAAI0G,WAAW,EAAE;QACb1G,OAAO,CAAC6G,OAAO,GAAGH,WAAW;MACjC,CAAC,MACI,IAAI1G,OAAO,CAAC6G,OAAO,KAAKjG,SAAS,EAAE;QACpCZ,OAAO,CAAC6G,OAAO,GAAG9K,cAAc,CAAC8K,OAAO;MAC5C;MACA,IAAIC,WAAW,GAAG,IAAI,CAACA,WAAW,KAAKlG,SAAS,GAAG,IAAI,CAACkG,WAAW,GAAG,IAAI,CAACzF,OAAO,CAAC3C,MAAM,CAACoI,WAAW;MACrG,IAAIA,WAAW,KAAKlG,SAAS,EAAE;QAC3BkG,WAAW,GAAG,sBAAsB;MACxC;MACA,IAAI,IAAI,CAACtF,MAAM,EAAE;QACbmF,MAAM,CAACI,IAAI,CAAC,IAAI,CAACvF,MAAM,CAAC,CAACrC,OAAO,CAAE6H,GAAG,IAAK;UACtC,IAAI,CAAC7F,QAAQ,CAAC8F,QAAQ,CAAC,IAAI,CAAClC,UAAU,EAAEiC,GAAG,EAAE,IAAI,CAACxF,MAAM,CAACwF,GAAG,CAAC,CAAC;QAClE,CAAC,CAAC;MACN;MACA,IAAI,IAAI,CAACxB,OAAO,EAAE;QACd,IAAI,CAAC0B,UAAU,CAAC,IAAI,CAAC1B,OAAO,CAAC;MACjC;MACA,IAAI,CAACtG,aAAa,CAACC,OAAO,CAAEC,YAAY,IAAK;QACzC,MAAMC,eAAe,GAAGT,KAAK,CAACU,MAAM,CAACF,YAAY,CAACE,MAAM,CAAC;QACzDD,eAAe,CAACE,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClDX,KAAK,CAACY,QAAQ,CAACH,eAAe,EAAE,IAAI,CAAC;MACzC,CAAC,CAAC;MACF,IAAI8H,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,GAAG,IAAI,CAACpC,UAAU,GAAG,IAAI,CAACoC,MAAM;MAClF,IAAI,CAACA,MAAM,EAAE;QACTA,MAAM,GAAG,IAAI,CAAC9F,OAAO,CAAC3C,MAAM,CAACyI,MAAM,GAAG,IAAI,CAAC9F,OAAO,CAAC3C,MAAM,CAACyI,MAAM,GAAG,IAAI,CAACrI,QAAQ,CAACsI,IAAI;MACzF;MACA,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI,CAACA,KAAK,IAAIA,KAAK,KAAK,KAAK,IAAI,IAAI,CAAChG,OAAO,CAAC3C,MAAM,CAAC2I,KAAK,EAAE;QACxDA,KAAK,GAAG,IAAI,CAAChG,OAAO,CAAC3C,MAAM,CAAC2I,KAAK;MACrC;MACA,IAAIC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACA,QAAQ,KAAK,KAAK,EAAE;QACtCA,QAAQ,GAAG,IAAI,CAACjG,OAAO,CAAC3C,MAAM,CAAC4I,QAAQ,KAAK1G,SAAS,GAAG,IAAI,CAACS,OAAO,CAAC3C,MAAM,CAAC4I,QAAQ,GAAG,KAAK;MAChG;MACA,IAAIxF,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC9C,IAAI,IAAI,CAACT,OAAO,CAAC3C,MAAM,CAAC6I,cAAc,CAAC,mBAAmB,CAAC,EAAE;QACzDzF,iBAAiB,GAAG,IAAI,CAACT,OAAO,CAAC3C,MAAM,CAACoD,iBAAiB;MAC7D;MACA,IAAI0F,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;MAChD,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,KAAK,IAAI,EAAE;QACzDA,kBAAkB,GACd,IAAI,CAACnG,OAAO,CAAC3C,MAAM,CAAC8I,kBAAkB,KAAK,IAAI,IACxC,IAAI,CAACnG,OAAO,CAAC3C,MAAM,CAAC8I,kBAAkB,GAAG,IAAI,CAACnG,OAAO,CAAC3C,MAAM,CAAC8I,kBAAkB,GAAG,IAAI;MACrG;MACA,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK7G,SAAS,EAAE;QACnC6G,OAAO,GAAG,IAAI,CAACpG,OAAO,CAAC3C,MAAM,CAAC+I,OAAO,GAAG,CAAC,GAAG,IAAI,CAACpG,OAAO,CAAC3C,MAAM,CAAC+I,OAAO,CAAC,GAAI,IAAI,CAACpG,OAAO,CAAC3C,MAAM,CAAC+I,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG7G,SAAU;MACxI;MACA,IAAI,CAACQ,IAAI,CAACsG,iBAAiB,CAAC,MAAM;QAC9B,IAAI,CAAC/E,WAAW,GAAG,IAAI/D,KAAK,CAAC,IAAI,CAACmG,UAAU,EAAE;UAC1CoC,MAAM;UACNE,KAAK,EAAEA,KAAK;UACZI,OAAO,EAAEA,OAAO;UAChBzH,OAAO;UACP8G,WAAW;UACXQ,QAAQ;UACRxF,iBAAiB;UACjB0F,kBAAkB,EAAEA,kBAAkB;UACtC/F,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBkG,KAAK,EAAE,IAAI,CAACA,KAAK,KAAK,IAAI,CAACtG,OAAO,CAAC3C,MAAM,CAACiJ,KAAK,GAAG,IAAI,CAACtG,OAAO,CAAC3C,MAAM,CAACiJ,KAAK,GAAG,MAAM;QACxF,CAAC,CAAC;QACF;QACA,IAAI,IAAI,CAACC,eAAe,EAAE;UACtB,MAAMC,OAAO,GAAG,IAAI,CAAClF,WAAW,EAAEgF,KAAK,EAAEE,OAAO;UAChD,MAAMC,KAAK,GAAGD,OAAO,EAAEE,IAAI,EAAEjF,aAAa,CAAC,kBAAkB,CAAC;UAC9D,IAAIgF,KAAK,EAAEE,OAAO,EAAE;YAChBF,KAAK,CAACE,OAAO,CAACC,IAAI,GAAG,IAAI,CAACL,eAAe;UAC7C;QACJ;MACJ,CAAC,CAAC;MACF,IAAI,IAAI,CAAC9C,OAAO,EAAE;QACd,MAAM1G,MAAM,GAAGD,SAAS,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACiD,OAAO,CAAC3C,MAAM,CAACN,MAAM,CAAC;QACjE,IAAIA,MAAM,KAAK,MAAM,EAAE;UACnB,IAAI,CAACuE,WAAW,CAACuF,OAAO,CAAC,IAAI,CAACpD,OAAO,EAAE,QAAQ,CAAC;QACpD,CAAC,MACI;UACD,MAAMqD,QAAQ,GAAG,IAAI,CAAC7E,WAAW,CAAC,IAAI,CAACX,WAAW,EAAE,IAAI,CAACmC,OAAO,CAAC;UACjE,IAAI,CAACnC,WAAW,CAACyF,WAAW,CAACD,QAAQ,EAAE,QAAQ,CAAC;QACpD;QACA,IAAI,CAACxF,WAAW,CAAC0F,SAAS,CAAC,SAAS,CAAC,CAACC,KAAK,EAAE;MACjD;MACA;MACA,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACC,sBAAsB,EAAE;MAC7B;MACA;MACA,IAAI,CAAC,IAAI,CAACzG,eAAe,CAACsC,QAAQ,IAAI,CAAC,IAAI,CAACoE,kBAAkB,EAAE;QAC5D;MACJ;MACA;MACA;MACA;MACAC,qBAAqB,CAAC,MAAM;QACxB,IAAI,IAAI,CAACD,kBAAkB,EAAE;UACzB,IAAI,CAACA,kBAAkB,EAAE;QAC7B;QACA,IAAI,CAAC1G,eAAe,CAACwC,IAAI,CAAC,IAAI,CAAC5B,WAAW,CAAC;QAC3C,IAAI,CAACZ,eAAe,CAAC4G,QAAQ,EAAE;MACnC,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,WAAW,GAAG;IACV,IAAI,CAACC,OAAO,EAAE;IACd,IAAI,CAACpG,iBAAiB,EAAEqG,WAAW,EAAE;IACrC,IAAI,CAACrG,iBAAiB,GAAG,IAAI;EACjC;EACAsG,WAAW,CAACC,OAAO,EAAE;IACjB,IAAI,CAAC,IAAI,CAACrG,WAAW,EAAE;MACnB;IACJ;IACA;IACA,IAAIqG,OAAO,CAAC1B,QAAQ,EAAE;MAClB,IAAI,CAAC3E,WAAW,CAACsG,MAAM,CAAC,CAACD,OAAO,CAAC1B,QAAQ,CAAC4B,YAAY,CAAC;IAC3D;IACA,IAAIF,OAAO,CAAClC,WAAW,EAAE;MACrB,IAAI,CAACnE,WAAW,CAACoF,IAAI,CAACC,OAAO,CAAClB,WAAW,GACrCkC,OAAO,CAAClC,WAAW,CAACoC,YAAY;IACxC;IACA,IAAIF,OAAO,CAAClH,iBAAiB,EAAE;MAC3B,IAAI,CAACa,WAAW,CAACoF,IAAI,CAACC,OAAO,CAAClG,iBAAiB,GAC3CkH,OAAO,CAAClH,iBAAiB,CAACoH,YAAY;IAC9C;IACA,IAAIF,OAAO,CAACxH,MAAM,EAAE;MAChB,MAAM2H,cAAc,GAAGH,OAAO,CAACxH,MAAM,CAAC0H,YAAY;MAClD,MAAME,eAAe,GAAGJ,OAAO,CAACxH,MAAM,CAAC6H,aAAa;MACpD,IAAID,eAAe,EAAE;QACjBzC,MAAM,CAACI,IAAI,CAACqC,eAAe,CAAC,CAACjK,OAAO,CAAE6H,GAAG,IAAK;UAC1C,IAAI,CAAC7F,QAAQ,CAACmI,WAAW,CAAC,IAAI,CAACvE,UAAU,EAAEiC,GAAG,CAAC;QACnD,CAAC,CAAC;MACN;MACA,IAAImC,cAAc,EAAE;QAChBxC,MAAM,CAACI,IAAI,CAACoC,cAAc,CAAC,CAAChK,OAAO,CAAE6H,GAAG,IAAK;UACzC,IAAI,CAAC7F,QAAQ,CAAC8F,QAAQ,CAAC,IAAI,CAAClC,UAAU,EAAEiC,GAAG,EAAE,IAAI,CAACxF,MAAM,CAACwF,GAAG,CAAC,CAAC;QAClE,CAAC,CAAC;MACN;IACJ;IACA,IAAIgC,OAAO,CAACxD,OAAO,EAAE;MACjB,MAAM+D,cAAc,GAAGP,OAAO,CAACxD,OAAO,CAAC0D,YAAY;MACnD,MAAMM,eAAe,GAAGR,OAAO,CAACxD,OAAO,CAAC6D,aAAa;MACrD,IAAIG,eAAe,EAAE;QACjB,IAAI,CAACC,aAAa,CAACD,eAAe,CAAC;MACvC;MACA,IAAID,cAAc,EAAE;QAChB,IAAI,CAACrC,UAAU,CAACqC,cAAc,CAAC;MACnC;IACJ;IACA;IACA;IACA,IAAIP,OAAO,CAAChL,YAAY,EAAE;MACtB,IAAI,CAACwK,sBAAsB,EAAE;IACjC;IACA;EACJ;;EACAtB,UAAU,CAACzB,SAAS,EAAE;IAClB3E,eAAe,CAACyE,mBAAmB,CAACE,SAAS,CAAC,CAACtG,OAAO,CAAEuK,CAAC,IAAK;MAC1D,IAAI,CAACvI,QAAQ,CAACwI,QAAQ,CAAC,IAAI,CAAC5E,UAAU,EAAE2E,CAAC,CAAC;IAC9C,CAAC,CAAC;EACN;EACAD,aAAa,CAAChE,SAAS,EAAE;IACrB3E,eAAe,CAACyE,mBAAmB,CAACE,SAAS,CAAC,CAACtG,OAAO,CAAEuK,CAAC,IAAK;MAC1D,IAAI,CAACvI,QAAQ,CAACyI,WAAW,CAAC,IAAI,CAAC7E,UAAU,EAAE2E,CAAC,CAAC;IACjD,CAAC,CAAC;EACN;EACAG,UAAU,CAACX,YAAY,EAAE;IACrB;IACA,IAAI,IAAI,CAACrH,UAAU,IAAIqH,YAAY,KAAK,IAAI,EAAE;MAC1C;IACJ;IACA,IAAI,CAACpE,OAAO,GAAGoE,YAAY;IAC3B,IAAI,CAAC,IAAI,CAACvG,WAAW,EAAE;MACnB;IACJ;IACA,MAAMvE,MAAM,GAAGD,SAAS,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACiD,OAAO,CAAC3C,MAAM,CAACN,MAAM,CAAC;IACjE,MAAM+J,QAAQ,GAAG,IAAI,CAAC7E,WAAW,CAAC,IAAI,CAACX,WAAW,EAAEuG,YAAY,CAAC;IACjE,IAAI,IAAI,CAACtH,aAAa,EAAE;MACpB,MAAMkI,kBAAkB,GAAG,IAAI,CAACnH,WAAW,CAACO,WAAW,EAAE;MACzD,IAAIC,IAAI,CAACC,SAAS,CAAC0G,kBAAkB,CAAC,KAAK3G,IAAI,CAACC,SAAS,CAAC+E,QAAQ,CAAC,EAAE;QACjE;MACJ;IACJ;IACA,IAAIe,YAAY,EAAE;MACd,IAAI9K,MAAM,KAAK,MAAM,EAAE;QACnB,IAAI,CAACuE,WAAW,CAACuF,OAAO,CAACgB,YAAY,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAACvG,WAAW,CAACyF,WAAW,CAACD,QAAQ,CAAC;MAC1C;MACA;IACJ;IACA,IAAI,CAACxF,WAAW,CAACuF,OAAO,CAAC,EAAE,CAAC;EAChC;EACAK,gBAAgB,CAACwB,UAAU,GAAG,IAAI,CAAC1H,QAAQ,EAAE;IACzC;IACA,IAAI,CAACA,QAAQ,GAAG0H,UAAU;IAC1B,IAAI,IAAI,CAACpH,WAAW,EAAE;MAClB,IAAIoH,UAAU,EAAE;QACZ,IAAI,CAACpH,WAAW,CAACqH,OAAO,EAAE;QAC1B,IAAI,CAAC7I,QAAQ,CAAC8I,YAAY,CAAC,IAAI,CAAClJ,UAAU,CAAC0F,aAAa,EAAE,UAAU,EAAE,UAAU,CAAC;MACrF,CAAC,MACI;QACD,IAAI,CAAC,IAAI,CAACa,QAAQ,EAAE;UAChB,IAAI,CAAC3E,WAAW,CAACsG,MAAM,EAAE;QAC7B;QACA,IAAI,CAAC9H,QAAQ,CAAC+I,eAAe,CAAC,IAAI,CAACnJ,UAAU,CAAC0F,aAAa,EAAE,UAAU,CAAC;MAC5E;IACJ;EACJ;EACA0D,gBAAgB,CAACC,EAAE,EAAE;IACjB,IAAI,CAAClF,aAAa,GAAGkF,EAAE;EAC3B;EACAC,iBAAiB,CAACD,EAAE,EAAE;IAClB,IAAI,CAAChG,cAAc,GAAGgG,EAAE;EAC5B;EACAE,yBAAyB,CAACF,EAAE,EAAE;IAC1B,IAAI,CAAC3B,kBAAkB,GAAG2B,EAAE;EAChC;EACAG,QAAQ,GAAG;IACP,IAAI,CAAC,IAAI,CAAC5H,WAAW,EAAE;MACnB,OAAO,IAAI;IACf;IACA,MAAM6H,GAAG,GAAG,CAAC,CAAC;IACd,IAAIC,KAAK,GAAG,IAAI;IAChB,MAAM5F,IAAI,GAAG,IAAI,CAAClC,WAAW,CAACM,OAAO,EAAE;IACvC;IACA,MAAMyH,UAAU,GAAG,IAAI,CAAC/I,gBAAgB,GAAGkD,IAAI,CAACa,IAAI,EAAE,CAACiF,MAAM,GAAI9F,IAAI,CAAC8F,MAAM,KAAK,CAAC,IAAI9F,IAAI,CAACa,IAAI,EAAE,CAACiF,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG9F,IAAI,CAAC8F,MAAM,GAAG,CAAE;IACrI,MAAMC,eAAe,GAAG,IAAI,CAACjI,WAAW,CAACO,WAAW,EAAE,CAAC2H,GAAG;IAC1D,MAAMC,kBAAkB,GAAGF,eAAe,IAAIA,eAAe,CAACD,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAClH,QAAQ,CAACmH,eAAe,CAAC,CAAC,CAAC,CAAC9G,MAAM,CAAC;IAC5H,IAAI,IAAI,CAACiH,SAAS,IAAIL,UAAU,IAAIA,UAAU,GAAG,IAAI,CAACK,SAAS,EAAE;MAC7DP,GAAG,CAACQ,cAAc,GAAG;QACjBC,KAAK,EAAEP,UAAU;QACjBK,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC;MACDN,KAAK,GAAG,KAAK;IACjB;IACA,IAAI,IAAI,CAACS,SAAS,IAAIR,UAAU,GAAG,IAAI,CAACQ,SAAS,EAAE;MAC/CV,GAAG,CAACW,cAAc,GAAG;QACjBF,KAAK,EAAEP,UAAU;QACjBQ,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC;MACDT,KAAK,GAAG,KAAK;IACjB;IACA,IAAI,IAAI,CAACnJ,QAAQ,IAAI,CAACoJ,UAAU,IAAII,kBAAkB,EAAE;MACpDN,GAAG,CAACY,aAAa,GAAG;QAChBC,KAAK,EAAE;MACX,CAAC;MACDZ,KAAK,GAAG,KAAK;IACjB;IACA,OAAOA,KAAK,GAAG,IAAI,GAAGD,GAAG;EAC7B;EACAhC,sBAAsB,GAAG;IACrB,IAAI,CAACK,OAAO,EAAE;IACd;IACA;IACA;IACA,IAAI,CAACzH,IAAI,CAACsG,iBAAiB,CAAC,MAAM;MAC9B,IAAI,CAAClF,YAAY,GAAG,IAAI5E,YAAY,EAAE;MACtC,IAAI,CAAC4E,YAAY,CAAC8I,GAAG;MACrB;MACAzN,SAAS,CAAC,IAAI,CAAC8E,WAAW,EAAE,kBAAkB,CAAC,CAAC6D,SAAS,CAAC,CAAC,CAACxC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,CAAC,KAAK;QACrF,IAAI,CAACH,sBAAsB,CAACC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,CAAC;MACxD,CAAC,CAAC,CAAC;MACH;MACA;MACA,IAAIqH,WAAW,GAAG1N,SAAS,CAAC,IAAI,CAAC8E,WAAW,EAAE,aAAa,CAAC;MAC5D,IAAI6I,aAAa,GAAG3N,SAAS,CAAC,IAAI,CAAC8E,WAAW,EAAE,eAAe,CAAC;MAChE,IAAI,OAAO,IAAI,CAAC3E,YAAY,KAAK,QAAQ,EAAE;QACvCuN,WAAW,GAAGA,WAAW,CAAC3L,IAAI,CAAC5B,YAAY,CAAC,IAAI,CAACA,YAAY,CAAC,CAAC;QAC/DwN,aAAa,GAAGA,aAAa,CAAC5L,IAAI,CAAC5B,YAAY,CAAC,IAAI,CAACA,YAAY,CAAC,CAAC;MACvE;MACA,IAAI,CAACwE,YAAY,CAAC8I,GAAG;MACrB;MACAC,WAAW,CAAC/E,SAAS,CAAC,CAAC,CAAC7B,KAAK,EAAEC,QAAQ,EAAEV,MAAM,CAAC,KAAK;QACjD,IAAI,CAACQ,iBAAiB,CAACC,KAAK,EAAEC,QAAQ,EAAEV,MAAM,CAAC;MACnD,CAAC,CAAC,CAAC;MACH,IAAI,CAAC1B,YAAY,CAAC8I,GAAG;MACrB;MACAE,aAAa,CAAChF,SAAS,CAAC,CAAC,CAACpB,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEpB,MAAM,CAAC,KAAK;QACvD,IAAI,CAACiB,mBAAmB,CAACC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEpB,MAAM,CAAC;MACzD,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN;EACA2E,OAAO,GAAG;IACN,IAAI,IAAI,CAACrG,YAAY,KAAK,IAAI,EAAE;MAC5B,IAAI,CAACA,YAAY,CAACsG,WAAW,EAAE;MAC/B,IAAI,CAACtG,YAAY,GAAG,IAAI;IAC5B;EACJ;AACJ;AACA1B,eAAe,CAACR,IAAI;EAAA,iBAAwFQ,eAAe,EAhhB5B7E,EAAE,mBAghB4CA,EAAE,CAACsE,QAAQ,GAhhBzDtE,EAAE,mBAghBoEA,EAAE,CAACU,UAAU,GAhhBnFV,EAAE,mBAghB8FA,EAAE,CAACW,iBAAiB,GAhhBpHX,EAAE,mBAghB+HsB,EAAE,CAACC,YAAY,GAhhBhJvB,EAAE,mBAghB2JM,WAAW,GAhhBxKN,EAAE,mBAghBmLA,EAAE,CAACY,SAAS,GAhhBjMZ,EAAE,mBAghB4MA,EAAE,CAACa,MAAM,GAhhBvNb,EAAE,mBAghBkOsC,YAAY;AAAA,CAA4C;AAC3XuC,eAAe,CAAC2K,IAAI,kBAjhB2ExP,EAAE;EAAA,MAihBD6E,eAAe;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAjhBhB7E,EAAE;AAAA,EAihB+jC;AAChqC;EAAA,mDAlhB+FA,EAAE,mBAkhBN6E,eAAe,EAAc,CAAC;IAC7GL,IAAI,EAAEjE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiE,IAAI,EAAExE,EAAE,CAACsE;IAAS,CAAC,EAAE;MAAEE,IAAI,EAAExE,EAAE,CAACU;IAAW,CAAC,EAAE;MAAE8D,IAAI,EAAExE,EAAE,CAACW;IAAkB,CAAC,EAAE;MAAE6D,IAAI,EAAElD,EAAE,CAACC;IAAa,CAAC,EAAE;MAAEiD,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QACzKJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAACnE,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEkE,IAAI,EAAExE,EAAE,CAACY;IAAU,CAAC,EAAE;MAAE4D,IAAI,EAAExE,EAAE,CAACa;IAAO,CAAC,EAAE;MAAE2D,IAAI,EAAElC;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEH,MAAM,EAAE,CAAC;MAC1GqC,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEkL,KAAK,EAAE,CAAC;MACRlH,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEuD,OAAO,EAAE,CAAC;MACVS,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE4K,KAAK,EAAE,CAAC;MACR5G,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE6K,QAAQ,EAAE,CAAC;MACX7G,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEqK,WAAW,EAAE,CAAC;MACdrG,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEyO,SAAS,EAAE,CAAC;MACZzK,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEsO,SAAS,EAAE,CAAC;MACZtK,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE6E,QAAQ,EAAE,CAAC;MACXb,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEgL,OAAO,EAAE,CAAC;MACVhH,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE8E,qBAAqB,EAAE,CAAC;MACxBd,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE+G,QAAQ,EAAE,CAAC;MACX/C,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE2J,YAAY,EAAE,CAAC;MACf3F,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE+E,MAAM,EAAE,CAAC;MACTf,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEgF,MAAM,EAAE,CAAC;MACThB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE+K,kBAAkB,EAAE,CAAC;MACrB/G,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE0K,MAAM,EAAE,CAAC;MACT1G,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEyC,aAAa,EAAE,CAAC;MAChBuB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEkD,aAAa,EAAE,CAAC;MAChBc,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEuI,YAAY,EAAE,CAAC;MACfvE,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEiF,kBAAkB,EAAE,CAAC;MACrBjB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE+I,OAAO,EAAE,CAAC;MACV/E,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEkF,gBAAgB,EAAE,CAAC;MACnBlB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEmL,eAAe,EAAE,CAAC;MAClBnH,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEmF,aAAa,EAAE,CAAC;MAChBnB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEoF,UAAU,EAAE,CAAC;MACbpB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEuB,YAAY,EAAE,CAAC;MACfyC,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEqF,iBAAiB,EAAE,CAAC;MACpBrB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEsF,eAAe,EAAE,CAAC;MAClBtB,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEsF,eAAe,EAAE,CAAC;MAClBvB,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEuF,gBAAgB,EAAE,CAAC;MACnBxB,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEwF,kBAAkB,EAAE,CAAC;MACrBzB,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEyF,OAAO,EAAE,CAAC;MACV1B,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAE0F,MAAM,EAAE,CAAC;MACT3B,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEgG,WAAW,EAAE,CAAC;MACdjC,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE6G,WAAW,EAAE,CAAC;MACd7C,IAAI,EAAEhE;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMiP,oBAAoB,SAAS5K,eAAe,CAAC;EAC/CtC,WAAW,CAACC,QAAQ,EAAEsC,UAAU,EAAEC,EAAE,EAAEC,YAAY,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACrF,KAAK,CAAC5C,QAAQ,EAAEsC,UAAU,EAAEC,EAAE,EAAEC,YAAY,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,CAAC;EACtF;AACJ;AACAqK,oBAAoB,CAACpL,IAAI;EAAA,iBAAwFoL,oBAAoB,EArmBtCzP,EAAE,mBAqmBsDA,EAAE,CAACsE,QAAQ,GArmBnEtE,EAAE,mBAqmB8EU,UAAU,GArmB1FV,EAAE,mBAqmBqGW,iBAAiB,GArmBxHX,EAAE,mBAqmBmIuB,YAAY,GArmBjJvB,EAAE,mBAqmB4JM,WAAW,GArmBzKN,EAAE,mBAqmBoLY,SAAS,GArmB/LZ,EAAE,mBAqmB0Ma,MAAM,GArmBlNb,EAAE,mBAqmB6NsC,YAAY;AAAA,CAA4C;AACtXmN,oBAAoB,CAACC,IAAI,kBAtmBsE1P,EAAE;EAAA,MAsmBIyP,oBAAoB;EAAA;EAAA;EAAA,WAtmB1BzP,EAAE,oBAsmBmF,CAC5K;IACI2P,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE5N,iBAAiB;IAC1B;IACA6N,WAAW,EAAE/O,UAAU,CAAC,MAAM2O,oBAAoB;EACtD,CAAC,EACD;IACIE,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE3N,aAAa;IACtB;IACA4N,WAAW,EAAE/O,UAAU,CAAC,MAAM2O,oBAAoB;EACtD,CAAC,CACJ,GAnnB0FzP,EAAE,6BAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE;MAAFA,EAAE,qFAunBhF;MAvnB8EA,EAAE,gBAwnBtC;MAxnBoCA,EAAE,qFA4nBhF;IAAA;IAAA;MA5nB8EA,EAAE,kDAonBjD;MApnB+CA,EAAE,aAynBjD;MAznB+CA,EAAE,kDAynBjD;IAAA;EAAA;EAAA,eAIuDqB,YAAY,EAA+BH,EAAE,CAAC4O,IAAI;EAAA;AAAA,EAAoE;AAC7N;EAAA,mDA9nB+F9P,EAAE,mBA8nBNyP,oBAAoB,EAAc,CAAC;IAClHjL,IAAI,EAAEzD,SAAS;IACf0D,IAAI,EAAE,CAAC;MAAEsL,aAAa,EAAE/O,iBAAiB,CAACgP,QAAQ;MAAEC,SAAS,EAAE,CACnD;QACIN,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE5N,iBAAiB;QAC1B;QACA6N,WAAW,EAAE/O,UAAU,CAAC,MAAM2O,oBAAoB;MACtD,CAAC,EACD;QACIE,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE3N,aAAa;QACtB;QACA4N,WAAW,EAAE/O,UAAU,CAAC,MAAM2O,oBAAoB;MACtD,CAAC,CACJ;MAAES,QAAQ,EAAE,cAAc;MAAEC,QAAQ,EAAG;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;MAAEC,UAAU,EAAE,IAAI;MAAEC,OAAO,EAAE,CAAChP,YAAY,CAAC;MAAEkE,MAAM,EAAE,CAAC,+BAA+B;IAAE,CAAC;EACjF,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEf,IAAI,EAAExE,EAAE,CAACsE;IAAS,CAAC,EAAE;MAAEE,IAAI,EAAExE,EAAE,CAACU,UAAU;MAAEkE,UAAU,EAAE,CAAC;QACzFJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAAC/D,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAE8D,IAAI,EAAExE,EAAE,CAACW,iBAAiB;MAAEiE,UAAU,EAAE,CAAC;QAC7CJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAAC9D,iBAAiB;MAC5B,CAAC;IAAE,CAAC,EAAE;MAAE6D,IAAI,EAAElD,EAAE,CAACC,YAAY;MAAEqD,UAAU,EAAE,CAAC;QACxCJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAAClD,YAAY;MACvB,CAAC;IAAE,CAAC,EAAE;MAAEiD,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAACnE,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEkE,IAAI,EAAExE,EAAE,CAACY,SAAS;MAAEgE,UAAU,EAAE,CAAC;QACrCJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAAC7D,SAAS;MACpB,CAAC;IAAE,CAAC,EAAE;MAAE4D,IAAI,EAAExE,EAAE,CAACa,MAAM;MAAE+D,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAAC5D,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAE2D,IAAI,EAAElC,YAAY;MAAEsC,UAAU,EAAE,CAAC;QACrCJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAACnC,YAAY;MACvB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMgO,sBAAsB,CAAC;EACzB/N,WAAW,CAACgO,SAAS,EAAEnL,OAAO,EAAE;IAC5B,IAAI,CAACmL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACnL,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACyD,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC/B,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC0J,UAAU,GAAG,SAAS;EAC/B;EACA1D,WAAW,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACrB,KAAK,EAAE;MACf,MAAMA,KAAK,GAAGqB,OAAO,CAACrB,KAAK,CAACuB,YAAY,KAAK,IAAI,CAAC7H,OAAO,CAAC3C,MAAM,CAACiJ,KAAK,GAAG,IAAI,CAACtG,OAAO,CAAC3C,MAAM,CAACiJ,KAAK,GAAG,MAAM,CAAC;MAC5G,IAAI,CAAC8E,UAAU,GAAI,MAAK9E,KAAM,sBAAqB;IACvD,CAAC,MACI,IAAI,CAAC,IAAI,CAACA,KAAK,EAAE;MAClB,MAAMA,KAAK,GAAG,IAAI,CAACtG,OAAO,CAAC3C,MAAM,CAACiJ,KAAK,GAAG,IAAI,CAACtG,OAAO,CAAC3C,MAAM,CAACiJ,KAAK,GAAG,MAAM;MAC5E,IAAI,CAAC8E,UAAU,GAAI,MAAK9E,KAAM,sBAAqB;IACvD;IACA,IAAIqB,OAAO,CAAClE,OAAO,EAAE;MACjB,MAAMA,OAAO,GAAGkE,OAAO,CAAClE,OAAO,CAACoE,YAAY;MAC5C,MAAM1F,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACD,QAAQ,CAAC,GAAG,IAAI,CAACA,QAAQ,GAAI,IAAI,CAACnC,OAAO,CAAC3C,MAAM,CAAC8E,QAAQ,IAAI,KAAM;MAChH,IAAI,CAACT,SAAS,GAAGS,QAAQ,GAAGsB,OAAO,GAAG,IAAI,CAAC0H,SAAS,CAACE,uBAAuB,CAAC5H,OAAO,CAAC;IACzF;EACJ;AACJ;AACAyH,sBAAsB,CAACjM,IAAI;EAAA,iBAAwFiM,sBAAsB,EAvsB1CtQ,EAAE,mBAusB0DuB,YAAY,GAvsBxEvB,EAAE,mBAusBmFsC,YAAY;AAAA,CAA4C;AAC5OgO,sBAAsB,CAACZ,IAAI,kBAxsBoE1P,EAAE;EAAA,MAwsBMsQ,sBAAsB;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAxsB9BtQ,EAAE,uBAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,4BAysB9C;MAzsB4CA,EAAE,uBA2sBvF;MA3sBqFA,EAAE,eA4sBzF;IAAA;IAAA;MA5sBuFA,EAAE,sCAysB/C;MAzsB6CA,EAAE,aA0sB/C;MA1sB6CA,EAAE,wCAAFA,EAAE,gBA0sB/C;IAAA;EAAA;EAAA,eAGqEqB,YAAY,EAA+BH,EAAE,CAACwP,OAAO;EAAA;EAAA;AAAA,EAAqG;AACjR;EAAA,mDA9sB+F1Q,EAAE,mBA8sBNsQ,sBAAsB,EAAc,CAAC;IACpH9L,IAAI,EAAEzD,SAAS;IACf0D,IAAI,EAAE,CAAC;MAAEsL,aAAa,EAAE/O,iBAAiB,CAAC2P,IAAI;MAAET,QAAQ,EAAE,iBAAiB;MAAEC,QAAQ,EAAG;AACpG;AACA;AACA;AACA;AACA,CAAC;MAAEC,UAAU,EAAE,IAAI;MAAEC,OAAO,EAAE,CAAChP,YAAY,CAAC;MAAEkE,MAAM,EAAE,CAAC,+CAA+C;IAAE,CAAC;EACjG,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEf,IAAI,EAAElD,EAAE,CAACC,YAAY;MAAEqD,UAAU,EAAE,CAAC;QACpEJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAAClD,YAAY;MACvB,CAAC;IAAE,CAAC,EAAE;MAAEiD,IAAI,EAAElC;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuG,OAAO,EAAE,CAAC;MAC9DrE,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEkL,KAAK,EAAE,CAAC;MACRlH,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE+G,QAAQ,EAAE,CAAC;MACX/C,IAAI,EAAEhE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoQ,kBAAkB,CAAC;EACrBrO,WAAW,CAACuC,UAAU,EAAEI,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAEJ,YAAY,EAAEC,UAAU,EAAE;IACvE,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACI,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACJ,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACO,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC9B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACT,aAAa,GAAG,EAAE;IACvB,IAAI,CAACwC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACK,eAAe,GAAG,IAAI1F,YAAY,EAAE;IACzC,IAAI,CAACiG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACG,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACa,WAAW,GAAG,CAACX,WAAW,EAAEY,KAAK,KAAK;MACvC,MAAMnF,MAAM,GAAGD,SAAS,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACiD,OAAO,CAAC3C,MAAM,CAACN,MAAM,CAAC;MACjE,IAAI0G,OAAO,GAAGvB,KAAK;MACnB,IAAInF,MAAM,KAAK,MAAM,EAAE;QACnBuE,WAAW,CAACuF,OAAO,CAACpD,OAAO,CAAC;MAChC,CAAC,MACI;QACD,IAAI1G,MAAM,KAAK,MAAM,EAAE;UACnB,MAAMoF,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACD,QAAQ,CAAC,GAAG,IAAI,CAACA,QAAQ,GAAI,IAAI,CAACnC,OAAO,CAAC3C,MAAM,CAAC8E,QAAQ,IAAI,KAAM;UAChH,IAAIA,QAAQ,EAAE;YACVD,KAAK,GAAG,IAAI,CAACtC,YAAY,CAACuC,QAAQ,CAAClH,eAAe,CAACoH,IAAI,EAAEH,KAAK,CAAC;UACnE;UACAuB,OAAO,GAAGnC,WAAW,CAACgB,SAAS,CAACC,OAAO,CAACL,KAAK,CAAC;QAClD,CAAC,MACI,IAAInF,MAAM,KAAK,MAAM,EAAE;UACxB,IAAI;YACA0G,OAAO,GAAG3B,IAAI,CAACU,KAAK,CAACN,KAAK,CAAC;UAC/B,CAAC,CACD,OAAOF,CAAC,EAAE;YACNyB,OAAO,GAAG,CAAC;cAAEhB,MAAM,EAAEP;YAAM,CAAC,CAAC;UACjC;QACJ;QACAZ,WAAW,CAACyF,WAAW,CAACtD,OAAO,CAAC;MACpC;IACJ,CAAC;EACL;EACAmB,QAAQ,GAAG;IACP,IAAI,CAAC3D,QAAQ,GAAG,IAAI,CAACZ,kBAAkB;EAC3C;EACAqH,WAAW,CAACC,OAAO,EAAE;IACjB,IAAI,CAAC,IAAI,CAACrG,WAAW,EAAE;MACnB;IACJ;IACA,IAAIqG,OAAO,CAAClE,OAAO,EAAE;MACjB,IAAI,CAACxB,WAAW,CAAC,IAAI,CAACX,WAAW,EAAEqG,OAAO,CAAClE,OAAO,CAACoE,YAAY,CAAC;IACpE;EACJ;EACAhD,eAAe,GAAG;IACd,IAAI7I,gBAAgB,CAAC,IAAI,CAAC6D,UAAU,CAAC,EAAE;MACnC;IACJ;IACA,IAAI,CAACuB,iBAAiB,GAAG,IAAI,CAACpB,OAAO,CAACpB,QAAQ,EAAE,CAACL,IAAI,CAAC7B,QAAQ,CAAEa,KAAK,IAAK;MACtE,MAAMuH,QAAQ,GAAG,CAAC,IAAI,CAAC9E,OAAO,CAAC3B,qBAAqB,CAACd,KAAK,EAAE,IAAI,CAACe,aAAa,CAAC,CAAC;MAChF,MAAMyG,YAAY,GAAG,IAAI,CAACA,YAAY,IAAI,IAAI,CAAC/E,OAAO,CAAC3C,MAAM,CAAC0H,YAAY;MAC1E,IAAIA,YAAY,EAAE;QACdD,QAAQ,CAACH,IAAI,CAACI,YAAY,EAAE,CAAC;MACjC;MACA,OAAOC,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC,CAACI,IAAI,CAAC,MAAM3H,KAAK,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC4H,SAAS,CAAC5H,KAAK,IAAI;MACnB,MAAMoB,OAAO,GAAG2G,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC5G,OAAO,IAAI,IAAI,CAACqB,OAAO,CAAC3C,MAAM,CAACsB,OAAO,CAAC;MAC9EA,OAAO,CAAC6G,OAAO,GAAG,KAAK;MACvB,IAAI,CAAC3H,aAAa,CAACC,OAAO,CAAEC,YAAY,IAAK;QACzC,MAAMC,eAAe,GAAGT,KAAK,CAACU,MAAM,CAACF,YAAY,CAACE,MAAM,CAAC;QACzDD,eAAe,CAACE,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClDX,KAAK,CAACY,QAAQ,CAACH,eAAe,EAAE,IAAI,CAAC;MACzC,CAAC,CAAC;MACF,IAAIgI,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI,CAACA,KAAK,IAAIA,KAAK,KAAK,KAAK,IAAI,IAAI,CAAChG,OAAO,CAAC3C,MAAM,CAAC2I,KAAK,EAAE;QACxDA,KAAK,GAAG,IAAI,CAAChG,OAAO,CAAC3C,MAAM,CAAC2I,KAAK;MACrC;MACA,IAAII,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK7G,SAAS,EAAE;QACnC6G,OAAO,GAAG,IAAI,CAACpG,OAAO,CAAC3C,MAAM,CAAC+I,OAAO,GACjCd,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACvF,OAAO,CAAC3C,MAAM,CAAC+I,OAAO,CAAC,GAAI,IAAI,CAACpG,OAAO,CAAC3C,MAAM,CAAC+I,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG7G,SAAU;MAClH;MACA,MAAM+G,KAAK,GAAG,IAAI,CAACA,KAAK,KAAK,IAAI,CAACtG,OAAO,CAAC3C,MAAM,CAACiJ,KAAK,GAAG,IAAI,CAACtG,OAAO,CAAC3C,MAAM,CAACiJ,KAAK,GAAG,MAAM,CAAC;MAC5F,IAAI,CAAC5C,UAAU,GAAG,IAAI,CAAChE,UAAU,CAAC0F,aAAa,CAAC3D,aAAa,CAAC,sBAAsB,CAAC;MACrF,IAAI,CAAC1B,IAAI,CAACsG,iBAAiB,CAAC,MAAM;QAC9B,IAAI,CAAC/E,WAAW,GAAG,IAAI/D,KAAK,CAAC,IAAI,CAACmG,UAAU,EAAE;UAC1CsC,KAAK,EAAEA,KAAK;UACZI,OAAO,EAAEA,OAAO;UAChBzH,OAAO;UACPsH,QAAQ,EAAE,IAAI;UACd7F,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBkG;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACxG,QAAQ,CAACwI,QAAQ,CAAC,IAAI,CAAC5E,UAAU,EAAE,gBAAgB,CAAC;MACzD,IAAI,IAAI,CAACD,OAAO,EAAE;QACd,IAAI,CAACxB,WAAW,CAAC,IAAI,CAACX,WAAW,EAAE,IAAI,CAACmC,OAAO,CAAC;MACpD;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAAC/C,eAAe,CAAC+K,SAAS,CAACnC,MAAM,EAAE;QACxC;MACJ;MACA;MACA;MACA;MACAjC,qBAAqB,CAAC,MAAM;QACxB,IAAI,CAAC3G,eAAe,CAACwC,IAAI,CAAC,IAAI,CAAC5B,WAAW,CAAC;QAC3C,IAAI,CAACZ,eAAe,CAAC4G,QAAQ,EAAE;MACnC,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,WAAW,GAAG;IACV,IAAI,CAACnG,iBAAiB,EAAEqG,WAAW,EAAE;IACrC,IAAI,CAACrG,iBAAiB,GAAG,IAAI;EACjC;AACJ;AACAoK,kBAAkB,CAACvM,IAAI;EAAA,iBAAwFuM,kBAAkB,EAp1BlC5Q,EAAE,mBAo1BkDA,EAAE,CAACU,UAAU,GAp1BjEV,EAAE,mBAo1B4EA,EAAE,CAACY,SAAS,GAp1B1FZ,EAAE,mBAo1BqGA,EAAE,CAACa,MAAM,GAp1BhHb,EAAE,mBAo1B2HsC,YAAY,GAp1BzItC,EAAE,mBAo1BoJsB,EAAE,CAACC,YAAY,GAp1BrKvB,EAAE,mBAo1BgLM,WAAW;AAAA,CAA4C;AACxUsQ,kBAAkB,CAAClB,IAAI,kBAr1BwE1P,EAAE;EAAA,MAq1BE4Q,kBAAkB;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAr1BtB5Q,EAAE,uBAAFA,EAAE;EAAA;EAAA;EAAA;EAAA;IAAA;MAAFA,EAAE,iEAs1BjD;MAt1B+CA,EAAE,iEAu1BlD;IAAA;IAAA;MAv1BgDA,EAAE,kCAs1BzD;MAt1BuDA,EAAE,aAu1B1D;MAv1BwDA,EAAE,iCAu1B1D;IAAA;EAAA;EAAA,eAC2EqB,YAAY,EAA+BH,EAAE,CAAC4O,IAAI;EAAA;EAAA;AAAA,EAA8G;AAClR;EAAA,mDAz1B+F9P,EAAE,mBAy1BN4Q,kBAAkB,EAAc,CAAC;IAChHpM,IAAI,EAAEzD,SAAS;IACf0D,IAAI,EAAE,CAAC;MAAEsL,aAAa,EAAE/O,iBAAiB,CAAC2P,IAAI;MAAET,QAAQ,EAAE,YAAY;MAAEC,QAAQ,EAAG;AAC/F;AACA;AACA,CAAC;MAAEC,UAAU,EAAE,IAAI;MAAEC,OAAO,EAAE,CAAChP,YAAY,CAAC;MAAEkE,MAAM,EAAE,CAAC,0CAA0C;IAAE,CAAC;EAC5F,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEf,IAAI,EAAExE,EAAE,CAACU;IAAW,CAAC,EAAE;MAAE8D,IAAI,EAAExE,EAAE,CAACY;IAAU,CAAC,EAAE;MAAE4D,IAAI,EAAExE,EAAE,CAACa;IAAO,CAAC,EAAE;MAAE2D,IAAI,EAAElC;IAAa,CAAC,EAAE;MAAEkC,IAAI,EAAElD,EAAE,CAACC;IAAa,CAAC,EAAE;MAAEiD,IAAI,EAAEG,SAAS;MAAEC,UAAU,EAAE,CAAC;QACvLJ,IAAI,EAAErE,MAAM;QACZsE,IAAI,EAAE,CAACnE,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE6B,MAAM,EAAE,CAAC;MACrCqC,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEkL,KAAK,EAAE,CAAC;MACRlH,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEuD,OAAO,EAAE,CAAC;MACVS,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE4K,KAAK,EAAE,CAAC;MACR5G,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEgL,OAAO,EAAE,CAAC;MACVhH,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE+G,QAAQ,EAAE,CAAC;MACX/C,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAE2J,YAAY,EAAE,CAAC;MACf3F,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEgF,MAAM,EAAE,CAAC;MACThB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEqI,OAAO,EAAE,CAAC;MACVrE,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEkD,aAAa,EAAE,CAAC;MAChBc,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEyC,aAAa,EAAE,CAAC;MAChBuB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEiF,kBAAkB,EAAE,CAAC;MACrBjB,IAAI,EAAEhE;IACV,CAAC,CAAC;IAAEsF,eAAe,EAAE,CAAC;MAClBtB,IAAI,EAAE/D;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqQ,WAAW,CAAC;EACd,OAAOC,OAAO,CAACtO,MAAM,EAAE;IACnB,OAAO;MACHuO,QAAQ,EAAEF,WAAW;MACrBb,SAAS,EAAE,CACP;QACIL,OAAO,EAAE7P,kBAAkB;QAC3BkR,QAAQ,EAAExO;MACd,CAAC;IAET,CAAC;EACL;AACJ;AACAqO,WAAW,CAACzM,IAAI;EAAA,iBAAwFyM,WAAW;AAAA,CAAkD;AACrKA,WAAW,CAACI,IAAI,kBA54B+ElR,EAAE;EAAA,MA44BQ8Q;AAAW,EAA6J;AACjRA,WAAW,CAACK,IAAI,kBA74B+EnR,EAAE;EAAA,UA64B+ByP,oBAAoB,EAAEmB,kBAAkB,EAAEN,sBAAsB;AAAA,EAAI;AACpM;EAAA,mDA94B+FtQ,EAAE,mBA84BN8Q,WAAW,EAAc,CAAC;IACzGtM,IAAI,EAAEvD,QAAQ;IACdwD,IAAI,EAAE,CAAC;MACC4L,OAAO,EAAE,CAACZ,oBAAoB,EAAEmB,kBAAkB,EAAEN,sBAAsB,CAAC;MAC3Ec,OAAO,EAAE,CAAC3B,oBAAoB,EAAEmB,kBAAkB,EAAEN,sBAAsB;IAC9E,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASzL,eAAe,EAAE4K,oBAAoB,EAAEqB,WAAW,EAAExO,YAAY,EAAEsO,kBAAkB,EAAEN,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}