{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../core/i18n/translation.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/table\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"../material-demo.component\";\nfunction DashboardComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"div\", 11);\n    i0.ɵɵelement(4, \"mat-spinner\", 12);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\")(8, \"em\");\n    i0.ɵɵtext(9, \"\\u041C\\u043E\\u043B\\u044F, \\u043E\\u0431\\u043D\\u043E\\u0432\\u0435\\u0442\\u0435 \\u0441\\u043B\\u0435\\u0434 \\u043A\\u0430\\u0442\\u043E ASP.NET backend-\\u044A\\u0442 \\u0435 \\u0441\\u0442\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430\\u043D.\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.t.common.loading, \"...\");\n  }\n}\nfunction DashboardComponent_mat_card_17_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"calendar_today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u0414\\u0430\\u0442\\u0430 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const forecast_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, forecast_r12.date, \"short\"));\n  }\n}\nfunction DashboardComponent_mat_card_17_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"thermostat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u0422\\u0435\\u043C\\u043F. (\\u00B0C) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", forecast_r13.temperatureC, \"\\u00B0\");\n  }\n}\nfunction DashboardComponent_mat_card_17_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"thermostat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u0422\\u0435\\u043C\\u043F. (\\u00B0F) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", forecast_r14.temperatureF, \"\\u00B0\");\n  }\n}\nfunction DashboardComponent_mat_card_17_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u0420\\u0435\\u0437\\u044E\\u043C\\u0435 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_mat_card_17_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const forecast_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(forecast_r15.summary);\n  }\n}\nfunction DashboardComponent_mat_card_17_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 28);\n  }\n}\nfunction DashboardComponent_mat_card_17_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 29);\n  }\n}\nfunction DashboardComponent_mat_card_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 13)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"table_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u0414\\u0430\\u043D\\u043D\\u0438 \\u0437\\u0430 \\u043F\\u0440\\u043E\\u0433\\u043D\\u043E\\u0437\\u0430\\u0442\\u0430 \\u0437\\u0430 \\u0432\\u0440\\u0435\\u043C\\u0435\\u0442\\u043E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 14)(8, \"table\", 15);\n    i0.ɵɵelementContainerStart(9, 16);\n    i0.ɵɵtemplate(10, DashboardComponent_mat_card_17_th_10_Template, 4, 0, \"th\", 17);\n    i0.ɵɵtemplate(11, DashboardComponent_mat_card_17_td_11_Template, 3, 4, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(12, 19);\n    i0.ɵɵtemplate(13, DashboardComponent_mat_card_17_th_13_Template, 4, 0, \"th\", 17);\n    i0.ɵɵtemplate(14, DashboardComponent_mat_card_17_td_14_Template, 3, 1, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(15, 20);\n    i0.ɵɵtemplate(16, DashboardComponent_mat_card_17_th_16_Template, 4, 0, \"th\", 17);\n    i0.ɵɵtemplate(17, DashboardComponent_mat_card_17_td_17_Template, 3, 1, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18, 21);\n    i0.ɵɵtemplate(19, DashboardComponent_mat_card_17_th_19_Template, 4, 0, \"th\", 17);\n    i0.ɵɵtemplate(20, DashboardComponent_mat_card_17_td_20_Template, 3, 1, \"td\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(21, DashboardComponent_mat_card_17_tr_21_Template, 1, 0, \"tr\", 22);\n    i0.ɵɵtemplate(22, DashboardComponent_mat_card_17_tr_22_Template, 1, 0, \"tr\", 23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.forecasts);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.displayedColumns);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    constructor(http, t) {\n      this.http = http;\n      this.t = t;\n      this.forecasts = [];\n      this.displayedColumns = ['date', 'temperatureC', 'temperatureF', 'summary'];\n    }\n    ngOnInit() {\n      this.getForecasts();\n    }\n    getForecasts() {\n      this.http.get(`${environment.apiUrl.replace('/api', '')}/weatherforecast`).subscribe(result => {\n        this.forecasts = result;\n      }, error => {\n        console.error(error);\n      });\n    }\n    static {\n      this.ɵfac = function DashboardComponent_Factory(t) {\n        return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.TranslationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        decls: 48,\n        vars: 12,\n        consts: [[1, \"dashboard-container\"], [1, \"header-card\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"data-card\", 4, \"ngIf\"], [1, \"data-card\", \"profile-demo-card\"], [1, \"profile-features\"], [1, \"feature-tag\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/profile-demo\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/profile/luna-starweaver\"], [1, \"loading-container\"], [1, \"loading-content\"], [\"diameter\", \"50\"], [1, \"data-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"weather-table\", 3, \"dataSource\"], [\"matColumnDef\", \"date\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"temperatureC\"], [\"matColumnDef\", \"temperatureF\"], [\"matColumnDef\", \"summary\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"temperature\"], [1, \"summary\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n            i0.ɵɵtext(5, \"wb_sunny\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n            i0.ɵɵtext(8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"p\");\n            i0.ɵɵtext(11, \"\\u0422\\u043E\\u0437\\u0438 \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442 \\u0434\\u0435\\u043C\\u043E\\u043D\\u0441\\u0442\\u0440\\u0438\\u0440\\u0430 \\u0438\\u0437\\u0432\\u043B\\u0438\\u0447\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0434\\u0430\\u043D\\u043D\\u0438 \\u043E\\u0442 \\u0441\\u044A\\u0440\\u0432\\u044A\\u0440\\u0430 \\u0441 \\u043F\\u043E\\u043C\\u043E\\u0449\\u0442\\u0430 \\u043D\\u0430 Angular Material \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u0438.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_12_listener() {\n              return ctx.getForecasts();\n            });\n            i0.ɵɵelementStart(13, \"mat-icon\");\n            i0.ɵɵtext(14, \"refresh\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(15, \" \\u041E\\u0431\\u043D\\u043E\\u0432\\u0438 \\u0434\\u0430\\u043D\\u043D\\u0438\\u0442\\u0435 \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(16, DashboardComponent_div_16_Template, 10, 1, \"div\", 3);\n            i0.ɵɵtemplate(17, DashboardComponent_mat_card_17_Template, 23, 3, \"mat-card\", 4);\n            i0.ɵɵelementStart(18, \"mat-card\", 5)(19, \"mat-card-header\")(20, \"mat-card-title\")(21, \"mat-icon\");\n            i0.ɵɵtext(22, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"mat-card-subtitle\");\n            i0.ɵɵtext(25);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"mat-card-content\")(27, \"p\");\n            i0.ɵɵtext(28, \"\\u0418\\u0437\\u0441\\u043B\\u0435\\u0434\\u0432\\u0430\\u0439\\u0442\\u0435 \\u0446\\u044F\\u043B\\u043E\\u0441\\u0442\\u043D\\u0430\\u0442\\u0430 \\u043F\\u0440\\u043E\\u0444\\u0435\\u0441\\u0438\\u043E\\u043D\\u0430\\u043B\\u043D\\u0430 \\u043F\\u0440\\u043E\\u0444\\u0438\\u043B\\u043D\\u0430 \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430 \\u0441 \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D \\u0438 \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u0438, \\u0432\\u0434\\u044A\\u0445\\u043D\\u043E\\u0432\\u0435\\u043D\\u0438 \\u043E\\u0442 LinkedIn.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 6)(30, \"span\", 7);\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"span\", 7);\n            i0.ɵɵtext(33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"span\", 7);\n            i0.ɵɵtext(35);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"span\", 7);\n            i0.ɵɵtext(37);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(38, \"mat-card-actions\")(39, \"button\", 8)(40, \"mat-icon\");\n            i0.ɵɵtext(41, \"launch\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(42);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"button\", 9)(44, \"mat-icon\");\n            i0.ɵɵtext(45, \"visibility\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(46);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(47, \"app-material-demo\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.title, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.weatherForecast, \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", !ctx.forecasts);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.forecasts);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.profileSystem, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.profileSystemDescription, \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.t.dashboard.readingPortfolio);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.t.dashboard.astrologicalSkills);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.t.dashboard.spiritualJourney);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.t.dashboard.cosmicAnalytics);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.tryProfileDemo, \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", ctx.t.dashboard.viewSampleProfile, \" \");\n          }\n        },\n        dependencies: [i3.NgIf, i4.RouterLink, i5.MatButton, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow, i8.MatIcon, i9.MatProgressSpinner, i10.MaterialDemoComponent, i3.DatePipe],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto}.header-card[_ngcontent-%COMP%]{margin-bottom:20px}.header-card[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;color:var(--theme-primary)}.header-card[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%]{color:#0009;margin-top:8px}.loading-container[_ngcontent-%COMP%]{margin:20px 0}.loading-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.loading-content[_ngcontent-%COMP%]   .mat-spinner[_ngcontent-%COMP%]{margin:0 auto 20px}.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:10px 0;color:#0009}.data-card[_ngcontent-%COMP%]{margin:20px 0}.data-card[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;color:var(--theme-primary)}.table-container[_ngcontent-%COMP%]{overflow-x:auto;margin-top:16px}.weather-table[_ngcontent-%COMP%]{width:100%;min-width:600px}.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#f5f5f5;font-weight:600}.weather-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{margin-right:8px;font-size:18px;vertical-align:middle}.temperature[_ngcontent-%COMP%]{font-weight:500;color:#ff9800}.summary[_ngcontent-%COMP%]{font-style:italic;color:#666}.profile-demo-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(103,58,183,.05) 0%,rgba(255,193,7,.05) 100%);border-left:4px solid var(--theme-primary)}.profile-features[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;margin:16px 0}.feature-tag[_ngcontent-%COMP%]{background-color:var(--theme-primary);color:#fff;padding:4px 12px;border-radius:16px;font-size:.8rem;font-weight:500}.profile-demo-card[_ngcontent-%COMP%]   .mat-card-actions[_ngcontent-%COMP%]{display:flex;gap:12px;flex-wrap:wrap}@media (max-width: 768px){.dashboard-container[_ngcontent-%COMP%]{padding:16px}.weather-table[_ngcontent-%COMP%]{min-width:500px}}\"]\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}