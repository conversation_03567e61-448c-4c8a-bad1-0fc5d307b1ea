<svg width="800" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with mystical gradient -->
  <defs>
    <linearGradient id="coverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#d2a6d0;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#a07ba0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#67455c;stop-opacity:1" />
    </linearGradient>
    <filter id="starGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main background -->
  <rect width="800" height="300" fill="url(#coverGradient)"/>
  
  <!-- Mystical stars and elements -->
  <g filter="url(#starGlow)">
    <!-- Stars -->
    <polygon points="100,50 105,65 120,65 108,75 113,90 100,80 87,90 92,75 80,65 95,65" fill="#ffffff" opacity="0.7"/>
    <polygon points="200,80 203,88 211,88 205,93 208,101 200,96 192,101 195,93 189,88 197,88" fill="#ffffff" opacity="0.6"/>
    <polygon points="300,40 302,46 308,46 304,50 306,56 300,52 294,56 296,50 292,46 298,46" fill="#ffffff" opacity="0.8"/>
    <polygon points="450,70 452,76 458,76 454,80 456,86 450,82 444,86 446,80 442,76 448,76" fill="#ffffff" opacity="0.5"/>
    <polygon points="600,45 603,53 611,53 605,58 608,66 600,61 592,66 595,58 589,53 597,53" fill="#ffffff" opacity="0.7"/>
    <polygon points="700,85 702,91 708,91 704,95 706,101 700,97 694,101 696,95 692,91 698,91" fill="#ffffff" opacity="0.6"/>
    
    <!-- Mystical circles -->
    <circle cx="150" cy="120" r="3" fill="#ffffff" opacity="0.4"/>
    <circle cx="350" cy="150" r="2" fill="#ffffff" opacity="0.5"/>
    <circle cx="550" cy="130" r="2.5" fill="#ffffff" opacity="0.3"/>
    <circle cx="750" cy="140" r="2" fill="#ffffff" opacity="0.6"/>
    
    <!-- Constellation lines -->
    <path d="M 100,50 L 200,80 L 300,40" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
    <path d="M 450,70 L 600,45 L 700,85" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
  </g>
  
  <!-- Subtle overlay pattern -->
  <rect width="800" height="300" fill="url(#coverGradient)" opacity="0.1"/>
</svg>
