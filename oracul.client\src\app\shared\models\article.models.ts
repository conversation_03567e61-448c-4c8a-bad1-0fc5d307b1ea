export interface Article {
  id: number;
  title: string;
  excerpt: string;
  content?: string; // Full content, may be null for preview mode
  author: string;
  publishedAt: Date;
  readTime: number;
  category: string;
  imageUrl: string;
  slug: string;
  readCount: number;
  tags: string[];
  featuredImageUrl?: string;
  userProfileId: number;
}

export interface ArticlePreview {
  id: number;
  title: string;
  excerpt: string;
  author: string;
  publishedAt: Date;
  readTime: number;
  category: string;
  imageUrl: string;
  slug: string;
  readCount: number;
  tags: string[];
  featuredImageUrl?: string;
}

export interface ArticleListResponse {
  articles: ArticlePreview[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ArticleSearchRequest {
  searchTerm?: string;
  category?: string;
  authorId?: number;
  page?: number;
  pageSize?: number;
  sortBy?: 'publishedAt' | 'readCount' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export interface ArticleManagementDto {
  id: number;
  title: string;
  excerpt: string;
  content?: string;
  category?: string;
  tags: string[];
  featuredImageUrl?: string;
  metaDescription?: string;
  metaKeywords?: string;
  allowComments: boolean;
  isFeatured: boolean;
  status: string;
  publishedAt?: Date;
  lastSavedAt?: Date;
  createdAt: Date;
  updatedAt?: Date;
  slug: string;
  readCount: number;
  estimatedReadTime: number;
  userProfileId: number;
  author: string;
}

export interface ArticleManagementListResponse {
  articles: ArticleManagementDto[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
