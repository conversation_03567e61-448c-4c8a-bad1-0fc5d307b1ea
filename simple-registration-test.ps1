# Simple registration test script
Write-Host "Simple Registration Field Storage Test" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

# Check if the server is running
$serverUrl = "http://localhost:5144"
$apiUrl = "$serverUrl/api"

Write-Host "`nChecking if server is running at $serverUrl..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri $serverUrl -Method GET -UseBasicParsing -ErrorAction Stop
    Write-Host "Server is running" -ForegroundColor Green
} catch {
    Write-Host "Server is not running. Please start the server first." -ForegroundColor Red
    Write-Host "Run: dotnet run --project Oracul.Server" -ForegroundColor Yellow
    exit 1
}

# Test 1: Register a general user
Write-Host "`n1. Testing General User Registration..." -ForegroundColor Cyan

$generalUser = @{
    firstName = "Test"
    lastName = "User"
    email = "testuser$(Get-Random)@example.com"
    phoneNumber = "+359888123456"
    password = "TestPassword123!"
    confirmPassword = "TestPassword123!"
    acceptTerms = $true
}

$generalUserJson = $generalUser | ConvertTo-Json
$generalUserBytes = [System.Text.Encoding]::UTF8.GetBytes($generalUserJson)

try {
    $response = Invoke-RestMethod -Uri "$apiUrl/auth/register" -Method POST -Body $generalUserBytes -ContentType "application/json; charset=utf-8"
    Write-Host "General user registration successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
} catch {
    Write-Host "General user registration failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Register an oracle user
Write-Host "`n2. Testing Oracle User Registration..." -ForegroundColor Cyan

$oracleUser = @{
    # Basic information
    firstName = "Oracle"
    lastName = "Tester"
    email = "oracle$(Get-Random)@example.com"
    phoneNumber = "+************"
    password = "OraclePassword123!"
    confirmPassword = "OraclePassword123!"
    acceptTerms = $true
    
    # Professional information
    professionalTitle = "Master Astrologer"
    headline = "Experienced astrologer specializing in natal charts and spiritual guidance"
    summary = "With over 10 years of experience in astrology and spiritual counseling, I provide deep insights into your life path through the wisdom of the stars."
    primarySpecialization = "Astrology"
    yearsOfExperience = 10
    
    # Location information
    city = "Sofia"
    state = "Sofia-grad"
    country = "Bulgaria"
    displayLocation = "Sofia, Bulgaria"
    
    # Oracle-specific information
    birthDate = "1985-06-15T00:00:00Z"
    birthTime = "14:30:00"
    birthLocation = "Plovdiv, Bulgaria"
    astrologicalSign = "Gemini"
    oracleTypes = @("Tarot Cards", "Astrology Charts", "Crystals")
    languagesSpoken = @("Bulgarian", "English", "Russian")
    skills = @("Natal Astrology", "Tarot Reading", "Crystal Healing", "Meditation")
    
    # Contact & business information
    website = "https://oracletester.com"
    portfolioUrl = "https://portfolio.oracletester.com"
    businessAddress = @{
        street = "Vitosha Street 123"
        city = "Sofia"
        state = "Sofia-grad"
        postalCode = "1000"
        country = "Bulgaria"
        isPublic = $true
    }
    
    # Consultation rates
    consultationRates = @{
        hourlyRate = 80
        sessionRate = 120
        currency = "BGN"
    }
}

$oracleUserJson = $oracleUser | ConvertTo-Json -Depth 3
$oracleUserBytes = [System.Text.Encoding]::UTF8.GetBytes($oracleUserJson)

try {
    $response = Invoke-RestMethod -Uri "$apiUrl/auth/register-oracle" -Method POST -Body $oracleUserBytes -ContentType "application/json; charset=utf-8"
    Write-Host "Oracle user registration successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
} catch {
    Write-Host "Oracle user registration failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorContent = $reader.ReadToEnd()
        Write-Host "Error details: $errorContent" -ForegroundColor Red
    }
}

# Test 3: Register oracle with minimal data
Write-Host "`n3. Testing Oracle User Registration with Minimal Data..." -ForegroundColor Cyan

$minimalOracle = @{
    firstName = "Minimal"
    lastName = "Oracle"
    email = "minimal$(Get-Random)@example.com"
    password = "MinimalPassword123!"
    confirmPassword = "MinimalPassword123!"
    acceptTerms = $true
    professionalTitle = "Astrologer"
    headline = "Basic astrology services"
    summary = "Providing basic astrology consultations"
    primarySpecialization = "Astrology"
    yearsOfExperience = 1
    city = "Varna"
    country = "Bulgaria"
    birthDate = "1990-01-01T00:00:00Z"
    birthLocation = "Varna, Bulgaria"
    oracleTypes = @("Astrology Charts")
    languagesSpoken = @("Bulgarian")
    skills = @("Astrology")
}

$minimalOracleJson = $minimalOracle | ConvertTo-Json -Depth 2
$minimalOracleBytes = [System.Text.Encoding]::UTF8.GetBytes($minimalOracleJson)

try {
    $response = Invoke-RestMethod -Uri "$apiUrl/auth/register-oracle" -Method POST -Body $minimalOracleBytes -ContentType "application/json; charset=utf-8"
    Write-Host "Minimal oracle user registration successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
} catch {
    Write-Host "Minimal oracle user registration failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nManual Registration Test Summary:" -ForegroundColor Yellow
Write-Host "- Test completed. Check the database to verify all fields are stored correctly." -ForegroundColor White
Write-Host "- You can use SQL Server Management Studio or similar tool to inspect the following tables:" -ForegroundColor White
Write-Host "  • Users (basic user information)" -ForegroundColor Gray
Write-Host "  • UserRoles (role assignments)" -ForegroundColor Gray
Write-Host "  • UserProfiles (oracle profile information)" -ForegroundColor Gray
Write-Host "  • ProfileLocations (location data)" -ForegroundColor Gray
Write-Host "  • ContactInformations (contact details)" -ForegroundColor Gray
Write-Host "  • BusinessAddresses (business address data)" -ForegroundColor Gray
Write-Host "  • ProfileSkills (skills and expertise)" -ForegroundColor Gray

Write-Host "`nTest completed!" -ForegroundColor Green
