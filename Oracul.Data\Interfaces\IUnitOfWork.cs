using Oracul.Data.Models;

namespace Oracul.Data.Interfaces
{
    /// <summary>
    /// Unit of Work pattern interface for managing transactions and repositories
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        // Repository properties
        IUserRepository Users { get; }
        IRepository<Role> Roles { get; }
        IRepository<Permission> Permissions { get; }
        IRepository<UserRole> UserRoles { get; }
        IRepository<RolePermission> RolePermissions { get; }
        IProfileRepository Profiles { get; }
        IBlogPostRepository BlogPosts { get; }

        // Transaction methods
        int SaveChanges();
        Task<int> SaveChangesAsync();
        void BeginTransaction();
        void CommitTransaction();
        void RollbackTransaction();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
