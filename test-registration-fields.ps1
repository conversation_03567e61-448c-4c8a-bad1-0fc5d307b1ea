# PowerShell script to test registration field storage
Write-Host "Testing Registration Field Storage" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

# Change to the test directory
Set-Location "Oracul.Server.Tests"

# Restore packages
Write-Host "`nRestoring test packages..." -ForegroundColor Yellow
dotnet restore

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to restore packages" -ForegroundColor Red
    exit 1
}

# Build the test project
Write-Host "`nBuilding test project..." -ForegroundColor Yellow
dotnet build

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to build test project" -ForegroundColor Red
    exit 1
}

# Run the registration field storage tests
Write-Host "`nRunning registration field storage tests..." -ForegroundColor Yellow
dotnet test --filter "RegistrationFieldStorageTests" --verbosity normal

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nAll tests passed! ✅" -ForegroundColor Green
    Write-Host "Registration fields are being stored correctly in the database." -ForegroundColor Green
} else {
    Write-Host "`nSome tests failed! ❌" -ForegroundColor Red
    Write-Host "There may be issues with field storage in the database." -ForegroundColor Red
}

# Return to original directory
Set-Location ".."

Write-Host "`nTest execution completed." -ForegroundColor Cyan
