import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface ImageUploadResponse {
  url: string;
  fileName: string;
  size: number;
}

@Injectable({
  providedIn: 'root'
})
export class FileUploadService {
  private readonly API_URL = `${environment.apiUrl}/fileupload`;

  constructor(private http: HttpClient) {}

  /**
   * Upload an image file
   */
  uploadImage(file: File): Observable<ImageUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<ImageUploadResponse>(`${this.API_URL}/image`, formData)
      .pipe(catchError(this.handleError));
  }

  /**
   * Delete an uploaded image
   */
  deleteImage(fileName: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/image?fileName=${encodeURIComponent(fileName)}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Validate image file before upload
   */
  validateImageFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Неподдържан формат на файла. Разрешени са: JPG, PNG, GIF, WebP'
      };
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'Файлът е твърде голям. Максималният размер е 5MB'
      };
    }

    return { valid: true };
  }

  /**
   * Get full URL for uploaded image
   */
  getImageUrl(relativePath: string): string {
    if (relativePath.startsWith('http')) {
      return relativePath; // Already a full URL
    }
    return `${environment.apiUrl.replace('/api', '')}${relativePath}`;
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Възникна неочаквана грешка при качването на файла';

    if (error.error && error.error.message) {
      errorMessage = error.error.message;
    } else if (error.status === 0) {
      errorMessage = 'Няма връзка със сървъра';
    } else if (error.status === 401) {
      errorMessage = 'Необходима е автентификация за качване на файлове';
    } else if (error.status === 403) {
      errorMessage = 'Нямате права за качване на файлове';
    } else if (error.status === 413) {
      errorMessage = 'Файлът е твърде голям';
    } else if (error.status >= 500) {
      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';
    }

    console.error('File upload service error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
